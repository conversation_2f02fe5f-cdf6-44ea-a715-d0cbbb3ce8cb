emailVerificationSubject=Potrdite e-poštni naslov
emailVerificationBody=Nekdo je ustvaril račun {2} s tem e-poštnim naslovom. Če ste to bili vi, kliknite spodnjo povezavo, da potrdite svoj e-poštni naslov\n\n{0}\n\nTa povezava bo potekla v {3}.\n\nČe niste ustvarili tega računa, preprosto ignorirajte to sporočilo.
emailVerificationBodyHtml=<p>Nekdo je ustvaril račun {2} s tem e-poštnim naslovom. Če ste to bili vi, kliknite spodnjo povezavo, da potrdite svoj e-poštni naslov</p><p><a href="{0}">Povezava za potrditev e-poštnega naslova</a></p><p>Ta povezava bo potekla v {3}.</p><p>Če niste ustvarili tega računa, preprosto ignorirajte to sporočilo.</p>
orgInviteSubject=Vabilo za pridružitev organizaciji {0}
orgInviteBody=Povabljeni ste bili, da se pridružite organizaciji "{3}". Kliknite spodnjo povezavo za pridružitev.\n\n{0}\n\nTa povezava bo potekla v {4}.\n\nČe se ne želite pridružiti organizaciji, preprosto ignorirajte to sporočilo.
orgInviteBodyHtml=<p>Povabljeni ste bili, da se pridružite organizaciji {3}. Kliknite spodnjo povezavo za pridružitev.</p><p><a href="{0}">Povezava za pridružitev organizaciji</a></p><p>Ta povezava bo potekla v {4}.</p><p>Če se ne želite pridružiti organizaciji, preprosto ignorirajte to sporočilo.</p>
orgInviteBodyPersonalized=Pozdravljeni, "{5}" "{6}".\n\nPovabljeni ste bili, da se pridružite organizaciji "{3}". Kliknite spodnjo povezavo za pridružitev.\n\n{0}\n\nTa povezava bo potekla v {4}.\n\nČe se ne želite pridružiti organizaciji, preprosto ignorirajte to sporočilo.
orgInviteBodyPersonalizedHtml=<p>Pozdravljeni, {5} {6}.</p><p>Povabljeni ste bili, da se pridružite organizaciji {3}. Kliknite spodnjo povezavo za pridružitev.</p><p><a href="{0}">Povezava za pridružitev organizaciji</a></p><p>Ta povezava bo potekla v {4}.</p><p>Če se ne želite pridružiti organizaciji, preprosto ignorirajte to sporočilo.</p>
emailUpdateConfirmationSubject=Potrdite nov e-poštni naslov
emailUpdateConfirmationBody=Za posodobitev vašega računa {2} z e-poštnim naslovom {1}, kliknite spodnjo povezavo\n\n{0}\n\nTa povezava bo potekla v {3}.\n\nČe ne želite nadaljevati s to spremembo, preprosto ignorirajte to sporočilo.
emailUpdateConfirmationBodyHtml=<p>Za posodobitev vašega računa {2} z e-poštnim naslovom {1}, kliknite spodnjo povezavo</p><p><a href="{0}">{0}</a></p><p>Ta povezava bo potekla v {3}.</p><p>Če ne želite nadaljevati s to spremembo, preprosto ignorirajte to sporočilo.</p>
emailTestSubject=[KEYCLOAK] - Testno SMTP sporočilo
emailTestBody=To je testno sporočilo
emailTestBodyHtml=<p>To je testno sporočilo</p>
identityProviderLinkSubject=Povežite {0}
identityProviderLinkBody=Nekdo želi povezati vaš račun "{1}" z računom "{0}" uporabnika {2}. Če ste to bili vi, kliknite spodnjo povezavo za povezavo računov\n\n{3}\n\nTa povezava bo potekla v {5}.\n\nČe ne želite povezati računa, preprosto ignorirajte to sporočilo. Če povežete račune, se boste lahko prijavili v {1} preko {0}.
identityProviderLinkBodyHtml=<p>Nekdo želi povezati vaš račun <b>{1}</b> z računom <b>{0}</b> uporabnika {2}. Če ste to bili vi, kliknite spodnjo povezavo za povezavo računov</p><p><a href="{3}">Povezava za potrditev povezave računov</a></p><p>Ta povezava bo potekla v {5}.</p><p>Če ne želite povezati računa, preprosto ignorirajte to sporočilo. Če povežete račune, se boste lahko prijavili v {1} preko {0}.</p>
passwordResetSubject=Ponastavite svoje geslo
passwordResetBody=Nekdo je pravkar zahteval spremembo poverilnic za vaš račun {2}. Če ste to bili vi, kliknite spodnjo povezavo, da jih ponastavite.\n\n{0}\n\nTa povezava bo potekla v {3}.\n\nČe ne želite ponastaviti svojih poverilnic, preprosto ignorirajte to sporočilo in nič se ne bo spremenilo.
passwordResetBodyHtml=<p>Nekdo je pravkar zahteval spremembo poverilnic za vaš račun {2}. Če ste to bili vi, kliknite spodnjo povezavo, da jih ponastavite.</p><p><a href="{0}">Povezava za ponastavitev poverilnic</a></p><p>Ta povezava bo potekla v {3}.</p><p>Če ne želite ponastaviti svojih poverilnic, preprosto ignorirajte to sporočilo in nič se ne bo spremenilo.</p>
executeActionsSubject=Posodobite svoj račun
executeActionsBody=Vaš administrator je pravkar zahteval, da posodobite svoj račun {2}, tako da izvedete naslednje dejanje(-a): {3}. Kliknite spodnjo povezavo, da začnete ta postopek.\n\n{0}\n\nTa povezava bo potekla v {4}.\n\nČe ne veste, da je vaš administrator to zahteval, preprosto ignorirajte to sporočilo in nič se ne bo spremenilo.
executeActionsBodyHtml=<p>Vaš administrator je pravkar zahteval, da posodobite svoj račun {2}, tako da izvedete naslednje dejanje(-a): {3}. Kliknite spodnjo povezavo, da začnete ta postopek.</p><p><a href="{0}">Povezava za posodobitev računa</a></p><p>Ta povezava bo potekla v {4}.</p><p>Če ne veste, da je vaš administrator to zahteval, preprosto ignorirajte to sporočilo in nič se ne bo spremenilo.</p>
eventLoginErrorSubject=Napaka pri prijavi
eventLoginErrorBody=Zaznana je bila neuspešna prijava v vaš račun dne {0} iz {1}. Če to niste bili vi, se obrnite na administratorja.
eventLoginErrorBodyHtml=<p>Zaznana je bila neuspešna prijava v vaš račun dne {0} iz {1}. Če to niste bili vi, se obrnite na administratorja.</p>
eventRemoveTotpSubject=Odstranitev OTP
eventRemoveTotpBody=OTP je bil odstranjen iz vašega računa dne {0} iz {1}. Če to niste bili vi, se obrnite na administratorja.
eventRemoveTotpBodyHtml=<p>OTP je bil odstranjen iz vašega računa dne {0} iz {1}. Če to niste bili vi, se obrnite na administratorja.</p>
eventUpdatePasswordSubject=Posodobite geslo
eventUpdatePasswordBody=Vaše geslo je bilo spremenjeno dne {0} iz {1}. Če to niste bili vi, se obrnite na administratorja.
eventUpdatePasswordBodyHtml=<p>Vaše geslo je bilo spremenjeno dne {0} iz {1}. Če to niste bili vi, se obrnite na administratorja.</p>
eventUpdateTotpSubject=Posodobitev OTP
eventUpdateTotpBody=OTP je bil posodobljen za vaš račun dne {0} iz {1}. Če to niste bili vi, se obrnite na administratorja.
eventUpdateTotpBodyHtml=<p>OTP je bil posodobljen za vaš račun dne {0} iz {1}. Če to niste bili vi, se obrnite na administratorja.</p>
eventUpdateCredentialSubject=Posodobitev poverilnice
eventUpdateCredentialBody=Vaša poverilnica {0} je bila spremenjena dne {1} iz {2}. Če to niste bili vi, se obrnite na administratorja.
eventUpdateCredentialBodyHtml=<p>Vaša poverilnica {0} je bila spremenjena dne {1} iz {2}. Če to niste bili vi, se obrnite na administratorja.</p>
eventRemoveCredentialSubject=Odstranitev poverilnice
eventRemoveCredentialBody=Poverilnica {0} je bila odstranjena iz vašega računa dne {1} iz {2}. Če to niste bili vi, se obrnite na administratorja.
eventRemoveCredentialBodyHtml=<p>Poverilnica {0} je bila odstranjena iz vašega računa dne {1} iz {2}. Če to niste bili vi, se obrnite na administratorja.</p>
eventUserDisabledByTemporaryLockoutSubject=Uporabnik začasno onemogočen zaradi zaklepa
eventUserDisabledByTemporaryLockoutBody=Vaš uporabniški račun je bil začasno onemogočen zaradi več neuspešnih poskusov dne {0}. Po potrebi se obrnite na administratorja.
eventUserDisabledByTemporaryLockoutHtml=<p>Vaš uporabniški račun je bil začasno onemogočen zaradi več neuspešnih poskusov dne {0}. Po potrebi se obrnite na administratorja.</p>
eventUserDisabledByPermanentLockoutSubject=Uporabnik trajno onemogočen zaradi zaklepa
eventUserDisabledByPermanentLockoutBody=Vaš uporabniški račun je bil trajno onemogočen zaradi več neuspešnih poskusov dne {0}. Prosimo, obrnite se na administratorja.
eventUserDisabledByPermanentLockoutHtml=<p>Vaš uporabniški račun je bil trajno onemogočen zaradi več neuspešnih poskusov dne {0}. Prosimo, obrnite se na administratorja.</p>
requiredAction.CONFIGURE_TOTP=Konfiguriraj OTP
requiredAction.TERMS_AND_CONDITIONS=Splošni pogoji
requiredAction.UPDATE_PASSWORD=Posodobi geslo
requiredAction.UPDATE_PROFILE=Posodobi profil
requiredAction.VERIFY_EMAIL=Potrdi e-naslov
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Generiraj kode za obnovitev

# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be '{0,choice,0#minut|1#minuta|2#minuty|2<minut}'
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#sekund|1#sekunda|2#sekundi|3#sekunde|4#sekunde|5#sekund}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#minut|1#minuta|2#minuti|3#minute|4#minute|5#minut}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#ur|1#ura|2#uri|3#ure|4#ure|5#ur}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#dni|1#dan|2#dneva|3#dni|4#dni|5#dni}
emailVerificationBodyCode=Prosimo, potrdite svoj e-poštni naslov z vnosom naslednje kode.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Prosimo, potrdite svoj e-poštni naslov z vnosom naslednje kode.</p><p><b>{0}</b></p>
