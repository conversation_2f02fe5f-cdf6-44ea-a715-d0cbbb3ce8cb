doSave=Mentés
doCancel=Mégsem
doLogOutAllSessions=Minden munkamenet kiléptetése
doRemove=Törlés
doAdd=Hozzáadás
doSignOut=Kilépés
doLogIn=Belépés
doLink=Összekötés
noAccessMessage=Nincs hozzáférés

personalInfoSidebarTitle=Személyes adatok
accountSecuritySidebarTitle=Fiók biztonság
signingInSidebarTitle=Bejelentkezés
deviceActivitySidebarTitle=Eszköz történet
linkedAccountsSidebarTitle=Összekapcsolt fiókok

editAccountHtmlTitle=Fiók szerkesztése
personalInfoHtmlTitle=Személyes adatok
federatedIdentitiesHtmlTitle=Összekapcsolt személyazonosságok
accountLogHtmlTitle=Fiók napló
changePasswordHtmlTitle=Je<PERSON>zó csere
deviceActivityHtmlTitle=Eszköz történet
sessionsHtmlTitle=Munkamenetek
accountManagementTitle=Keycloak Fiók Kezelő
authenticatorTitle=Hitelesítő
applicationsHtmlTitle=Alkalmazások
linkedAccountsHtmlTitle=Összekötött fiókok

accountManagementWelcomeMessage=Üdvözöljük a Keycloak Fiók Kezelőben
personalInfoIntroMessage=Kezelje az alap személyes adatait
accountSecurityTitle=Fiók biztonság
accountSecurityIntroMessage=Szabályozza jelszó és fiók hozzáféréseit
applicationsIntroMessage=Kezelje alkalmazás jogosultságait, hogy hozzáférjen a fiókjához
resourceIntroMessage=Ossza meg az erőforrásait csapattagjai között
passwordLastUpdateMessage=A jelszava ekkor módosult
updatePasswordTitle=Módosítsa jelszavát
updatePasswordMessageTitle=Kérem, válasszon erős jelszót
updatePasswordMessage=Egy erős jelszó számok, betűk és speciális karakterek keveréke, nehéz kitalálni, nem hasonlít valódi (szótári) szóra és csak ehhez a fiókhoz tartozik.
personalSubTitle=Személyes adatai
personalSubMessage=Kezelje alapvető személyes adatait: vezetéknév, keresztnév, e-mail cím

authenticatorCode=Egyszer használatos kód
email=E-mail cím
firstName=Keresztnév
givenName=Keresztnév
fullName=Teljes név
lastName=Vezetéknév
familyName=Vezetéknév
password=Jelszó
currentPassword=Jelenlegi jelszó
passwordConfirm=Megerősítés
passwordNew=Új jelszó
username=Felhasználónév
address=Cím
street=Közterület
locality=Település
region=Állam, Tartomány, Megye, Régió
postal_code=Irányítószám
country=Ország
emailVerified=Ellenőrzött e-mail cím
website=Weboldal
phoneNumber=Telefonszám
phoneNumberVerified=Ellenőrzött telefonszám
gender=Nem
birthday=Születési dátum
zoneinfo=Időzóna
gssDelegationCredential=GSS delegált hitelesítés

profileScopeConsentText=Felhasználói fiók
emailScopeConsentText=E-mail cím
addressScopeConsentText=Cím
phoneScopeConsentText=Telefonszám
offlineAccessScopeConsentText=Offline hozzáférés
samlRoleListScopeConsentText=Szerepköreim
rolesScopeConsentText=Felhasználói szerepkörök

role_admin=Adminisztrátor
role_realm-admin=Tartomány Adminisztrátor
role_create-realm=Tartomány létrehozása
role_view-realm=Tartományok megtekintése
role_view-users=Felhasználók megtekintése
role_view-applications=Alkalmazások megtekintése
role_view-groups=Csoportok megtekintése
role_view-clients=Kliensek megtekintése
role_view-events=Események megtekintése
role_view-identity-providers=Személyazonosság-kezelők megtekintése
role_view-consent=Jóváhagyó nyilatkozatok megtekintése
role_manage-realm=Tartományok kezelése
role_manage-users=Felhasználók kezelése
role_manage-applications=Alkalmazások kezelése
role_manage-identity-providers=Személyazonosság-kezelők karbantartása
role_manage-clients=Kliensek kezelése
role_manage-events=Események kezelése
role_view-profile=Fiók megtekintése
role_manage-account=Fiók kezelése
role_manage-account-links=Fiók összekötések kezelése
role_manage-consent=Jóváhagyó nyilatkozatok kezelése
role_read-token=Olvasási token
role_offline-access=Offline hozzáférés
role_uma_authorization=Hozzáférés jogosultságokhoz (UMA)
client_account=Fiók
client_account-console=Fiók kezelés
client_security-admin-console=Biztonsági, adminisztrátor fiók kezelés
client_admin-cli=Admin CLI
client_realm-management=Tartomány kezelés
client_broker=Ügynök


requiredFields=Kötelezően kitöltendő mezők
allFieldsRequired=Minden mező kitöltése kötelező

backToApplication=&laquo; Vissza az alkalmazásba
backTo=Vissza a {0}-ba/be

date=Dátum
event=Esemény
ip=IP cím
client=Kliens
clients=Kliensek
details=Részletek
started=Kezdete
lastAccess=Utolsó hozzáférés
expires=Lejárat
applications=Alkalmazások

account=Fiók
federatedIdentity=Összekapcsolt személyazonosság
authenticator=Hitelesítő
device-activity=Eszköz történet
sessions=Munkamentek
log=Napló

application=Alkalmazás
availableRoles=Elérhető szerepkörök
grantedPermissions=Engedélyezett jogosultságok
grantedPersonalInfo=Engedélyezett személyes adatok
additionalGrants=További engedélyek
action=Művelet
inResource=itt:
fullAccess=Teljes hozzáférés
offlineToken=Offline Token
revoke=Engedély visszavonása

configureAuthenticators=Beállított Hitelesítők
mobile=Mobil eszköz
totpStep1=Kérem, telepítse az itt felsorolt alkalmazások egyikét a mobil eszközére:
totpStep2=Indítsa el az alkalmazást a mobil eszközén és olvassa be ezt a (QR) kódot:
totpStep3=Adja meg az alkalmazás által generált egyszer használatos kódot majd kattintson a Mentés gombra a beállítás befejezéséhez.
totpStep3DeviceName=Adja meg a mobil eszköz nevét. Ez a későbbiekben segíthet az eszköz azonosításában.

totpManualStep2=Indítsa el az alkalmazás és adja meg a következő kulcsot:
totpManualStep3=Használja a következő beállításokat, ha az alkalmazása támogatja ezeket:
totpUnableToScan=Nem tud (QR) kódot beolvasni?
totpScanBarcode=Inkább (QR) kódot olvasna be?

totp.totp=Idő alapú
totp.hotp=Számláló alapú

totpType=Típus
totpAlgorithm=Algoritmus
totpDigits=Számjegyek
totpInterval=Intervallum
totpCounter=Számláló
totpDeviceName=Eszköz neve

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

irreversibleAction=Ez a művelet visszavonhatatlan
deletingImplies=A felhasználói fiókjának törlésével jár:
errasingData=Összes adatának törlése
loggingOutImmediately=Azonnali kijelentkezés
accountUnusable=Az alkalmazás további használata nem lesz lehetséges ezzel a felhasználói fiókkal

missingUsernameMessage=Kérem, adja meg a felhasználónevét.
missingFirstNameMessage=Kérem, adja meg a keresztnevet.
invalidEmailMessage=Érvénytelen e-mail cím.
missingLastNameMessage=Kérem, adja meg a vezetéknevet.
missingEmailMessage=Kérem, adja meg az e-mail címet.
missingPasswordMessage=Kérem, adja meg a jelszót.
notMatchPasswordMessage=A jelszavak nem egyeznek meg.
invalidUserMessage=Érvénytelen felhasználó
updateReadOnlyAttributesRejectedMessage=Csak olvasható tulajdonság módosítása megtagadva

missingTotpMessage=Kérem, adja meg a hitelesítő kódot.
missingTotpDeviceNameMessage=Kérem, adja meg az eszköz nevét.
invalidPasswordExistingMessage=Érvénytelen jelenlegi jelszó.
invalidPasswordConfirmMessage=A jelszavak nem egyeznek meg.
invalidTotpMessage=Érvénytelen hitelesítő kód.

usernameExistsMessage=Ez a felhasználónév már foglalt.
emailExistsMessage=Ez az e-mail cím már foglalt.

readOnlyUserMessage=A felhasználói fiókja csak olvasható, módosítás nem lehetséges.
readOnlyUsernameMessage=A felhasználónév nem módosítható.
readOnlyPasswordMessage=A felhasználói fiókja csak olvasható, így jelszó módosítás nem lehetséges.

successTotpMessage=A mobil hitelesítőt beállítottuk.
successTotpRemovedMessage=A mobil hitelesítőt eltávolítottuk.

successGrantRevokedMessage=Az engedélyt visszavontuk.

accountUpdatedMessage=Felhasználói fiókját módosítottuk.
accountPasswordUpdatedMessage=Jelszavát módosítottuk.

missingIdentityProviderMessage=Nincs megadva személyazonosság-kezelő.
invalidFederatedIdentityActionMessage=Érvénytelen, vagy nem létező művelet.
identityProviderNotFoundMessage=A megadott személyazonosság-kezelő nem található.
federatedIdentityLinkNotActiveMessage=Ez a személyazonosság összekötés már nem érvényes.
federatedIdentityRemovingLastProviderMessage=Az utolsó összekapcsolt személyazonosság nem törölhető, mert Ön nem rendelkezik érvényes jelszóval.
identityProviderRedirectErrorMessage=Nem sikerült az átirányítás a személyazonosság-kezelőre.
identityProviderRemovedMessage=A személyazonosság-kezelő összekötést töröltük.
identityProviderAlreadyLinkedMessage=Az összekapcsolt személyazonosság-kezelő által bizotsított személyazonosság már össze van kötve egy másik felhasználói fiókkal.
staleCodeAccountMessage=Az oldal érvényességi ideje lejárt. Kérem, próbálja meg újra a kérést.
consentDenied=Jóváhagyó nyilatkozat elutasítva.
access-denied-when-idp-auth=Hozzáférés megtagadva hitelesítés során: {0}

accountDisabledMessage=Felhasználói fiókja inaktív, kérem, vegye fel a kapcsolatot az alkalmazás adminisztrátorával.

accountTemporarilyDisabledMessage=Felhasználói fiókja átmenetileg inaktív, kérem, vegye fel a kapcsolatot az alkalmazás adminisztrátorával, vagy próbálkozzon később.
invalidPasswordMinLengthMessage=Érvénytelen jelszó: minimum hossz: {0}.
invalidPasswordMaxLengthMessage=Érvénytelen jelszó: maximum hossz: {0}.
invalidPasswordMinLowerCaseCharsMessage=Érvénytelen jelszó: legalább {0} darab kisbetűt kell tartalmaznia.
invalidPasswordMinDigitsMessage=Érvénytelen jelszó: legalább {0} darab számjegyet kell tartalmaznia.
invalidPasswordMinUpperCaseCharsMessage=Érvénytelen jelszó: legalább {0} darab nagybetűt kell tartalmaznia.
invalidPasswordMinSpecialCharsMessage=Érvénytelen jelszó: legalább {0} darab speciális karaktert (pl. #!$@ stb.) kell tartalmaznia.
invalidPasswordNotUsernameMessage=Érvénytelen jelszó: nem lehet azonos a felhasználónévvel.
invalidPasswordNotEmailMessage=Érvénytelen jelszó: nem lehet azonos az e-mail címmel.
invalidPasswordRegexPatternMessage=Érvénytelen jelszó: a jelszó nem illeszkedik a megadott reguláris kifejezés mintára.
invalidPasswordHistoryMessage=Érvénytelen jelszó: nem lehet azonos az utolsó {0} darab, korábban alkalmazott jelszóval.
invalidPasswordBlacklistedMessage=Érvénytelen jelszó: a jelszó tiltó listán szerepel.
invalidPasswordGenericMessage=Érvénytelen jelszó: az új jelszó nem felel meg a jelszó házirendnek.

# Authorization
myResources=Erőforrásaim
myResourcesSub=Erőforrásaim
doDeny=Tiltás
doRevoke=Visszavonás
doApprove=Jóváhagyás
doRemoveSharing=Megosztás törlése
doRemoveRequest=Kérelem törlése
peopleAccessResource=Az erőforráshoz hozzáférő felhasználók
resourceManagedPolicies=Az erőforrás hozzáféréshez szükséges jogosultságok
resourceNoPermissionsGrantingAccess=Az erőforrás hozzáféréshez nem szükségesek jogosultságok
anyAction=Bármelyik művelet
description=Leírás
name=Név
scopes=Hatókör
resource=Erőforrás
user=Felhasználó
peopleSharingThisResource=Az erőforrást megosztó felhasználók
shareWithOthers=Megosztás más felhasználókkal
needMyApproval=A jóváhagyásom szükséges
requestsWaitingApproval=A kérése jóváhagyásra vár
icon=Ikon
requestor=Kérelmező
owner=Tulajdonos
resourcesSharedWithMe=Velem megosztott erőforrások
permissionRequestion=Jogosultság kérelem
permission=Jogosultság
shares=megosztás(ok)
notBeingShared=Az erőforrás nincs megosztva
notHaveAnyResource=Nincsen erőforrása
noResourcesSharedWithYou=Nincsenek Önnel megosztott erőforrásai
havePermissionRequestsWaitingForApproval=Önnek {0} darab várakozó, jóváhagyandó jogosultság kérése van.
clickHereForDetails=Kattintson ide a részletekért.
resourceIsNotBeingShared=Az erőforrás nincs megosztva

# Applications
applicationName=Név
applicationType=Alkalmazás típus
applicationInUse=Csak használatban lévő alkalmazás
clearAllFilter=Szűrő mezők törlése
activeFilters=Aktív szűrők
filterByName=Név alapú keresés
allApps=Minden alkalmazás
internalApps=Belső alkalmazások
thirdpartyApps=Harmadik féltől származó alkalmazások
appResults=Eredmény
clientNotFoundMessage=A kliens nem található.

# Linked account
authorizedProvider=Meghatalmazott szolgáltató
authorizedProviderMessage=A felhasználói fiókjához kötött meghatalmazott szolgáltatók
identityProvider=Személyazonosság-kezelő
identityProviderMessage=Fiókja személyazonosság-kezelőkhöz kötéséhez eddig ezeket a beállításokat adta meg
socialLogin=Közösségi bejelentkezés
userDefined=Felhasználó által meghatározott
removeAccess=Hozzáférés törlése
removeAccessMessage=Újra engedélyeznie kell a hozzáférést az alkalmazás ismételt használatához.

#Authenticator
authenticatorStatusMessage=A kétszintű hitelesítés jelenleg
authenticatorFinishSetUpTitle=Kétszintű hitelesítés
authenticatorFinishSetUpMessage=Minden Keycloak fiók bejelentkezéskor kérni fogunk Öntől egy második szintű hitelesítő kódot.
authenticatorSubTitle=Állítsa be a második szintű hitelesítést
authenticatorSubMessage=Felhasználói fiókjának biztonsági szintjét növelheti, ha legalább egy második szintű hitelesítést is bekapcsol az elérhető eljárások közül.
authenticatorMobileTitle=Mobil eszköz alapú hitelesítés
authenticatorMobileMessage=Mobil eszközön generált ellenőrző kód, mint második szintű hitelesítés.
authenticatorMobileFinishSetUpMessage=A hitelesítés a mobil eszközéhez kötődik.
authenticatorActionSetup=Beállítás
authenticatorSMSTitle=SMS kód
authenticatorSMSMessage=A Keycloak SMS ellenőrző kódot küld a telefonjára (második szintű hitelesítő kód).
authenticatorSMSFinishSetUpMessage=A következő telefonszámokra SMS-t küldünk
authenticatorDefaultStatus=Alapértelmezett
authenticatorChangePhone=Módosítsa telefonszámát

#Authenticator - Mobile Authenticator setup
authenticatorMobileSetupTitle=Mobil hitelesítő eszköz beállítása
smscodeIntroMessage=Adja meg a telefonszámát, melyre egy ellenőrző kódot küldünk.
mobileSetupStep1=Telepítsen egy hitelesítő alkalmazást mobil eszközére az itt felsorolt, támogatott, alkalmazások közül.
mobileSetupStep2=Indítsa el az alkalmazást és olvassa be a következő (QR) kódot:
mobileSetupStep3=Adja meg a mobil alkalmazás által generált egyszer használatos kódot, majd kattintson a Mentés gombra a beállításhoz.
scanBarCode=Inkább (QR) kódot olvasna be?
enterBarCode=Adja meg az egyszer használatos kódot
doCopy=Másolás
doFinish=Befejezés

#Authenticator - SMS Code setup
authenticatorSMSCodeSetupTitle=SMS kód beállítása
chooseYourCountry=Válassza ki az országot
enterYourPhoneNumber=Adja meg a telefonszámát
sendVerficationCode=Ellenőrző kód küldése
enterYourVerficationCode=Adja meg az ellenőrző kódot

#Authenticator - backup Code setup
authenticatorBackupCodesSetupTitle=Tartalék kódok beállítása
realmName=Tartomány
doDownload=Letöltés
doPrint=Nyomtatás
generateNewBackupCodes=Új tartalék kódok generálása
backtoAuthenticatorPage=Vissza a hitelesítő lapra


#Resources
resources=Erőforrások
sharedwithMe=Velem megosztott erőforrások
share=Megosztás
sharedwith=Megosztva
accessPermissions=Hozzáférési jogosultságok
permissionRequests=Jogosultság kérések
approve=Jóváhagyás
approveAll=Mindet jóváhagyja
people=felhasználó
perPage=oldalanként
currentPage=Aktuális oldal
sharetheResource=Erőforrás megosztása
group=Csoport
selectPermission=Jogosultság választás
addPeople=Adjon hozzá felhasználókat az erőforrás megosztáshoz
addTeam=Adjon meg csoportot az erőforrás megosztáshoz
myPermissions=Jogosultságaim
waitingforApproval=Jóváhagyásra vár
anyPermission=Bármilyen jogosultság

# Openshift messages
openshift.scope.user_info=Felhasználó adatok
openshift.scope.user_check-access=Felhasználó hozzáférés adatok
openshift.scope.user_full=Teljes hozzáférés
openshift.scope.list-projects=Projektek listája

error-invalid-value=Érvénytelen érték
error-invalid-blank=Kérem, adja meg a mező értékét.
error-empty=Kérem, adja meg a mező értékét.
error-invalid-length={0} hossza {1} és {2} karakter között kell legyen.
error-invalid-length-too-short={0} minimális hossza {1} karakter.
error-invalid-length-too-long={0} maximális hossza {2} karakter.
error-invalid-email=Érvénytelen e-mail cím.
error-invalid-number=Érvénytelen szám.
error-number-out-of-range={0} értéke {1} és {2} közötti szám kell legyen.
error-number-out-of-range-too-small={0} minimum értéke: {1}.
error-number-out-of-range-too-big={0} maximum értéke: {2}.
error-pattern-no-match=Érvénytelen érték.
error-invalid-uri=Érvénytelen URL.
error-invalid-uri-scheme=Érvénytelen URL séma.
error-invalid-uri-fragment=Érvénytelen URL fragmens.
error-user-attribute-required=Kérem, adja meg a(z) {0} értékét.
error-invalid-date=Érvénytelen dátum.
error-user-attribute-read-only=A(z) {0} mező csak olvasható.
error-username-invalid-character=A felhasználónév érvénytelen karaktert tartalmaz.
error-person-name-invalid-character=A név érvénytelen karaktert tartalmaz.
