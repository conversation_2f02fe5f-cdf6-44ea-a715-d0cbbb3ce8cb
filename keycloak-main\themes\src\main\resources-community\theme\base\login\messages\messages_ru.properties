doLogIn=Вход
doRegister=Регистрация
doRegisterSecurityKey=Регистрация
doCancel=Отмена
doSubmit=Подтвердить
doBack=Назад
doYes=Да
doNo=Нет
doContinue=Продолжить
doIgnore=Игнорировать
doAccept=Подтвердить
doDecline=Отменить
doForgotPassword=Забыли пароль?
doClickHere=Нажмите сюда
doImpersonate=Имперсонализироваться
doTryAgain=Попробуйте еще раз
doTryAnotherWay=Попробуйте другой способ
doConfirmDelete=Подтвердите удаление
errorDeletingAccount=Произошла ошибка при удалении аккаунта
deletingAccountForbidden=У вас недостаточно прав для удаления учетной записи, обратитесь к администратору.
kerberosNotConfigured=Kerberos не сконфигурирован
kerberosNotConfiguredTitle=Kerberos не сконфигурирован
bypassKerberosDetail=Либо вы не вошли в систему с помощью Kerberos, либо ваш браузер не настроен для входа в систему Kerberos. Пожалуйста, нажмите кнопку ''Продолжить'' для входа с помощью других средств
kerberosNotSetUp=Kerberos не настроен. Вы не можете войти.
registerTitle=Зарегистрироваться
loginAccountTitle=Вход в учетную запись
loginTitle=Вход {0}
loginTitleHtml={0}
impersonateTitle={0} Имперсонализация пользователя
impersonateTitleHtml=<strong>{0}</strong> Имперсонализация пользователя
realmChoice=Realm
unknownUser=Неизвестный пользователь
loginTotpTitle=Настройка мобильного аутентификатора
loginProfileTitle=Обновление информации учетной записи
loginIdpReviewProfileTitle=Обновление информации учетной записи
loginTimeout=Вы слишком долго бездействовали. Процесс аутентификации начнется с начала.
reauthenticate=Пожалуйста, пройдите повторную аутентификацию, чтобы продолжить
authenticateStrong=Для продолжения требуется строгая аутентификация
oauthGrantTitle=Согласовать доступ для {0}
oauthGrantTitleHtml={0}
oauthGrantInformation=Убедитесь, что вы доверяете {0}, узнав, как {0} будет обрабатывать ваши данные.
oauthGrantReview=Вы могли бы просмотреть
oauthGrantTos=условия обслуживания.
oauthGrantPolicy=политика конфиденциальности.
errorTitle=Мы сожалеем...
errorTitleHtml=Мы <strong>сожалеем</strong> ...
emailVerifyTitle=Подтверждение адреса E-mail
emailForgotTitle=Забыли пароль?
updateEmailTitle=Обновить электронную почту
emailUpdateConfirmationSentTitle=Подтверждение отправлено по электронной почте
emailUpdateConfirmationSent=Письмо с подтверждением отправлено на {0}. Следуйте инструкциям, чтобы завершить обновление электронной почты.
emailUpdatedTitle=Электронная почта обновлена
emailUpdated=Адрес электронной почты учетной записи успешно обновлен на {0}.
updatePasswordTitle=Обновление пароля
codeSuccessTitle=Успешный код
codeErrorTitle=Ошибочный код: {0}
displayUnsupported=Запрошенный тип отображения не поддерживается
browserRequired=Для входа требуется браузер
browserContinue=Для завершения входа требуется браузер
browserContinuePrompt=Открыть браузер и продолжить вход? [y/n]:
browserContinueAnswer=у

# Transports
usb=USB
nfc=NFC
bluetooth=Bluetooth
internal=Внутренний
unknown=Неизвестный
termsTitle=Условия и положения
termsText=<p>Условия и положения должны быть определены</p>
termsPlainText=Условия и положения должны быть определены.
termsAcceptanceRequired=Вы должны согласиться с нашими условиями.
acceptTerms=Я согласен с условиями
deleteCredentialTitle=Удалить {0}
deleteCredentialMessage=Вы хотите удалить {0}?
linkIdpActionTitle=Связывание {0}
linkIdpActionMessage=Хотите ли вы связать свой аккаунт с {0}?
recaptchaFailed=Некорректная Recaptcha
recaptchaNotConfigured=Recaptcha требуется, но не сконфигурирована
consentDenied=В согласовании отказано.
noAccount=Новый пользователь?
username=Имя пользователя
usernameOrEmail=Имя пользователя или E-mail
firstName=Имя
givenName=Выданное имя
fullName=Полное имя
lastName=Фамилия
familyName=Фамилия
email=E-mail
password=Пароль
passwordConfirm=Подтверждение пароля
passwordNew=Новый пароль
passwordNewConfirm=Подтверждение нового пароля
hidePassword=Скрыть пароль
showPassword=Показать пароль
rememberMe=Запомнить меня
authenticatorCode=Одноразовый код
address=Адрес
street=Улица
locality=Город
region=Регион
postal_code=Почтовый индекс
country=Страна
emailVerified=E-mail подтвержден
website=Веб-сайт
phoneNumber=Номер телефона
phoneNumberVerified=Номер телефона проверен
gender=Пол
birthday=Дата рождения
zoneinfo=Часовой пояс
gssDelegationCredential=Делегирование учетных данных GSS
logoutOtherSessions=Выполнить выход на других устройствах
profileScopeConsentText=Профиль пользователя
emailScopeConsentText=Адрес электронной почты
addressScopeConsentText=Адрес
phoneScopeConsentText=Номер телефона
offlineAccessScopeConsentText=Оффлайн доступ
samlRoleListScopeConsentText=Мои роли
rolesScopeConsentText=Роли пользователей
organizationScopeConsentText=Организация
restartLoginTooltip=Перезапустить вход
loginTotpIntro=Вам необходимо настроить генератор одноразовых паролей для доступа к этой учетной записи
loginTotpStep1=Установите одно из следующих приложений на ваш мобильный телефон:
loginTotpStep2=Откройте приложение и просканируйте QR-код:
loginTotpStep3=Введите одноразовый код, выданный приложением, и нажмите Подтвердить для завершения настройки.
loginTotpStep3DeviceName=Укажите имя устройства, которое поможет вам найти его в списке ваших устройств.
loginTotpManualStep2=Откройте приложение и введите ключ:
loginTotpManualStep3=Используйте следующие настройки, если приложение позволяет их устанавливать:
loginTotpUnableToScan=Не удается выполнить сканирование?
loginTotpScanBarcode=Сканировать QR-код?
loginCredential=Учетные данные
loginOtpOneTime=Одноразовый код
loginTotpType=Тип
loginTotpAlgorithm=Алгоритм
loginTotpDigits=Количество цифр
loginTotpInterval=Интервал
loginTotpCounter=Счетчик
loginTotpDeviceName=Имя устройства
loginTotp.totp=На основе времени
loginTotp.hotp=На основе счетчика
totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Аутентификатор
totpAppMicrosoftAuthenticatorName=Аутентификатор Microsoft
loginChooseAuthenticator=Выберите способ входа
oauthGrantRequest=Вы согласуете доступ к этим привилегиям?
inResource=в
oauth2DeviceVerificationTitle=Вход в устройство
verifyOAuth2DeviceUserCode=Введите код, предоставленный вашим устройством, и нажмите «Отправить»
oauth2DeviceInvalidUserCodeMessage=Неверный код, попробуйте еще раз.
oauth2DeviceExpiredUserCodeMessage=Код устарел. Пожалуйста, вернитесь к устройству и попробуйте подключиться снова.
oauth2DeviceVerificationCompleteHeader=Успешный вход в устройство
oauth2DeviceVerificationCompleteMessage=Вы можете закрыть это окно браузера и вернуться на свое устройство.
oauth2DeviceVerificationFailedHeader=Ошибка входа в устройство
oauth2DeviceVerificationFailedMessage=Вы можете закрыть это окно браузера, вернуться к своему устройству и повторить попытку подключения.
oauth2DeviceConsentDeniedMessage=Отказано в согласии на подключение устройства.
oauth2DeviceAuthorizationGrantDisabledMessage=Клиенту не разрешено инициировать OAuth 2.0 Device Authorization Grant. Поток отключен для клиента.
emailVerifyInstruction1=Вам было отправлено письмо с инструкциями для подтверждения адреса E-mail {0}.
emailVerifyInstruction2=Не получили письмо с кодом подтверждения?
emailVerifyInstruction3=для повторной отправки письма.
emailVerifyInstruction4=Для проверки вашего адреса электронной почты мы отправим вам письмо с инструкциями на адрес {0}.
emailVerifyResend=Повторно отправить письмо с подтверждением
emailVerifySend=Отправить письмо с подтверждением
emailLinkIdpTitle=Связать {0}
emailLinkIdp1=Вам было отправлено письмо с инструкциями по объединению {0} учетной записи {1} с вашей учетной записью {2}.
emailLinkIdp2=Не получили код подтверждения на ваш E-mail?
emailLinkIdp3=для повторной отправки письма.
emailLinkIdp4=Если вы уже подтвердили адрес электронной почты в другом браузере
emailLinkIdp5=продолжить.
backToLogin=&laquo; Назад ко входу
emailInstruction=Введите Ваше имя пользователя или E-mail и мы вышлем Вам инструкции по получению нового пароля.
emailInstructionUsername=Введите свое имя пользователя, и мы вышлем вам инструкции по созданию нового пароля.
copyCodeInstruction=Пожалуйста, скопируйте этот код в приложение:
pageExpiredTitle=Страница устарела
pageExpiredMsg1=Чтобы перезапустить процесс входа в систему
pageExpiredMsg2=Чтобы продолжить процесс входа в систему
personalInfo=Персональная информация:
role_admin=Администратор
role_realm-admin=Администратор realm
role_create-realm=Создание realm
role_create-client=Создание клиента
role_view-realm=Просмотр realm
role_view-users=Просмотр пользователей
role_view-applications=Просмотр приложений
role_view-clients=Просмотр клиентов
role_view-events=Просмотр событий
role_view-identity-providers=Просмотр провайдеров учетных записей
role_manage-realm=Управление realm
role_manage-users=Управление пользователями
role_manage-applications=Управление приложениями
role_manage-identity-providers=Управление провайдерами учетных записей
role_manage-clients=Управление клиентами
role_manage-events=Управление событиями
role_view-profile=Просмотр профиля
role_manage-account=Управление учетной записью
role_manage-account-links=Управление ссылками на аккаунты
role_read-token=Чтение токена
role_offline-access=Оффлайн доступ
client_account=Учетная запись
client_account-console=Консоль аккаунта
client_security-admin-console=Консоль администратора безопасности
client_admin-cli=Командный интерфейс администратора
client_realm-management=Управление realm
client_broker=Брокер
requiredFields=Обязательные поля
invalidUserMessage=Неправильное имя пользователя или пароль.
invalidUsernameMessage=Неверное имя пользователя.
invalidUsernameOrEmailMessage=Неверное имя пользователя или адрес электронной почты.
invalidPasswordMessage=Неверный пароль.
invalidEmailMessage=Неправильный E-mail.
accountDisabledMessage=Учетная запись заблокирована, свяжитесь с администратором.
# These properties are deliberately the same as "invalidUsernameMessage", so by default, it is not possible to recognize the reason of failed authentication is temporarily disabled account
accountTemporarilyDisabledMessage=Неправильное имя пользователя или пароль.
accountPermanentlyDisabledMessage=Неправильное имя пользователя или пароль.
# These properties are deliberately the same as "invalidTotpMessage", so by default, it is not possible to recognize the reason of failed authentication is temporarily disabled account
accountTemporarilyDisabledMessageTotp=Неверный код аутентификатора.
accountPermanentlyDisabledMessageTotp=Неверный код аутентификатора.
expiredCodeMessage=Вход просрочен по таймауту. Пожалуйста, войдите снова.
expiredActionMessage=Сессия завершена. Пожалуйста, войдите снова.
expiredActionTokenNoSessionMessage=Сессия завершена.
expiredActionTokenSessionExistsMessage=Сессия завершена. Пожалуйста, начните снова.
sessionLimitExceeded=Слишком много сессий
identityProviderLogoutFailure=Ошибка выхода из SAML IdP
missingFirstNameMessage=Пожалуйста введите имя.
missingLastNameMessage=Пожалуйста введите фамилию.
missingEmailMessage=Пожалуйста введите E-mail.
missingUsernameMessage=Пожалуйста введите имя пользователя.
missingPasswordMessage=Пожалуйста введите пароль.
missingTotpMessage=Пожалуйста введите код аутентификатора.
missingTotpDeviceNameMessage=Укажите название устройства.
notMatchPasswordMessage=Пароли не совпадают.
error-invalid-value=Недопустимое значение.
error-invalid-blank=Укажите значение.
error-empty=Укажите значение.
error-invalid-length=Длина должна быть между {1} и {2}.
error-invalid-length-too-short=Минимальная длина — {1}.
error-invalid-length-too-long=Максимальная длина — {2}.
error-invalid-email=Неверный адрес электронной почты.
error-invalid-number=Неверный номер.
error-number-out-of-range=Число должно быть между {1} и {2}.
error-number-out-of-range-too-small=Число должно иметь минимальное значение {1}.
error-number-out-of-range-too-big=Число должно иметь максимальное значение {2}.
error-pattern-no-match=Недопустимое значение.
error-invalid-uri=Неверный URL-адрес.
error-invalid-uri-scheme=Неверная схема URL.
error-invalid-uri-fragment=Неверный фрагмент URL.
error-user-attribute-required=Пожалуйста, укажите это поле.
error-invalid-date=Неверная дата.
error-user-attribute-read-only=Это поле доступно только для чтения.
error-username-invalid-character=Значение содержит недопустимый символ.
error-person-name-invalid-character=Значение содержит недопустимый символ.
error-reset-otp-missing-id=Пожалуйста, выберите конфигурацию OTP.
invalidPasswordExistingMessage=Неверный существующий пароль.
invalidPasswordBlacklistedMessage=Неверный пароль: пароль занесен в черный список.
invalidPasswordConfirmMessage=Подтверждение пароля не совпадает.
invalidTotpMessage=Неверный код аутентификатора.
usernameExistsMessage=Имя пользователя уже занято.
emailExistsMessage=E-mail уже существует.
federatedIdentityExistsMessage=Пользователь с {0} {1} уже существует. Пожалуйста войдите в управление учетными записями, чтобы связать эту учетную запись.
federatedIdentityUnavailableMessage=Пользователь {0}, аутентифицированный с помощью поставщика удостоверений {1}, не существует. Обратитесь к администратору.
federatedIdentityUnmatchedEssentialClaimMessage=Идентификационный токен, выданный поставщиком удостоверений, не соответствует настроенному essential claim. Обратитесь к администратору.
confirmLinkIdpTitle=Учетная запись уже существует
confirmOverrideIdpTitle=Ссылка на брокера уже существует
federatedIdentityConfirmLinkMessage=Пользователь с {0} {1} уже сущестует. Хотите продолжить?
federatedIdentityConfirmOverrideMessage=Вы пытаетесь связать свою учетную запись {0} с учетной записью {1} {2}. Но ваша учетная запись уже связана с другой {3} учетной записью {4}. Можете ли вы подтвердить, хотите ли вы заменить существующую ссылку на новую учетную запись?
federatedIdentityConfirmReauthenticateMessage=Аутентифицируйтесь, чтобы связать Вашу учетную запись с {0}
nestedFirstBrokerFlowMessage=Пользователь {0} {1} не связан ни с одним известным пользователем.
confirmLinkIdpReviewProfile=Обзор профиля
confirmLinkIdpContinue=Добавить в существующую учетную запись
confirmOverrideIdpContinue=Да, переопределить связь с текущим аккаунтом
configureTotpMessage=Вам необходимо настроить аутентификатор в мобильном устройстве, чтобы активировать учетную запись.
configureBackupCodesMessage=Для активации вашей учетной записи вам необходимо настроить резервные коды.
updateProfileMessage=Вам необходимо обновить свой профиль, чтобы активировать Вашу учетную запись.
updatePasswordMessage=Вам необходимо изменить пароль, чтобы активировать Вашу учетную запись.
updateEmailMessage=Для активации учетной записи вам необходимо обновить адрес электронной почты.
resetPasswordMessage=Вам необходимо сменить пароль.
verifyEmailMessage=Вам необходимо подтвердить Ваш E-mail, чтобы активировать Вашу учетную запись.
linkIdpMessage=Вам необходимо подтвердить Ваш E-mail, чтобы связать Вашу учетную запись с {0}.
emailSentMessage=В ближайшее время Вы должны получить письмо с дальнейшими инструкциями.
emailSendErrorMessage=Не получается отправить письмо. Пожалуйста, повторите позже.
accountUpdatedMessage=Ваша учетная запись успешно обновлена.
accountPasswordUpdatedMessage=Ваш пароль успешно обновлен.
delegationCompleteHeader=Успешный вход
delegationCompleteMessage=Вы можете закрыть это окно браузера и вернуться в консольное приложение.
delegationFailedHeader=Ошибка входа
delegationFailedMessage=Вы можете закрыть это окно браузера, вернуться в консольное приложение и попробовать войти снова.
noAccessMessage=Нет доступа
invalidPasswordMinLengthMessage=Некорректный пароль: длина пароля должна быть не менее {0} символов(а).
invalidPasswordMaxLengthMessage=Неверный пароль: максимальная длина {0}.
invalidPasswordMinDigitsMessage=Некорректный пароль: пароль должен содержать не менее {0} цифр(ы).
invalidPasswordMinLowerCaseCharsMessage=Некорректный пароль: пароль должен содержать не менее {0} символов(а) в нижнем регистре.
invalidPasswordMinUpperCaseCharsMessage=Некорректный пароль: пароль должен содержать не менее {0} символов(а) в верхнем регистре.
invalidPasswordMinSpecialCharsMessage=Некорректный пароль: пароль должен содержать не менее {0} спецсимволов(а).
invalidPasswordNotUsernameMessage=Некорректный пароль: пароль не должен совпадать с именем пользователя.
invalidPasswordNotContainsUsernameMessage=Неверный пароль: не может содержать имя пользователя.
invalidPasswordNotEmailMessage=Неверный пароль: не должен совпадать с адресом электронной почты.
invalidPasswordRegexPatternMessage=Некорректный пароль: пароль не прошел проверку по регулярному выражению.
invalidPasswordHistoryMessage=Некорректный пароль: пароль не должен совпадать с последним(и) {0} паролем(ями).
invalidPasswordGenericMessage=Некорректный пароль: новый пароль не соответствует правилам пароля.
failedToProcessResponseMessage=Не удалось обработать ответ
httpsRequiredMessage=Требуется HTTPS
realmNotEnabledMessage=Realm не включен
invalidRequestMessage=Неверный запрос
successLogout=Вы вышли из системы
failedLogout=Выйти не удалось
unknownLoginRequesterMessage=Неизвестный клиент
loginRequesterNotEnabledMessage=Клиент отключен
bearerOnlyMessage=Bearer-only приложениям не разрешается инициализация входа через браузер
standardFlowDisabledMessage=Клиенту не разрешается инициировать вход через браузер с данным response_type. Standard flow отключен для этого клиента.
implicitFlowDisabledMessage=Клиенту не разрешается инициировать вход через браузер с данным response_type. Implicit flow отключен для этого клиента.
invalidRedirectUriMessage=Неверный uri для переадресации
unsupportedNameIdFormatMessage=Неподдерживаемый NameIDFormat
invalidRequesterMessage=Неверный запрашивающий
registrationNotAllowedMessage=Регистрация не разрешена
resetCredentialNotAllowedMessage=Сброс идентификационных данных не разрешен
permissionNotApprovedMessage=Разрешение не подтверждено.
noRelayStateInResponseMessage=Нет изменения состояния в ответе от провайдера учетных записей.
insufficientPermissionMessage=Недостаточно полномочий для связывания идентификаторов.
couldNotProceedWithAuthenticationRequestMessage=Невозможно обработать аутентификационный запрос в провайдере учетных записей.
couldNotObtainTokenMessage=Не удалось получить токен от провайдера учетных записей.
unexpectedErrorRetrievingTokenMessage=Непредвиденная ошибка при получении токена от провайдера учетных записей.
unexpectedErrorHandlingResponseMessage=Непредвиденная ошибка при обработке ответа от провайдера учетных записей.
identityProviderAuthenticationFailedMessage=Аутентификация провалена. Невозможно аутентифицировать с поставщиком учетных записей.
couldNotSendAuthenticationRequestMessage=Не получается выполнить запрос аутентификации к поставщику учетных записей.
unexpectedErrorHandlingRequestMessage=Непредвиденная ошибка при обработке запроса аутентификации поставщика учетных записей.
invalidAccessCodeMessage=Неверный код доступа.
sessionNotActiveMessage=Сессия не активна.
invalidCodeMessage=Произошла ошибка. Пожалуйста, войдите в систему снова через ваше приложение.
cookieNotFoundMessage=Перезапустить вход cookie не найден. Возможно, истек срок его действия; он был удален или в вашем браузере отключены файлы cookie. Если файлы cookie отключены, включите их. Нажмите Вернуться в приложение, чтобы снова войти.
insufficientLevelOfAuthentication=Запрошенный уровень аутентификации не был удовлетворен.
identityProviderUnexpectedErrorMessage=Непредвиденная ошибка при проверке подлинности поставщика учетных записей
identityProviderMissingStateMessage=Отсутствует параметр состояния в ответе от поставщика удостоверений.
identityProviderMissingCodeOrErrorMessage=Отсутствует код или параметр ошибки в ответе от поставщика удостоверений.
identityProviderInvalidResponseMessage=Неверный ответ от поставщика удостоверений.
identityProviderInvalidSignatureMessage=Недействительная подпись в ответе от поставщика удостоверений.
identityProviderNotFoundMessage=Не удалось найти поставщика учетных записей с данным идентификатором.
identityProviderLinkSuccess=Ваша учетная запись была успешно соединена с {0} учетной записью {1} .
staleCodeMessage=Эта страница больше не действительна, пожалуйста, вернитесь в приложение и снова войдите в систему
realmSupportsNoCredentialsMessage=Realm не поддерживает никакой тип учетных данных.
credentialSetupRequired=Невозможно войти в систему, требуется настройка учетных данных.
identityProviderNotUniqueMessage=Realm поддерживает несколько поставщиков учетных записей. Не удалось определить, какой именно поставщик должен использоваться для аутентификации.
emailVerifiedMessage=Ваш E-mail был подтвержден.
emailVerifiedAlreadyMessage=Ваш адрес электронной почты уже подтвержден.
staleEmailVerificationLink=Ссылка, по которой Вы перешли, устарела и больше не действует. Возможно, вы уже подтвердили свой E-mail.
identityProviderAlreadyLinkedMessage=Федеративная идентификация, возвращенная {0}, уже связана с другим пользователем.
confirmAccountLinking=Подтвердите привязку учетной записи {0} поставщика удостоверений {1} к вашей учетной записи.
confirmEmailAddressVerification=Подтвердите действительность адреса электронной почты {0}.
confirmExecutionOfActions=Выполните следующие действия
backToApplication=&laquo; Назад в приложение
missingParameterMessage=Пропущенные параметры: {0}
clientNotFoundMessage=Клиент не найден.
clientDisabledMessage=Клиент отключен.
invalidParameterMessage=Неверный параметр: {0}
alreadyLoggedIn=Вы уже вошли.
differentUserAuthenticated=Вы уже аутентифицированы как другой пользователь ''{0}'' в этой сессии. Пожалуйста, сначала выйдете.
brokerLinkingSessionExpired=Запрошена привязка брокерского счета, но текущая сессия больше не действительна.
proceedWithAction=» Нажмите здесь, чтобы продолжить
acrNotFulfilled=Требования аутентификации не выполнены
requiredAction.CONFIGURE_TOTP=Настроить одноразовый пароль
requiredAction.TERMS_AND_CONDITIONS=Условия и положения
requiredAction.UPDATE_PASSWORD=Обновить пароль
requiredAction.UPDATE_PROFILE=Обновить профиль
requiredAction.VERIFY_EMAIL=Подтвердить адрес электронной почты
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Генерация кодов восстановления
requiredAction.webauthn-register-passwordless=Webauthn регистрация Без пароля
requiredAction.webauthn-register=Регистрация Webauthn
invalidTokenRequiredActions=Требуемые действия, указанные в ссылке, недействительны
doX509Login=Вы войдете в систему как:
clientCertificate=Сертификат клиента X509:
noCertificate=[Нет сертификата]


pageNotFound=Страница не найдена
internalServerError=Произошла внутренняя ошибка сервера
console-username=Имя пользователя:
console-password=Пароль:
console-otp=Одноразовый пароль:
console-new-password=Новый пароль:
console-confirm-password=Подтвердите пароль:
console-update-password=Требуется обновить ваш пароль.
console-verify-email=Вам необходимо подтвердить свой адрес электронной почты. Мы отправили письмо на адрес {0}, содержащее код подтверждения. Введите этот код в поле ниже.
console-email-code=Код электронной почты:
console-accept-terms=Принимаете ли вы условия? [y/n]:
console-accept=у

# Openshift messages
openshift.scope.user_info=Информация о пользователе
openshift.scope.user_check-access=Информация о доступе пользователя
openshift.scope.user_full=Полный доступ
openshift.scope.list-projects=Список проектов

# SAML authentication
saml.post-form.title=Перенаправление аутентификации
saml.post-form.message=Перенаправление, пожалуйста подождите.
saml.post-form.js-disabled=JavaScript отключен. Настоятельно рекомендуем включить его. Нажмите кнопку ниже, чтобы продолжить.
saml.artifactResolutionServiceInvalidResponse=Не удалось разрешить артефакт.

#authenticators
otp-display-name=Приложение-аутентификатор
otp-help-text=Введите проверочный код из приложения-аутентификатора.
otp-reset-description=Какую конфигурацию OTP следует удалить?
password-display-name=Пароль
password-help-text=Войдите, введя свой пароль.
auth-username-form-display-name=Имя пользователя
auth-username-form-help-text=Начните вход, введя свое имя пользователя
auth-username-password-form-display-name=Имя пользователя и пароль
auth-username-password-form-help-text=Войдите, введя свое имя пользователя и пароль.
auth-x509-client-username-form-display-name=Сертификат X509
auth-x509-client-username-form-help-text=Войдите в систему, используя клиентский сертификат X509.

# Recovery Codes
auth-recovery-authn-code-form-display-name=Восстановление кода аутентификации
auth-recovery-authn-code-form-help-text=Введите код аутентификации восстановления из ранее сгенерированного списка.
auth-recovery-code-info-message=Введите указанный код восстановления.
auth-recovery-code-prompt=Код восстановления #{0}
auth-recovery-code-header=Войти с кодом восстановления аутентификации
recovery-codes-error-invalid=Неверный код аутентификации восстановления
recovery-code-config-header=Восстановление кодов аутентификации
recovery-code-config-warning-title=Эти коды восстановления больше не появятся после того, как вы покинете эту страницу
recovery-code-config-warning-message=Обязательно распечатайте, загрузите или скопируйте их в менеджер паролей и сохраните. Отмена этой настройки удалит эти коды восстановления из вашей учетной записи.
recovery-codes-print=Печать
recovery-codes-download=Скачать
recovery-codes-copy=Копировать
recovery-codes-copied=Скопировано
recovery-codes-confirmation-message=Я сохранил эти коды в надежном месте
recovery-codes-action-complete=Полная настройка
recovery-codes-action-cancel=Отменить настройку
recovery-codes-download-file-header=Сохраните эти коды восстановления в надежном месте.
recovery-codes-download-file-description=Коды восстановления — это одноразовые коды доступа, которые позволяют вам войти в свою учетную запись, если у вас нет доступа к аутентификатору.
recovery-codes-download-file-date=Эти коды были сгенерированы
recovery-codes-label-default=Коды восстановления

# WebAuthn
webauthn-display-name=Пароль
webauthn-help-text=Для входа используйте свой пароль.
webauthn-passwordless-display-name=Пароль
webauthn-passwordless-help-text=Используйте свой пароль для входа без пароля.
webauthn-login-title=Вход с паролем
webauthn-registration-title=Регистрация ключа доступа
webauthn-available-authenticators=Доступные пароли
webauthn-unsupported-browser-text=WebAuthn не поддерживается этим браузером. Попробуйте другой или обратитесь к администратору.
webauthn-doAuthenticate=Войти с помощью пароля
webauthn-createdAt-label=Созданный
webauthn-registration-init-label=Пароль (метка по умолчанию)
webauthn-registration-init-label-prompt=Пожалуйста, введите метку вашего зарегистрированного ключа доступа


# WebAuthn Error
webauthn-error-title=Ошибка ключа доступа
webauthn-error-registration=Не удалось зарегистрировать ваш пароль.<br /> {0}
webauthn-error-api-get=Не удалось выполнить аутентификацию с помощью пароля.<br /> {0}
webauthn-error-different-user=Первый аутентифицированный пользователь — это не тот, который аутентифицирован с помощью ключа доступа.
webauthn-error-auth-verification=Результат аутентификации ключа доступа недействителен.<br /> {0}
webauthn-error-register-verification=Результат регистрации ключа доступа недействителен.<br /> {0}
webauthn-error-user-not-found=Неизвестный пользователь аутентифицирован с помощью пароля.

# Passkey
passkey-login-title=Вход с паролем
passkey-available-authenticators=Доступные пароли
passkey-unsupported-browser-text=Passkey не поддерживается этим браузером. Попробуйте другой или обратитесь к администратору.
passkey-doAuthenticate=Войти с помощью пароля
passkey-createdAt-label=Созданный
passkey-autofill-select=Выберите свой пароль

# Identity provider
identity-provider-redirector=Подключитесь к другому поставщику удостоверений
identity-provider-login-label=Или войдите с помощью
idp-email-verification-display-name=Проверка электронной почты
idp-email-verification-help-text=Привяжите свою учетную запись, подтвердив адрес электронной почты.
idp-username-password-form-display-name=Имя пользователя и пароль
idp-username-password-form-help-text=Подключите свою учетную запись, войдя в систему.
finalDeletionConfirmation=Если вы удалите свой аккаунт, его нельзя будет восстановить. Чтобы сохранить свой аккаунт, нажмите «Отмена».
irreversibleAction=Это действие необратимо
deleteAccountConfirm=Подтверждение удаления учетной записи
deletingImplies=Удаление вашего аккаунта подразумевает:
errasingData=Удаление всех ваших данных
loggingOutImmediately=Немедленный выход из системы
accountUnusable=Любое последующее использование приложения с этой учетной записью будет невозможно
userDeletedSuccessfully=Пользователь успешно удален
access-denied=Доступ запрещен
access-denied-when-idp-auth=Доступ запрещен при аутентификации с помощью {0}
frontchannel-logout.title=Выход из системы
frontchannel-logout.message=Вы выходите из следующих приложений
logoutConfirmTitle=Выход из системы
logoutConfirmHeader=Хотите выйти?
doLogout=Выйти
readOnlyUsernameMessage=Вы не можете обновить свое имя пользователя, так как оно доступно только для чтения.
error-invalid-multivalued-size=Атрибут {0} должен иметь не менее {1} и не более {2} {2,choice,0#values|1#value|1<values}.
organization.confirm-membership.title=Вы собираетесь присоединиться к организации ${kc.org.name}
organization.confirm-membership=Нажав на ссылку ниже, вы станете членом организации {0}:
organization.member.register.title=Создайте учетную запись, чтобы присоединиться к организации ${kc.org.name}
organization.select=Выберите организацию для продолжения:
notMemberOfOrganization=Пользователь не является членом организации {0}
notMemberOfAnyOrganization=Пользователь не является членом какой-либо организации
