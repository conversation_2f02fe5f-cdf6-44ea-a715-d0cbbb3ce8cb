<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.keycloak.testsuite</groupId>
        <artifactId>integration-arquillian-tests-other</artifactId>
        <version>999.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>integration-arquillian-tests-webauthn</artifactId>
    <name>WebAuthn tests</name>

    <properties>
        <firefoxUserPreferences>${project.build.directory}/dependency/firefox-user-preferences.js</firefoxUserPreferences>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>htmlunit3-driver</artifactId>
            <version>${selenium.version}</version>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest</artifactId>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>firefox</id>
            <activation>
                <property>
                    <name>browser</name>
                    <value>firefox</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-resources-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>copy-common-dependencies</id>
                                <phase>generate-resources</phase>
                                <goals>
                                    <goal>copy-resources</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.build.directory}/dependency</outputDirectory>
                                    <resources>
                                        <resource>
                                            <directory>src/test/resources</directory>
                                            <includes>
                                                <include>firefox-user-preferences.js</include>
                                            </includes>
                                        </resource>
                                    </resources>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
