doLogIn=Prijava
doRegister=Registracija
doRegisterSecurityKey=Registracija
doCancel=Prekliči
doSubmit=Potrdi
doBack=Nazaj
doYes=Da
doNo=Ne
doContinue=Nadaljuj
doIgnore=Prezri
doAccept=Sprejmi
doDecline=Zavrni
doForgotPassword=Pozabljeno geslo?
doClickHere=Kliknite tukaj
doImpersonate=Predstavljaj se kot
doTryAgain=Poskusi znova
doTryAnotherWay=Poskusi drug način
doConfirmDelete=Potrdi brisanje
errorDeletingAccount=Pri brisanju računa je prišlo do napake
deletingAccountForbidden=Nimate dovolj pravic za izbris svojega računa, kontaktirajte administratorja.
kerberosNotConfigured=Kerberos ni konfiguriran
kerberosNotConfiguredTitle=Kerberos ni konfiguriran
bypassKerberosDetail=Ali niste prijavljeni preko Kerberosa ali pa vaš brskalnik ni nastavljen za prijavo preko Kerberosa. Prosimo, kliknite nadaljuj za prijavo na drug način
kerberosNotSetUp=Kerberos ni nastavljen. Ne morete se prijaviti.
registerTitle=Registracija
loginAccountTitle=Prijavite se v svoj račun
loginTitle=Prijava v {0}
loginTitleHtml={0}
impersonateTitle={0} Predstavljaj se kot uporabnik
impersonateTitleHtml=<strong>{0}</strong> Predstavljaj se kot uporabnik
realmChoice=Domena
unknownUser=Neznan uporabnik
loginTotpTitle=Nastavitev mobilnega avtentikatorja
loginProfileTitle=Posodobi račun
loginIdpReviewProfileTitle=Posodobi račun
loginTimeout=Vaš poskus prijave je potekel. Prijava se bo začela znova.
reauthenticate=Prosimo, ponovno se avtenticirajte za nadaljevanje
authenticateStrong=Za nadaljevanje je potrebna močna avtentikacija
oauthGrantTitle=Odobritev dostopa za {0}
oauthGrantTitleHtml={0}
oauthGrantInformation=Prepričajte se, da zaupate {0}, tako da izveste, kako bo {0} ravnal z vašimi podatki.
oauthGrantReview=Lahko pregledate
oauthGrantTos=splošne pogoje poslovanja.
oauthGrantPolicy=pravilnik o zasebnosti.
errorTitle=Oprostite...
errorTitleHtml=<strong>Oprostite</strong>...
emailVerifyTitle=Potrditev e-poštnega naslova
emailForgotTitle=Ste pozabili geslo?
updateEmailTitle=Posodobi e-poštni naslov
emailUpdateConfirmationSentTitle=Potrditveno sporočilo poslano
emailUpdateConfirmationSent=Potrditveno sporočilo je bilo poslano na {0}. Za dokončanje posodobitve e-poštnega naslova morate slediti navodilom v sporočilu.
emailUpdatedTitle=E-poštni naslov posodobljen
emailUpdated=E-poštni naslov računa je bil uspešno posodobljen na {0}.
updatePasswordTitle=Posodobi geslo
codeSuccessTitle=Koda uspešna
codeErrorTitle=Koda napake: {0}
displayUnsupported=Zahtevana vrsta prikaza ni podprta
browserRequired=Za prijavo je potreben brskalnik
browserContinue=Za dokončanje prijave je potreben brskalnik
browserContinuePrompt=Odprem brskalnik in nadaljujem s prijavo? [y/n]:
browserContinueAnswer=y

# Transports
usb=USB
nfc=NFC
bluetooth=Bluetooth
internal=Notranji
unknown=Neznan
termsTitle=Pogoji uporabe
termsText=<p>Pogoji uporabe bodo določeni</p>
termsPlainText=Pogoji uporabe bodo določeni.
termsAcceptanceRequired=Strinjati se morate z našimi pogoji uporabe.
acceptTerms=Strinjam se s pogoji uporabe
deleteCredentialTitle=Izbriši {0}
deleteCredentialMessage=Ali želite izbrisati {0}?
recaptchaFailed=Neveljaven Recaptcha
recaptchaNotConfigured=Recaptcha je zahtevana, vendar ni konfigurirana
consentDenied=Soglasje zavrnjeno.
noAccount=Nov uporabnik?
username=Uporabniško ime
usernameOrEmail=Uporabniško ime ali e-poštni naslov
firstName=Ime
givenName=Dano ime
fullName=Polno ime
lastName=Priimek
familyName=Družinsko ime
email=E-poštni naslov
password=Geslo
passwordConfirm=Potrdi geslo
passwordNew=Novo geslo
passwordNewConfirm=Potrditev novega gesla
hidePassword=Skrij geslo
showPassword=Pokaži geslo
rememberMe=Zapomni si me
authenticatorCode=Enkratna koda
address=Naslov
street=Ulica
locality=Mesto ali kraj
region=Država, pokrajina ali regija
postal_code=Poštna številka
country=Država
emailVerified=E-poštni naslov potrjen
website=Spletna stran
phoneNumber=Telefonska številka
phoneNumberVerified=Telefonska številka potrjena
gender=Spol
birthday=Datum rojstva
zoneinfo=Časovni pas
gssDelegationCredential=GSS poverilnica delegiranja
logoutOtherSessions=Odjava iz drugih naprav
profileScopeConsentText=Uporabniški profil
emailScopeConsentText=E-poštni naslov
addressScopeConsentText=Naslov
phoneScopeConsentText=Telefonska številka
offlineAccessScopeConsentText=Dostop brez povezave
samlRoleListScopeConsentText=Moje vloge
rolesScopeConsentText=Uporabniške vloge
organizationScopeConsentText=Organizacija
restartLoginTooltip=Ponovno zaženi prijavo
loginTotpIntro=Za dostop do tega računa morate nastaviti generator enkratnih gesel
loginTotpStep1=Namestite eno od naslednjih aplikacij na svoj mobilni telefon:
loginTotpStep2=Odprite aplikacijo in skenirajte črtno kodo:
loginTotpStep3=Vnesite enkratno kodo, ki jo je ustvarila aplikacija, in kliknite Potrdi za dokončanje nastavitve.
loginTotpStep3DeviceName=Vnesite ime naprave, ki vam bo pomagalo upravljati vaše OTP naprave.
loginTotpManualStep2=Odprite aplikacijo in vnesite ključ:
loginTotpManualStep3=Če aplikacija omogoča nastavitev, uporabite naslednje konfiguracijske vrednosti:
loginTotpUnableToScan=Ne morete skenirati?
loginTotpScanBarcode=Skeniraj črtno kodo?
loginCredential=Poverilnica
loginOtpOneTime=Enkratna koda
loginTotpType=Tip
loginTotpAlgorithm=Algoritem
loginTotpDigits=Števke
loginTotpInterval=Interval
loginTotpCounter=Števec
loginTotpDeviceName=Ime naprave
loginTotp.totp=Časovno osnovano
loginTotp.hotp=Števno osnovano
totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator
loginChooseAuthenticator=Izberite način prijave
oauthGrantRequest=Ali odobrite te pravice dostopa?
inResource=v
oauth2DeviceVerificationTitle=Prijava naprave
verifyOAuth2DeviceUserCode=Vnesite kodo, ki jo je prikazala vaša naprava, in kliknite Potrdi
oauth2DeviceInvalidUserCodeMessage=Neveljavna koda, prosimo, poskusite znova.
oauth2DeviceExpiredUserCodeMessage=Koda je potekla. Prosimo, vrnite se na svojo napravo in poskusite ponovno vzpostaviti povezavo.
oauth2DeviceVerificationCompleteHeader=Prijava naprave uspešna
oauth2DeviceVerificationCompleteMessage=Lahko zaprete to okno brskalnika in se vrnete na svojo napravo.
oauth2DeviceVerificationFailedHeader=Prijava naprave ni uspela
oauth2DeviceVerificationFailedMessage=Lahko zaprete to okno brskalnika, se vrnete na svojo napravo in poskusite ponovno vzpostaviti povezavo.
oauth2DeviceConsentDeniedMessage=Soglasje za povezavo naprave je zavrnjeno.
oauth2DeviceAuthorizationGrantDisabledMessage=Odjemalcu ni dovoljeno začeti OAuth 2.0 pooblastila za napravo. Ta funkcija je onemogočena za odjemalca.
emailVerifyInstruction1=E-pošta z navodili za potrditev vašega e-poštnega naslova je bila poslana na naslov {0}.
emailVerifyInstruction2=Niste prejeli potrditvene kode po e-pošti?
emailVerifyInstruction3=za ponovno pošiljanje e-pošte.
emailLinkIdpTitle=Poveži {0}
emailLinkIdp1=E-pošta z navodili za povezavo računa {0} {1} z vašim računom {2} vam je bila poslana.
emailLinkIdp2=Niste prejeli potrditvene kode po e-pošti?
emailLinkIdp3=za ponovno pošiljanje e-pošte.
emailLinkIdp4=Če ste e-poštni naslov že potrdili v drugem brskalniku
emailLinkIdp5=za nadaljevanje.
backToLogin=&laquo; Back to Login
emailInstruction=Vnesite svoje uporabniško ime ali e-poštni naslov in poslali vam bomo navodila za ustvarjanje novega gesla.
emailInstructionUsername=Vnesite svoje uporabniško ime in poslali vam bomo navodila za ustvarjanje novega gesla.
copyCodeInstruction=Prosimo, kopirajte to kodo in jo prilepite v svojo aplikacijo:
pageExpiredTitle=Stran je potekla
pageExpiredMsg1=Za ponovni zagon postopka prijave
pageExpiredMsg2=Za nadaljevanje postopka prijave
personalInfo=Osebni podatki:
role_admin=Administrator
role_realm-admin=Administrator področja
role_create-realm=Ustvari področje
role_create-client=Ustvari odjemalca
role_view-realm=Preglej področje
role_view-users=Preglej uporabnike
role_view-applications=View applications
role_view-clients=Preglej odjemalce
role_view-events=Preglej dogodke
role_view-identity-providers=Preglej ponudnike identitete
role_manage-realm=Upravljaj področje
role_manage-users=Upravljaj uporabnike
role_manage-applications=Manage applications
role_manage-identity-providers=Upravljaj ponudnike identitete
role_manage-clients=Upravljaj odjemalce
role_manage-events=Upravljaj dogodke
role_view-profile=Preglej profil
role_manage-account=Upravljaj račun
role_manage-account-links=Upravljaj povezave računa
role_read-token=Preberi žeton
role_offline-access=Dostop brez povezave
client_account=Račun
client_account-console=Konzola računa
client_security-admin-console=Varnostna administratorska konzola
client_admin-cli=Admin CLI
client_realm-management=Upravljanje področja
client_broker=Posrednik
requiredFields=Required fields
invalidUserMessage=Neveljavno uporabniško ime ali geslo.
invalidUsernameMessage=Neveljavno uporabniško ime.
invalidUsernameOrEmailMessage=Neveljavno uporabniško ime ali e-poštni naslov.
invalidPasswordMessage=Neveljavno geslo.
invalidEmailMessage=Neveljaven e-poštni naslov.
accountDisabledMessage=Račun je onemogočen, obrnite se na administratorja.
# These properties are deliberately the same as "invalidUsernameMessage", so by default, it is not possible to recognize the reason of failed authentication is temporarily disabled account
accountTemporarilyDisabledMessage=Neveljavno uporabniško ime ali geslo.
accountPermanentlyDisabledMessage=Neveljavno uporabniško ime ali geslo.
# These properties are deliberately the same as "invalidTotpMessage", so by default, it is not possible to recognize the reason of failed authentication is temporarily disabled account
accountTemporarilyDisabledMessageTotp=Neveljavna koda avtentikatorja.
accountPermanentlyDisabledMessageTotp=Neveljavna koda avtentikatorja.
expiredCodeMessage=Prijava je potekla. Prosimo, prijavite se ponovno.
expiredActionMessage=Dejanje je poteklo. Prosimo, nadaljujte s prijavo.
expiredActionTokenNoSessionMessage=Dejanje je poteklo.
expiredActionTokenSessionExistsMessage=Dejanje je poteklo. Prosimo, začnite znova.
sessionLimitExceeded=Preveliko število sej
identityProviderLogoutFailure=SAML IdP napaka pri odjavi
missingFirstNameMessage=Prosimo, vnesite ime.
missingLastNameMessage=Prosimo, vnesite priimek.
missingEmailMessage=Prosimo, vnesite e-poštni naslov.
missingUsernameMessage=Prosimo, vnesite uporabniško ime.
missingPasswordMessage=Prosimo, vnesite geslo.
missingTotpMessage=Prosimo, vnesite kodo avtentikatorja.
missingTotpDeviceNameMessage=Prosimo, vnesite ime naprave.
notMatchPasswordMessage=Gesli se ne ujemata.
error-invalid-value=Neveljavna vrednost.
error-invalid-blank=Prosimo, določite vrednost.
error-empty=Prosimo, določite vrednost.
error-invalid-length=Dolžina mora biti med {1} in {2}.
error-invalid-length-too-short=Najmanjša dolžina je {1}.
error-invalid-length-too-long=Največja dolžina je {2}.
error-invalid-email=Neveljaven e-poštni naslov.
error-invalid-number=Neveljavno število.
error-number-out-of-range=Število mora biti med {1} in {2}.
error-number-out-of-range-too-small=Število mora imeti najmanjšo vrednost {1}.
error-number-out-of-range-too-big=Število mora imeti največjo vrednost {2}.
error-pattern-no-match=Neveljavna vrednost.
error-invalid-uri=Neveljaven URL.
error-invalid-uri-scheme=Neveljavna URL shema.
error-invalid-uri-fragment=Neveljaven URL fragment.
error-user-attribute-required=Prosimo, izpolnite to polje.
error-invalid-date=Neveljaven datum.
error-user-attribute-read-only=To polje je samo za branje.
error-username-invalid-character=Vrednost vsebuje neveljaven znak.
error-person-name-invalid-character=Vrednost vsebuje neveljaven znak.
error-reset-otp-missing-id=Prosimo, izberite OTP konfiguracijo.
invalidPasswordExistingMessage=Neveljavno obstoječe geslo.
invalidPasswordBlacklistedMessage=Neveljavno geslo: geslo je na črni listi.
invalidPasswordConfirmMessage=Potrditev gesla se ne ujema.
invalidTotpMessage=Neveljavna koda avtentikatorja.
usernameExistsMessage=Uporabniško ime že obstaja.
emailExistsMessage=E-poštni naslov že obstaja.
federatedIdentityExistsMessage=Uporabnik s {0} {1} že obstaja. Prosimo, prijavite se v upravitelja računa za povezavo računa.
federatedIdentityUnavailableMessage=Uporabnik {0}, avtenticiran s ponudnikom identitete {1}, ne obstaja. Prosimo, kontaktirajte administratorja.
federatedIdentityUnmatchedEssentialClaimMessage=ID žeton, ki ga je izdal ponudnik identitete, se ne ujema z bistvenim konfiguriranim zahtevkom. Prosimo, obrnite se na administratorja.
confirmLinkIdpTitle=Račun že obstaja
confirmOverrideIdpTitle=Povezava posrednika že obstaja
federatedIdentityConfirmLinkMessage=Uporabnik s {0} {1} že obstaja. Kako želite nadaljevati?
federatedIdentityConfirmOverrideMessage=Poskušate povezati svoj račun {0} z računom {1} {2}. Vendar je vaš račun že povezan z drugim računom {3} {4}. Ali želite zamenjati obstoječo povezavo z novim računom?
federatedIdentityConfirmReauthenticateMessage=Avtenticirajte se za povezavo vašega računa z {0}
nestedFirstBrokerFlowMessage=Uporabnik {0} {1} ni povezan z nobenim znanim uporabnikom.
confirmLinkIdpReviewProfile=Preglej profil
confirmLinkIdpContinue=Dodaj k obstoječemu računu
confirmOverrideIdpContinue=Da, zamenjaj povezavo s trenutnim računom
configureTotpMessage=Za aktivacijo vašega računa morate nastaviti mobilni avtentikator.
configureBackupCodesMessage=You need to set up Backup Codes to activate your account.
updateProfileMessage=Za aktivacijo vašega računa morate posodobiti svoj uporabniški profil.
updatePasswordMessage=Za aktivacijo vašega računa morate spremeniti svoje geslo.
updateEmailMessage=Za aktivacijo vašega računa morate posodobiti svoj e-poštni naslov.
resetPasswordMessage=Spremeniti morate svoje geslo.
verifyEmailMessage=Za aktivacijo vašega računa morate potrditi svoj e-poštni naslov.
linkIdpMessage=Za povezavo vašega računa z {0} morate potrditi svoj e-poštni naslov.
emailSentMessage=Kmalu boste prejeli e-pošto z nadaljnjimi navodili.
emailSendErrorMessage=Pošiljanje e-pošte ni uspelo, poskusite kasneje.
accountUpdatedMessage=Vaš račun je bil posodobljen.
accountPasswordUpdatedMessage=Vaše geslo je bilo posodobljeno.
delegationCompleteHeader=Prijava uspešna
delegationCompleteMessage=To okno brskalnika lahko zaprete in se vrnete v aplikacijo konzole.
delegationFailedHeader=Prijava ni uspela
delegationFailedMessage=To okno brskalnika lahko zaprete in se vrnete v aplikacijo konzole ter poskusite znova.
noAccessMessage=Ni dostopa
invalidPasswordMinLengthMessage=Neveljavno geslo: minimalna dolžina {0}.
invalidPasswordMaxLengthMessage=Neveljavno geslo: presega največje dovoljeno dolžino {0}.
invalidPasswordMinDigitsMessage=Neveljavno geslo: mora vsebovati vsaj {0} številk.
invalidPasswordMinLowerCaseCharsMessage=Neveljavno geslo: mora vsebovati vsaj {0} malih črk.
invalidPasswordMinUpperCaseCharsMessage=Neveljavno geslo: mora vsebovati vsaj {0} velikih črk.
invalidPasswordMinSpecialCharsMessage=Neveljavno geslo: mora vsebovati vsaj {0} posebnih znakov.
invalidPasswordNotUsernameMessage=Neveljavno geslo: ne sme biti enako uporabniškemu imenu.
invalidPasswordNotContainsUsernameMessage=Neveljavno geslo: ne sme vsebovati uporabniškega imena.
invalidPasswordNotEmailMessage=Neveljavno geslo: ne sme biti enako e-poštnemu naslovu.
invalidPasswordRegexPatternMessage=Neveljavno geslo: se ne ujema z zahtevanim vzorcem.
invalidPasswordHistoryMessage=Neveljavno geslo: ne sme biti enako kateremu od zadnjih {0} gesel.
invalidPasswordGenericMessage=Neveljavno geslo: novo geslo ne ustreza pravilom gesla.
failedToProcessResponseMessage=Ni bilo mogoče obdelati odgovora
httpsRequiredMessage=Zahtevan je HTTPS
realmNotEnabledMessage=Področje ni omogočeno
invalidRequestMessage=Neveljavna zahteva
successLogout=Odjavljeni ste
failedLogout=Odjava ni uspela
unknownLoginRequesterMessage=Neznan zahtevatelj prijave
loginRequesterNotEnabledMessage=Zahtevatelj prijave ni omogočen
bearerOnlyMessage=Aplikacije bearer-only ne smejo sprožiti prijave v brskalniku
standardFlowDisabledMessage=Odjemalcu ni dovoljeno sprožiti prijave v brskalniku z danim odzivnim tipom. Standardni tok je onemogočen za odjemalca.
implicitFlowDisabledMessage=Odjemalcu ni dovoljeno sprožiti prijave v brskalniku z danim odzivnim tipom. Implicitni tok je onemogočen za odjemalca.
invalidRedirectUriMessage=Neveljaven URI preusmeritve
unsupportedNameIdFormatMessage=Nepodprt NameIDFormat
invalidRequesterMessage=Neveljaven zahtevatelj
registrationNotAllowedMessage=Registracija ni dovoljena
resetCredentialNotAllowedMessage=Ponastavitev poverilnice ni dovoljena
permissionNotApprovedMessage=Dovoljenje ni odobreno.
noRelayStateInResponseMessage=Ni stanja releja v odzivu od ponudnika identitete.
insufficientPermissionMessage=Nezadostna dovoljenja za povezovanje identitet.
couldNotProceedWithAuthenticationRequestMessage=Ni bilo mogoče nadaljevati z zahtevo za avtentikacijo do ponudnika identitete.
couldNotObtainTokenMessage=Ni bilo mogoče pridobiti žetona od ponudnika identitete.
unexpectedErrorRetrievingTokenMessage=Nepričakovana napaka pri pridobivanju žetona od ponudnika identitete.
unexpectedErrorHandlingResponseMessage=Nepričakovana napaka pri obdelavi odziva od ponudnika identitete.
identityProviderAuthenticationFailedMessage=Avtentikacija ni uspela. Ni bilo mogoče avtenticirati s ponudnikom identitete.
couldNotSendAuthenticationRequestMessage=Ni bilo mogoče poslati zahteve za avtentikacijo do ponudnika identitete.
unexpectedErrorHandlingRequestMessage=Nepričakovana napaka pri obdelavi zahteve za avtentikacijo do ponudnika identitete.
invalidAccessCodeMessage=Neveljavna dostopna koda.
sessionNotActiveMessage=Seja ni aktivna.
invalidCodeMessage=Prišlo je do napake, prosimo, prijavite se ponovno preko vaše aplikacije.
cookieNotFoundMessage=Piškotek ni bil najden. Prepričajte se, da imate v brskalniku omogočene piškotke.
insufficientLevelOfAuthentication=Zahtevana raven avtentikacije ni bila dosežena.
identityProviderUnexpectedErrorMessage=Nepričakovana napaka pri avtentikaciji s ponudnikom identitete
identityProviderMissingStateMessage=Manjkajoč state parameter v odzivu od ponudnika identitete.
identityProviderMissingCodeOrErrorMessage=Manjkajoč parameter code ali error v odzivu od ponudnika identitete.
identityProviderInvalidResponseMessage=Neveljaven odziv od ponudnika identitete.
identityProviderInvalidSignatureMessage=Neveljaven podpis v odzivu od ponudnika identitete.
identityProviderNotFoundMessage=Ponudnik identitete s tem identifikatorjem ni bil najden.
identityProviderLinkSuccess=Uspešno ste potrdili svoj e-poštni naslov. Prosimo, vrnite se v svoj izvirni brskalnik in nadaljujte s prijavo.
staleCodeMessage=Ta stran ni več veljavna, prosimo, vrnite se v vašo aplikacijo in se prijavite ponovno
realmSupportsNoCredentialsMessage=Področje ne podpira nobene vrste poverilnic.
credentialSetupRequired=Prijava ni mogoča, zahtevana je nastavitev poverilnic.
identityProviderNotUniqueMessage=Področje podpira več ponudnikov identitete. Ni bilo mogoče določiti, s katerim ponudnikom identitete bi se morala izvesti avtentikacija.
emailVerifiedMessage=Vaš e-poštni naslov je bil potrjen.
emailVerifiedAlreadyMessage=Vaš e-poštni naslov je že bil potrjen.
staleEmailVerificationLink=Povezava, ki ste jo kliknili, je zastarela in ni več veljavna. Morda ste že potrdili svoj e-poštni naslov?
identityProviderAlreadyLinkedMessage=Federirana identiteta, ki jo je vrnil {0}, je že povezana z drugim uporabnikom.
confirmAccountLinking=Potrdite povezavo računa {0} ponudnika identitete {1} z vašim računom.
confirmEmailAddressVerification=Potrdite veljavnost e-poštnega naslova {0}.
confirmExecutionOfActions=Izvedite naslednja dejanja
backToApplication=&laquo; Nazaj na aplikacijo
missingParameterMessage=Manjkajoči parametri: {0}
clientNotFoundMessage=Odjemalec ni najden.
clientDisabledMessage=Odjemalec je onemogočen.
invalidParameterMessage=Neveljaven parameter: {0}
alreadyLoggedIn=Že ste prijavljeni.
differentUserAuthenticated=V tej seji ste že avtenticirani kot drug uporabnik ''{0}''. Prosimo, najprej se odjavite.
brokerLinkingSessionExpired=Zahtevana je povezava posredniškega računa, vendar trenutna seja ni več veljavna.
proceedWithAction=&raquo; Kliknite tukaj za nadaljevanje
acrNotFulfilled=Zahteve avtentikacije niso izpolnjene
requiredAction.CONFIGURE_TOTP=Konfiguriraj OTP
requiredAction.TERMS_AND_CONDITIONS=Pogoji in določila
requiredAction.UPDATE_PASSWORD=Posodobi geslo
requiredAction.UPDATE_PROFILE=Posodobi profil
requiredAction.VERIFY_EMAIL=Potrdi e-poštni naslov
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Generiraj kode za obnovitev
requiredAction.webauthn-register-passwordless=Webauthn registracija brez gesla
requiredAction.webauthn-register=Webauthn Register
invalidTokenRequiredActions=Zahtevana dejanja v povezavi niso veljavna
doX509Login=Prijavljeni boste kot:
clientCertificate=X509 certifikat odjemalca:
noCertificate=[Brez certifikata]
pageNotFound=Stran ni bila najdena
internalServerError=Prišlo je do notranje napake strežnika
console-username=Uporabniško ime:
console-password=Geslo:
console-otp=Enkratno geslo:
console-new-password=Novo geslo:
console-confirm-password=Potrdite geslo:
console-update-password=Zahtevana je posodobitev vašega gesla.
console-verify-email=Potrditi morate svoj e-poštni naslov. Poslali smo e-pošto na naslov {0}, ki vsebuje potrditveno kodo. Prosimo, vnesite to kodo v spodnje polje.
console-email-code=E-poštna koda:
console-accept-terms=Sprejemate pogoje? [y/n]:
console-accept=y

# Openshift messages
openshift.scope.user_info=Podatki o uporabniku
openshift.scope.user_check-access=Podatki o uporabniškem dostopu
openshift.scope.user_full=Poln dostop
openshift.scope.list-projects=Seznam projektov

# SAML authentication
saml.post-form.title=Preusmeritev avtentikacije
saml.post-form.message=Preusmerjanje, prosimo počakajte.
saml.post-form.js-disabled=JavaScript je onemogočen. Močno priporočamo, da ga omogočite. Kliknite spodnji gumb za nadaljevanje.
saml.artifactResolutionServiceInvalidResponse=Ni mogoče razrešiti artefakta.

#authenticators
otp-display-name=Aplikacija za avtentikacijo
otp-help-text=Vnesite potrditveno kodo iz aplikacije za avtentikacijo.
otp-reset-description=Katero OTP konfiguracijo želite odstraniti?
password-display-name=Geslo
password-help-text=Prijavite se z vnosom gesla.
auth-username-form-display-name=Uporabniško ime
auth-username-form-help-text=Začnite prijavo z vnosom uporabniškega imena
auth-username-password-form-display-name=Uporabniško ime in geslo
auth-username-password-form-help-text=Prijavite se z vnosom uporabniškega imena in gesla.
auth-x509-client-username-form-display-name=X509 certifikat
auth-x509-client-username-form-help-text=Prijavite se z odjemalskim certifikatom X509.

# Recovery Codes
auth-recovery-authn-code-form-display-name=Obnovitvena avtentikacijska koda
auth-recovery-authn-code-form-help-text=Vnesite obnovitveno avtentikacijsko kodo iz predhodno ustvarjenega seznama.
auth-recovery-code-info-message=Vnesite določeno obnovitveno kodo.
auth-recovery-code-prompt=Obnovitvena koda #{0}
auth-recovery-code-header=Prijava z obnovitveno avtentikacijsko kodo
recovery-codes-error-invalid=Neveljavna obnovitvena avtentikacijska koda
recovery-code-config-header=Obnovitvene avtentikacijske kode
recovery-code-config-warning-title=Te obnovitvene kode ne bodo več prikazane po zapustitvi te strani
recovery-code-config-warning-message=Poskrbite, da jih natisnete, prenesete ali kopirate v upravitelja gesel in jih varno shranite. Preklic te nastavitve bo odstranil te obnovitvene kode iz vašega računa.
recovery-codes-print=Natisni
recovery-codes-download=Prenesi
recovery-codes-copy=Kopiraj
recovery-codes-copied=Kopirano
recovery-codes-confirmation-message=Te kode sem shranil/a na varno mesto
recovery-codes-action-complete=Zaključi nastavitev
recovery-codes-action-cancel=Prekliči nastavitev
recovery-codes-download-file-header=Shranite te obnovitvene kode na varno mesto.
recovery-codes-download-file-description=Obnovitvene kode so enkratna gesla, ki vam omogočajo prijavo v vaš račun, če nimate dostopa do svojega avtentikatorja.
recovery-codes-download-file-date=Te kode so bile ustvarjene
recovery-codes-label-default=Obnovitvene kode

# WebAuthn
webauthn-display-name=Ključ za dostop
webauthn-help-text=Uporabite svoj ključ za dostop za prijavo.
webauthn-passwordless-display-name=Ključ za dostop
webauthn-passwordless-help-text=Uporabite svoj ključ za dostop za prijavo brez gesla.
webauthn-login-title=Prijava s ključem za dostop
webauthn-registration-title=Registracija ključa za dostop
webauthn-available-authenticators=Razpoložljivi ključi za dostop
webauthn-unsupported-browser-text=WebAuthn ni podprt v tem brskalniku. Poskusite z drugim ali se obrnite na administratorja.
webauthn-doAuthenticate=Prijava s ključem za dostop
webauthn-createdAt-label=Ustvarjeno
webauthn-registration-init-label=Ključ za dostop (Privzeta oznaka)
webauthn-registration-init-label-prompt=Prosimo, vnesite oznako za vaš registriran ključ za dostop

# WebAuthn Error
webauthn-error-title=Napaka ključa za dostop
webauthn-error-registration=Registracija vašega ključa za dostop ni uspela.<br /> {0}
webauthn-error-api-get=Avtentikacija s ključem za dostop ni uspela.<br /> {0}
webauthn-error-different-user=Prvi avtenticirani uporabnik ni enak uporabniku, avtenticiranemu s ključem za dostop.
webauthn-error-auth-verification=Rezultat avtentikacije s ključem za dostop ni veljaven.<br /> {0}
webauthn-error-register-verification=Rezultat registracije ključa za dostop ni veljaven.<br /> {0}
webauthn-error-user-not-found=Neznan uporabnik, avtenticiran s ključem za dostop.

# Passkey
passkey-login-title=Prijava s ključem za dostop
passkey-available-authenticators=Razpoložljivi ključi za dostop
passkey-unsupported-browser-text=Ključ za dostop ni podprt v tem brskalniku. Poskusite z drugim ali se obrnite na administratorja.
passkey-doAuthenticate=Prijava s ključem za dostop
passkey-createdAt-label=Ustvarjeno
passkey-autofill-select=Izberite svoj ključ za dostop

# Identity provider
identity-provider-redirector=Poveži se z drugim ponudnikom identitete
identity-provider-login-label=Ali se prijavite z
idp-email-verification-display-name=Potrditev e-poštnega naslova
idp-email-verification-help-text=Poveži vaš račun z overitvijo e-poštnega naslova.
idp-username-password-form-display-name=Uporabniško ime in geslo
idp-username-password-form-help-text=Poveži vaš račun z vnosom uporabniškega imena in gesla.
finalDeletionConfirmation=Če izbrišete vaš račun, ga ne boste mogli obnoviti. Če želite obdržati vaš račun, pritisnite Prekliči.
irreversibleAction=To dejanje je nepreklicno
deleteAccountConfirm=Potrditev izbrisa računa
deletingImplies=Izbris vašega računa pomeni:
errasingData=Izbris vseh vaših podatkov
loggingOutImmediately=Takojšnja odjava
accountUnusable=Nadaljnja uporaba aplikacije s tem računom ne bo več mogoča
userDeletedSuccessfully=Uporabnik je bil uspešno izbrisan
access-denied=Dostop zavrnjen
access-denied-when-idp-auth=Dostop zavrnjen pri avtentikaciji s {0}
frontchannel-logout.title=Odjavljanje
frontchannel-logout.message=Odjavljate se iz naslednjih aplikacij
logoutConfirmTitle=Odjavljanje
logoutConfirmHeader=Ali se želite odjaviti?
doLogout=Odjava
readOnlyUsernameMessage=Ne morete posodobiti svojega uporabniškega imena, ker je samo za branje.
error-invalid-multivalued-size=Atribut {0} mora imeti najmanj {1} in največ {2} vrednosti.
organization.confirm-membership.title=Pridružili se boste organizaciji ${kc.org.name}
organization.confirm-membership=S klikom na spodnjo povezavo boste postali član organizacije {0}:
organization.member.register.title=Ustvarite račun za pridružitev organizaciji ${kc.org.name}
organization.select=Izberite organizacijo za nadaljevanje:
notMemberOfOrganization=Uporabnik ni član organizacije {0}
notMemberOfAnyOrganization=Uporabnik ni član nobene organizacije
