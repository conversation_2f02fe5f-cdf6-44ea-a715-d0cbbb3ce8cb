{"id": "my-sso-realm", "realm": "my-sso-realm", "displayName": "My SSO Realm", "displayNameHtml": "<div class=\"kc-logo-text\"><span>My Company SSO</span></div>", "enabled": true, "// === BASIC REALM SETTINGS ===": "", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "// === SSL/SECURITY SETTINGS ===": "", "sslRequired": "external", "loginTheme": "keycloak", "accountTheme": "keycloak", "adminTheme": "keycloak", "emailTheme": "keycloak", "// === TOKEN LIFESPANS (in seconds) ===": "", "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "// === USER REGISTRATION & LOGIN SETTINGS ===": "", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": true, "verifyEmail": true, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": true, "editUsernameAllowed": false, "// === BRUTE FORCE PROTECTION ===": "", "bruteForceProtected": true, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "// === REQUIRED CREDENTIALS ===": "", "requiredCredentials": ["password"], "// === DEFAULT ROLES ===": "", "defaultRoles": ["default-roles-my-sso-realm"], "// === SMTP SERVER CONFIGURATION ===": "", "smtpServer": {"from": "<EMAIL>", "fromDisplayName": "My Company SSO", "replyTo": "<EMAIL>", "replyToDisplayName": "Support Team", "envelopeFrom": "<EMAIL>", "host": "smtp.mycompany.com", "port": "587", "ssl": "false", "starttls": "true", "auth": "true", "user": "<EMAIL>", "password": "smtp-password"}, "// === INTERNATIONALIZATION ===": "", "internationalizationEnabled": true, "supportedLocales": ["en", "es", "fr", "de"], "defaultLocale": "en", "// === REALM ROLES ===": "", "roles": {"realm": [{"name": "default-roles-my-sso-realm", "description": "Default realm role", "composite": true, "composites": {"client": {"account": ["view-profile", "manage-account"]}}}, {"name": "user", "description": "Standard user role"}, {"name": "admin", "description": "Administrator role"}, {"name": "manager", "description": "Manager role"}]}, "// === CLIENT CONFIGURATIONS ===": "", "clients": [{"// === OIDC WEB APPLICATION CLIENT ===": "", "clientId": "my-web-app", "name": "My Web Application", "description": "Main web application using OpenID Connect", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "my-web-app-secret-change-this", "protocol": "openid-connect", "publicClient": false, "bearerOnly": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "authorizationServicesEnabled": false, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"], "rootUrl": "https://myapp.company.com", "baseUrl": "/", "adminUrl": "", "redirectUris": ["https://myapp.company.com/*", "https://myapp.company.com/auth/callback"], "webOrigins": ["https://myapp.company.com"], "notBefore": 0, "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "oidc.ciba.grant.enabled": "false", "saml.signature.algorithm": "RSA_SHA256", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}}, {"// === SAML APPLICATION CLIENT ===": "", "clientId": "https://samlapp.company.com/saml", "name": "My SAML Application", "description": "SAML-based application", "enabled": true, "protocol": "saml", "publicClient": false, "bearerOnly": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "fullScopeAllowed": true, "rootUrl": "https://samlapp.company.com", "baseUrl": "/saml", "adminUrl": "https://samlapp.company.com/saml/admin", "redirectUris": ["https://samlapp.company.com/saml/acs"], "attributes": {"saml.assertion.signature": "true", "saml.force.post.binding": "true", "saml.multivalued.roles": "false", "saml.encrypt": "false", "saml.server.signature": "true", "saml.server.signature.keyinfo.ext": "false", "saml.signature.algorithm": "RSA_SHA256", "saml.client.signature": "false", "saml.authnstatement": "true", "saml.onetimeuse.condition": "false", "saml_name_id_format": "username", "saml_force_name_id_format": "false"}, "protocolMappers": [{"name": "username", "protocol": "saml", "protocolMapper": "saml-user-property-mapper", "consentRequired": false, "config": {"attribute.nameformat": "Basic", "user.attribute": "username", "friendly.name": "username", "attribute.name": "username"}}, {"name": "email", "protocol": "saml", "protocolMapper": "saml-user-property-mapper", "consentRequired": false, "config": {"attribute.nameformat": "Basic", "user.attribute": "email", "friendly.name": "email", "attribute.name": "email"}}]}], "// === IDENTITY PROVIDERS ===": "", "identityProviders": [{"// === GOOGLE OIDC PROVIDER ===": "", "alias": "google", "providerId": "google", "enabled": true, "updateProfileFirstLoginMode": "on", "trustEmail": true, "storeToken": false, "addReadTokenRoleOnCreate": false, "authenticateByDefault": false, "linkOnly": false, "firstBrokerLoginFlowAlias": "first broker login", "config": {"syncMode": "IMPORT", "clientId": "your-google-client-id.apps.googleusercontent.com", "clientSecret": "your-google-client-secret", "hostedDomain": "mycompany.com", "useJwksUrl": "true", "hideOnLoginPage": "false", "loginHint": "false", "uiLocales": "false", "backchannelSupported": "false", "disableUserInfo": "false", "acceptsPromptNoneForwardFromClient": "false", "filteredByClaim": "false"}}, {"// === SAML IDENTITY PROVIDER ===": "", "alias": "corporate-saml", "providerId": "saml", "enabled": true, "updateProfileFirstLoginMode": "on", "trustEmail": true, "storeToken": false, "addReadTokenRoleOnCreate": false, "authenticateByDefault": false, "linkOnly": false, "firstBrokerLoginFlowAlias": "first broker login", "config": {"syncMode": "IMPORT", "nameIDPolicyFormat": "urn:oasis:names:tc:SAML:2.0:nameid-format:persistent", "principalType": "SUBJECT", "signatureAlgorithm": "RSA_SHA256", "xmlSigKeyInfoKeyNameTransformer": "KEY_ID", "allowCreate": "true", "entityId": "https://keycloak.company.com/realms/my-sso-realm", "authnContextComparisonType": "exact", "hideOnLoginPage": "false", "backchannelSupported": "false", "postBindingResponse": "true", "postBindingAuthnRequest": "true", "postBindingLogout": "true", "wantAuthnRequestsSigned": "false", "wantAssertionsSigned": "true", "wantAssertionsEncrypted": "false", "forceAuthn": "false", "validateSignature": "true", "signSpMetadata": "false", "singleSignOnServiceUrl": "https://corporate-idp.company.com/sso/saml", "singleLogoutServiceUrl": "https://corporate-idp.company.com/slo/saml", "allowedClockSkew": "0", "attributeConsumingServiceIndex": "0"}}, {"// === MICROSOFT AZURE AD OIDC ===": "", "alias": "microsoft", "providerId": "microsoft", "enabled": true, "updateProfileFirstLoginMode": "on", "trustEmail": true, "storeToken": false, "addReadTokenRoleOnCreate": false, "authenticateByDefault": false, "linkOnly": false, "firstBrokerLoginFlowAlias": "first broker login", "config": {"syncMode": "IMPORT", "clientId": "your-azure-app-id", "clientSecret": "your-azure-client-secret", "tenant": "your-tenant-id-or-domain.com", "hostedDomain": "", "useJwksUrl": "true", "hideOnLoginPage": "false", "loginHint": "false", "uiLocales": "false", "backchannelSupported": "false", "disableUserInfo": "false", "acceptsPromptNoneForwardFromClient": "false", "filteredByClaim": "false"}}], "// === AUTHENTICATION FLOWS ===": "", "authenticationFlows": [{"alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "authenticatorFlow": false}, {"flowAlias": "forms", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "authenticatorFlow": true}]}], "// === BROWSER SECURITY HEADERS ===": "", "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=********; includeSubDomains"}, "// === SAMPLE USERS (Remove in production) ===": "", "users": [{"username": "admin", "enabled": true, "emailVerified": true, "firstName": "Admin", "lastName": "User", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "admin123", "temporary": true}], "realmRoles": ["admin", "user"], "clientRoles": {"account": ["view-profile", "manage-account"]}}, {"username": "testuser", "enabled": true, "emailVerified": true, "firstName": "Test", "lastName": "User", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "test123", "temporary": true}], "realmRoles": ["user"], "clientRoles": {"account": ["view-profile", "manage-account"]}}], "// === CLIENT SCOPES ===": "", "clientScopes": [{"name": "company-profile", "description": "Company-specific profile information", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "Company profile information"}, "protocolMappers": [{"name": "department", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "department", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "department", "jsonType.label": "String"}}, {"name": "employee-id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "employeeId", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "employee_id", "jsonType.label": "String"}}]}]}