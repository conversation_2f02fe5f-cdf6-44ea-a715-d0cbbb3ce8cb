doSave=保存
doCancel=キャンセル
doLogOutAllSessions=全セッションのログアウト
doRemove=削除
doAdd=追加
doSignOut=サインアウト
doLogIn=ログイン
doLink=リンク


editAccountHtmlTitle=アカウントの編集
personalInfoHtmlTitle=個人情報
federatedIdentitiesHtmlTitle=連携済みアイデンティティー
accountLogHtmlTitle=アカウントログ
changePasswordHtmlTitle=パスワードの変更
deviceActivityHtmlTitle=デバイスアクティビティー
sessionsHtmlTitle=セッション
accountManagementTitle=Keycloakアカウント管理
authenticatorTitle=オーセンティケーター
applicationsHtmlTitle=アプリケーション
linkedAccountsHtmlTitle=リンクされたアカウント
accountManagementWelcomeMessage=Keycloakアカウント管理へようこそ
personalInfoIntroMessage=基本情報を管理する
accountSecurityTitle=アカウントセキュリティー
accountSecurityIntroMessage=パスワードとアカウントアクセスを制御する
applicationsIntroMessage=アカウントへアクセスするためにアプリのパーミッションを追跡して管理する
resourceIntroMessage=チームメンバー間でリソースを共有する
passwordLastUpdateMessage=パスワードは更新されました
updatePasswordTitle=パスワードの更新
updatePasswordMessageTitle=強力なパスワードを選択してください
updatePasswordMessage=強力なパスワードは、数字、文字、記号を含みます。推測が難しく、実在する言葉に似ておらず、このアカウントだけで使用されています。
personalSubTitle=個人情報
personalSubMessage=基本情報を管理してください。
authenticatorCode=ワンタイムコード
email=メールアドレス
firstName=名
givenName=名
fullName=氏名
lastName=姓
familyName=姓
password=パスワード
currentPassword=現在のパスワード
passwordConfirm=新しいパスワード（確認）
passwordNew=新しいパスワード
username=ユーザー名
address=住所
street=番地
locality=市区町村
region=都道府県
postal_code=郵便番号
country=国
emailVerified=検証済みメールアドレス
gssDelegationCredential=GSS委譲クレデンシャル
profileScopeConsentText=ユーザープロファイル
emailScopeConsentText=メールアドレス
addressScopeConsentText=アドレス
phoneScopeConsentText=電話番号
offlineAccessScopeConsentText=オフラインアクセス
samlRoleListScopeConsentText=ロール
rolesScopeConsentText=ユーザーロール
role_admin=管理者
role_realm-admin=レルム管理者
role_create-realm=レルムの作成
role_view-realm=レルムの参照
role_view-users=ユーザーの参照
role_view-applications=アプリケーションの参照
role_view-clients=クライアントの参照
role_view-events=イベントの参照
role_view-identity-providers=アイデンティティープロバイダーの参照
role_view-consent=同意の参照
role_manage-realm=レルムの管理
role_manage-users=ユーザーの管理
role_manage-applications=アプリケーションの管理
role_manage-identity-providers=アイデンティティープロバイダーの管理
role_manage-clients=クライアントの管理
role_manage-events=イベントの管理
role_view-profile=プロファイルの参照
role_manage-account=アカウントの管理
role_manage-account-links=アカウントリンクの管理
role_manage-consent=同意の管理
role_read-token=トークンの参照
role_offline-access=オフラインアクセス
role_uma_authorization=パーミッションの取得
client_account=アカウント
client_account-console=アカウントコンソール
client_security-admin-console=セキュリティー管理コンソール
client_admin-cli=管理CLI
client_realm-management=レルム管理
client_broker=ブローカー


requiredFields=必須
allFieldsRequired=全ての入力項目が必須
backToApplication=&laquo; アプリケーションに戻る
backTo={0}に戻る
date=日付
event=イベント
ip=IP
client=クライアント
clients=クライアント
details=詳細
started=開始
lastAccess=最終アクセス
expires=有効期限
applications=アプリケーション
account=アカウント
federatedIdentity=連携されたアイデンティティー
authenticator=オーセンティケーター
device-activity=デバイスアクティビティー
sessions=セッション
log=ログ
application=アプリケーション
availableRoles=利用可能なロール
grantedPermissions=許可されたパーミッション
grantedPersonalInfo=許可された個人情報
additionalGrants=追加の許可
action=アクション
inResource=in
fullAccess=フルアクセス
offlineToken=オフライントークン
revoke=許可の取り消し
configureAuthenticators=設定済みのオーセンティケーター
mobile=モバイル
totpStep1=モバイルに以下のアプリケーションのいずれかをインストールしてください。
totpStep2=アプリケーションを開き、バーコードをスキャンしてください。
totpStep3=アプリケーションで提供されたワンタイムコードを入力して保存をクリックし、セットアップを完了してください。
totpStep3DeviceName=OTPデバイスの管理に役立つようなデバイス名を指定してください。
totpManualStep2=アプリケーションを開き、キーを入力してください。
totpManualStep3=アプリケーションが設定できる場合は、次の設定値を使用してください。
totpUnableToScan=スキャンできませんか？
totpScanBarcode=バーコードをスキャンしますか？
totp.totp=時間ベース
totp.hotp=カウンターベース
totpType=タイプ
totpAlgorithm=アルゴリズム
totpDigits=数字
totpInterval=間隔
totpCounter=カウンター
totpDeviceName=デバイス名
missingUsernameMessage=ユーザー名を指定してください。
missingFirstNameMessage=名を指定してください。
invalidEmailMessage=無効なメールアドレスです。
missingLastNameMessage=姓を指定してください。
missingEmailMessage=メールアドレスを指定してください。
missingPasswordMessage=パスワードを指定してください。
notMatchPasswordMessage=パスワードが一致していません。
invalidUserMessage=無効なユーザーです
missingTotpMessage=オーセンティケーターコードを指定してください。
missingTotpDeviceNameMessage=デバイス名を指定してください。
invalidPasswordExistingMessage=既存のパスワードが不正です。
invalidPasswordConfirmMessage=新しいパスワード（確認）と一致していません。
invalidTotpMessage=無効なオーセンティケーターコードです。
usernameExistsMessage=既に存在するユーザー名です。
emailExistsMessage=既に存在するメールアドレスです。
readOnlyUserMessage=読み取り専用のため、アカウントを更新することはできません。
readOnlyUsernameMessage=読み取り専用のため、ユーザー名を更新することはできません。
readOnlyPasswordMessage=読み取り専用のため、パスワードを更新することはできません。
successTotpMessage=モバイルオーセンティケーターが設定されました。
successTotpRemovedMessage=モバイルオーセンティケーターが削除されました。
successGrantRevokedMessage=許可が正常に取り消しされました。
accountUpdatedMessage=アカウントが更新されました。
accountPasswordUpdatedMessage=パスワードが更新されました。
missingIdentityProviderMessage=アイデンティティープロバイダーが指定されていません。
invalidFederatedIdentityActionMessage=無効または存在しないアクションです。
identityProviderNotFoundMessage=指定されたアイデンティティープロバイダーが見つかりません。
federatedIdentityLinkNotActiveMessage=このアイデンティティーは有効ではありません。
federatedIdentityRemovingLastProviderMessage=パスワードがないため、最後の連携されたアイデンティティーを削除できません。
identityProviderRedirectErrorMessage=アイデンティティープロバイダーへのリダイレクトに失敗しました。
identityProviderRemovedMessage=アイデンティティープロバイダーが正常に削除されました。
identityProviderAlreadyLinkedMessage={0}から返された連携されたアイデンティティーは既に他のユーザーに関連付けされています。
staleCodeAccountMessage=有効期限切れです。再度お試しください。
consentDenied=同意が拒否されました。
accountDisabledMessage=アカウントが無効です。管理者に連絡してください。
accountTemporarilyDisabledMessage=アカウントが一時的に無効です。管理者に連絡するか、しばらく時間をおいてから再度お試しください。
invalidPasswordMinLengthMessage=無効なパスワード: 最小長は{0}です。
invalidPasswordMinLowerCaseCharsMessage=無効なパスワード: 少なくとも{0}文字の小文字を含む必要があります。
invalidPasswordMinDigitsMessage=無効なパスワード: 少なくとも{0}文字の数字を含む必要があります。
invalidPasswordMinUpperCaseCharsMessage=無効なパスワード:少なくとも{0}文字の大文字を含む必要があります。
invalidPasswordMinSpecialCharsMessage=無効なパスワード: 少なくとも{0}文字の特殊文字を含む必要があります。
invalidPasswordNotUsernameMessage=無効なパスワード: ユーザー名と同じパスワードは禁止されています。
invalidPasswordRegexPatternMessage=無効なパスワード: 正規表現パターンと一致しません。
invalidPasswordHistoryMessage=無効なパスワード: 直近{0}個のパスワードのいずれかと同じパスワードは禁止されています。
invalidPasswordBlacklistedMessage=無効なパスワード: パスワードがブラックリストに含まれています。
invalidPasswordGenericMessage=無効なパスワード: 新しいパスワードはパスワードポリシーと一致しません。

# Authorization
myResources=マイリソース
myResourcesSub=マイリソース
doDeny=拒否
doRevoke=取り消し
doApprove=承認
doRemoveSharing=共有の削除
doRemoveRequest=要求の削除
peopleAccessResource=このリソースにアクセスできる人
resourceManagedPolicies=このリソースへのアクセスを許可するパーミッション
resourceNoPermissionsGrantingAccess=このリソースへのアクセスを許可する権限はありません
anyAction=任意のアクション
description=説明
name=名前
scopes=スコープ
resource=リソース
user=ユーザー
peopleSharingThisResource=このリソースを共有している人
shareWithOthers=他人と共有
needMyApproval=承認が必要
requestsWaitingApproval=承認待ちの要求
icon=アイコン
requestor=要求者
owner=オーナー
resourcesSharedWithMe=共有しているリソース
permissionRequestion=パーミッションの要求
permission=パーミッション
shares=共有（複数）
notBeingShared=このリソースは共有されていません。
notHaveAnyResource=リソースがありません
noResourcesSharedWithYou=共有しているリソースはありません
havePermissionRequestsWaitingForApproval=承認を待っている{0}個のパーミッションの要求があります。
clickHereForDetails=詳細はこちらをクリックしてください。
resourceIsNotBeingShared=リソースは共有されていません

# Applications
applicationName=名前
applicationType=アプリケーションタイプ
applicationInUse=使用中のアプリケーションのみ
clearAllFilter=すべてのフィルターの消去
activeFilters=アクティブなフィルター
filterByName=名前でフィルタリング...
allApps=すべてのアプリケーション
internalApps=内部アプリケーション
thirdpartyApps=サードパーティーのアプリケーション
appResults=結果
clientNotFoundMessage=クライアントが見つかりません。

# Linked account
authorizedProvider=認可済みプロバイダー
authorizedProviderMessage=アカウントにリンクされた認可済みプロバイダー
identityProvider=アイデンティティープロバイダー
identityProviderMessage=アカウントと設定したアイデンティティープロバイダーをリンクするには
socialLogin=ソーシャルログイン
userDefined=ユーザー定義
removeAccess=アクセス権の削除
removeAccessMessage=このアプリアカウントを使用する場合は、アクセス権を再度付与する必要があります。

#Authenticator
authenticatorStatusMessage=2要素認証は現在
authenticatorFinishSetUpTitle=あなたの2要素認証
authenticatorFinishSetUpMessage=Keycloakアカウントにサインインするたびに、2要素認証コードを入力するように求められます。
authenticatorSubTitle=2要素認証を設定する
authenticatorSubMessage=アカウントのセキュリティーを強化するには、利用可能な2要素認証の方式のうち少なくとも1つを有効にします。
authenticatorMobileTitle=モバイルオーセンティケーター
authenticatorMobileMessage=モバイルオーセンティケーターを使用して、2要素認証として確認コードを取得します。
authenticatorMobileFinishSetUpMessage=オーセンティケーターはあなたの携帯電話にバインドされています。
authenticatorActionSetup=セットアップ
authenticatorSMSTitle=SMSコード
authenticatorSMSMessage=Keycloakは、2要素認証として確認コードを携帯電話に送信します。
authenticatorSMSFinishSetUpMessage=テキストメッセージが次の電話番号宛に送信されます
authenticatorDefaultStatus=デフォルト
authenticatorChangePhone=電話番号の変更

#Authenticator - Mobile Authenticator setup
authenticatorMobileSetupTitle=モバイルオーセンティケーターのセットアップ
smscodeIntroMessage=電話番号を入力すると、確認コードがあなたの電話に送信されます。
mobileSetupStep1=携帯電話にオーセンティケーターアプリケーションをインストールします。ここにリストされているアプリケーションがサポートされています。
mobileSetupStep2=アプリケーションを開き、バーコードをスキャンしてください。
mobileSetupStep3=アプリケーションから提供されたワンタイムコードを入力し、保存をクリックしてセットアップを終了します。
scanBarCode=バーコードをスキャンしますか？
enterBarCode=ワンタイムコードを入力してください
doCopy=コピー
doFinish=終了

#Authenticator - SMS Code setup
authenticatorSMSCodeSetupTitle=SMSコードのセットアップ
chooseYourCountry=国を選んでください
enterYourPhoneNumber=電話番号を入力してください
sendVerficationCode=確認コードの送信
enterYourVerficationCode=確認コードを入力してください

#Authenticator - backup Code setup
authenticatorBackupCodesSetupTitle=バックアップコードのセットアップ
realmName=レルム
doDownload=ダウンロード
doPrint=印刷
generateNewBackupCodes=新しいバックアップコードを生成する
backtoAuthenticatorPage=オーセンティケーターページに戻る


#Resources
resources=リソース
sharedwithMe=私と共有
share=共有
sharedwith=共有
accessPermissions=アクセスパーミッション
permissionRequests=パーミッションの要求
approve=承認
approveAll=すべて承認
people=人
perPage=1ページあたり
currentPage=現在のページ
sharetheResource=リソースの共有
group=グループ
selectPermission=パーミッションの選択
addPeople=あなたのリソースを共有する人の追加
addTeam=あなたのリソースを共有するチームの追加
myPermissions=私のパーミッション
waitingforApproval=承認待ち
anyPermission=任意のパーミッション

# Openshift messages
openshift.scope.user_info=ユーザー情報
openshift.scope.user_check-access=ユーザーアクセス情報
openshift.scope.user_full=フルアクセス
openshift.scope.list-projects=プロジェクトの一覧表示
accountSecuritySidebarTitle=アカウントセキュリティー
deviceActivitySidebarTitle=デバイスアクティビティー
personalInfoSidebarTitle=個人情報
signingInSidebarTitle=サインイン
website=Webページ
phoneNumber=電話番号
phoneNumberVerified=電話番号検証済み
gender=性別
birthday=生年月日
zoneinfo=タイムゾーン
organizationScopeConsentText=組織
role_view-groups=グループの参照
totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator
irreversibleAction=このアクションは不可逆的です
deletingImplies=以下のアカウントを削除することを意味します。
errasingData=すべてのデータを消去する
loggingOutImmediately=すぐにログアウトする
accountUnusable=このアカウントでアプリケーションを使用することはできません
federatedIdentityBoundOrganization=組織に関連付けられた アイデンティティープロバイダーへのリンクは削除できません。
access-denied-when-idp-auth={0}での認証時にアクセスが拒否されました
invalidPasswordMaxLengthMessage=無効なパスワード: 最大長は{0}です。
invalidPasswordNotContainsUsernameMessage=無効なパスワード: ユーザー名を含めることはできません。
invalidPasswordNotEmailMessage=無効なパスワード: メールアドレスと同じパスワードは禁止されています。
error-invalid-blank=値を指定してください。
error-invalid-length=属性{0}の長さは{1}から{2}までの範囲でなければなりません。
error-invalid-length-too-long=属性{0}の最大長は{2}である必要があります。
error-invalid-email=メールアドレスが無効です。
error-invalid-number=無効な数値です。
error-number-out-of-range=属性{0}は{1}と{2}の間の数値でなければならなりません。
error-number-out-of-range-too-small=属性{0}の最小値は{1}である必要があります。
error-number-out-of-range-too-big=属性{0}の最大値は{2}である必要があります。
error-pattern-no-match=無効な値です。
error-invalid-uri=無効なURLです。
error-invalid-uri-scheme=無効なURLスキームです。
error-invalid-uri-fragment=無効なURLフラグメントです。
error-user-attribute-required=属性{0}を指定してください。
error-invalid-date=無効な日付です。
error-user-attribute-read-only=フィールド{0}は読み取り専用です。
error-username-invalid-character=ユーザー名に無効な文字が含まれています。
error-person-name-invalid-character=名前に無効な文字が含まれています。
error-invalid-value=無効な値です。
updateReadOnlyAttributesRejectedMessage=読み取り専用属性の更新が拒否されました
accountManagementBaseThemeCannotBeUsedDirectly=基本アカウントテーマには、アカウントコンソール用の翻訳しか含まれていません。アカウントコンソールを表示するには、テーマの親を別のアカウントテーマに設定するか、独自のindex.ftlファイルを用意する必要があります。詳細はドキュメントを参照してください。
noAccessMessage=アクセスが許可されていません
linkedAccountsSidebarTitle=リンクされたアカウント
error-invalid-length-too-short=属性{0}の最小の長さは{1}である必要があります。
error-empty=値を指定してください。
