[{"id": "master", "realm": "master", "displayName": "Keycloak", "displayNameHtml": "<div class=\"kc-logo-text\"><span>Keycloak</span></div>", "notBefore": 0, "revokeRefreshToken": false, "accessTokenLifespan": 60, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "offlineSessionIdleTimeout": 2592000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "ef0b1123-6d34-4bcd-a91e-d7e85f63f3d2", "name": "create-realm", "description": "${role_create-realm}", "scopeParamRequired": false, "composite": false, "clientRole": false, "containerId": "master"}, {"id": "0398c8e8-ac85-492a-bb65-5b757664edc0", "name": "offline_access", "description": "${role_offline-access}", "scopeParamRequired": true, "composite": false, "clientRole": false, "containerId": "master"}, {"id": "d9512952-9ca0-4c61-9cc1-6260fe102650", "name": "master-test-realm-role", "scopeParamRequired": false, "composite": false, "clientRole": false, "containerId": "master"}, {"id": "cc8bf72b-c430-4c8c-ba33-c8b7c321674e", "name": "uma_authorization", "description": "${role_uma_authorization}", "scopeParamRequired": false, "composite": false, "clientRole": false, "containerId": "master"}, {"id": "1d4e9d65-6d21-4751-b461-ea34d4a122d6", "name": "admin", "description": "${role_admin}", "scopeParamRequired": false, "composite": true, "composites": {"realm": ["create-realm"], "client": {"Migration-realm": ["view-events", "view-realm", "manage-authorization", "impersonation", "manage-realm", "manage-users", "view-users", "view-clients", "view-identity-providers", "manage-clients", "manage-identity-providers", "create-client", "manage-events", "view-authorization"], "master-realm": ["manage-realm", "manage-events", "view-clients", "create-client", "view-identity-providers", "view-realm", "manage-users", "manage-authorization", "manage-identity-providers", "view-events", "view-users", "view-authorization", "impersonation", "manage-clients"], "Migration2-realm": ["manage-authorization", "view-realm", "view-identity-providers", "view-authorization", "manage-identity-providers", "view-events", "create-client", "manage-users", "manage-clients", "manage-realm", "view-users", "impersonation", "view-clients", "manage-events"]}}, "clientRole": false, "containerId": "master"}], "client": {"security-admin-console": [], "master-test-client": [{"id": "a69d83c3-c1e0-4b4c-b6e2-9c5ea4cefdb9", "name": "master-test-client-role", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c74f3ddd-7a3d-4ecd-8967-d89e2138e4c1"}], "admin-cli": [], "Migration-realm": [{"id": "69088781-7ee2-4e3e-9007-1800f6b116d1", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "********-e475-4355-9b0e-6d41d9270870"}, {"id": "81880d61-6186-430b-b5bb-995e0a11fad0", "name": "view-events", "description": "${role_view-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "********-e475-4355-9b0e-6d41d9270870"}, {"id": "0fbc6b25-79ae-4129-8cb2-9f966157c135", "name": "manage-clients", "description": "${role_manage-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "********-e475-4355-9b0e-6d41d9270870"}, {"id": "25b0a8c1-2c8f-4d93-8036-4440c1de6a1a", "name": "view-realm", "description": "${role_view-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "********-e475-4355-9b0e-6d41d9270870"}, {"id": "fa9f7d2b-61ec-4611-a47c-a1764ee936e5", "name": "manage-authorization", "description": "${role_manage-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "********-e475-4355-9b0e-6d41d9270870"}, {"id": "7e11814f-6a38-4821-bf71-6e90fc56af37", "name": "impersonation", "description": "${role_impersonation}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "********-e475-4355-9b0e-6d41d9270870"}, {"id": "144be4a8-9368-460c-b95d-41cad64232c0", "name": "manage-realm", "description": "${role_manage-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "********-e475-4355-9b0e-6d41d9270870"}, {"id": "c0de8dfa-f906-4802-b93f-5a0cde6f2b43", "name": "manage-users", "description": "${role_manage-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "********-e475-4355-9b0e-6d41d9270870"}, {"id": "bcfe4261-8592-46b4-9ae4-62c8ef1c574d", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "********-e475-4355-9b0e-6d41d9270870"}, {"id": "a5dbd4ba-ccd9-4d97-9644-125a741485bf", "name": "create-client", "description": "${role_create-client}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "********-e475-4355-9b0e-6d41d9270870"}, {"id": "ac6186f7-21d3-4443-affc-f2aa744dde36", "name": "manage-events", "description": "${role_manage-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "********-e475-4355-9b0e-6d41d9270870"}, {"id": "c292c4d7-e15c-459f-a414-2067b381aaeb", "name": "view-authorization", "description": "${role_view-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "********-e475-4355-9b0e-6d41d9270870"}, {"id": "71402ffc-70a3-4340-ad05-c9c9facb80a2", "name": "view-users", "description": "${role_view-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "********-e475-4355-9b0e-6d41d9270870"}, {"id": "0ff155ab-2d99-4ef7-b7a5-0aeb53dc918c", "name": "view-clients", "description": "${role_view-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "********-e475-4355-9b0e-6d41d9270870"}], "broker": [{"id": "cfe92094-cf37-4312-9622-bebe5f20b7ad", "name": "read-token", "description": "${role_read-token}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "3069ca02-e036-476d-b425-2cc965e6763f"}], "master-realm": [{"id": "2fded3fa-0773-4838-886a-9851dcc3d0cd", "name": "manage-users", "description": "${role_manage-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d"}, {"id": "8319fd2c-5de2-44b7-add3-f4d8715e9b33", "name": "manage-authorization", "description": "${role_manage-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d"}, {"id": "e01b416b-9eb7-48bc-a29c-501bac43ab8d", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d"}, {"id": "d4cb5518-6793-4015-a4db-b5e58cab35c6", "name": "view-events", "description": "${role_view-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d"}, {"id": "efa08022-9356-4cc0-b2b5-dde4778ee2d6", "name": "view-users", "description": "${role_view-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d"}, {"id": "9b23220c-b741-4a4d-a344-23d8d1be2e88", "name": "view-authorization", "description": "${role_view-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d"}, {"id": "75db1573-3356-4a3a-b095-e4801e541117", "name": "manage-realm", "description": "${role_manage-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d"}, {"id": "dbfccefc-070e-4240-afcc-e7309f59b158", "name": "manage-events", "description": "${role_manage-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d"}, {"id": "14b36bab-5aaf-47ef-9300-55d585673e15", "name": "view-clients", "description": "${role_view-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d"}, {"id": "ee23bd41-a1a5-4b2f-806f-0aaff9cff9fe", "name": "impersonation", "description": "${role_impersonation}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d"}, {"id": "e16e564d-d3ca-4cc2-a423-a1f9a7a77b8b", "name": "create-client", "description": "${role_create-client}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d"}, {"id": "76ceb0bf-3868-4182-9017-bb0814a0b612", "name": "manage-clients", "description": "${role_manage-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d"}, {"id": "dcacac6e-381a-4842-87ae-d924a920d23f", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d"}, {"id": "180f2d18-420d-49bf-8cb2-a4c637365911", "name": "view-realm", "description": "${role_view-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d"}], "Migration2-realm": [{"id": "7de3d7b5-1fac-4fd6-b990-beacab8540d1", "name": "manage-authorization", "description": "${role_manage-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7dfdf2da-3b6c-4014-b28f-fff402b446e1"}, {"id": "ebca292a-b94f-46a7-b536-f4a9e0bf0578", "name": "view-events", "description": "${role_view-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7dfdf2da-3b6c-4014-b28f-fff402b446e1"}, {"id": "8efd0ce4-02dc-4e30-86e9-0c3fb5cf3735", "name": "create-client", "description": "${role_create-client}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7dfdf2da-3b6c-4014-b28f-fff402b446e1"}, {"id": "f41e09a0-72ee-4aa7-ab45-0b349c5231ce", "name": "view-realm", "description": "${role_view-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7dfdf2da-3b6c-4014-b28f-fff402b446e1"}, {"id": "5bde4652-277a-4f6e-a92b-c00d18aca1bc", "name": "manage-users", "description": "${role_manage-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7dfdf2da-3b6c-4014-b28f-fff402b446e1"}, {"id": "e039a406-987b-4862-83c6-030f1cc4dfbb", "name": "manage-clients", "description": "${role_manage-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7dfdf2da-3b6c-4014-b28f-fff402b446e1"}, {"id": "a31c3926-b445-408d-aa3e-28d8d9638e81", "name": "manage-realm", "description": "${role_manage-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7dfdf2da-3b6c-4014-b28f-fff402b446e1"}, {"id": "477fe08e-35fe-4ddf-806d-839632716153", "name": "view-users", "description": "${role_view-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7dfdf2da-3b6c-4014-b28f-fff402b446e1"}, {"id": "fbd0be16-54f7-44a2-b4ae-4dfc8690b33b", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7dfdf2da-3b6c-4014-b28f-fff402b446e1"}, {"id": "b4ac9f18-a857-47dd-b741-c0aa56604d46", "name": "impersonation", "description": "${role_impersonation}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7dfdf2da-3b6c-4014-b28f-fff402b446e1"}, {"id": "1bef7689-4591-4d5c-86b7-ff9c4a719340", "name": "view-clients", "description": "${role_view-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7dfdf2da-3b6c-4014-b28f-fff402b446e1"}, {"id": "cd2d4875-1e94-4d5c-99bb-ccb64be5acdc", "name": "view-authorization", "description": "${role_view-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7dfdf2da-3b6c-4014-b28f-fff402b446e1"}, {"id": "3667596d-8ff4-4ca5-9de3-b6dee0e41ef0", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7dfdf2da-3b6c-4014-b28f-fff402b446e1"}, {"id": "a32d5ab7-a82e-42c5-9403-bf95ad99a71a", "name": "manage-events", "description": "${role_manage-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7dfdf2da-3b6c-4014-b28f-fff402b446e1"}], "account": [{"id": "456aa889-e839-4108-a126-6a647aae1662", "name": "view-profile", "description": "${role_view-profile}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "ec45df9d-e110-4f87-874d-e0f684543c80"}, {"id": "7b25b1b0-fe55-42fe-975a-51d40e858139", "name": "manage-account", "description": "${role_manage-account}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "ec45df9d-e110-4f87-874d-e0f684543c80"}]}}, "groups": [{"id": "75afcc87-19bf-4cdd-ae6b-5b539f423339", "name": "master-test-group", "path": "/master-test-group", "attributes": {}, "realmRoles": [], "clientRoles": {}, "subGroups": []}], "defaultRoles": ["offline_access", "uma_authorization", "master-test-realm-role"], "requiredCredentials": ["password"], "passwordPolicy": "hashIterations(20000)", "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "users": [{"id": "17b6ae2e-169c-41e4-815f-2956c86ac6ba", "createdTimestamp": *************, "username": "admin", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"type": "password", "hashedSaltedValue": "UahGF6QpiHbshvw6GmaLR+cgM7v6ZxNKd3mPywudvAz3KWOhv0sMvespERCykvSC2xP4OBMzbwwxBACRSmGE0w==", "salt": "mnR0QbmURHVkSej+2eG9ng==", "hashIterations": 20000, "counter": 0, "algorithm": "pbkdf2", "digits": 0, "period": 0, "createdDate": *************, "config": {}}], "disableableCredentialTypes": ["password"], "requiredActions": [], "realmRoles": ["offline_access", "uma_authorization", "admin"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "groups": []}, {"id": "757d5b1e-fed9-4dec-b935-6d865563b26a", "createdTimestamp": *************, "username": "master-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["offline_access", "uma_authorization"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "groups": []}], "scopeMappings": [{"client": "admin-cli", "roles": ["admin"]}, {"client": "security-admin-console", "roles": ["admin"]}], "clients": [{"id": "********-e475-4355-9b0e-6d41d9270870", "clientId": "Migration-realm", "name": "Migration Realm", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "af26f8af-8d47-4bd8-ad42-fcb172dd272c", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "10a8be93-07d9-48cc-acfa-a16402a1646a", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "77a4d3ec-c5ea-44d5-bcdd-7e836cd10c32", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "1c3eb495-7ad5-4c74-8e00-351225b25616", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "eba679fe-5384-4f4b-9afe-da49f85a5374", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "d346ff3b-db3e-41d8-b483-5d72a3c14980", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "a8e89ec5-3dd0-4a19-8c2d-8c3f24eebf46", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "7dfdf2da-3b6c-4014-b28f-fff402b446e1", "clientId": "Migration2-realm", "name": "Migration2 Realm", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "8573a6bd-4a64-4946-b2a7-8782e72e20b1", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "0ef33aa3-4ec2-4534-9930-3922f234ec1d", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "09049ebd-d7a9-41b0-bc7f-2479d91354ae", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "c8f0a824-acc0-42d0-9531-05782ab81848", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "8ee906ab-7bc2-4f85-8669-df17c3845901", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "7c65a5de-3c7f-4bc4-819e-827df3457f6a", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "988c26d6-9f3f-40b8-aef2-bfcf16cc9f34", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "ec45df9d-e110-4f87-874d-e0f684543c80", "clientId": "account", "name": "${client_account}", "baseUrl": "/auth/realms/master/account", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "ce49d798-ce8f-400e-b113-3e562da9b70a", "defaultRoles": ["view-profile", "manage-account"], "redirectUris": ["/auth/realms/master/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "********-1da1-4031-ab89-2dfeceefab58", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "8ba06688-9217-45d4-9cc7-ac8bc9584776", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "2c257c6b-4084-4f8c-aab5-584c82c94545", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "33513775-4978-43cd-aad1-188b060bed59", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "4beeea08-33c9-46f6-90dc-18d3b0062baa", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "6be10118-4680-4e84-b501-65e7d9514cff", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "af37f4e2-1d2e-464c-b0d9-430e93df26ab", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "10fc8525-6695-4a69-a8cd-7e7e483498a3", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "c69aea6f-3ce9-49af-9497-ace7ca5b0aee", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "303b3957-f1b2-470e-bf30-8dbf4f0fb56f", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "4ece3544-abae-4e99-b357-4009366fd942", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "df016daa-5590-4493-a506-bddf8227198e", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "3a826d25-916e-4e16-b64c-83812def6944", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "3c5e56c2-7e95-4c40-9d76-5f4127625fc0", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "3069ca02-e036-476d-b425-2cc965e6763f", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "8bd856ff-6f3c-4be6-9e66-3176b647e1c0", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "2a7c65ec-517f-4437-b046-3ea42420a5bf", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "515a42d8-6010-4559-b167-d7eeab3efa1b", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "9c449cf9-0d22-43cd-850a-0f3ce5c6b2ec", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "4627b4a3-5b0c-4021-9a02-6e957889d694", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "690c8fd8-4421-4cf0-a807-162bf6cf7732", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "9156c1c8-3a7f-42a9-9612-844a03faf11b", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "577ac9f2-9252-4f64-8b67-006b2f2c6c9d", "clientId": "master-realm", "name": "master Realm", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "98f111af-ee62-423e-91f9-e5be90ce51fe", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "81c57a41-c922-4251-9ec5-fa7097adfd26", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "c46bbab4-344e-417a-b241-588415de8725", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "2bc0f24e-88f7-4c32-a735-991300ca05bd", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "25c4af98-2158-473a-b6fb-190a0477ce5a", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "ae8e5033-2c0f-4041-bd2d-7ea0efadca7f", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "8b72a9e2-3acf-44c0-b992-9483144d834d", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "c74f3ddd-7a3d-4ecd-8967-d89e2138e4c1", "clientId": "master-test-client", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "4f427905-9843-4986-9d6c-97a304055f92", "defaultRoles": ["master-test-client-role"], "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "2018f7f2-c517-4ddb-88ea-b977c44471a1", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "e772b240-ea55-402c-aa60-e1ce4271d7d5", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "405a50a7-3daa-46ef-9896-3ae09f8f5960", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "b04a8ae1-7739-4fe9-88e4-c62335bbd5ea", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "c7fedad1-6591-45b4-bfa8-890bbefc8fdc", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "39a869ab-5446-4f1e-bad5-569f21297324", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "71e56ab0-802a-43eb-8690-b22f462eb032", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "baseUrl": "/auth/admin/master/console/index.html", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "f305cbec-b5bd-47eb-b9d9-10234fac5336", "redirectUris": ["/auth/admin/master/console/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "865640e9-9ee1-430f-b667-aa758915c7be", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "e871b99c-3764-46f7-b59c-121d0d46dc43", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "b0fb1a73-1275-458d-8aed-257820f6becd", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "d9844860-d9a6-4c40-8c64-733ffa886e68", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "bb5c5fd3-2767-4b4e-9994-8e4fb0d5539f", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "61d7f735-9487-474f-802c-23af1bdee98f", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "consentText": "${locale}", "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "1a3769ef-5d2d-4b2f-8bba-156bc1e19ac1", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}], "clientTemplates": [], "browserSecurityHeaders": {"xContentTypeOptions": "nosniff", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "b4d32642-74c3-4405-85a6-6fd7bc82314e", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "fbab53ba-4a40-4df8-9357-38d4f12549e8", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "167887f3-bd5d-41ec-8b26-deeddfb759ba", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "ec767d9f-b8c9-4dc4-be2e-c306e34f0986", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "0c3da821-e9ba-408b-8457-c6e7fde7880f", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper", "oidc-address-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-role-list-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "df60926f-e9d8-4764-9c97-85b192966425", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "844f7dcb-e17c-4fb1-bbd5-c0b17bc8c6d7", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper", "oidc-address-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-role-list-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "6d602e59-7138-4ab8-b4db-3a9952c28986", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {}}], "org.keycloak.keys.KeyProvider": [{"id": "a792d72c-c8e5-4934-b2f5-fb0483703377", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAjiiXzintKn00WYaUB8+8C77UB8qYbi6SkPxNJBMHGf8v4UqY150YzGmq9nhXcju54XPhc9kIgw8PVqulfEtcCMszxYT03A0TidGw/BVaxGG/GEnd6+UsCJm5bRjFzWHAGPROY97b0g8oTDqalLr6wHamljaCismxw/yfRuxRm1g7IAYtDPvQEaBzbHGB5X21bechFqcwr7svHaUBsuVN6T17tsbXAwafv8V3YsRk+r+4gPhRJwOdpq7pdxiNa6Ye2Ls6NFPXYUoHxGPSWk98ba0VqErKVxVWQFEpdrD1gAYwmdhNwXN6BIUiwgSKMiK3uePoPRSfpG/tAtjKQ3VxUQIDAQABAoIBAC5XZUU7vaiAMDSO/mqdeXuKtavki1Q/sl+5H5yG52W+Hmme27xN5euc9rJqAHeoqKuqDvCo63Ft0145jjS6jEIBm9rjwL6LNLdUuBE1FOW3x4evbLyddIFJO30o+jI9j1WTLoq394i+kDBpWkLpwRxEkYGeO2fq4Bw3meSQ3Pl6hwHKMq4FeHQybtHMG8s5cvIOVl+gYuXG4D4XgumsqBwBGy9n6KnFqKt+K6tO03ve2dFt+y7gHWWxNG/o+PsSqSWdNxeG3myglInmEejZXAJRMN9gy4os8gW7AIEcE9wcu2UzgsINACkq6rYtMNFff//GmARPEq7tqdwgEN4FfQECgYEA5wZ5fufc+zLYr2BEN5YM71MGbRA1zzl7dMLYLGmPerNqnyrJeYpiRBYBGQbt0rpLp5cmgevt+rredQHp7iaVKh9P3m2OwDdb6hb9hmnw89P8YEWBk739I0WSUKcBoDHxuFCclijozbL2CZ3XKTVqZHC0S8sAS8Wj7mFYW+WFXIkCgYEAnYbFIzCrhw7eWmeYKFXM61p3iYjg1pRgWEasCUcKKxujg+qFTAES9CqGHbrbCxoIWDtBxOpUOy01hB4rl4Eg0YohiUERCbOBG0yMKhzPZqtEQAsyJb0VwJV0blrerXKxNeDaOc5SwksA41l6UgCAtXUblBIh2zQocb1sJPWcjIkCgYB6CS6Gvf46Z3qU8Zo3brj4dvgPBqAWDfxMClzc4Qocu8fXumdhH5uW8bS0vrdEOIqcRbjeqn00lB/M2Sz0U0c6T/4FtgPRP6z69BgKN85DTrijNjQjkhm01xxaJE40jA1u2o5Mt+F6P02+eLVenEvOMMbcvqgyXn2g/6UILi8LWQKBgQCY5uDheHN2I+OGQvm5ZRcD94sCD7+V6O3fobGJS9A96ZT9DZ2XGY/JOqF1qBLHotwosFP1jDJ4Qjio5hQP+gHeAkLtFCAyl28hDVsb4V0bXfBgbb2eTfwYM7mRCFkOaL5D+t43r+I6v0y8YPTPNbe46lP7lOISAH2lcWTQNyteaQKBgAS3YMhslJU8N6LtHSIAQXF1yXSDWg5P+e95bnYYVwqTPU6LErMonwmARVMfufOOsNfHvxJylnSXPn9hZdUtKIKTIdBs/DzVggOve7spp9qxi5FkhnSqa8U+UCh8I8nnFkCn67Y+qlbCVjESNGfCHFlCdRPbeWGAY1oXeT11tDfC"], "certificate": ["MIICmzCCAYMCBgFa3Gga8zANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZtYXN0ZXIwHhcNMTcwMzE3MTMxMzE0WhcNMjcwMzE3MTMxNDU0WjARMQ8wDQYDVQQDDAZtYXN0ZXIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCOKJfOKe0qfTRZhpQHz7wLvtQHyphuLpKQ/E0kEwcZ/y/hSpjXnRjMaar2eFdyO7nhc+Fz2QiDDw9Wq6V8S1wIyzPFhPTcDROJ0bD8FVrEYb8YSd3r5SwImbltGMXNYcAY9E5j3tvSDyhMOpqUuvrAdqaWNoKKybHD/J9G7FGbWDsgBi0M+9ARoHNscYHlfbVt5yEWpzCvuy8dpQGy5U3pPXu2xtcDBp+/xXdixGT6v7iA+FEnA52mrul3GI1rph7Yuzo0U9dhSgfEY9JaT3xtrRWoSspXFVZAUSl2sPWABjCZ2E3Bc3oEhSLCBIoyIre54+g9FJ+kb+0C2MpDdXFRAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAG+ckqM0jyz4pTgm2utIsaYjTGj77YyfL7kU49dhq8H9VBl1NL4gwOLT3pmHX4aCp08vgBmoNknCZjUN92yFi9k1qpfdsu+wFOO52N4wO2EGm7hRTL4lmf5J4eQmPoZBUidZr5yKKIhSqHCgJ2B2LkC95f5uz9lC5Gx2YwJ3DSTm51W518L+oDXocIhgVLmX2DKDR52ffqIzuNp20UrLXRFVKq2LLGbOaaJugCiIkhZD5W+2xTCdtlOfhvhGTK6+CnW7+/I5tk36AA9dlYYLffo39TlGToN0ZVWEINf99NjFAquxw/pKzz2gXWsCjDRmgpjGKUSnQEQD4sEmZ2Ophcs="], "priority": ["100"]}}, {"id": "fe3b0025-77f1-4f64-8d25-1af1c9459109", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["e5ae3dae-37e5-4c2a-843c-0031e151cf8e"], "secret": ["dsePpP-5kWD4YoWLjiPB8jfRXblKOPSGG4mlxsR_Gtk"], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "5e5f94b1-0109-495e-8bac-f846b38490e4", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "b345033e-74d5-4e40-b17b-cdef469f7b80", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "3b6b1de8-5e70-4ec9-aae3-d2ba8e52c270", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "ae8d0df6-8b13-4ea8-9c0d-0825c07aa964", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "8c19d745-b49c-47ac-8d50-68d1e1f7518c", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "OPTIONAL", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "b8e0c30c-214d-4e54-b4e2-44b021093e6b", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "d6030c45-c50f-498b-a08e-1caaf3fcd7ac", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "7c95a872-2b24-4efc-b97e-1a8c63e822e1", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "22532bcc-4537-4b3d-9b68-b434d6ebbe99", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "29fb380e-6428-4ee7-8295-6ddf54b7dc66", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "OPTIONAL", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "b4b2903b-e1e3-4a7c-9458-aef65f3252ad", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "49618dba-24ee-474f-8994-36ef63e8343c", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "a81032a6-f288-41b7-9fa6-afb7c696f539", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "attributes": {"_browser_header.xFrameOptions": "SAMEORIGIN", "failureFactor": "30", "quickLoginCheckMilliSeconds": "1000", "maxDeltaTimeSeconds": "43200", "displayName": "Keycloak", "_browser_header.xContentTypeOptions": "nosniff", "bruteForceProtected": "false", "maxFailureWaitSeconds": "900", "_browser_header.contentSecurityPolicy": "frame-src 'self'", "minimumQuickLoginWaitSeconds": "60", "displayNameHtml": "<div class=\"kc-logo-text\"><span>Keycloak</span></div>", "waitIncrementSeconds": "60"}, "keycloakVersion": "2.5.5.Final"}, {"id": "Migration", "realm": "Migration", "notBefore": 0, "revokeRefreshToken": false, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "offlineSessionIdleTimeout": 2592000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "9264fdb4-d8a1-4793-b54c-4cc8fd04f682", "name": "migration-test-realm-role", "scopeParamRequired": false, "composite": false, "clientRole": false, "containerId": "Migration"}, {"id": "0d3c10ef-eacd-4368-bf43-5b0c01644a3f", "name": "uma_authorization", "description": "${role_uma_authorization}", "scopeParamRequired": false, "composite": false, "clientRole": false, "containerId": "Migration"}, {"id": "7eac8ffa-aaf7-417a-9f4e-0d6aae747d66", "name": "offline_access", "description": "${role_offline-access}", "scopeParamRequired": true, "composite": false, "clientRole": false, "containerId": "Migration"}], "client": {"migration-test-client": [{"id": "fd5b3ce2-f2aa-4889-bf5d-477270b0e570", "name": "migration-test-client-role", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "1903b814-7b40-4bc0-a69c-fd1380cf1fb8"}], "realm-management": [{"id": "efd9585f-dd86-4aa8-a425-5f5989db2cd3", "name": "view-events", "description": "${role_view-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}, {"id": "0ea6f2ae-dc32-44dd-a673-0a58ada6507e", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}, {"id": "ec18ab2c-f6d0-44e8-b000-53bd065ba6ad", "name": "create-client", "description": "${role_create-client}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}, {"id": "3af1fee4-3cb6-4acb-8e7f-3b91db767e24", "name": "manage-authorization", "description": "${role_manage-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}, {"id": "dfca60b5-509f-4e26-a19e-b882e34bfb3d", "name": "realm-admin", "description": "${role_realm-admin}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"realm-management": ["view-events", "view-identity-providers", "create-client", "manage-authorization", "view-clients", "manage-realm", "view-realm", "manage-clients", "manage-events", "view-authorization", "impersonation", "view-users", "manage-users", "manage-identity-providers"]}}, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}, {"id": "a6d4dc7a-d8e5-4dc5-8b65-a2c73ad971f5", "name": "view-clients", "description": "${role_view-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}, {"id": "ac85bace-ddc2-4066-a1d1-5b51c4cd4e65", "name": "manage-realm", "description": "${role_manage-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}, {"id": "e6b0fd16-e699-41b2-a11e-7ce9a34f6017", "name": "view-realm", "description": "${role_view-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}, {"id": "24f10acf-5693-4235-ad91-ae83774c7d87", "name": "manage-clients", "description": "${role_manage-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}, {"id": "5d4f0d6a-a0f4-4147-93da-7637944635fc", "name": "manage-events", "description": "${role_manage-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}, {"id": "7acd37ad-cacd-4d55-9bc7-fb80817a77bf", "name": "view-authorization", "description": "${role_view-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}, {"id": "d23cacab-0c06-4bb5-af63-081aec7bfe91", "name": "impersonation", "description": "${role_impersonation}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}, {"id": "a5a89dff-954d-43ae-9436-a3b1d7f7e414", "name": "view-users", "description": "${role_view-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}, {"id": "e87a2a2b-2094-441a-b4f9-c33f6717f1e0", "name": "manage-users", "description": "${role_manage-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}, {"id": "86e36aee-c71b-4c33-bf6a-0650b349ddd3", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b"}], "security-admin-console": [], "admin-cli": [], "broker": [{"id": "7c6fd1f8-56a9-425b-bf6a-432ae9439328", "name": "read-token", "description": "${role_read-token}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "573d0751-2969-4060-b668-6c924f25e4f4"}], "account": [{"id": "c7ae7cd6-2d32-41f2-b58e-8ef9e1c08aa9", "name": "view-profile", "description": "${role_view-profile}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "6f27b0c3-9fc0-4e04-b69a-2031349acf04"}, {"id": "811da1a3-3239-40ce-b896-1ddc2c6d3822", "name": "manage-account", "description": "${role_manage-account}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "6f27b0c3-9fc0-4e04-b69a-2031349acf04"}]}}, "groups": [{"id": "fdd708ff-49a8-4141-979e-1cabf867918d", "name": "migration-test-group", "path": "/migration-test-group", "attributes": {}, "realmRoles": [], "clientRoles": {}, "subGroups": []}], "defaultRoles": ["offline_access", "uma_authorization", "migration-test-realm-role"], "requiredCredentials": ["password"], "passwordPolicy": "hashIterations(20000)", "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "users": [{"id": "456eb430-d574-4956-908a-83527a77932a", "createdTimestamp": *************, "username": "migration-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"type": "totp", "hashedSaltedValue": "dSdmuHLQhkm54oIm0A0S", "hashIterations": 0, "counter": 0, "algorithm": "HmacSHA1", "digits": 8, "period": 40, "createdDate": *************, "config": {}}, {"type": "password", "hashedSaltedValue": "kNwotFPNeuwelpT1HWt+E4ONXFK6wjd+h0zbzNBRGwOqacAjeY7vYN9QZQ46DlEKSdn04cEU/3RvX8WPcRegxg==", "salt": "rEIJDbs+BQqpx31v8mONWA==", "hashIterations": 27500, "counter": 0, "algorithm": "pbkdf2-sha256", "digits": 0, "period": 0, "createdDate": *************, "config": {}}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "groups": []}, {"id": "556eb430-d574-4956-908a-83527a77932a", "createdTimestamp": *************, "username": "offline-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"type": "password", "hashedSaltedValue": "D3F6cEj0pNv1UvkPq2XhnH5TTg2BaR2qKQd+vMoT8Pj+cHEGvISbBujjD9+889LIhWUSbQS8nkZH0yEnrTKBAA==", "salt": "C2vKhAsajS53Xu816IcKIw==", "hashIterations": 20000, "counter": 0, "algorithm": "pbkdf2", "digits": 0, "period": 0, "createdDate": *************, "config": {}}], "disableableCredentialTypes": ["password"], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "groups": []}], "clientScopeMappings": {"realm-management": [{"client": "admin-cli", "roles": ["realm-admin"]}, {"client": "security-admin-console", "roles": ["realm-admin"]}], "migration-test-client": [{"clientTemplate": "Default test template", "roles": ["migration-test-client-role"]}]}, "clients": [{"id": "6f27b0c3-9fc0-4e04-b69a-2031349acf04", "clientId": "account", "name": "${client_account}", "baseUrl": "/auth/realms/Migration/account", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "c34c6c4c-a208-415c-ac1a-f2b520a4c385", "defaultRoles": ["view-profile", "manage-account"], "redirectUris": ["/auth/realms/Migration/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "f406f922-0aa5-44ab-83bd-fb3b596e9032", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "d3ffecae-3e6d-493e-a5ac-cb2d1446e9fc", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "e337e585-e509-49ec-a934-a01c5e7dfa34", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "cb5096d0-6c5f-4646-babc-33cdcfed387e", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "d78e393c-b5f3-4ea6-95e7-f3eaa5e83649", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "ed30dd8d-2624-46e0-b513-e799362ff0ba", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "a91baa2f-f6c3-4e5a-b321-3d951258d929", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "4cd9dafb-0ecd-4d46-a351-94abdb764f11", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "684b2fcf-50c1-471a-87c5-b88a6d4bd3a4", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "b8260206-b5dc-4fd9-af9e-8dfbd599dd55", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "4de30825-99a5-4226-a1d1-0c9421128aa4", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "10d97d1b-ddb7-4d42-ab70-0b17ecb3c75a", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "76c2ecb0-cf53-4364-99fb-36debf566350", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "53d115aa-c9f3-4006-b81f-5a9afdb25416", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "573d0751-2969-4060-b668-6c924f25e4f4", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "b84d8413-0fc8-4133-9f52-1b6fffa55b0f", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "0ebd920b-6629-4fc3-8e4d-690011b4d828", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "eeb47dd6-571b-4582-847e-d6b2961df00b", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "0c131188-9db1-464a-b006-20144cb543d4", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "848b7816-4255-4caf-9dd5-64900aab78e5", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "13e9c269-b218-4447-9b54-2adb5f974d08", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "80298431-9a86-42b1-9d52-432d20be88e5", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "1903b814-7b40-4bc0-a69c-fd1380cf1fb8", "clientId": "migration-test-client", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "secret", "defaultRoles": ["migration-test-client-role"], "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "0252fc64-bf67-4042-be15-4bfcb7c234c9", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "281a803d-0a7e-4e5e-9404-26ef620d5d46", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "de4d240f-fdb8-414a-8eba-152343583b4c", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "8d98c2dd-d8b0-46e2-b590-0ab353e9259a", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "b8094177-3c42-4e6a-aa73-3528d5bdfc1d", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "4361cd9a-8322-42e1-a0d3-7a723a788dd3", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"clientId": "migration-saml-client", "enabled": true, "fullScopeAllowed": true, "protocol": "saml", "baseUrl": "http://localhost:8080/sales-post", "redirectUris": ["http://localhost:8080/sales-post/*"], "attributes": {"saml_assertion_consumer_url_post": "http://localhost:8080/sales-post/saml", "saml_single_logout_service_url_post": "http://localhost:8080/sales-post/saml", "saml.authnstatement": "true", "saml_idp_initiated_sso_url_name": "sales-post"}}, {"id": "c8204f6f-f8c2-4af8-9bac-c45c95b4673b", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "7d239280-c0b5-4f8b-887f-f7b5ccd2cbf2", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "09617e7f-699a-4d40-bf4c-56295799ba60", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "82c2f928-331e-4ee4-be70-80490c26eb18", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "41b86180-eb0e-40bd-9406-34295c709a0a", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "10c7a9cc-6384-4e8d-9440-97ed3031dc5f", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "fecbea26-e22a-4fda-82ee-929d63d89116", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "b03d5572-f001-4ab0-98dd-f179e8167096", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false, "authorizationServicesEnabled": true, "authorizationSettings": {"resources": [{"name": "group.resource.a", "scopes": ["view-members"]}, {"name": "group.resource.b", "scopes": ["view-members"]}, {"name": "group.resource.c", "scopes": ["view-members"]}, {"name": "group.resource.d", "scopes": ["view-members"]}, {"name": "group.resource.e", "scopes": ["view-members"]}]}}, {"id": "401e0c86-afd1-4f4f-8322-32b05222bd06", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "baseUrl": "/auth/admin/Migration/console/index.html", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "57a4a548-5e72-414a-8fee-2f2d0bbc6b7c", "redirectUris": ["/auth/admin/Migration/console/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "dd59269d-26ff-48ef-bf3a-b5a217411c4c", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "3e6b11ad-1c5a-4421-8d7b-8b3941f88ef3", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "fc9bc4bf-e08f-418a-b19a-f65ac98ab22e", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "56659148-b115-4557-80e5-9fd9f8597559", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "80a23415-9b70-48c8-8d3e-b74113f7cc02", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "04023632-7cfb-4d97-b3b2-dafbf1598e2b", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "b54ec0ff-9f7d-42c1-9f76-86047f65b4d7", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "consentText": "${locale}", "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "********-3cd4-4f2a-974b-e1447907834a", "clientId": "client-with-template", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": -1, "clientTemplate": "Default test template", "useTemplateConfig": false, "useTemplateScope": true, "useTemplateMappers": true}, {"id": "70e8e897-82d4-49ab-82c9-c37e1a48b6bb", "clientId": "authz-servlet", "adminUrl": "http://localhost:8080/authz-servlet", "baseUrl": "http://localhost:8080/authz-servlet", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["http://localhost:8080/authz-servlet/*"], "webOrigins": ["http://localhost:8080"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "authorizationSettings": {"resources": [{"name": "Admin Resource", "uri": "/protected/admin/*", "type": "http://servlet-authz/protected/admin", "_id": "af06c58d-32b6-44d2-9057-2673ced120eb"}, {"name": "Protected Resource", "uri": "/*", "type": "http://servlet-authz/protected/resource", "_id": "d8ec89d2-7fc3-416c-9584-f242e8a6f827"}, {"name": "Premium Resource", "uri": "/protected/premium/*", "type": "urn:servlet-authz:protected:resource", "_id": "9c4dd55d-b7a1-45a5-a379-d2ae48b7b309"}, {"name": "Main Page", "type": "urn:servlet-authz:protected:resource", "_id": "01394f0e-8b06-4ae8-a1cb-9f6ff7eeb6b4"}]}}], "clientTemplates": [{"id": "d43ae38d-80a1-471a-9a80-5f9a0d34d7a4", "name": "Default test template", "description": "Test client template", "protocol": "openid-connect", "fullScopeAllowed": false}], "browserSecurityHeaders": {"xContentTypeOptions": "nosniff", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "7bd729e2-9099-45b3-aa05-2ec43bc974e8", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "38c943ce-0bc1-4137-b80d-05d514e12ee5", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "136cc895-ace6-444a-b6ca-08158a0514ca", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "63d387da-fa95-46e5-9321-9780d4086ac6", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "73357d65-d98f-4ba8-b312-f93b1e5efb3b", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper", "oidc-address-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-role-list-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "8452e4a6-9bd1-4a13-bff5-e3cff94fbd6f", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "806417ec-a33a-4d40-969d-98fd8e1822e2", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper", "oidc-address-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-role-list-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "075b4dc9-2657-4243-b25f-921b245cdfcd", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {}}], "org.keycloak.keys.KeyProvider": [{"id": "93c9cce7-c15f-4067-a790-d52d113bfa9f", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAhiLZwTEe2nNLi/IqXHFziVVW8XXRUGMUnMhprvMu86K4Xz9MRfjYzpffo+I10V+VSdRimMPgyqaSZ9x+mRSl61029Bilq4LZbfHAitjk5nZw1J645dVTRwul9u8/NsgqpFoT1Z4s63KOrXI4dduvBqLcLjaPcqiXs2nN8npS7fSEygrvHWUN8YZHMEwr5NmPk77RaiBXokXljgjBJNsQK8VUS56ynqYs9aK3sM/Ms49m+kb7BmdWlXlSPQ0ctxc10kOGX8KpepE78kset/u/6KnEaYlSDC+uuPMLTnB7y6OY23fzhGUJNhav/Pqhr9WXPqxhy+vQnJd5j38i20iuiwIDAQABAoIBAB04Ej8nOBVV8Cr4AYpJnGVaFapbg9EDBEZy0bKufpfL5mW5ICBN6Mx4ANtuN1L4g0il3yRv5tO+HT5Kpbkq6kmu8H2TsOL/ieWNN+gG/zXErydhu9007wMW4nw7+YUxbTiH9rId1qMjPTlDHFFwHVxQta3LbMwJNaJ7dcVQKCh1PomPh5Z7THeN31eTSRajvd3vP9bAorxJW8kh6vD+FHG5P1r5aOiF6SacKySCVJ+RxUeJvn9/8KLSP4n4GyLt9HWxSOCoRAjMiZuXUkDRXP09Csb4L5tgqaSIjiWRXOEH3xMcrBUoovM9iBbuqmkGPEjvyUTabLxPvxKGUEipV6ECgYEAvFNykRIjKwH8KN1LUd+ZOwM+Oe85yeMACCZqyjIiD7O7lAs261kOlyiycimTF3SGlbw60SzGO9FmTh722e50Mktl/uR3+iG7WoYy8t41uphoReHw4i8kl+bU/4+O0faHoTFXMN+TFaHeHzY3XQbAekXKXaKsP978NkdKcfXZUacCgYEAtlZaFhBevYOLt14S/LPlXoE0PZFqOJasOHSkIcotdq5zLEVppTzcxf4xTFSR+SOpsJUScGivO5F0Ko9svvaqxXzdH97Urb/4oDfuUgIFLnBs9ixNpj3fUPirBD9tolRqFAQCN+M2FTjUcbWkVDsusmXoynrPNaryyHEQ/c7PsH0CgYAU84esYgMtlrPx6+SkQCbw7MRwd4/LSQjI31VEF124t3sn1jOO7UJ1SJ7Jmpx1oI4UfHNisAOBxiMGj+VQi3m72q2HNO2wEy6fvIxQfq4liy0WkTNWRn5gs9qAb/0omK+wHCgiFM1tO6vVdxejrsudDsSbtCxLksgmVboPgZViwwKBgEc+cE6ksM/twT6xhEu24s1NQKM52T4pKTuZ37PQMnln6lqG+KN2p3C5RHvlo2+sTpZPePfw/18QE0q55ayr5sfpZOEaCRGmq7e2gukKHwopJXn+j4yAKmyR7Qho3CaMQzJ06oT5w10BaojPh1jeEv3jsdTDrYjztY86FoCFOUNlAoGBALhragR4OTeW+XaQhu9zVaLs4MIJIGdfe7zRnkrdMsKpyqBL4RJ6SUqtVe8CwJoSbOCkoiH/mLkppZdgUatSDW57qG3Y3V4/z3jfg7IlthdbBCcDKRk8UqTwXOM+IWjmaCg1cU/kIW9ZAbNm99DPX36Rdk8d1pEUQU96kK259UVc"], "certificate": ["MIICoTCCAYkCBgFa3GvSnTANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAlNaWdyYXRpb24wHhcNMTcwMzE3MTMxNzE4WhcNMjcwMzE3MTMxODU4WjAUMRIwEAYDVQQDDAlNaWdyYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCGItnBMR7ac0uL8ipccXOJVVbxddFQYxScyGmu8y7zorhfP0xF+NjOl9+j4jXRX5VJ1GKYw+DKppJn3H6ZFKXrXTb0GKWrgtlt8cCK2OTmdnDUnrjl1VNHC6X27z82yCqkWhPVnizrco6tcjh1268GotwuNo9yqJezac3yelLt9ITKCu8dZQ3xhkcwTCvk2Y+TvtFqIFeiReWOCMEk2xArxVRLnrKepiz1orewz8yzj2b6RvsGZ1aVeVI9DRy3FzXSQ4Zfwql6kTvySx63+7/oqcRpiVIML6648wtOcHvLo5jbd/OEZQk2Fq/8+qGv1Zc+rGHL69Ccl3mPfyLbSK6LAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAAHub1rD7Q3uCxBWDZsmg8are61BHsTiBHx8uFZ+dUlideGrIfZ9Cdw+4MY970fFG77hetTcyzJmA9Xob3aZxRxj6T83Ibu41FTVpAoSNiqzRcrdPTCka/ey+AtCaymWeF9QFVPXorL8lBCYtbvpZ/ueTjCVdN3HkX59za0ntE9H48mxbBYoIvX8QN2v91xy3uTK0H5fZsE0MfvtJqgwgvlKtb2xOn79Tpob8f92D7ZCBQVIaXwXw34BIZYRqFX9Rftpx+Hr7O3KjpmUBOmPiJNjLX2ce9MnQyag6SXqs+9RQqOBYwgieCUwtTzjkuOx2FYkXCDy7Nd2dxyrwq3CM0E="], "priority": ["100"]}}, {"id": "676c2a48-745c-477c-b8d0-93da72d7fa4e", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["3d84b2f9-a3f2-4521-9f28-5761e1fb7b1b"], "secret": ["cVfUcv4wf-uT6VFAaY5fOw1biyTgwr9W0ueDv--twKc"], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "e601d1ab-d031-4b0d-8da6-b20975353906", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "00b46bc9-ab8a-4403-bec6-9fd7f3a3f14d", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "7ee834ed-414b-4b34-a480-7d7cb158482a", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "cbcf873c-a965-4c42-90cf-0bca617803fe", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "59bc4c61-ce2b-4f80-8538-6ec4bd4e93d0", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "OPTIONAL", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "8ebafdee-ed12-43ed-979f-6bc5e0c3a6b6", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "13dff5d3-3f43-4d8d-a605-dd46751cb4cd", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "912947ec-29a1-4f82-838f-3e6f0cb92544", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "e312ae45-7a9a-4ecd-a29a-************", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "3c8d1428-d801-4f27-a96a-6835a40377d5", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "OPTIONAL", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "e76a5616-d4d1-4b1d-98cb-0b45cfb8d361", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "17259498-3563-4313-8111-fb8c015b0666", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "6a3f26f7-b906-4786-bfc5-cea683cfddfb", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "attributes": {"_browser_header.xFrameOptions": "SAMEORIGIN", "failureFactor": "30", "quickLoginCheckMilliSeconds": "1000", "maxDeltaTimeSeconds": "43200", "_browser_header.xContentTypeOptions": "nosniff", "bruteForceProtected": "false", "maxFailureWaitSeconds": "900", "_browser_header.contentSecurityPolicy": "frame-src 'self'", "minimumQuickLoginWaitSeconds": "60", "waitIncrementSeconds": "60"}, "keycloakVersion": "2.5.5.Final"}, {"id": "Migration2", "realm": "Migration2", "notBefore": 0, "revokeRefreshToken": false, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "offlineSessionIdleTimeout": 2592000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "3ca75db0-3a8b-4ac5-b8f3-b145e9d97aa4", "name": "uma_authorization", "description": "${role_uma_authorization}", "scopeParamRequired": false, "composite": false, "clientRole": false, "containerId": "Migration2"}, {"id": "8dfe45d2-489b-4751-8332-2ca8b1cc9486", "name": "offline_access", "description": "${role_offline-access}", "scopeParamRequired": true, "composite": false, "clientRole": false, "containerId": "Migration2"}, {"id": "a495da40-f44c-4e28-8f82-75bb5677e597", "name": "default-roles-migration2", "description": "${role_default-roles}", "scopeParamRequired": true, "composite": false}], "client": {"realm-management": [{"id": "25219cc0-8d01-467c-a504-534ac254bc5d", "name": "manage-authorization", "description": "${role_manage-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}, {"id": "0ba6266a-3b91-4d34-993b-6877b36e4cce", "name": "manage-clients", "description": "${role_manage-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}, {"id": "cda51a8d-c757-4858-8d32-fcd7263186df", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}, {"id": "d14d3fa7-bcb6-4394-b6cb-4b9cc1bac68a", "name": "manage-users", "description": "${role_manage-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}, {"id": "9aabb672-6c26-4d0c-9c8d-b13e8367b772", "name": "realm-admin", "description": "${role_realm-admin}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"realm-management": ["manage-authorization", "manage-clients", "view-identity-providers", "manage-users", "view-realm", "manage-events", "impersonation", "view-users", "view-authorization", "manage-realm", "manage-identity-providers", "view-clients", "create-client", "view-events"]}}, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}, {"id": "54fa9d64-e184-40c5-aac0-7f7368e41067", "name": "view-realm", "description": "${role_view-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}, {"id": "f8baca32-6261-4fbd-8450-1a78453996da", "name": "manage-events", "description": "${role_manage-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}, {"id": "8eca12ea-1ada-43f2-b110-8ba4046ddcfa", "name": "impersonation", "description": "${role_impersonation}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}, {"id": "a0a808e0-e1fe-493d-a50a-dbb95fb6c8ad", "name": "view-users", "description": "${role_view-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}, {"id": "eeeb42e5-1fdc-422d-bfb8-c94b1c5dfb0f", "name": "view-authorization", "description": "${role_view-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}, {"id": "a77e7f4e-e675-4f4f-8f0a-4cb3a9f2b674", "name": "manage-realm", "description": "${role_manage-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}, {"id": "d9880b2d-27fd-4eb0-86a4-cf27eaa85c59", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}, {"id": "65ec15f1-84de-4ec6-82da-cbde752faf2f", "name": "view-clients", "description": "${role_view-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}, {"id": "3c63876e-ef7e-4166-831e-90d9e8e99cf0", "name": "create-client", "description": "${role_create-client}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}, {"id": "5383cc58-3a84-43b1-a84b-59205cf14193", "name": "view-events", "description": "${role_view-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc"}], "security-admin-console": [], "admin-cli": [], "broker": [{"id": "37b7f08f-f0e7-4dc7-aa3a-a339265bdf08", "name": "read-token", "description": "${role_read-token}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "1af6d357-8e4f-4ebd-b852-d4c584dffe8d"}], "account": [{"id": "********-1c0c-460a-914d-46803ed5e172", "name": "manage-account", "description": "${role_manage-account}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "55b6e874-9df4-41a2-9efb-8168f4f9ef0f"}, {"id": "612d66f0-5e22-438b-a5dc-c635f0c75578", "name": "view-profile", "description": "${role_view-profile}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "55b6e874-9df4-41a2-9efb-8168f4f9ef0f"}]}}, "groups": [], "defaultRoles": ["offline_access", "uma_authorization"], "requiredCredentials": ["password"], "passwordPolicy": "hashIterations(20000)", "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "clientScopeMappings": {"realm-management": [{"client": "admin-cli", "roles": ["realm-admin"]}, {"client": "security-admin-console", "roles": ["realm-admin"]}]}, "clients": [{"id": "55b6e874-9df4-41a2-9efb-8168f4f9ef0f", "clientId": "account", "name": "${client_account}", "baseUrl": "/auth/realms/Migration2/account", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "8dfaceeb-2772-494c-bf6d-d49c34e55d42", "defaultRoles": ["view-profile", "manage-account"], "redirectUris": ["/auth/realms/Migration2/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "6406e544-302b-4bee-8508-2bde135ca397", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "372005a1-b0b0-43d5-8ced-4bd8019243c9", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "defb4a33-1cbc-428c-abb1-39836e710fd8", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "75672101-7a90-4d9d-8613-e065bb7c0fa4", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "3a137c4b-4e5d-401c-8308-fdbc3a0c1f29", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "65b72f01-66bb-4420-840f-e105938096cd", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "25b38437-8a49-4ad8-8bde-ba36f444a014", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "f218b8da-83c5-48bd-af15-afe98ecc50c0", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "d9608107-9888-4162-a496-f4f21372db20", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "bf5ee69f-9fb0-4c91-93cd-6b82ae22b99b", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "1a2302cb-6edb-44f7-9d66-cbd8b6433ced", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "dde6a8e4-d574-4697-8132-5d43c43a298b", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "a1b36bef-1460-453a-affc-a931f8927e7b", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "3d7e0cbd-6f8e-4f70-a879-846211aef6d1", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "1af6d357-8e4f-4ebd-b852-d4c584dffe8d", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "8b879d60-4dfa-4aaf-bdd6-c56c803ea8d3", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "2520d2a5-490d-492e-92ae-558b1acf5852", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "5a43651a-10f5-4446-a7b2-bdb6ee97e75c", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "c251a13f-0f81-4367-b1c0-b495695fb4bb", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "3d4150f0-5c31-484b-8e5b-6ccdf56b430a", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "efae169e-2d9e-4aec-842f-35287ae9b993", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "1bbae8eb-5670-41b2-bf14-e5bcd5fcd578", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "7028079b-fe5c-4a3b-b4ff-1d81fab31dbc", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "882a040f-d7db-4417-99ac-0d9b77d34d94", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "f6c4f5b1-8d7b-4fb1-a68c-b05c4fe7a9b7", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "3c1bfffa-18d7-471e-87d0-bae7dd44caf3", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "4e779369-50ab-4a54-947c-ace45236a599", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "73f458f7-3069-4189-bf1c-99fb12513899", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "d6c5f1cb-ccb9-4107-bfb6-0395c93a9d6a", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "be37a03e-c842-45a3-9cd1-ad63ce4e350e", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "ee19c9a7-e7bd-47c6-a43e-bc6eff9bf0c2", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "baseUrl": "/auth/admin/Migration2/console/index.html", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "4c4ce40d-dedd-4f77-b76f-319520516b4d", "redirectUris": ["/auth/admin/Migration2/console/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "c10048b1-d3ed-463d-b85b-2acd04ee5c7d", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "52cfc1e8-d160-4276-a4bb-1d32c43ef65b", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "60523a72-fb43-4fea-91f5-03b914c5020f", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "0498d224-0d99-4e79-baed-170f952ca7c7", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "consentText": "${locale}", "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "c3407c64-7d39-4de5-aefd-c437fc5ee0f5", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "010054c8-ebb9-4cf5-9b8f-3eab0ff00c38", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "a94915a4-4061-4109-9cbb-4ea7930251aa", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}], "clientTemplates": [], "browserSecurityHeaders": {"xContentTypeOptions": "nosniff", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "29dc4ac1-7121-489e-a057-7155f05044a7", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "ef24941c-ab4a-4d50-8d3b-c0e17686e538", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "5ce2cf82-c2b2-4fd9-9262-c89e7bf31af4", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "5975bf2d-003b-4545-9c98-1fb3986c5f80", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "47fb230a-8c18-4e64-abf1-7d1ec326b567", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper", "oidc-address-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-role-list-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "c6face0b-df25-448f-b2d2-746b798dda85", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "6f3552ff-4b2e-4817-88b0-1dbb09a1b772", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper", "oidc-address-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-role-list-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "1bc070b4-f5af-49e8-b57d-d8fdd7e1cc5b", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {}}], "org.keycloak.keys.KeyProvider": [{"id": "416d683a-254c-4f0c-b8ce-6290752573e7", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEoQIBAAKCAQEA0wexM5VrVeD4G0K3XXqvhomXds3k4GyOn5UWoEQ/BsAmWiA8tt6zF0TTdf6QUO6htGFMBOo1eESE0lDlhhEf7IbSmm6UlCk2hqoTWNvhQZgesiA8QLsq0cLmn1djqpj9vD1RMWBWt0wECwq3HzyViZEdtGUf2UrI/m/Lmb8hWKmhnucWcLSi0or/bnZIbvS2697KVMuKGEFxMTP89LygAKOI/MzqcdywXnITWzTekp5ecyB0CH2mihwX7gbPD5/xewPtuAC/xPpFVvYNJehucf6yG0FD1wo3Q3nuEMmTiHu/6jtzpK2l2QRf/DV0ZuLjiKFqNPUTdFSD029Dz8+7wQIDAQABAoIBADHAn4N019BwcK4kpYxxb059w+rrkeljTqDLCoGQdXeG98PJ0+adTIXQ4TXxZV7fU9puEfkaqq6HIPVgwyw/f1JJWv+z0G255bQMBvGtLUdEsP/335y3Q9oeZDbu6H2BAllN74V/nBIs5ssfpJrFss+zGdi8+qxFGGv8OXku+ZKoRejrRHcVSw/kMZ/bRBuQH54MRPj+nSqM3SxqhJ8MDSB4yKtCse8JjzhpokacMFv2tDEdCGsd2fNwQ/eWoQDoj/JI2YNNH/YQTKHRvXB8W2V5w0TPzEOYAq2zJyJnoPodsV0OyuVcYh9s3d5PN7irQmjXDKc3CxtKeQJ1gAi/4q0CgYEA7Dc5DMczYGLiIK+Q7+Gv4ALpMFm3899n41ZNsD7PXlL4plFkopypTCtLJsBeXFndGIFrIe5/fyMAWinIHJ0iVBPJOOYnxIHlbbBFLlwfZHeWJUTmCVf+/dkDKn9dlIkhmKxGhf8FGu3fKqusHbEXGXGoHy/F5ZvKT7CxxTBCFAcCgYEA5LR0kmEv+NX3R8GMdNqKZB7bzdG6dk7zozUN0J6nSO0+C2kpEYrRCVatZj8sWcJFxNypnVHwrr8ALl5a8//58oSoitUmlYL/lwxuk0xhtAHHXAAbnulXxCpUfIeVhunE1HhKuIajMxeQ0NtYk7vcRhY4eic804JCDzSkb1OOD/cCgYBaQ0PemSQNudt06AG3WINPU21uF2qOmxjkqy7moSm2fngbfdC5vqmGvKhr92hXW9N7sMCSZ8N6fpJ84ahhHr5maGUlA/XARm64OnpFG9i3GGdceHZ6rZB3yfxGugK1Zq1a3MHaJDf8MVdsw8ryUaBlDaRTvnqdoaluSDO2CRnG8wJ/AclnH72iok8Jn4XjkwYiDqYvEWySCFqVjLDdasPSir9PvMcViw2KlIgY0rOrpQffg6o1qQT63GKhnbse+skFKt/ecTc5FfHwDT4Lnxr5qG5bIFzf2l34UZADGPX2NReecgp+ivKCU+PbPcZ46q6NCf8icdHcp8pcucGvhy8f7QKBgQDMWjVt34tMRR6G/DM14QLDgKfjISiqrtGoouKaEQ7aME/wf8lOpCtfSZASzIPdpz5CC+vvi0N7OwsllNL93S7xLlJ2BLVubRb3DMCtQSz9EUVL6Pti6Pnkb84BmXhRcT8e+RG73WP1SmXxuIL8bZ+bXEwa65jl8p5C8BpRYIJFUQ=="], "certificate": ["MIICozCCAYsCBgFa3GxEqjANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApNaWdyYXRpb24yMB4XDTE3MDMxNzEzMTc0N1oXDTI3MDMxNzEzMTkyN1owFTETMBEGA1UEAwwKTWlncmF0aW9uMjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBANMHsTOVa1Xg+BtCt116r4aJl3bN5OBsjp+VFqBEPwbAJlogPLbesxdE03X+kFDuobRhTATqNXhEhNJQ5YYRH+yG0ppulJQpNoaqE1jb4UGYHrIgPEC7KtHC5p9XY6qY/bw9UTFgVrdMBAsKtx88lYmRHbRlH9lKyP5vy5m/IVipoZ7nFnC0otKK/252SG70tuveylTLihhBcTEz/PS8oACjiPzM6nHcsF5yE1s03pKeXnMgdAh9poocF+4Gzw+f8XsD7bgAv8T6RVb2DSXobnH+shtBQ9cKN0N57hDJk4h7v+o7c6StpdkEX/w1dGbi44ihajT1E3RUg9NvQ8/Pu8ECAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAqPLouXjLeW2IJtGnVSCtV8GsCphzSV97aJeRymhEgMRfbjmfwenrB24+9mQt+sq78LNE5tgi50+/jxQhYS0lZ7U0hyvD2CL7nPo/08CrJfVWal0G5MIEGVVg3TQV+mMVnYObpB4OVy2BdLrUw4Hu3U+HfrdqjaWLeWDLlBVvY9+PPtN9rvPE6iGzHlZRLsLe0QrJqLXEY8TKy8iSJQTmZQDnoPQh6LYXxz9mP29AOReYDA17bl0dHo1trhurVTFRnZOOhSLnOdp2GMNBPCQ4pZUNKUBskF8Cj60C/667AnQ/5Dsn4TTLpPp2u01MIHxuv0H9RFzglQ+4GesWnDZIzg=="], "priority": ["100"]}}, {"id": "d083afb9-70c2-403c-9807-2125c1e4442e", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["07d897f7-d1c4-4553-a46c-ca0dc2b6b5fd"], "secret": ["W70htWKvXRziLRgp6oMnDmtL28Ruf2Gc12x4vk3qiAg"], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "6caba987-2390-43de-b1e8-d61b774353e6", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "********-5330-461d-b05f-0ca97c27c06c", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "8fcb72b6-5936-418c-b2db-a766de6c95dd", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "0cca7264-b861-464a-a7a2-3e9528075005", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "a7c6cffc-e45c-4310-99b9-d372d385b15f", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "OPTIONAL", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "6aeb0c6e-7f76-4d9d-b028-2d9bbf878a73", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "5a2cb584-2b10-4cc9-820e-b40ea8c7d4ee", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "d6f55560-23a4-4952-9967-58a409e7f1ac", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "2f9827b8-589e-4ea7-ac2b-8ddb2d6dbf40", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "3cf05239-7f60-4303-a98b-9a09efda49a9", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "OPTIONAL", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "8aef44c6-44b0-4aae-9754-100cfb6f9283", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "c56cb058-5ff0-4531-aee1-99778fd8597b", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "cc75f878-02a9-44f0-953f-e9e8f2fb8b24", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "attributes": {"_browser_header.xFrameOptions": "SAMEORIGIN", "failureFactor": "30", "quickLoginCheckMilliSeconds": "1000", "maxDeltaTimeSeconds": "43200", "_browser_header.xContentTypeOptions": "nosniff", "bruteForceProtected": "false", "maxFailureWaitSeconds": "900", "_browser_header.contentSecurityPolicy": "frame-src 'self'", "minimumQuickLoginWaitSeconds": "60", "waitIncrementSeconds": "60"}, "keycloakVersion": "2.5.5.Final"}, {"id": "test ' and ; and -- and \"", "realm": "test ' and ; and -- and \""}]