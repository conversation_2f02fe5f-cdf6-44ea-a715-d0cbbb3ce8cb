#!/bin/bash

# Keycloak Realm Deployment Script
# This script helps you deploy the realm configuration step by step

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration variables
KEYCLOAK_URL="${KEYCLOAK_URL:-http://localhost:8080}"
ADMIN_USER="${ADMIN_USER:-admin}"
ADMIN_PASSWORD="${ADMIN_PASSWORD}"
REALM_FILE="${REALM_FILE:-realm-config.json}"

echo -e "${BLUE}=== Keycloak Realm Deployment Script ===${NC}"
echo ""

# Function to print colored output
print_step() {
    echo -e "${GREEN}[STEP]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check prerequisites
print_step "Checking prerequisites..."

# Check if realm config file exists
if [ ! -f "$REALM_FILE" ]; then
    print_error "Realm configuration file '$REALM_FILE' not found!"
    exit 1
fi

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    print_error "jq is required but not installed. Please install jq first."
    exit 1
fi

# Check if curl is installed
if ! command -v curl &> /dev/null; then
    print_error "curl is required but not installed. Please install curl first."
    exit 1
fi

print_info "Prerequisites check passed!"
echo ""

# Get admin password if not provided
if [ -z "$ADMIN_PASSWORD" ]; then
    echo -n "Enter Keycloak admin password: "
    read -s ADMIN_PASSWORD
    echo ""
fi

# Step 1: Validate configuration file
print_step "Validating realm configuration file..."

# Check if it's valid JSON
if ! jq empty "$REALM_FILE" 2>/dev/null; then
    print_error "Invalid JSON in realm configuration file!"
    exit 1
fi

# Extract realm name
REALM_NAME=$(jq -r '.realm' "$REALM_FILE")
if [ "$REALM_NAME" = "null" ]; then
    print_error "Realm name not found in configuration file!"
    exit 1
fi

print_info "Realm name: $REALM_NAME"
print_info "Configuration file is valid JSON"
echo ""

# Step 2: Test Keycloak connection
print_step "Testing connection to Keycloak..."

if ! curl -s -f "$KEYCLOAK_URL/health" > /dev/null; then
    print_error "Cannot connect to Keycloak at $KEYCLOAK_URL"
    print_info "Make sure Keycloak is running and accessible"
    exit 1
fi

print_info "Successfully connected to Keycloak"
echo ""

# Step 3: Get admin token
print_step "Authenticating with Keycloak admin..."

TOKEN_RESPONSE=$(curl -s -X POST \
    "$KEYCLOAK_URL/realms/master/protocol/openid-connect/token" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "username=$ADMIN_USER" \
    -d "password=$ADMIN_PASSWORD" \
    -d "grant_type=password" \
    -d "client_id=admin-cli")

if [ $? -ne 0 ]; then
    print_error "Failed to authenticate with Keycloak"
    exit 1
fi

ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token')
if [ "$ACCESS_TOKEN" = "null" ]; then
    print_error "Failed to get access token. Check your credentials."
    print_info "Response: $TOKEN_RESPONSE"
    exit 1
fi

print_info "Successfully authenticated"
echo ""

# Step 4: Check if realm already exists
print_step "Checking if realm already exists..."

REALM_EXISTS=$(curl -s -o /dev/null -w "%{http_code}" \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    "$KEYCLOAK_URL/admin/realms/$REALM_NAME")

if [ "$REALM_EXISTS" = "200" ]; then
    print_warning "Realm '$REALM_NAME' already exists!"
    echo -n "Do you want to update it? (y/N): "
    read -r CONFIRM
    if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
        print_info "Deployment cancelled"
        exit 0
    fi
    UPDATE_MODE=true
else
    UPDATE_MODE=false
fi

echo ""

# Step 5: Validate configuration before deployment
print_step "Pre-deployment validation..."

# Check for placeholder values that need to be replaced
PLACEHOLDERS=$(jq -r '.. | strings | select(test("your-.*|change-this|placeholder|example\\.com"))' "$REALM_FILE" 2>/dev/null || true)

if [ -n "$PLACEHOLDERS" ]; then
    print_warning "Found placeholder values that should be replaced:"
    echo "$PLACEHOLDERS" | while read -r placeholder; do
        echo "  - $placeholder"
    done
    echo ""
    echo -n "Continue anyway? (y/N): "
    read -r CONFIRM
    if [[ ! $CONFIRM =~ ^[Yy]$ ]]; then
        print_info "Please update the placeholder values and try again"
        exit 0
    fi
fi

# Check for required fields
REQUIRED_FIELDS=("realm" "enabled")
for field in "${REQUIRED_FIELDS[@]}"; do
    VALUE=$(jq -r ".$field" "$REALM_FILE")
    if [ "$VALUE" = "null" ]; then
        print_error "Required field '$field' is missing from configuration"
        exit 1
    fi
done

print_info "Pre-deployment validation passed"
echo ""

# Step 6: Deploy or update realm
if [ "$UPDATE_MODE" = true ]; then
    print_step "Updating existing realm..."
    
    RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/realm_response.json \
        -X PUT \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -H "Content-Type: application/json" \
        -d @"$REALM_FILE" \
        "$KEYCLOAK_URL/admin/realms/$REALM_NAME")
    
    if [ "$RESPONSE" = "204" ]; then
        print_info "Realm updated successfully!"
    else
        print_error "Failed to update realm (HTTP $RESPONSE)"
        if [ -f /tmp/realm_response.json ]; then
            print_info "Error details:"
            cat /tmp/realm_response.json
        fi
        exit 1
    fi
else
    print_step "Creating new realm..."
    
    RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/realm_response.json \
        -X POST \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -H "Content-Type: application/json" \
        -d @"$REALM_FILE" \
        "$KEYCLOAK_URL/admin/realms")
    
    if [ "$RESPONSE" = "201" ]; then
        print_info "Realm created successfully!"
    else
        print_error "Failed to create realm (HTTP $RESPONSE)"
        if [ -f /tmp/realm_response.json ]; then
            print_info "Error details:"
            cat /tmp/realm_response.json
        fi
        exit 1
    fi
fi

echo ""

# Step 7: Post-deployment verification
print_step "Verifying deployment..."

# Check if realm is accessible
REALM_CHECK=$(curl -s -o /dev/null -w "%{http_code}" \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    "$KEYCLOAK_URL/admin/realms/$REALM_NAME")

if [ "$REALM_CHECK" = "200" ]; then
    print_info "Realm is accessible and properly configured"
else
    print_error "Realm verification failed (HTTP $REALM_CHECK)"
    exit 1
fi

# Check if clients were created
CLIENTS_COUNT=$(curl -s \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients" | jq '. | length')

print_info "Number of clients configured: $CLIENTS_COUNT"

# Check if identity providers were created
IDP_COUNT=$(curl -s \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    "$KEYCLOAK_URL/admin/realms/$REALM_NAME/identity-provider/instances" | jq '. | length')

print_info "Number of identity providers configured: $IDP_COUNT"

echo ""

# Step 8: Display next steps
print_step "Deployment completed successfully!"
echo ""
print_info "Next steps:"
echo "1. Access the admin console: $KEYCLOAK_URL/admin/"
echo "2. Navigate to realm: $REALM_NAME"
echo "3. Test client configurations"
echo "4. Test identity provider connections"
echo "5. Configure additional users if needed"
echo "6. Test the complete SSO flow"
echo ""
print_info "Realm URL: $KEYCLOAK_URL/realms/$REALM_NAME"
print_info "OIDC Discovery: $KEYCLOAK_URL/realms/$REALM_NAME/.well-known/openid-configuration"
print_info "SAML Metadata: $KEYCLOAK_URL/realms/$REALM_NAME/protocol/saml/descriptor"
echo ""

# Cleanup
rm -f /tmp/realm_response.json

print_info "Deployment script completed!"
