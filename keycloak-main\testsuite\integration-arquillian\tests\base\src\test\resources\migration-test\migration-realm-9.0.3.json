[{"id": "Migration", "realm": "Migration", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "a0ed5009-48c8-477a-9852-af020a584773", "name": "migration-test-realm-role", "composite": false, "clientRole": false, "containerId": "Migration", "attributes": {}}, {"id": "4af8856b-dd6a-4cc7-add0-6091097a3206", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "Migration", "attributes": {}}, {"id": "bdb9ab81-ea29-4f39-8eef-c173c13757ae", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "Migration", "attributes": {}}], "client": {"migration-test-client": [{"id": "52ce63ed-8cd7-4cb1-adc1-7b218c5dfe15", "name": "migration-test-client-role", "composite": false, "clientRole": true, "containerId": "21ce6e52-25ef-4514-851e-c67ecc2f820a", "attributes": {}}], "realm-management": [{"id": "4c3a55e2-9e53-4330-82d2-e003824062dc", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "c6d69a08-3cf7-4f47-97b1-da8146cbdeb0", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "a2b3c57d-badc-4456-bd9e-69ddbc8100a4", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "b5b6095b-49da-443e-b114-b4a6cb328b81", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "0d620fd0-2e0b-439b-8714-4b032b647530", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "aa0bad0e-9cef-4370-b33c-b80c9a00e5d0", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "1ae75da8-d9ac-45d4-ae8e-677b9e5b710d", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["create-client", "query-groups", "view-users", "impersonation", "query-users", "view-authorization", "manage-authorization", "view-realm", "view-events", "query-realms", "manage-identity-providers", "manage-realm", "query-clients", "manage-events", "manage-users", "manage-clients", "view-identity-providers", "view-clients"]}}, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "68f8f5e3-c5d5-44a1-b451-6cde306f8e17", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "1009c46b-91f3-4784-beae-e051afb8a161", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "9df79b9c-dae1-4686-93f2-a98a1873ad38", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "01bb20f9-9e83-45e8-9dfb-a687c71d3add", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "d9d36f07-b31a-46cc-b2a8-7691c6c7fda0", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "71f309d5-9f1a-46bc-986f-b94bccf6b812", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "f8be6680-3830-4688-911d-6bfc021d1add", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "94cb722d-79c2-40ec-aa43-11eaa079f40f", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "99e57325-85b4-433c-8bf9-7064637249e3", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "c45cc70f-a68a-4c9e-9f8f-774db43b64ca", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "5a636944-b8ac-4470-b3c1-a37f69b1f5fb", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}, {"id": "f3d900ca-920c-42aa-a63e-c3b2bfabb562", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "94e1c25e-2558-440f-aa18-f2473c76881e", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "6b7dde10-8057-4370-86a6-43cff66224db", "attributes": {}}], "account": [{"id": "324c669a-cab5-4ac7-895d-200fa6b8380c", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "b95de947-a8d2-4209-be80-ede9d76db53b", "attributes": {}}, {"id": "a748d09c-8c4a-426c-abb8-6ebfbcd1d597", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "b95de947-a8d2-4209-be80-ede9d76db53b", "attributes": {}}, {"id": "b8297d48-e697-4fab-8e4c-1eb747f16e56", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "b95de947-a8d2-4209-be80-ede9d76db53b", "attributes": {}}, {"id": "0519fe6b-5e81-407a-bc3e-f63f1eeb729d", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "b95de947-a8d2-4209-be80-ede9d76db53b", "attributes": {}}, {"id": "cc391242-94ae-4a7a-a4a9-ef57eb60d3ab", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "b95de947-a8d2-4209-be80-ede9d76db53b", "attributes": {}}, {"id": "3c69ad1f-8a41-4522-99e0-75be5ae60423", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "b95de947-a8d2-4209-be80-ede9d76db53b", "attributes": {}}]}}, "groups": [{"id": "eb4a84be-10b1-4303-a762-c7f1dbc740f3", "name": "migration-test-group", "path": "/migration-test-group", "attributes": {}, "realmRoles": [], "clientRoles": {}, "subGroups": []}], "defaultRoles": ["offline_access", "uma_authorization", "migration-test-realm-role"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "users": [{"id": "cf47dd8b-3719-449f-9892-bac9f8ae7ef7", "createdTimestamp": *************, "username": "migration-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["offline_access", "uma_authorization"], "clientRoles": {"account": ["manage-account", "view-profile"]}, "notBefore": 0, "groups": []}, {"id": "47611b1e-6e38-415f-99b1-8babab008505", "createdTimestamp": *************, "username": "offline-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"type": "password", "hashedSaltedValue": "kNwotFPNeuwelpT1HWt+E4ONXFK6wjd+h0zbzNBRGwOqacAjeY7vYN9QZQ46DlEKSdn04cEU/3RvX8WPcRegxg==", "salt": "rEIJDbs+BQqpx31v8mONWA==", "hashIterations": 27500, "counter": 0, "algorithm": "pbkdf2-sha256", "digits": 0, "period": 0, "createdDate": *************, "config": {}}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["offline_access", "uma_authorization"], "clientRoles": {"account": ["manage-account", "view-profile"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "b95de947-a8d2-4209-be80-ede9d76db53b", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Migration/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "0c37001b-9572-40bf-af8b-b3972cb84c2f", "defaultRoles": ["manage-account", "view-profile"], "redirectUris": ["/realms/Migration/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "65306a65-9202-4db0-8f1d-1a9f3f710ec8", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Migration/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "a306d7f3-9e33-46c7-825f-2ce5182e6547", "redirectUris": ["/realms/Migration/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "d521a163-e895-4681-be0b-23b1ebbed150", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "b9f0d711-c712-435a-b1bf-7b0d7df17e52", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "5feb62bf-e534-4e3a-90e5-65173e69bb2b", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "6b7dde10-8057-4370-86a6-43cff66224db", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "889468ce-7804-42ed-9bf0-a57124552b1e", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "21ce6e52-25ef-4514-851e-c67ecc2f820a", "clientId": "migration-test-client", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "ff12b4c2-abba-4b88-a76b-ffbdb4d725fd", "defaultRoles": ["migration-test-client-role"], "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"clientId": "migration-saml-client", "enabled": true, "fullScopeAllowed": true, "protocol": "saml", "baseUrl": "http://localhost:8080/sales-post", "redirectUris": ["http://localhost:8080/sales-post/*"], "attributes": {"saml_assertion_consumer_url_post": "http://localhost:8080/sales-post/saml", "saml_single_logout_service_url_post": "http://localhost:8080/sales-post/saml", "saml.authnstatement": "true", "extremely_long_attribute": "     00000     00010     00020     00030     00040     00050     00060     00070     00080     00090     00100     00110     00120     00130     00140     00150     00160     00170     00180     00190     00200     00210     00220     00230     00240     00250     00260     00270     00280     00290     00300     00310     00320     00330     00340     00350     00360     00370     00380     00390     00400     00410     00420     00430     00440     00450     00460     00470     00480     00490     00500     00510     00520     00530     00540     00550     00560     00570     00580     00590     00600     00610     00620     00630     00640     00650     00660     00670     00680     00690     00700     00710     00720     00730     00740     00750     00760     00770     00780     00790     00800     00810     00820     00830     00840     00850     00860     00870     00880     00890     00900     00910     00920     00930     00940     00950     00960     00970     00980     00990     01000     01010     01020     01030     01040     01050     01060     01070     01080     01090     01100     01110     01120     01130     01140     01150     01160     01170     01180     01190     01200     01210     01220     01230     01240     01250     01260     01270     01280     01290     01300     01310     01320     01330     01340     01350     01360     01370     01380     01390     01400     01410     01420     01430     01440     01450     01460     01470     01480     01490     01500     01510     01520     01530     01540     01550     01560     01570     01580     01590     01600     01610     01620     01630     01640     01650     01660     01670     01680     01690     01700     01710     01720     01730     01740     01750     01760     01770     01780     01790     01800     01810     01820     01830     01840     01850     01860     01870     01880     01890     01900     01910     01920     01930     01940     01950     01960     01970     01980     01990     02000     02010     02020     02030     02040     02050     02060     02070     02080     02090     02100     02110     02120     02130     02140     02150     02160     02170     02180     02190     02200     02210     02220     02230     02240     02250     02260     02270     02280     02290     02300     02310     02320     02330     02340     02350     02360     02370     02380     02390     02400     02410     02420     02430     02440     02450     02460     02470     02480     02490     02500     02510     02520     02530     02540     02550     02560     02570     02580     02590     02600     02610     02620     02630     02640     02650     02660     02670     02680     02690     02700     02710     02720     02730     02740     02750     02760     02770     02780     02790     02800     02810     02820     02830     02840     02850     02860     02870     02880     02890     02900     02910     02920     02930     02940     02950     02960     02970     02980     02990     03000     03010     03020     03030     03040     03050     03060     03070     03080     03090     03100     03110     03120     03130     03140     03150     03160     03170     03180     03190     03200     03210     03220     03230     03240     03250     03260     03270     03280     03290     03300     03310     03320     03330     03340     03350     03360     03370     03380     03390     03400     03410     03420     03430     03440     03450     03460     03470     03480     03490     03500     03510     03520     03530     03540     03550     03560     03570     03580     03590     03600     03610     03620     03630     03640     03650     03660     03670     03680     03690     03700     03710     03720     03730     03740     03750     03760     03770     03780     03790     03800     03810     03820     03830     03840     03850     03860     03870     03880     03890     03900     03910     03920     03930     03940     03950     03960     03970     03980", "saml_idp_initiated_sso_url_name": "sales-post"}}, {"id": "cb1a7042-228c-4f8e-b0c9-654f1855d1b8", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "ab1c03c9-0ec1-4700-ac38-c4b394d03e26", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "16e4dd03-df00-45d9-bd84-de58645d8ed0", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/Migration/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "3befa63d-0ae5-4ca9-ae88-8a5ca6ce61de", "redirectUris": ["/admin/Migration/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "13d0c007-6cbc-4998-adf9-df0f30bdfaf6", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "b07f8b45-a22b-46b6-a597-366cc<PERSON>bab36", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "795ba4c1-a95d-4d6a-82eb-502bf24dbfcb", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "482ad270-c246-4303-8c77-bda122285c63", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "790cf33b-604c-4565-aea9-098d092dda7e", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "ff0e972a-bd00-4562-b53a-1cff07c6219f", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "baec39d8-301f-4a16-9ecb-06a8879165c2", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "19d11e98-3e71-4ffc-8959-c8b93dda5603", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "746db6e3-e484-4e5f-aca3-c70b20c43548", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}, {"id": "7213462b-458c-4227-b3ed-7c110bea8a35", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "54601798-2b88-4f07-82a6-f1fdecf4f73a", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "f9579126-53fd-4328-b3ee-7a7b53a93ebb", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}, {"id": "4b4c5d2d-ae5d-4109-b791-150f071c60bf", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}]}, {"id": "b776015e-f281-438c-9b97-8a72c2b18dac", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "ab1f2ddf-ab82-4b07-9965-d5a7c962445a", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "a8f7af7e-44c3-48ef-bcd5-c9e2b187cd21", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "a43913ec-cdff-4c32-be27-d0f5c4e501e4", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "bbb9dfe2-1b0a-4c68-bf70-b210d955b3d3", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "fa713622-7e31-4f08-8d23-3825c44e7fc9", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "a9e21703-3434-4659-b049-e17f44bbc4b2", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "e52560f1-94de-4861-a6e1-77af40f90996", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "006bef70-48a8-47a5-9d96-f6b11b003233", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "de2ad222-47cd-4d78-ab92-a65b14410238", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "922f350d-b1bb-458b-bc9f-d357cb02384a", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "e62ab38e-0995-4613-b23d-f955564402a2", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "7280e6f6-e957-4b90-b85f-5093be386888", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "5f3e019a-5b6e-4210-b706-40002952c329", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "2708cebe-84f6-4caf-8e98-022fed65c41b", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}]}, {"id": "f4b14275-36f4-451c-abb2-07188de06ba1", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "737bd972-b68a-485f-bd11-3079994b7e34", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "8185d4aa-79c4-4623-ac53-d6db0c164df6", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "b2405613-2c4c-4842-b50d-903d951afff8", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "69fbdb28-2899-44bf-b117-76348a404f9a", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "25533971-b3a9-414b-afb5-9508f75fe77a", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}]}, {"id": "04f57ca0-e45b-42f6-b135-65e59fe27421", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "2491ee57-90d9-4d09-8734-49a16e4602e5", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "817466e8-feff-4fcd-b6b3-55367a5a0b17", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "8a2e8e08-8cdf-4ea3-93e1-6ad21c0722aa", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-property-mapper", "saml-user-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-full-name-mapper", "oidc-usermodel-attribute-mapper", "saml-role-list-mapper", "saml-user-property-mapper", "oidc-address-mapper"]}}, {"id": "a17df264-87e9-4877-88e7-29e1f0d9cc68", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "25a8f3f1-28ac-4a77-8547-0255bc164bbe", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "d3ed97ea-2ff6-484b-abf4-4c8f0f156a02", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "61d53255-80c4-4da8-9ac1-96b94bef326d", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "42ebe47e-9f72-4717-b25d-4bb52e518a3c", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "c32e3e67-4ecd-4ff9-a024-0df49be606bb", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-property-mapper", "saml-user-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-property-mapper", "oidc-full-name-mapper", "oidc-usermodel-attribute-mapper", "oidc-address-mapper", "saml-role-list-mapper"]}}], "org.keycloak.keys.KeyProvider": [{"id": "d094c422-0bec-48c9-8515-763206f1ff7c", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["37f68eb1-db39-4606-838d-469839e6252f"], "secret": ["a9C0LEzxzfGWXyr9k0XFsWFH-Ro8npY_MDKNtKoSow7irx4p3tUT4IaaWvO-WBVFA1NPkEvd3kORX454TUeS3w"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "9c7dd285-e30c-424d-ba1e-b77ecd2631d0", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpAIBAAKCAQEAhx2Smeu8jeVYAXES2vhecOIKQ9529ZnSBXbBNjKunD6ptiF2eK1Qqj/e8LHP3vwAF+gm48IB+qXjaXVwM3gmrqUeJqAzKihgUdshcOuWR+6P0DS5/Ni89bNyBbdQpuIEcgwOz2RI/m71+p9Lf7OJe/bf2RQUdjL7EF3B5aLtB2Ik9xuoOtKmMdliZSxoQ/mpmIJxa+tJpgo2F6uyLXp9d8NKACJJMq/QNDb5+CMoLPp/hFL1DfnY51NDC1azI+eh78nycgMtCKUZR4pgdfXUzymM8K8oNPWwImOqhToTa2bqQFAeiCzBD71dMmxv4S1SqcattUGpRTUWZUgsFlsAcwIDAQABAoIBAASCzD4vTKBAI+wIZmTBHgYOxeD2MGqKtCRSjqjMkF/PB8N44oes4aCNeVf318TODQK/UW0RMRe5Khp/PTu6qy/xe0w21P53Up/o/PjUJLRKFqsQ15TFd6mMUCY4tIydxyA3ntb1tioocjXn05Fz4QcuetK4hOZdVVx6hNqx0dAJPiHl9yENQQ8xtqrfG1u+JHf8e+sPNRsOKlsJ2z9qieejjw6Zx8Ohoo6wAsLRpOOX7h2wycbkvCoXGCq3bizLf3tDpzIQYG04PqwgpNUjjtTTbv5L6YWkjipgUATK9wk9VH6zhZdbCLlM76Ub/dNsYqHPP5SEbzK3jQ4HSMHi0+ECgYEA4mUTNFbxZYmGCVidf8fA3bjAlfI2tUte8t50GTslN8p0lS5HxwGMwARUnjoVyDEZ8mxnnEbXNKkDckSmjA79PDGEmFRvZQOYmW1SdvRkVlovDXTGbvnuilOxOcFfHf1/CcpTE/r4A2NevUls5MD1Yr6Kkvl2klbbPdUCNd2TR+0CgYEAmMjHR4thtSOOGVTkSubAfmdeLtWJgOawPt7d8Mh4r6jsk856wiTOGQqcdK8HUzPxMwvhHOkBhfXbfFKrXke7kHoLCANrPcVSPsF+eAouR6e57yKl0dpgF2yX862ChnzW01pfwax9RdMFH+ejAKoFnrJiyVPk3P9Yd/ghmWtfnd8CgYEAmpjs+VPZuj2kIyO5QbXVdVMhaAOj6j9S0kSoQUaLTsLYw7p712Cujb0nT3lYwIEPN+VhB220rcgwi0pn9MKTmRQzeJZID1HaVgM6SfLvxawF74lrrALOKvWl00vCOUePBc981esOi7nCyz2lt7wwlUKKfH0PxDS9uEaKIOBzH60CgYAz5g/7d5dLJrbAWBvsbZH53jcaBaNdy/8WfDI79n04flNOqhb9F++Chh47bLE6TCykwGZJocTIsolKI/LeZdOK8koJANBO/VZAxw2kzSCHaqw4VTaqoFl/232KYugxsvpudQaODX/IVjQpcE/oi9i43bO82+ikQYDVz83qBZVHVQKBgQCJ/nmFP7DDrF3gqbjjar+DzHbFz1ANHhCXZgeyQ3HIZmONQTpRvUsB20PJfvkDXSWgBTKmj2xHf9BvS4IKe1z4802v9+Y0Wbke9X2iyYXb67Qh2LIeNFvekfMt3qYFxhKebfuPib7WePd/IhI+8WDzz8OwzhApqNoA8Xe7dybTHA=="], "certificate": ["MIICoTCCAYkCBgFyJF1ifDANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAlNaWdyYXRpb24wHhcNMjAwNTE3MjAzODE5WhcNMzAwNTE3MjAzOTU5WjAUMRIwEAYDVQQDDAlNaWdyYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCHHZKZ67yN5VgBcRLa+F5w4gpD3nb1mdIFdsE2Mq6cPqm2IXZ4rVCqP97wsc/e/AAX6CbjwgH6peNpdXAzeCaupR4moDMqKGBR2yFw65ZH7o/QNLn82Lz1s3IFt1Cm4gRyDA7PZEj+bvX6n0t/s4l79t/ZFBR2MvsQXcHlou0HYiT3G6g60qYx2WJlLGhD+amYgnFr60mmCjYXq7Iten13w0oAIkkyr9A0Nvn4Iygs+n+EUvUN+djnU0MLVrMj56HvyfJyAy0IpRlHimB19dTPKYzwryg09bAiY6qFOhNrZupAUB6ILMEPvV0ybG/hLVKpxq21QalFNRZlSCwWWwBzAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAENGYhwIK2b0FTJ6+sWsOF41Kq5tN5Mlr3gjMTV3oIUzaqWRSjVAY6SYJI0XWZEP0ra020zJb4SK+zjnssxSqsTY+mkcrGecJQz64xR7PAKL/VYdvA4kufF7mWU91psapv6Kj9BCCGf5NldguhYkfJk4HY1zh4Mw0RgYlxDzFRzNb7hmEFGh49FQZSw/1gwuAPGnV1mul056RVg+6P/y5GMtXnsDE2tlVNFhLJqE9RcZd1QDM8ajApQVG+HFAKHSvdRh704HyPXzjSnt+uKlZgLLu8LazoPdQ3xdiYGHLpJ0yxJoPavqvZCXQ8HEK+1PAWIMmTXZ5lcDPa8cCDKWYJQ="], "priority": ["100"]}}, {"id": "80bb8edd-6aa7-4fde-a8e9-6c8379d11858", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["0fccf556-2643-446f-b2e8-d35617f73f63"], "secret": ["yNDmKSelv8vvm51eX6A8Rg"], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "28f9095c-d86c-4f78-9a8c-a9831a1746a8", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "2cfaaa66-590c-4f6a-b2c1-d01f2905ee83", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth-otp", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "fa8795f7-a3d0-4944-b3e1-78df8dfd1b25", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "bd1f9011-8a2d-4150-9652-c139b0993c94", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "640d8aab-0aed-4824-a0ec-10ec12b005ee", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "ebfb3270-f9fe-4a26-90c8-5f86b207f9e8", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "Account verification options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "61922ab6-74fc-48c2-a54c-2b986b7d51a6", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "a3688f83-7ca5-4f9f-bca8-7b7bd49b2334", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "7c59ad4b-b530-4524-aaaf-fa5899315354", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 20, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "09fffd1e-f852-46fc-892e-5539e8b1a8ed", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "6e94ff6a-8c81-442b-ac5b-7d95c3ec735c", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-x509", "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "da7662ca-d79c-4916-86fa-642ae1eae27b", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 30, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "06e26e8f-ac44-4e23-b06d-88ca29c3ae66", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "b31ff27c-7d58-4c85-8bdc-19168bf3a675", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "User creation or linking", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "45626af6-3105-4ddc-b158-fae740322358", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 20, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "689be90f-a930-4544-8a94-aa9630f2828c", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "Authentication Options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "67d35da7-cfb9-4af4-ae07-1107c99c8ecd", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "ef49e7ce-9ee3-4ff4-87ac-21383e283b7d", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "03a530c8-a005-4dfa-b715-ccc174c5f75f", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 40, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "801929db-254f-41cd-b1f4-c34f47f16c06", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "e3a794a8-888b-4087-a205-bda0a21e74f1", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "1ffd74af-3a9f-45b5-bd0f-bb2c002790bd", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"custom_attribute": "custom_value"}, "keycloakVersion": "9.0.3", "userManagedAccessAllowed": false}, {"id": "Migration2", "realm": "Migration2", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "62343fc1-2c8b-4fdb-8580-ee1af4b7ac53", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "Migration2", "attributes": {}}, {"id": "3518b1d8-363f-4082-82ba-ded4ab2fbd10", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "Migration2", "attributes": {}}, {"id": "a495da40-f44c-4e28-8f82-75bb5677e597", "name": "default-roles-migration2", "description": "${role_default-roles}", "scopeParamRequired": true, "composite": false}], "client": {"realm-management": [{"id": "6781f48b-954d-4967-ba70-498f76291971", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "f0bf7b3c-5f59-4db8-8c9d-49a6efd30dbb", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "ed5958a8-29f0-49ed-a302-bcf8fc6475b9", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "944d7028-e09f-428b-bda0-f4e3a7f66860", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "64adf606-6c6d-478e-8de1-b84a016d6835", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "c605d760-aa20-44dc-ab66-bffee3c2894b", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "1a95ebf4-a503-427c-ad8b-430e4ab7cc64", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "ef6139c4-214a-49a6-9fb9-da5233f5e0ce", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "4d746232-e6b0-4700-ae76-a8b0813e2aae", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["view-events", "query-groups", "query-clients", "impersonation", "view-users", "manage-users", "query-users", "manage-clients", "query-realms", "view-realm", "create-client", "view-identity-providers", "view-authorization", "manage-events", "manage-realm", "manage-identity-providers", "view-clients", "manage-authorization"]}}, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "095fcc64-0a7f-492b-a546-40aaa35d744d", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "62ccda93-5e13-4c9a-8799-c622372c1899", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "12c923e0-0f84-4306-bd28-8052e395459d", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "3db200f0-213f-43aa-ae18-a9d2b189255f", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "3dc5824e-7a01-4043-a326-fe08238d0f04", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "babe19de-8b49-4f71-964f-406da8890faf", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "347ac7dd-fd44-4bad-bad4-ffa3f31c9dbf", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "4085d6ed-5cd1-4c0c-9f02-ac2dc83b51e1", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "6846d78a-81aa-47a1-aac5-3ad9f0d2c959", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}, {"id": "3bcf300a-9f1c-4ee0-b0dc-c9ea1c53d527", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "41bd33f5-3065-4e89-8bc9-06c25659713f", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "ef979566-4142-4a8c-bf38-8bbaf76a3efb", "attributes": {}}], "account": [{"id": "4e38e6b2-6e6f-40f7-b789-930eb77168bf", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "********-ffbc-42ec-a9b7-7ad1d14ffe95", "attributes": {}}, {"id": "cc76f77a-d9dd-47cf-ae64-3cf072d2c270", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "********-ffbc-42ec-a9b7-7ad1d14ffe95", "attributes": {}}, {"id": "138f80fa-deac-4099-908c-70de1f71d168", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "********-ffbc-42ec-a9b7-7ad1d14ffe95", "attributes": {}}, {"id": "cc7cbcc8-80b1-40e5-a8a4-5d3624172cee", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "********-ffbc-42ec-a9b7-7ad1d14ffe95", "attributes": {}}, {"id": "e2efc710-47df-4464-8f10-34ff8ab121ba", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "********-ffbc-42ec-a9b7-7ad1d14ffe95", "attributes": {}}, {"id": "a1c03d3d-bf1a-4e49-b074-2b25b108a14c", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "********-ffbc-42ec-a9b7-7ad1d14ffe95", "attributes": {}}]}}, "groups": [], "defaultRoles": ["uma_authorization", "offline_access"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "********-ffbc-42ec-a9b7-7ad1d14ffe95", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Migration2/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "88228a08-0ee5-48ed-a086-79489c0683e0", "defaultRoles": ["manage-account", "view-profile"], "redirectUris": ["/realms/Migration2/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "6d799159-262c-428b-84b9-116a57678840", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Migration2/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "11e1d66f-e746-45f8-b8d8-96c187d1783d", "redirectUris": ["/realms/Migration2/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "5708d89e-db24-45b7-890a-9530ed646818", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "90d523b1-8976-458e-9416-7b47bafbbf79", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "f37aebd1-c77f-498e-9674-60920d483e8c", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "ef979566-4142-4a8c-bf38-8bbaf76a3efb", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "2e0092eb-f6c5-418f-acf3-75b29dcad699", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "00f14cf2-1fa5-4fb0-a95f-b1f6db013ff0", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "f34e0bb9-b2ab-4889-9864-c51446fc4651", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "32548e6e-5141-43a9-950f-00546a42ac80", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/Migration2/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "4f5eb29b-ec9d-4239-99be-b9e1b6cb465b", "redirectUris": ["/admin/Migration2/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "099663c7-58d8-45c3-abe6-48ac111ea690", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "fc41480b-e1d8-41b5-9278-f7efb789b60a", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "24d50733-121c-43e2-9e80-3e60e19a9ac1", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "36333160-f69e-4c42-b206-6c008a2fa4ce", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "afd94378-36ad-496f-b70a-d213114daecc", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "fa045f8f-e35c-4fbe-a710-1492c14c0fff", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "e69cd0c4-02ca-430f-bc90-c6e168a0256b", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "8dcb370b-d649-4766-b787-45655f80c875", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "0f22d59e-9af9-42a9-99c0-b9e939e49c05", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "b285d924-bd83-464e-996a-4698907494e1", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "582cc8b9-039f-4a89-917f-f01d3305af5e", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "5c3aa86e-d890-4b80-857d-ebaa127612c8", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "76122637-f0af-4e25-b4fb-ac0bb41033d5", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "aec11a2b-92a9-4efc-8083-59ddec8314ca", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "f0ca2215-aa92-4538-8c7f-078ccedfcd55", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "f91e8884-d702-4fee-a3f5-be7e1d061e3e", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "77b7fe66-00c6-4fcc-9e7d-20895cb18e65", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "dcd668f8-1d2e-40ad-865a-16d8bbf96c45", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "842225fa-18c6-41d8-8650-fcb8ba705a66", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "91fdca14-0cdd-4b84-a5b6-cdd0ce903181", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "6c35aba5-bcaf-4809-997d-77f9aa1e0c56", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "306ca049-90c4-4a19-a171-504d100fa81e", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "f76a86fb-6fdf-4bca-a4bb-ee3a0e7bfbdc", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "4a7d1478-8d77-41f6-97a7-057a12968857", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "e0e1ba7e-1b69-4ad2-a4f5-babcff68d5c7", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "36128a04-521a-401c-8b67-748b2d8db4d8", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "de2a1958-14e6-463f-acb9-c976d23bf159", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "ef07b40b-6b61-4c1e-9ea6-82b5fa939d0a", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}]}, {"id": "37f32cd7-207d-4b5c-add0-5d8d23cc5be9", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "5ac9435e-1ffc-49dc-a4d8-9de71dc56e5f", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "e5272b01-7297-4924-919f-2fc6bf4b3f40", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "7a160788-e69a-472e-8dc4-36f22a2965b9", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "298fb83b-b07b-4f50-84e9-732ea1bfa204", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "ee3b87bd-1c06-4b38-82db-5c42a1f6059d", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "8bad9dee-6d1c-40af-b0fb-734fb1c058e8", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "dadb1f97-f14f-4513-8e19-76acbf694388", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "d3b6fc32-5d02-4b5c-b782-eab638aa4efb", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "235ecbff-a090-4fb0-bc84-76734e830632", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "6a3953ce-c8c2-4950-b04e-1e3b11972f9d", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "ea557103-0351-483d-a92f-0f1c1a5ed9c1", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "969ec347-33a9-4761-a15d-6ee12358b8da", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "52c1f35b-6a89-4c7f-9556-2665a66e00fa", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-property-mapper", "saml-user-attribute-mapper", "oidc-usermodel-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-role-list-mapper", "oidc-full-name-mapper", "oidc-address-mapper", "oidc-usermodel-property-mapper"]}}, {"id": "8a91d98d-facd-4263-a8c3-b8e5a461eb15", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "saml-user-attribute-mapper", "saml-role-list-mapper", "saml-user-property-mapper", "oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper"]}}, {"id": "9f33938d-3a33-482b-824a-22ed91732fcc", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}], "org.keycloak.keys.KeyProvider": [{"id": "3d7607fc-f70c-439f-b0da-104ce051658f", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["6452aab0-85d8-44a3-9c0a-edbcb4d4d57c"], "secret": ["EXBMAQNwJ2jOo1Hav_txv4pSefyyDh33kA-C5fu4az37cAwJH9g7IJKztxdszeMj82Kect_u8q-qJZLDZmPasA"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "f19e7967-353a-4e99-b7f6-3da7f1179cbe", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["6763b11e-d854-4dff-bb0a-9f272d955219"], "secret": ["d6jB94kFNpvOnhclUsnORg"], "priority": ["100"]}}, {"id": "edcc67b2-5610-4f4d-b9bd-3330f8f13665", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpQIBAAKCAQEA5V+OtcIcEwPmzPTeIojgYk1RYWA1uSFNCoHcVS0xXa1cvEzuvVxlUz1CICD0P1GrChGPsoGkIM3KMfQDZfxwSBJU8y2Ym8j6YD9tYnWyex5PXBT5RyDPSZoQ8S98Hxlyvm39xKI7kIo4AjwMTbAjmys0n4mZDqIsdYVK9UxSZqUBZsd8dWdWJsYoq8KowYEnpbuIABa/VJhUc0iKj83je3grdUjzAdoz9/Zq0Kutc7uJ4uZ51lTt0KnmNPYQrMKiXaix2Gw2E8CjxLvhGgb/GPxlU05RT8yUkoRNBiY0Y1k++z6DoXqmmStQBY+wqE8UkLak0Syq/GQOZRsxyjaUiQIDAQABAoIBAQCF8BfIYPG91NBoLDCuX9kO7fEweFrGoQB4uz3q6vnhpsL5Hb9nCdYNFj9PvY6CjvDeb0TGytcJZMHGC6QUYKU8IKBEZAKyLViu7Tq1fN7c7+UNcx3g//KrGvD7tWu4SUbslhPSR52amGt+OoxArTMNf9w5sMKUffSSJ2SO+qM/Vc4Llszhows9onT/ItMP4MFcnBXBB8vGcDH4z5aelp9NARXO4OhZ4RdW/a0NfwfBiiW3txXN9BRxJRZ/FanndjZm3IFl0oDFSp4sUydOVHcqaIb/ECFbYIEd3pCNzmcGIms504n03wcoT2JrU+yAKQF9natCV3f0e9egyQqfBrE1AoGBAPNuN9VhAAdr0+fxwdxZAUjNHUX1Th0VkVco1Dx8UWMRg8HXdSntA+d1byGslcqUV4+IzKVX9NTt4JsTqY0QodJ8yEv5BBLLGPwaMv8kHccqF1fVFcag7qzQbDwM8QaD8u9K1VKNK5oKC9nYit7jcxQN3sXHIELrxHLhrsRURVWbAoGBAPE3hgzS6flf4mNJhUThlJfJuXLEgBL+9lfd0DPJBiblzSPMb1I7DtEfKacUjUxvhCVdxJ9g/T53Rqgo3RZepPGWFP7ixbMhFX/6UWp4QCG2KBQjcZSGz6PYEH2wRA7Ic6XQisY+1zTqHBQcIdwQRD8iST7aS0W2+OlQifBafJKrAoGAEq/oW1oJ7YfqiYi2kBrJDXSphaBSMcthD+bTfB0zBj7m9W970A8g2JjcQiKEkTuYCS3AQAUjh2EHeAAnaOvY8Fah7asdE/BBZ8D/HzT/kDV7+0MhW9Eo4V1Sob2AlzZz5MjC3YZ2zWluBXgpGPku8WArNNWXdWux9fu7NdkeRXcCgYEAgqxQizvrEnQWITFf59vIcp4OP5UhyKikbR81vggFtCbDhcQm/kYOFc2q899XWHQFA3UToj+ZkTBub4SLmKW290UVpKrW7N5cVlMVrfbszxZuCdLVod+SNrFC5cQKanO+6rHZq+G5FepJQb+nvB53yjfZtBnvsxmXKFXAFBwA5jcCgYEA5wyYv+gG0jn3xGpFN+qxs9wZN6dgn46YeWt+NzqH11lG3qFZX8Q2AhbUy1fxNy4Y52N3L3y/5R2D7tfEGd5o4Kc49aoV7yGK64KECQ+QNZvuJqxeFMWeGPGFtMwglui9AW+8aRE929x8ypvK6ZhB0474f9OLhLxiZn2luyPy7m8="], "certificate": ["MIICozCCAYsCBgFyJHKT4DANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApNaWdyYXRpb24yMB4XDTIwMDUxNzIxMDEyOFoXDTMwMDUxNzIxMDMwOFowFTETMBEGA1UEAwwKTWlncmF0aW9uMjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAOVfjrXCHBMD5sz03iKI4GJNUWFgNbkhTQqB3FUtMV2tXLxM7r1cZVM9QiAg9D9RqwoRj7KBpCDNyjH0A2X8cEgSVPMtmJvI+mA/bWJ1snseT1wU+Ucgz0maEPEvfB8Zcr5t/cSiO5CKOAI8DE2wI5srNJ+JmQ6iLHWFSvVMUmalAWbHfHVnVibGKKvCqMGBJ6W7iAAWv1SYVHNIio/N43t4K3VI8wHaM/f2atCrrXO7ieLmedZU7dCp5jT2EKzCol2osdhsNhPAo8S74RoG/xj8ZVNOUU/MlJKETQYmNGNZPvs+g6F6ppkrUAWPsKhPFJC2pNEsqvxkDmUbMco2lIkCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAGZve7ICpGYHlJnb6m6xRVqpWhI8fozTomLgr99TZ1FAWE9SiZCnpnz3J5XQLuEbT0/ZT/Et9wpDEHTTfYRTeB4CPRgb5ss9mYG96QF3FoxcwOO3svtzGwZp7tR/0wiN8mFnYnGZowAUY0+otAMTQL0ubIX0Z894lzBAjh2mHQ9lupLkOREuQCL0i8bPS/t4J8gfEwPYsNTu4GjqdjBTK8d2T1MtEannZxKWI+GKJ9QWhgLgpHR0L9E/egQyKz7bJiqIGPf0zz/Tge/H2iSvVfs56+hqzJMcnK9iPP/kXyj9+i54W3xDMXH9gTJu6VlzPGtZ635LGk2fbMwM2019QnA=="], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "2ea4f9ce-5252-4330-abed-d4d0d915e12b", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "30919e08-00ea-4ec4-9c7f-b9aa2e88a37a", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth-otp", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "de9807b0-0ef7-4f7d-84b1-a76a68ce8d4c", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "66bc5e6d-e80c-4598-8a10-4009d43a2af6", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "d1d16fc6-b6bf-47b0-a712-bacc9f659667", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "ce84b7ea-ed2a-4250-b6fb-11808c00376e", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "Account verification options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "239ed1d8-dcad-4e3d-83fa-1954e8d96786", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "47692d70-4fea-4035-8729-b1d0772f1df9", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "5d361218-6e25-469f-afa4-2bf14c3a782f", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 20, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "2aec1a63-ad9d-4b74-a2c8-81eab0b0c0e2", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "0e439dd7-8d62-40c0-832e-aff687c323b3", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-x509", "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "b86ba04f-d461-48f4-9cd5-5f3c0c3cb53c", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 30, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "88ab491d-e62f-4c45-9589-8e7c071eb86a", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "86a95d2d-4b90-4a03-9165-10ecd34db6e5", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "User creation or linking", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "81abfc8b-1f97-4191-94fd-87fa885d0872", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 20, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "68100bff-2708-4816-8265-b3dc58c39d74", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "Authentication Options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "9c284372-e60a-4159-ad60-515eec505c1d", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "b1ecb597-4546-450d-8c1d-02dbdad0c07e", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "205bd43c-1eb7-4fb1-bd40-15f9c3f11d1c", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 40, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "b5587a06-1a86-4007-8fef-a641d74449a2", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "dcec71b4-4f27-43a4-b4f2-22310165bd25", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "4afa2321-3cf2-4281-9c39-5532fb2f8cb8", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {}, "keycloakVersion": "9.0.3", "userManagedAccessAllowed": false}, {"id": "master", "realm": "master", "displayName": "Keycloak", "displayNameHtml": "<div class=\"kc-logo-text\"><span>Keycloak</span></div>", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 60, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "47c16971-4585-4a59-983b-96eb0934c4ad", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "5ccfbade-6801-47ca-a76b-1070bc335213", "name": "admin", "description": "${role_admin}", "composite": true, "composites": {"realm": ["create-realm"], "client": {"Migration-realm": ["manage-authorization", "manage-realm", "manage-clients", "view-realm", "manage-events", "impersonation", "query-groups", "view-clients", "view-identity-providers", "query-clients", "manage-identity-providers", "manage-users", "view-events", "query-users", "view-users", "create-client", "view-authorization", "query-realms"], "master-realm": ["query-groups", "view-realm", "manage-identity-providers", "manage-authorization", "view-authorization", "view-identity-providers", "query-clients", "query-realms", "manage-clients", "view-users", "impersonation", "query-users", "manage-users", "manage-realm", "manage-events", "view-events", "create-client", "view-clients"], "Migration2-realm": ["manage-realm", "view-realm", "manage-authorization", "view-authorization", "impersonation", "manage-users", "manage-events", "query-groups", "view-clients", "manage-identity-providers", "manage-clients", "view-identity-providers", "view-users", "query-clients", "query-users", "create-client", "query-realms", "view-events"]}}, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "b27ac197-f210-49c8-a557-073f92558a20", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "7c6b2684-caec-425c-846c-d3ce1d4a3ec2", "name": "create-realm", "description": "${role_create-realm}", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "431d3968-7366-449c-b183-65ec7e8ce50d", "name": "master-test-realm-role", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}], "client": {"security-admin-console": [], "master-test-client": [{"id": "925e40fa-6384-49ac-87d2-e6b0a756622b", "name": "master-test-client-role", "composite": false, "clientRole": true, "containerId": "d9cf7447-47b3-49a7-a44f-23d7bab69df2", "attributes": {}}], "admin-cli": [], "Migration-realm": [{"id": "e3eecb89-0300-450e-9bcd-089f16478d87", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "0ade685d-6717-49bd-984f-d343cbbcc260", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "178a623b-bd7c-4abf-946d-1a7a70dd1771", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "b737e678-ca78-4b37-bf15-d3c9f077fc49", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "87dd5365-69c0-44eb-8cb2-75f7f35ce61b", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"Migration-realm": ["query-clients"]}}, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "c14388c4-f187-4fe2-876e-da03883cfe9e", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "e7bbaf64-af0c-4898-bd16-d0d6ca7e3b00", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "b397ba5a-65f8-4e9c-a257-f0b32416ea13", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "89b3fd09-fe21-4af4-814d-1bdafa323ca9", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "3605cdc5-69c9-4ab4-83b9-d11567340e30", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "045f646b-4300-4088-baaa-62c803962f99", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "cc8460c0-f3e8-4885-8374-12fb65cab7f5", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "2fc599e8-0262-44bb-aead-91f879df042f", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "0a0c5af6-7106-47b4-adc8-d9f81eeb522f", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "b4c200b6-80e2-4afd-9b88-b2bd53fe0704", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"Migration-realm": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "ae8910b8-83e0-45e3-9fdc-049ef20ce089", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "e5f17f19-91f8-42a5-8450-d16b6767fc2e", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}, {"id": "2db8d9bf-abf3-47ca-b958-b569390e40de", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "attributes": {}}], "account-console": [], "broker": [{"id": "d6570850-8992-48e1-ab33-0f9120c87731", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "f58dd086-ee9a-4cae-ba0b-2614fa65f812", "attributes": {}}], "master-realm": [{"id": "55ed5848-cf08-4fa3-b27d-d98fe5ee211a", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "efd4826e-3ee4-4db1-bbfc-716f1a5c5e35", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "e651e62d-25a8-4d2d-8f40-01484f4ef068", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "77549caa-c098-4752-943e-7d5542c5c4e3", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "c4baecba-4b72-4783-8b64-8751bae054a6", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"master-realm": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "210ec8ab-b6ec-4a60-8473-8c93394aac56", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "9b56fe62-2051-4481-a0ca-2bb5ff88307f", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "7e819317-becd-4f8c-9c27-31abafbb5125", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "7966bc93-eac0-4a27-8f57-4bcdcf14fd11", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "074335da-2568-4bb6-8864-d66eb56bc8d1", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "32d28256-bd52-44ac-bdef-bb35b0ce5202", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "c3c535d8-82b1-4c77-99e9-9c3dbcc9b95b", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "0d6cbd8f-d49b-4c0b-bdba-f9ba75cf7152", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "40178b3e-72e4-43a0-9f22-e09c46da063c", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "e47bdcd6-2e37-49c0-90cd-128151e383d5", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "e291ea6c-00d5-422e-a23f-599ef7f40930", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"master-realm": ["query-clients"]}}, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "1f4ca592-e3dc-43c6-bdc9-47a00b96a3aa", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}, {"id": "170801d3-3627-4604-9da4-f25f1d63b4cc", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "attributes": {}}], "Migration2-realm": [{"id": "ff4b2a06-9f14-47aa-90c6-b7a41fc23df8", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "b04736cf-7846-420a-81c9-ca91007749f6", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "bc8aaaee-2404-4952-89af-d96672b0a1e5", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "52f0c566-46fc-4721-a25b-54f95036db38", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"Migration2-realm": ["query-clients"]}}, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "fabeef31-dc38-4f4b-b932-75b871360f83", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "2034f991-300c-48b6-ab59-9fa32c70df38", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "7cb4b278-2788-4806-bb1e-66cf4b144a0e", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "a1f03b40-b39c-48d0-879a-5a56c78f378d", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "47ea0cd9-891a-47c1-974f-5825405b1035", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "52299541-0fdf-4ee7-a9e8-42d246d62bb1", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "7076dbf2-7a41-4eb6-aba3-4f8099d39644", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "4b94e028-a0cf-4c2a-89f4-54341d0134e5", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "23424a57-565d-4790-a3f0-44d114b1adf4", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "e4d242a7-4d92-47d0-b380-0ac8c60e9bba", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "61b1f81b-5979-4368-8001-dff57b408e77", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"Migration2-realm": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "cd98e3b7-5a81-408b-86e9-58003b54b688", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "63d7c735-e18f-40d0-939c-708240870b3c", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}, {"id": "f7fcb2c4-8dd2-475c-aa0c-1d1f7d86476f", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "d434ef36-3838-4bd1-be11-e50d39f65378", "attributes": {}}], "account": [{"id": "add27b19-dc88-4c2c-ae53-c58ef14357cc", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "b13ebf37-a215-4f3e-bfb3-f9d686e11e32", "attributes": {}}, {"id": "c20c0284-2727-4cca-9dc4-cecf2a302115", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "b13ebf37-a215-4f3e-bfb3-f9d686e11e32", "attributes": {}}, {"id": "56689be9-cd63-4a74-a0a6-d1d595a3bb3c", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "b13ebf37-a215-4f3e-bfb3-f9d686e11e32", "attributes": {}}, {"id": "97622a61-668d-4d6c-9ffc-979fb978a532", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "b13ebf37-a215-4f3e-bfb3-f9d686e11e32", "attributes": {}}, {"id": "330c36d9-aca3-42c4-9f37-86a0351ef542", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "b13ebf37-a215-4f3e-bfb3-f9d686e11e32", "attributes": {}}, {"id": "531dab33-66d7-457e-abdb-f9aa42e246c7", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "b13ebf37-a215-4f3e-bfb3-f9d686e11e32", "attributes": {}}]}}, "groups": [{"id": "e168ee9c-c87c-4437-989b-dd4d66f51b33", "name": "master-test-group", "path": "/master-test-group", "attributes": {}, "realmRoles": [], "clientRoles": {}, "subGroups": []}], "defaultRoles": ["uma_authorization", "offline_access", "master-test-realm-role"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "users": [{"id": "8c75fca9-b08b-4d6f-a4e9-29dff3d27421", "createdTimestamp": 1589747978814, "username": "admin", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"id": "49082b41-019d-4c64-b127-dac90386423f", "type": "password", "createdDate": *************, "secretData": "{\"value\":\"v5ktaLYXAJgePltpedJm6oZq+CzIQM+XzRs+oktwnUHusJiMMDHzkcNFUx2WQaem595/RGCTw/8tO8tcazV4Ww==\",\"salt\":\"rBWsuTNkZ1aXmkrzqVr2fA==\"}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\"}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access", "admin"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}, {"id": "38b42651-3512-4e99-b25d-f644c7c1e57d", "createdTimestamp": *************, "username": "master-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "d7209dc3-e34b-4b4a-b992-87ce79c10a51", "clientId": "Migration-realm", "name": "Migration Realm", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "a6a58db9-fcb8-4510-95b0-7bf85c6d1ca4", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "d434ef36-3838-4bd1-be11-e50d39f65378", "clientId": "Migration2-realm", "name": "Migration2 Realm", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "1aab7f02-16b2-43c6-a11a-5c462023455b", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "b13ebf37-a215-4f3e-bfb3-f9d686e11e32", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/master/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "e689883b-8a5d-4860-a788-2120139d6d85", "defaultRoles": ["view-profile", "manage-account"], "redirectUris": ["/realms/master/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "c9980820-8423-4e85-a61f-f99ea0359d44", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/master/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "57681a4f-592d-4f05-be18-3d9ed86382e1", "redirectUris": ["/realms/master/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "dd4943ae-4957-4920-9c4c-1b88121c39fc", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "2d192085-8534-4ca7-86fc-af801abed934", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "6ebe02af-2744-428b-baed-81e48f68944d", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "f58dd086-ee9a-4cae-ba0b-2614fa65f812", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "08345b76-46bd-4bf8-be6e-669bc7752cb5", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "ef328968-0b9a-4e3e-b34c-89c447a65c68", "clientId": "master-realm", "name": "master Realm", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "341af723-e8a8-411f-a348-5c899759b524", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "d9cf7447-47b3-49a7-a44f-23d7bab69df2", "clientId": "master-test-client", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "a7064a25-7d2d-4a2d-a916-e7b033870803", "defaultRoles": ["master-test-client-role"], "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "fc097d6c-e992-46b5-bbf8-d9d323795e0e", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/master/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "526f2d0d-1064-4b89-bf16-c9db1317d56b", "redirectUris": ["/admin/master/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "fde09397-02f6-4745-b967-2f64222e1dee", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "2ba6e14f-b825-49f2-9afe-eff5ddab11f5", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "9ce05d2f-4167-4aae-843e-8105c45edd15", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "3178cac8-091c-433d-bc51-7a2ec0a483ff", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "5bdd97e2-9169-4473-a890-4223e77d5cfe", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "0ee79da8-fbd3-4b5c-b71a-6f90caff70f0", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "3f981ec6-6e6e-4cf4-ab48-1c3985d0444b", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "e643bff9-b433-4f89-bf6f-1f38f559004a", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "10af79d8-07d0-4835-8d78-f83f6e4ecdab", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}, {"id": "91db5781-f01c-4b4f-8864-5903151b5a50", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "6f68e53e-3c4b-4d57-95ce-b82903207cfd", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "544092c6-df0c-41af-86f0-573b3b3be93c", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "0c73059c-7847-4434-9691-bde7f97e11eb", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "e351cc14-99fb-459c-9500-c36b3c428329", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "a7d01974-d3bd-4d41-86fd-6932f65c39d9", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "c688ada9-aea7-49e4-9ac2-b005db1e6703", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "67373b09-2c2b-4024-8fc4-c0f27c3ace51", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "6b0bdd2c-2d49-42cc-a3fe-379c19c64b98", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "a972ac03-c9ca-44bf-adb7-afd506c0a024", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "596033dd-0366-4f11-9c88-cc96f774cc8c", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "d7fae853-f0d6-463a-a4b5-79306543c1db", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "84fca522-c92c-41f3-b639-0078c6a5802a", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "b7cfb6f4-a1c3-4a62-a55a-bcf95a1ab51e", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "36c6d061-ebd9-4350-aebd-cb59803d28ea", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "c9ed4d85-5ead-47d0-bd13-d1a1bb57f29a", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "4424dec9-717a-4b4c-a875-740637877d38", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "c2b1bf82-1cea-4aa9-a4a7-3de0169d7407", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "bd1dfdca-8fe2-4dea-bab6-54833412d625", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}]}, {"id": "b28b8dff-2ecb-40ef-8c42-4480e462df6f", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "fe0a7356-4970-47bb-856c-4ab997111c61", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "67cce907-ab37-47de-b25d-a619d0a63ec8", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "19b5939f-0afe-4c80-90ee-b96879a3cb9b", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "64b355ca-3dab-4159-ab24-39844b9af18a", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "8c31de52-7856-4588-bca0-ba8c319af3bc", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "be34f669-cbfb-4dda-b62e-2e322912d3c4", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "1a7b71aa-f1d8-4600-8fc0-eb3180eec255", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "xXSSProtection": "1; mode=block", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "395830d8-5952-455f-b227-1bbf8ffc9d3c", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "7b73540c-2218-4c92-9c97-4a8bcfd174d3", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-property-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-address-mapper", "saml-user-attribute-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "oidc-full-name-mapper", "saml-role-list-mapper"]}}, {"id": "6e0289a6-27ed-48eb-b638-f1d75aa93963", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "080e9dda-1d7b-41a4-9274-26bad91f21e7", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper", "oidc-usermodel-attribute-mapper", "oidc-address-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper"]}}, {"id": "4754620a-0bb8-41c1-8287-6680a6c31240", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "1f9116b3-0e59-42c5-aa1f-70cf59769e25", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "e66340d7-54d7-43ac-b3f6-39301ae7ede6", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "bec3ca27-fc58-4a78-a993-9edcd7ecd7dc", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}], "org.keycloak.keys.KeyProvider": [{"id": "508b32ea-5bf2-4e0e-8675-7c7f75a76830", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAhS0hZGzzakCaOAk/BKUL+xSqBVNWFVFoc8PQWT0CUfyKOy05QukKr1CD3euDyZ+VwUB2HTXvSMUby+tJYk6XI9nMoeK40rO3Ke6M4pyAqppeGoXnIr3UTEvJf7zAafhHIwTVSXoNz/Q/PVQ5HaoRTK1/Wiic7dtXVbaHXMBJShEr6etFZC2PYr+6qow+H/3kNpjS+fkWclP7L/F+bDOAmn/dM1zmm19uGCtN3jJpQ16S+NBnCn8zFjLy2QFqK0lbhAliAycUrxuSKhAtAOuJv1sZGovoHfRsWUlrzjnVC/opajVgbKpEnErfHrGsxOtwDfFMUUdSbP/yAJ+rnpFbUQIDAQABAoIBAHHrFPFOsTlmYYUlg555gDIajwVXL/+0T2EXUxXCtkVjfOr52E2AAv/MI8ClQWoq9HMGrQJ3rGPXF9vrPC63g1nLaNvhkK/LwxY/VIxTy0wkF/MgL4LsVrmc6pQ15XHASzmNXNUpbr8/8wiHDwRtAbdRgeqhavub8K0FIe0ZHpgXsNYVimtQjXhBuNZyEhlU6NT4UZKvRNd8Bv657vd3z8Ys2elHwIkLjCVE2E7Phknl4FDz1VMAdSK+li5ijaZW1qlaSA0P986Jyf2t/Rydi15BEy528vCn3sH0hrVBFv8K0kq4PuUBeJpF/vSqyrt5qufPaJus+KqzGNSxqmjPgnECgYEA1igYpcbwIOz32uFN2V5Mfpg7wZrgIpI1MwM9crZoACztwWozGXNvRaM2Xb2WWldHT1N3wyDfkgCKLdXYPiDrXDOCJlXhsv/KE+3FoBgrKUnmwyf5yszjhP6mGYQNfr1DjExxvQWySi5bAP9RWkc+qRuHteiSJF/o4wgPurKeWpcCgYEAnzJ6Ozg28YQWOx22G35sW/vYK4h8Fc0n8LHSnZPAnlbA40K3hSWw5Z4IZwqi3dWox86JJQ8+YTF62CHvmNRexzfmtC58UnnYlkthD1FLdI1vlHmC5HS6Gw6ixTr+tQj7sLDG3TDKJCHYvLOdxbQR1TS+mKREM8UashH+4B+wPlcCgYBCTv/ytHo9d6H2v7QfDbeZqwE1nQKxMZwPQ32POzNQepjTPlK8GqXlZfe7ZqxbJJML2MhIPrF6v3gJYSEne4Hkdn5h6E2N4u1q5kA+pb0Xd65f1szdIjuAPminld+n0mpL0o+U+2KM3XWIBYXm8hBQxBltFanKYXf2c9SK9itZ2QKBgDGLWBcOC6tmXDTRrlcoJTkLS99gfp2aV4/FB7MaxJuCjE0t+kdpz3/mit7mpIE9eWWVlYD/GX7OE1koVpTNoyGE+DujK/XPDeTYWMCXoFAJpGwk1OOcoD7Dc0zGa3aT8SCwI3O7N22/SuCPVc6tl88utxJyLPefpAs/Jn9B3WvJAoGBALK1nyMyhFx3FRxsRC0HG0atTbhqytlco/Kn+bvekXjhaCJ5B0DOYhewFWCzBdZE+ptONRUi0BMJKJnqe0Y14DutgrddYKt+gJmHvrF5itlVnoWoOKBOooUK8z754LHoxJLMXAJMTqyBtm8OXFkeGBBwE3AEKsHkSUgC3ALgqME5"], "certificate": ["MIICmzCCAYMCBgFyJFOz5zANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZtYXN0ZXIwHhcNMjAwNTE3MjAyNzQ0WhcNMzAwNTE3MjAyOTI0WjARMQ8wDQYDVQQDDAZtYXN0ZXIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCFLSFkbPNqQJo4CT8EpQv7FKoFU1YVUWhzw9BZPQJR/Io7LTlC6QqvUIPd64PJn5XBQHYdNe9IxRvL60liTpcj2cyh4rjSs7cp7ozinICqml4ahecivdRMS8l/vMBp+EcjBNVJeg3P9D89VDkdqhFMrX9aKJzt21dVtodcwElKESvp60VkLY9iv7qqjD4f/eQ2mNL5+RZyU/sv8X5sM4Caf90zXOabX24YK03eMmlDXpL40GcKfzMWMvLZAWorSVuECWIDJxSvG5IqEC0A64m/Wxkai+gd9GxZSWvOOdUL+ilqNWBsqkScSt8esazE63AN8UxRR1Js//IAn6uekVtRAgMBAAEwDQYJKoZIhvcNAQELBQADggEBACda3NTeR8kLxqYm6rlCkNawPaPSMVmAKxfM7cfbPJw6UJJJd+4IIpa+ffqeonITO0UZz3uZ5m2/rU8FrA55cF7i6yM60Ly0cRECCV6xZC6L+UjGIBpFJn5wV18+z5g72lWDPdieZS++3Ra4jZh13uD0rm7FjTI6QABo1ZIwHxzJT3Y/CS2BdaXBeE2JEKKhQDvNRk8bXOyS8Y1mY1HsblHGyPR6KihFaGV+9AJRHKq/sJrAlyZlP25JpQcEeSQwGuKGKBthzpfH2+FZGoXPZCObXRagIXdZP74DbuNiURAETz4d7MHoTE+ICtkCKhd+ptgQt3JIXAEe9aM5hq7JRko="], "priority": ["100"]}}, {"id": "0ad86174-ae6d-4cc1-804e-5024d5195963", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["b799ff69-23f4-48cd-9ad2-b19e59f9b847"], "secret": ["a3PCfLst1nQX-Uogl0_aXbCj2hNBShQhmeA-TXS6nu715-RDTPh58mqv5RB4Ca7C_AoF8OVDRWM4xmle00zViQ"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "76f518c5-1501-4cdc-891e-28bf8e8a4706", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["4ccac494-07d5-4748-9448-006c4aa591c4"], "secret": ["RLmAWeGHZ6sKfpBr-2bfrQ"], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "b63b646b-9114-400c-a06c-71bc00256995", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "c6456917-589a-4ccb-9bbe-365bac8c696b", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth-otp", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "3300a6a4-2980-4973-9a8e-b5f4665301a4", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "37be8192-bcab-45c2-9d71-2a307fb96c22", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "aa686a2d-d549-4eb3-ab42-6ac09e803282", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "31bfe753-6e3c-4ecf-a3ae-c9f877acf070", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "Account verification options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "c398c05f-2153-4886-a0ce-e7649f11b869", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "16a59dc6-d0c2-412e-b9ee-0df839337308", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "71b8c63d-6d26-4f40-bcd8-4b5e66e6d050", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 20, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "6460d4d1-b1b5-4896-8e7b-a39ae485e564", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "2af0d149-640a-407d-aaec-69f2ef9059fb", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-x509", "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "49c76643-3375-4dab-b0bf-e1b231cb3fe8", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 30, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "0d28496e-47dd-4aac-91ce-cd658c2b544a", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "5c9a489e-d48d-44de-91d5-2c38c30ff729", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "User creation or linking", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "3638d12f-889d-460a-9555-3d991beb73fd", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 20, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "e5d95cad-d222-4733-9ea0-afd2f55bd25a", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "Authentication Options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "4500e185-5ff0-4743-8c80-602d0505480e", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "a8054a36-fd65-4405-a41e-c5a69c7d931c", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "8a6afe4e-bc90-46cf-bebf-e79d50a65937", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 40, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "091a0b6e-c873-4830-9ca0-cf4d276907de", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "dc9535a1-f341-46f8-998f-427123fc864c", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "3c844483-53fb-445a-9b82-95a8935cf205", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {}, "keycloakVersion": "9.0.3", "userManagedAccessAllowed": false}]