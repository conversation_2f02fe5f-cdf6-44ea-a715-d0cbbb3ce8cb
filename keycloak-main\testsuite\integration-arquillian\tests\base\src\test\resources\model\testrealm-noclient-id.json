{"realm": "demo-no-client-id", "enabled": true, "accessTokenLifespan": 300, "accessCodeLifespan": 10, "accessCodeLifespanUserAction": 600, "sslRequired": "external", "requiredCredentials": ["password"], "users": [{"username": "<EMAIL>", "enabled": true, "email": "<EMAIL>", "credentials": [{"type": "Password", "value": "password"}], "realmRoles": ["user"]}], "roles": {"realm": [{"name": "user", "description": "Have User privileges"}, {"name": "admin", "description": "Have Administrator privileges"}]}, "scopeMappings": [{"client": "third-party", "roles": ["user"]}], "clients": [{"name": "third-party", "enabled": true, "bearerOnly": true}], "clientScopeMappings": {"realm-management": [{"client": "some-client", "roles": ["create-client"]}]}}