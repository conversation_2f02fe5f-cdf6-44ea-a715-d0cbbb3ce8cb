# Keycloak Realm Configuration Guide

This guide explains each section of the `realm-config.json` file and how to configure it properly for SSO authentication.

## 1. Basic Realm Settings

```json
{
  "id": "my-sso-realm",
  "realm": "my-sso-realm",
  "displayName": "My SSO Realm",
  "displayNameHtml": "<div class=\"kc-logo-text\"><span>My Company SSO</span></div>",
  "enabled": true
}
```

**Key Points:**
- `id` and `realm`: Must be unique across your Keycloak instance
- `displayName`: Shown in the admin console
- `displayNameHtml`: Custom HTML for branding (optional)
- `enabled`: Must be `true` for the realm to be active

## 2. Security and SSL Settings

```json
{
  "sslRequired": "external",
  "defaultSignatureAlgorithm": "RS256",
  "revokeRefreshToken": false,
  "refreshTokenMaxReuse": 0
}
```

**SSL Options:**
- `"external"`: SSL required for external requests (recommended for production)
- `"none"`: No SSL required (development only)
- `"all"`: SSL required for all requests

**Security Best Practices:**
- Always use `"external"` or `"all"` in production
- Use `RS256` for signature algorithm (most secure)
- Set `revokeRefreshToken: true` for high-security environments

## 3. Token Lifespans (Critical for SSO)

```json
{
  "accessTokenLifespan": 300,
  "accessTokenLifespanForImplicitFlow": 900,
  "ssoSessionIdleTimeout": 1800,
  "ssoSessionMaxLifespan": 36000,
  "offlineSessionIdleTimeout": 2592000,
  "accessCodeLifespan": 60,
  "accessCodeLifespanUserAction": 300
}
```

**Recommended Values:**
- `accessTokenLifespan`: 300 seconds (5 minutes) - short for security
- `ssoSessionIdleTimeout`: 1800 seconds (30 minutes) - user inactivity timeout
- `ssoSessionMaxLifespan`: 36000 seconds (10 hours) - maximum session duration
- `accessCodeLifespan`: 60 seconds - authorization code validity

## 4. User Registration and Login Settings

```json
{
  "registrationAllowed": false,
  "registrationEmailAsUsername": false,
  "rememberMe": true,
  "verifyEmail": true,
  "loginWithEmailAllowed": true,
  "duplicateEmailsAllowed": false,
  "resetPasswordAllowed": true,
  "editUsernameAllowed": false
}
```

**For SSO Environments:**
- `registrationAllowed`: Usually `false` (users come from identity providers)
- `verifyEmail`: `true` for security
- `editUsernameAllowed`: `false` to prevent confusion
- `loginWithEmailAllowed`: `true` for user convenience

## 5. Brute Force Protection

```json
{
  "bruteForceProtected": true,
  "permanentLockout": false,
  "maxFailureWaitSeconds": 900,
  "minimumQuickLoginWaitSeconds": 60,
  "waitIncrementSeconds": 60,
  "failureFactor": 30
}
```

**Security Configuration:**
- Always enable `bruteForceProtected: true`
- `failureFactor`: Number of failed attempts before lockout
- `maxFailureWaitSeconds`: Maximum wait time after failures

## 6. SMTP Configuration

```json
{
  "smtpServer": {
    "from": "<EMAIL>",
    "fromDisplayName": "My Company SSO",
    "host": "smtp.mycompany.com",
    "port": "587",
    "ssl": "false",
    "starttls": "true",
    "auth": "true",
    "user": "<EMAIL>",
    "password": "smtp-password"
  }
}
```

**Required for:**
- Email verification
- Password reset
- User notifications

## 7. Client Configurations

### OpenID Connect Client (Web Application)

```json
{
  "clientId": "my-web-app",
  "name": "My Web Application",
  "enabled": true,
  "clientAuthenticatorType": "client-secret",
  "secret": "my-web-app-secret-change-this",
  "protocol": "openid-connect",
  "publicClient": false,
  "standardFlowEnabled": true,
  "implicitFlowEnabled": false,
  "directAccessGrantsEnabled": false,
  "serviceAccountsEnabled": false,
  "redirectUris": [
    "https://myapp.company.com/*",
    "https://myapp.company.com/auth/callback"
  ],
  "webOrigins": [
    "https://myapp.company.com"
  ]
}
```

**Key Settings:**
- `publicClient`: `false` for server-side apps, `true` for SPAs
- `standardFlowEnabled`: `true` for authorization code flow
- `implicitFlowEnabled`: `false` (deprecated, use PKCE instead)
- `directAccessGrantsEnabled`: `false` for security
- `redirectUris`: Must match exactly what your app uses

### SAML Client

```json
{
  "clientId": "https://samlapp.company.com/saml",
  "protocol": "saml",
  "attributes": {
    "saml.assertion.signature": "true",
    "saml.server.signature": "true",
    "saml.signature.algorithm": "RSA_SHA256",
    "saml.authnstatement": "true",
    "saml_name_id_format": "username"
  }
}
```

**SAML-Specific Settings:**
- `clientId`: Usually the SP entity ID (URL format)
- `saml.assertion.signature`: `true` for security
- `saml.server.signature`: `true` to sign responses
- `saml_name_id_format`: How to identify users (`username`, `email`, `persistent`)

## 8. Identity Providers

### Google OIDC Provider

```json
{
  "alias": "google",
  "providerId": "google",
  "enabled": true,
  "trustEmail": true,
  "config": {
    "syncMode": "IMPORT",
    "clientId": "your-google-client-id.apps.googleusercontent.com",
    "clientSecret": "your-google-client-secret",
    "hostedDomain": "mycompany.com"
  }
}
```

### SAML Identity Provider

```json
{
  "alias": "corporate-saml",
  "providerId": "saml",
  "config": {
    "syncMode": "IMPORT",
    "nameIDPolicyFormat": "urn:oasis:names:tc:SAML:2.0:nameid-format:persistent",
    "singleSignOnServiceUrl": "https://corporate-idp.company.com/sso/saml",
    "validateSignature": "true",
    "wantAssertionsSigned": "true"
  }
}
```

**Sync Modes:**
- `"IMPORT"`: Import user on first login, don't update
- `"FORCE"`: Always update user data from IdP
- `"LEGACY"`: Don't import or update

## 9. Security Best Practices

### Browser Security Headers

```json
{
  "browserSecurityHeaders": {
    "xContentTypeOptions": "nosniff",
    "xFrameOptions": "SAMEORIGIN",
    "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';",
    "xXSSProtection": "1; mode=block",
    "strictTransportSecurity": "max-age=31536000; includeSubDomains"
  }
}
```

### Required Credentials

```json
{
  "requiredCredentials": ["password"]
}
```

**Options:**
- `["password"]`: Standard password authentication
- `["password", "otp"]`: Password + OTP/2FA required
- `["otp"]`: OTP only (for passwordless)

## 10. Deployment Steps

1. **Prepare the Configuration:**
   - Replace all placeholder values (secrets, URLs, etc.)
   - Update client secrets and identity provider credentials
   - Verify all URLs match your environment

2. **Import the Realm:**
   ```bash
   # Using Admin CLI
   kcadm.sh create realms -f realm-config.json

   # Using REST API
   curl -X POST \
     http://localhost:8080/admin/realms \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d @realm-config.json
   ```

3. **Post-Import Configuration:**
   - Test all identity provider connections
   - Verify client redirect URIs
   - Test authentication flows
   - Configure additional users if needed

## 11. Common Configuration Mistakes

1. **Incorrect Redirect URIs**: Must match exactly
2. **Wrong SSL Settings**: Use "external" for production
3. **Weak Token Lifespans**: Too long = security risk, too short = poor UX
4. **Missing SMTP Configuration**: Required for email verification
5. **Insecure Client Settings**: Don't enable unnecessary flows
6. **Wrong Identity Provider URLs**: Verify SSO and SLO endpoints

## 12. Testing Your Configuration

1. **Test User Login**: Try logging in with local users
2. **Test Identity Providers**: Verify SSO with each configured IdP
3. **Test Client Applications**: Ensure all apps can authenticate
4. **Test Logout**: Verify single logout works across all apps
5. **Test Token Refresh**: Ensure tokens refresh properly
6. **Test Security**: Verify brute force protection works

This configuration provides a solid foundation for SSO authentication with Keycloak. Customize the values according to your specific requirements and security policies.
