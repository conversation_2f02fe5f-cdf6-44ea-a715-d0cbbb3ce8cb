emailVerificationSubject=Zweryfikuj email
emailVerificationBody=<PERSON><PERSON>ś utworzył konto {2} z Twoim adresem e-mail. Jeś<PERSON> to <PERSON>, kliknij proszę poniższy odnośnik, aby potwierdzić założenie konta\n\n{0}\n\nOdnośnik wygaśnie w ciągu {3}.\n\nJeśli nie utworzyłaś/eś tego konta, po prostu zignoruj niniejszą wiadomość.
emailVerificationBodyHtml=<p>Ktoś utworzył konto {2} z Twoim adresem e-mail. Jeś<PERSON> to Ty, kliknij proszę <a href="{0}">odnośnik</a>, aby potwierdzić założenie konta</p><p>Odnośnik wygaśnie w ciągu {3}</p><p>Jeśli nie utworzyłaś/eś tego konta, po prostu zignoruj niniej<PERSON> wiadomoś<PERSON>.</p>
orgInviteSubject=Zaproszenie, aby dołączyć do organizacji {0}
orgInviteBody=Zostałeś zaproszony do dołączenia do organizacji "{3}". Kliknij poniższy link, aby dołączyć.\n\n{0}\nTen link wygaśnie w ciągu {4}.\n\nJeśli nie chcesz dołączyć do organizacji, zignoruj tę wiadomość.
orgInviteBodyHtml=<p>Zostałeś zaproszony do dołączenia do organizacji {3}. Kliknij poniższy link, aby dołączyć. </p><p><a href="{0}">Link, aby dołączyć do organizacji</a></p><p>Ten link wygaśnie w ciągu {4}.</p><p>Jeśli nie chcesz dołączyć do organizacji, zignoruj tę wiadomość.</p>
orgInviteBodyPersonalized=Cześć, "{5}" "{6}".\n\n Zostałeś zaproszony do dołączenia do organizacji "{3}". Kliknij poniższy link, aby dołączyć.\n\n{0}\n\nTen link wygaśnie w ciągu {4}.\n\nJeśli nie chcesz dołączyć do organizacji, zignoruj tę wiadomość.
orgInviteBodyPersonalizedHtml=<p>Cześć, {5} {6}.</p><p>Zostałeś zaproszony do dołączenia do organizacji {3}. Kliknij poniższy link, aby dołączyć.</p><p><a href="{0}">Link, aby dołączyć do organizacji</a></p><p>Ten link wygaśnie w ciągu {4}.</p><p>Jeśli nie chcesz dołączyć do organizacji, zignoruj tę wiadomość.</p>
emailUpdateConfirmationSubject=Zweryfikuj nowy e-mail
emailUpdateConfirmationBody=Aby zaktualizować konto {2} za pomocą adresu e-mail {1}, kliknij poniższy link\n\n{0}\n\nTen link wygaśnie w ciągu {3}.\n\nJeśli nie chcesz kontynuować tej modyfikacji, zignoruj tę wiadomość.
emailUpdateConfirmationBodyHtml=<p>Aby zaktualizować konto {2} za pomocą adresu e-mail {1}, kliknij poniższy link</p><p><a href="{0}">{0}</a></p><p>Ten link wygaśnie w ciągu {3}.</p><p>Jeśli nie chcesz kontynuować tej modyfikacji, zignoruj tę wiadomość.</p>
emailTestSubject=[KEYCLOAK] - wiadomość testowa SMTP
emailTestBody=To jest wiadomość testowa
emailTestBodyHtml=<p>To jest wiadomość testowa</p>
identityProviderLinkSubject=Link {0}
identityProviderLinkBody=Ktoś chce połączyć Twoje konto "{1}" z kontem "{0}" użytkownika {2}. Jeśli to Ty, kliknij proszę poniższy odnośnik by połączyć konta\n\n{3}\n\nOdnośnik wygaśnie w ciągu {5}.\n\nJeśli nie chcesz połączyć konta to zignoruj niniejszą wiadomość. Jeśli połączysz konta, będziesz mogła/mógł się zalogować na {1} przez {0}.
identityProviderLinkBodyHtml=<p>Ktoś chce połączyć Twoje konto <b>{1}</b> z kontem <b>{0}</b> użytkownika {2}. Jeśli to Ty, kliknij proszę <a href="{3}">odnośnik</a> by połączyć konta.</p><p>Odnośnik wygaśnie w ciągu {5}.</p><p>Jeśli nie chcesz połączyć konta to zignoruj niniejszą wiadomość. Jeśli połączysz konta, będziesz mogła/mógł się zalogować na {1} przez {0}.</p>
passwordResetSubject=Zresetuj hasło
passwordResetBody=Ktoś właśnie poprosił o zmianę hasła do Twojego konta {2}. Jeśli to Ty, kliknij poniższy odnośnik, aby je zresetować.\n\n{0}\n\nOdnośnik i kod stracą ważność w ciągu {3}.\n\nJeśli nie chcesz zresetować swoich danych logowania, po prostu zignoruj tę wiadomość i nic się nie zmieni.
passwordResetBodyHtml=<p>Ktoś właśnie poprosił o zmianę hasła do Twojego konta {2}. Jeśli to Ty, kliknij poniższy odnośnik, aby je zresetować.</p><p><a href="{0}">Odnośnik do resetowania poświadczeń</a></p><p>Ten odnośnik wygaśnie w ciągu {3}.</p><p>Jeśli nie chcesz resetować swoich poświadczeń, po prostu zignoruj tę wiadomość i nic się nie zmieni.</p>
executeActionsSubject=Zaktualizuj swoje konto
executeActionsBody=Administrator właśnie zażądał aktualizacji konta {2} poprzez wykonanie następujących działań: {3}. Kliknij poniższy odnośnik, aby rozpocząć proces.\n\n{0}\n\nOdnośnik wygaśnie w ciągu {4}.\n\nJeśli nie masz pewności, że administrator tego zażądał, po prostu zignoruj tę wiadomość i nic się nie zmieni.
executeActionsBodyHtml=<p>Administrator właśnie zażądał aktualizacji konta {2} poprzez wykonanie następujących działań: {3}. Kliknij proszę <a href="{0}">odnośnik</a>, aby rozpocząć proces.</p><p>Odnośnik wygaśnie w ciągu {4}.</p><p>Jeśli nie masz pewności, że administrator tego zażądał, po prostu zignoruj tę wiadomość i nic się nie zmieni.</p>
eventLoginErrorSubject=Błąd logowania
eventLoginErrorBody=Nieudana próba logowania została wykryta na Twoim koncie {0} z {1}. Jeśli to nie Ty, skontaktuj się z administratorem.
eventLoginErrorBodyHtml=<p>Nieudana próba logowania została wykryta na Twoim koncie {0} z {1}. Jeśli to nie Ty, skontaktuj się z administratorem.</p>
eventRemoveTotpSubject=Usuń hasło jednorazowe (OTP)
eventRemoveTotpBody=Hasło jednorazowe (OTP) zostało usunięte z Twojego konta w {0} z {1}. Jeśli to nie Ty, skontaktuj się z administratorem.
eventRemoveTotpBodyHtml=<p>Hasło jednorazowe (OTP) zostało usunięte z Twojego konta w {0} z {1}. Jeśli to nie Ty, skontaktuj się z administratorem.</p>
eventUpdatePasswordSubject=Aktualizuj hasło
eventUpdatePasswordBody=Twoje hasło zostało zmienione {0} z {1}. Jeśli to nie Ty, skontaktuj się z administratorem.
eventUpdatePasswordBodyHtml=<p>Twoje hasło zostało zmienione {0} z {1}. Jeśli to nie Ty, skontaktuj się z administratorem.</p>
eventUpdateTotpSubject=Aktualizuj hasło jednorazowe (OTP)
eventUpdateTotpBody=Hasło jednorazowe (OTP) zostało zaktualizowane na Twoim koncie {0} z {1}. Jeśli to nie Ty, skontaktuj się z administratorem.
eventUpdateTotpBodyHtml=<p>Hasło jednorazowe (OTP) zostało zaktualizowane na Twoim koncie {0} z {1}. Jeśli to nie Ty, skontaktuj się z administratorem.</p>
eventUpdateCredentialSubject=Aktualizacja poświadczenia
eventUpdateCredentialBody=Twoje poświadczenie {0} zostało zmienione dnia {1} z {2}. Jeśli to nie byłeś Ty, skontaktuj się z administratorem.
eventUpdateCredentialBodyHtml=<p>Twoje poświadczenie {0} zostało zmienione dnia {1} z {2}. Jeśli to nie byłeś Ty, skontaktuj się z administratorem.</p>
eventRemoveCredentialSubject=Usunięcie poświadczenia
eventRemoveCredentialBody=Poświadczenie {0} zostało usunięte z Twojego konta dnia {1} z {2}. Jeśli to nie byłeś Ty, skontaktuj się z administratorem.
eventRemoveCredentialBodyHtml=<p>Poświadczenie {0} zostało usunięte z Twojego konta dnia {1} z {2}. Jeśli to nie byłeś Ty, skontaktuj się z administratorem.</p>
eventUserDisabledByTemporaryLockoutSubject=Użytkownik zablokowany tymczasowo
eventUserDisabledByTemporaryLockoutBody=Twoje konto zostało tymczasowo zablokowane z powodu wielokrotnych nieudanych prób logowania dnia {0}. W razie potrzeby skontaktuj się z administratorem.
eventUserDisabledByTemporaryLockoutHtml=<p>Twoje konto zostało tymczasowo zablokowane z powodu wielokrotnych nieudanych prób logowania dnia {0}. W razie potrzeby skontaktuj się z administratorem.</p>
eventUserDisabledByPermanentLockoutSubject=Użytkownik zablokowany na stałe
eventUserDisabledByPermanentLockoutBody=Twoje konto zostało trwale zablokowane z powodu wielokrotnych nieudanych prób logowania dnia {0}. Skontaktuj się z administratorem.
eventUserDisabledByPermanentLockoutHtml=<p>Twoje konto zostało trwale zablokowane z powodu wielokrotnych nieudanych prób logowania dnia {0}. Skontaktuj się z administratorem.</p>

requiredAction.CONFIGURE_TOTP=Konfiguracja hasła jednorazowego (OTP)
requiredAction.TERMS_AND_CONDITIONS=Regulamin
requiredAction.UPDATE_PASSWORD=Aktualizacja hasła
requiredAction.UPDATE_PROFILE=Aktualizacja profilu
requiredAction.VERIFY_EMAIL=Weryfikacja adresu e-mail
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Wygeneruj kody odzyskiwania

# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be '{0,choice,0#minut|1#minuta|2#minuty|2<minut}'
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#sekund|1#sekunda|2#sekundy|4<sekund}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#minut|1#minuta|2#minuty|4<minut}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#godzin|1#godzina|2#godziny|4<godzin}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#dni|1#dzień|1<dni}

emailVerificationBodyCode=Potwierdź proszę swój adres e-mail wprowadzając następujący kod.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Potwierdź proszę swój adres e-mail, wprowadzając następujący kod.</p><p><b>{0}</b></p>