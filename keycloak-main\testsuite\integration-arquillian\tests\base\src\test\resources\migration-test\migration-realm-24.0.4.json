[{"id": "e3decc1f-6f9a-44c5-83bd-94e146b1cf0b", "realm": "master", "displayName": "Keycloak", "displayNameHtml": "<div class=\"kc-logo-text\"><span>Keycloak</span></div>", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 60, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxTemporaryLockouts": 0, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "0afd302e-4297-4418-9416-3c26956d2844", "name": "master-test-realm-role", "composite": false, "clientRole": false, "containerId": "e3decc1f-6f9a-44c5-83bd-94e146b1cf0b", "attributes": {}}, {"id": "e1d256cd-0249-4369-8a8b-0469063604dc", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "e3decc1f-6f9a-44c5-83bd-94e146b1cf0b", "attributes": {}}, {"id": "5855ea3d-989f-4068-916b-d0f8f65eba16", "name": "create-realm", "description": "${role_create-realm}", "composite": false, "clientRole": false, "containerId": "e3decc1f-6f9a-44c5-83bd-94e146b1cf0b", "attributes": {}}, {"id": "1ba585c8-d329-4abb-b2e6-bb4f59816e0a", "name": "admin", "description": "${role_admin}", "composite": true, "composites": {"realm": ["create-realm"], "client": {"Migration-realm": ["view-users", "manage-realm", "create-client", "view-realm", "view-authorization", "query-realms", "manage-users", "query-groups", "query-clients", "manage-identity-providers", "query-users", "manage-events", "manage-clients", "impersonation", "view-identity-providers", "view-events", "view-clients", "manage-authorization"], "master-realm": ["view-clients", "query-clients", "query-realms", "manage-identity-providers", "view-realm", "view-authorization", "view-events", "create-client", "view-identity-providers", "manage-realm", "view-users", "query-groups", "query-users", "manage-clients", "manage-authorization", "manage-users", "manage-events", "impersonation"], "Migration2-realm": ["manage-realm", "query-groups", "create-client", "query-clients", "manage-authorization", "view-clients", "query-users", "view-authorization", "query-realms", "view-identity-providers", "view-events", "manage-clients", "manage-users", "view-users", "impersonation", "manage-events", "manage-identity-providers", "view-realm"]}}, "clientRole": false, "containerId": "e3decc1f-6f9a-44c5-83bd-94e146b1cf0b", "attributes": {}}, {"id": "b4d1e52f-9c15-45a0-9d6c-3aa4fd48e496", "name": "default-roles-master", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["manage-account", "view-profile"]}}, "clientRole": false, "containerId": "e3decc1f-6f9a-44c5-83bd-94e146b1cf0b", "attributes": {}}, {"id": "530b8076-e234-4cb4-bff6-18a320895a10", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "e3decc1f-6f9a-44c5-83bd-94e146b1cf0b", "attributes": {}}], "client": {"security-admin-console": [], "master-test-client": [{"id": "f5d4e540-65ff-405f-993b-878874ff50f3", "name": "master-test-client-role", "composite": false, "clientRole": true, "containerId": "b7dfd740-e7e1-4dbc-bd21-529a848100ec", "attributes": {}}], "admin-cli": [], "Migration-realm": [{"id": "37517b66-a4aa-4577-ac82-e4249efd315b", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"Migration-realm": ["query-clients"]}}, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "53deff74-ed30-4b09-b9a3-d7753b0ae97a", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "66b42ed7-4498-41cf-96e1-a6217c9b4d5b", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "e3f52dbf-e5fe-4083-817f-00de718f26b6", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "afd45907-9c83-434d-8a1c-713f456c6a07", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "13758984-df4a-4191-9729-35e5ff9b4a17", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "35cf8f99-0b69-4f54-93ec-4d9b8a93be63", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "45a2a79a-3897-4f52-bcb1-1498512a8e1d", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"Migration-realm": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "617c2203-cc5e-4e30-ac98-f4aec336505c", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "55edb36c-75c4-4b7a-84f9-dbfc2235b8f3", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "145753d2-1102-41a6-98e8-03cb97403ed4", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "c23f4b0d-94ae-4a53-b600-663243071629", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "6ce2f223-7a3f-41aa-900f-aa3384557546", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "761e89ab-144f-4a0e-b8af-7ec43b1555d0", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "498b38b3-2cd2-4ed0-93ba-7c88196833d6", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "af7c8bb0-68e6-42a9-8a63-89972e1a6034", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "c7e47197-befc-4721-8244-1e340f334ed1", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}, {"id": "a4ee5098-0552-4893-a1d6-3a39fad11c8c", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "aae49aae-55b7-4dc1-a010-62ec6b817def", "attributes": {}}], "account-console": [], "broker": [{"id": "a3d03ef2-7974-44c0-96dd-4f5896ae0f48", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "66a9b075-9cfb-4595-96ab-8a703c376021", "attributes": {}}], "master-realm": [{"id": "7bebffe1-5508-4064-b172-edae93d7a9e8", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "51cd38a9-27ed-4e8e-9c64-1ccf66f3070a", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "1483bd94-f936-4cbf-b8f4-2aab56c21d7a", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"master-realm": ["query-clients"]}}, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "a6cb8a92-6b9e-4316-b181-3469ce923e9d", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "aa2e716a-b42b-4bcc-adc7-c4a37a38ddc3", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "d0c85fcd-582b-4fe2-bffe-1395ccd42361", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"master-realm": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "29a92320-e298-4a30-9049-25ecd1904633", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "9892b58f-bfe1-4e24-8634-c56823528994", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "1ff8e114-22ce-4558-ad5c-10545cf19b63", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "b2137471-f09e-4ff5-a7af-1c2fc445982f", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "dd73a0c4-1420-47d2-8d14-aa68df9fc59f", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "8671f240-5776-40d6-87ae-b6e73448149c", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "caa92b17-205f-44fa-9b3c-b44a3b54d8ff", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "aad7caf5-4a65-4897-a2c5-d0a918cad738", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "292a250d-2235-49d0-a876-122bc4990039", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "3c5af6cb-3de2-4886-8e52-42248d1636a6", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "56a217f6-9fab-427b-854d-58d7bcfb9c18", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}, {"id": "4dfd0d4d-c069-4696-ac0c-86cb02321427", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "attributes": {}}], "account": [{"id": "5e853e7f-78c8-4356-8c94-e58bb09dbd54", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "f4f213d3-07ca-4f66-99d2-5bfa8260a65e", "attributes": {}}, {"id": "53e161ac-5cba-43c4-8186-fec3765c9e53", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "f4f213d3-07ca-4f66-99d2-5bfa8260a65e", "attributes": {}}, {"id": "b6e8a0e9-26f7-4f1a-972d-7a9fef813ed9", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "f4f213d3-07ca-4f66-99d2-5bfa8260a65e", "attributes": {}}, {"id": "7073f653-5260-47c0-b1f8-3d4b60cd506c", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "f4f213d3-07ca-4f66-99d2-5bfa8260a65e", "attributes": {}}, {"id": "44fd5b4e-0804-46d5-8b1c-e3ecd1c12b48", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "f4f213d3-07ca-4f66-99d2-5bfa8260a65e", "attributes": {}}, {"id": "6b088031-322f-49b9-a529-850aa0bee57a", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "f4f213d3-07ca-4f66-99d2-5bfa8260a65e", "attributes": {}}, {"id": "3cf00970-b56f-4967-bb1c-899efa4f5974", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "f4f213d3-07ca-4f66-99d2-5bfa8260a65e", "attributes": {}}, {"id": "c1061236-b8c6-4cd4-8d02-29ec9fc2d406", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "f4f213d3-07ca-4f66-99d2-5bfa8260a65e", "attributes": {}}], "Migration2-realm": [{"id": "9a8bd828-4301-42f6-acf2-8577b53719fa", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "bafd0eb2-e82d-4590-9be8-3c71d46a9729", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "1d03c99e-1ea6-4434-a4d5-0605cf91b79c", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "b925f1c7-eae3-4374-a491-d459e37079d4", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "2c51cce0-da6e-4d9d-99b9-c1989fb49179", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "0acb8f4e-dc9b-4691-b241-190b82b4e3c0", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "119fe27c-55c8-47a7-b031-b97b9d62e04a", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "3f48b6aa-95fe-4fb5-89b4-0549c501a0bb", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "4950b4ff-69f7-498b-9a19-7b79c4962eb6", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"Migration2-realm": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "fd481a20-e1fb-4f4c-abe1-5342e396b44e", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"Migration2-realm": ["query-clients"]}}, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "ac97984a-6b72-4782-aae3-5bdfdf9e129c", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "257d9533-4fc4-4813-93f7-f0eb412f4037", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "c0d0db28-b125-469c-8282-abb4f01bb87d", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "b597559a-33d1-4c32-8fce-bfba19868834", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "46bb0ce2-06d6-4f32-b759-b9e5bf795990", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "0b100ab2-2863-42bc-875a-bb329341334f", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "3e39f666-fbca-402a-98e8-7a34623c07f8", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}, {"id": "cdc6f3f1-967a-43bb-8e3d-9a5ce6fd9efc", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "attributes": {}}]}}, "groups": [{"id": "5ba589e2-d88a-479d-a0c1-bb35ea6e3fd3", "name": "master-test-group", "path": "/master-test-group", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {}}], "defaultRole": {"id": "b4d1e52f-9c15-45a0-9d6c-3aa4fd48e496", "name": "default-roles-master", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "e3decc1f-6f9a-44c5-83bd-94e146b1cf0b"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "users": [{"id": "d6ce8fe7-bab3-4d41-9c38-0ef8cafc2d05", "username": "admin", "emailVerified": false, "createdTimestamp": 1716808035158, "enabled": true, "totp": false, "credentials": [{"id": "bac8754e-2044-4d11-8adc-fcdade4fa827", "type": "password", "createdDate": 1716808035239, "secretData": "{\"value\":\"OnsOPGVN5ku0lAuW+6IUMhiYyre5C9itUlMBH9xtQgWYYqeYG3kiRkWrdBVWfvv1t5F4r7FnNLqQzZ7R5Fna2w==\",\"salt\":\"WQWgqINSHrX0yk5oIl4xpw==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\",\"additionalParameters\":{}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["admin", "default-roles-master"], "clientRoles": {"Migration-realm": ["view-identity-providers", "view-users", "manage-realm", "create-client", "view-events", "view-clients", "view-realm", "view-authorization", "manage-authorization", "query-realms", "manage-users", "query-groups", "query-clients", "manage-identity-providers", "query-users", "manage-events", "manage-clients"], "Migration2-realm": ["query-clients", "manage-realm", "manage-authorization", "view-clients", "query-users", "view-authorization", "query-realms", "view-identity-providers", "query-groups", "view-events", "create-client", "manage-clients", "manage-users", "view-users", "manage-events", "manage-identity-providers", "view-realm"]}, "notBefore": 0, "groups": []}, {"id": "********-03af-40df-bdcd-a5710677f0e6", "username": "master-test-user", "emailVerified": false, "createdTimestamp": *************, "enabled": true, "totp": false, "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["offline_access", "uma_authorization"], "clientRoles": {"account": ["manage-account", "view-profile"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "f4f213d3-07ca-4f66-99d2-5bfa8260a65e", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/master/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/master/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "756246fa-fd46-42df-9db5-2efb6edc1272", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/master/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/master/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "7ac6d821-3974-41a5-9e64-808dacf709fb", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "14c4a8f8-d5e8-47ca-aaaa-6ed19e90d070", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "66a9b075-9cfb-4595-96ab-8a703c376021", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "819af43f-45b5-4fe2-9a79-d4f67b5c3c28", "clientId": "master-realm", "name": "master Realm", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "b7dfd740-e7e1-4dbc-bd21-529a848100ec", "clientId": "master-test-client", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "backchannel.logout.session.required": "true", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "e91c64d0-3e51-4af1-bf26-b9fc6e71819e", "clientId": "Migration2-realm", "name": "Migration2 Realm", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": [], "optionalClientScopes": []}, {"id": "aae49aae-55b7-4dc1-a010-62ec6b817def", "clientId": "Migration-realm", "name": "Migration Realm", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": [], "optionalClientScopes": []}, {"id": "dd25bb35-c8e4-4949-bfbe-23a38ba519cc", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/master/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/master/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "b32342cf-55ae-4818-a9aa-7bc21f091339", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "fa2c22dc-d7c3-430f-bbaa-7bc985961777", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "a4d34da3-f0aa-494a-a8d4-e42e605fb188", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}, {"id": "0d136df4-3d87-4ece-84b8-db707d3eaae9", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "c7852f5c-a154-4589-bccc-53d84b1d7e79", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "0f97614c-710c-43ed-86c1-bc3572f59235", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}, {"id": "253f4713-b360-4c59-bd1a-6746474d8173", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "3064fe6e-343f-4603-a99f-f2a2f82f375e", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "f1a39d1e-dfbb-46b7-95bb-e538d5edd7ae", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "b9e19744-b0eb-4ee6-b5de-a4b7684872d1", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "f416c714-9fee-4f5e-97a4-6b40de0cafd5", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "95996853-c7ea-4ecd-af11-31abaca0a389", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "caffbaae-c93c-4317-ab07-e15c8f584854", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "406a8e3a-4b82-4c60-b3d6-7d308998f60a", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "2a1185bf-adda-41e6-baf5-174f27150707", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "7c684b36-c16e-4643-846b-ca4162d007ea", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "c389e8f2-bb2e-4c7a-97bb-b73a4187000e", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "16f579eb-693a-4f53-ac31-3cea8ee8dbce", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "c0c38449-523e-4a32-9adb-ce15da6bd8ae", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "e28fd216-6d92-4ce5-bec5-52e5ebb19b2f", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "9012ca8c-cfdd-4cee-9f7f-beb08eb3a00b", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "ff54c9c8-e903-4e76-80e8-962eb1944349", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "49e307a2-a76b-497d-abee-7fc467926c40", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "f676cea6-453b-435a-bb5a-bb3154908a33", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "ed84424e-6474-44e7-b955-9e6ea0c88de7", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "2e628710-2cdf-4c94-ab19-8c0a807029a3", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "53848d5c-9e64-4156-822b-0c73f4ad26d6", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "67828e1b-c2a1-4411-ad36-03c4410f230f", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "long"}}]}, {"id": "f2272999-bb1e-415e-bcf0-3376bee202ba", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "ab1b9be7-3c9b-468e-905c-1457532133ec", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "44658bc9-1219-4b78-a70d-76ab3fdd196c", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "5b04fc2f-9a70-4ad7-88dd-8e956a1c1f55", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "15968ffd-f76b-4f28-acf5-be9a7abc8c65", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "b380e39f-1724-4e1f-ad0e-3d66ad9a1534", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "1fdfd97a-d921-4eee-85ca-782482653e67", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "8d9007d1-7edf-4785-b335-a0cc8ff44164", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "9900db29-ab3b-43c2-94c0-bbb0c5a35abd", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "4c1e79a7-30eb-4e12-99fe-566764fe4bda", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins", "acr"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "xXSSProtection": "1; mode=block", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "strictTransportSecurity": "max-age=********; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "147a3803-091a-4b96-8f08-8d8c24a13bb7", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-attribute-mapper", "saml-role-list-mapper", "oidc-usermodel-property-mapper", "saml-user-property-mapper", "oidc-full-name-mapper", "oidc-address-mapper", "saml-user-attribute-mapper", "oidc-sha256-pairwise-sub-mapper"]}}, {"id": "98dc5357-3c5f-45c2-9747-275db668e74b", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "bb84f7c1-f488-4145-b4e9-781f193c250a", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "3c264d7b-54e5-47de-9e7f-640c31691348", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "0815e873-7c3c-454d-9fcc-36037c84dba5", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "034f5b29-d03c-403f-aa85-d61af8b7cf65", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "saml-role-list-mapper", "saml-user-property-mapper", "oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper", "saml-user-attribute-mapper", "oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper"]}}, {"id": "499afe0f-44e8-4bbf-93d9-a69b596e8fc1", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "04ee7403-ef5b-4d3c-b071-e58cca49036a", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}], "org.keycloak.userprofile.UserProfileProvider": [{"id": "8dc69d53-dcd8-48e5-bd9b-8a4bef5d446f", "providerId": "declarative-user-profile", "subComponents": {}, "config": {"kc.user.profile.config": ["{\"attributes\":[{\"name\":\"username\",\"displayName\":\"${username}\",\"validations\":{\"length\":{\"min\":3,\"max\":255},\"username-prohibited-characters\":{},\"up-username-not-idn-homograph\":{}},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"email\",\"displayName\":\"${email}\",\"validations\":{\"email\":{},\"length\":{\"max\":255}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"firstName\",\"displayName\":\"${firstName}\",\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"lastName\",\"displayName\":\"${lastName}\",\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false}],\"groups\":[{\"name\":\"user-metadata\",\"displayHeader\":\"User metadata\",\"displayDescription\":\"Attributes, which refer to user metadata\"}],\"unmanagedAttributePolicy\":\"ENABLED\"}"]}}], "org.keycloak.keys.KeyProvider": [{"id": "32f94ea1-19d9-42bc-b0c6-79346978301d", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"privateKey": ["MIIEogIBAAKCAQEAnkqlskwYApqRH2v0l+ogLkw2xo+TMyvLtwpxeNd1vp89WcT6H0gqH6m0cZ6RzOHold74APb7yid+DON7zBsgkZFVB/aMC8RU0c1TYTO8rZjLO7pgw7olaiQx7qXUbTk4AVssIbvbzHOzOZN/EWAFBRkZjPXnHChI4ykMtM9yv4ZKwYWlMh39eCyKWIUFf5CDGL1K2vL8vwl/q3QTEdtGEh1Rlma+9NKXFuWVCpAN/imkDaM0chFfipLJR8PijxD0nVqYDl7RdBGL/OmKZ3ixohJU3jGL3hFZIO2DWmr2QhV+KsRu8UpstLeZk2nHUDnaKcXPSQfqvFtAKoWsddIzlQIDAQABAoIBAAsWLbJ7Zj0ccqim6NvmWo2o6eHmDB3L5fnygc3AbYSbO51OJg9LKyQfleiqqLSXErFt4KSUp+sEnBgEANeOri0ekZGRYPAGtsLLukN3Gl0gSVpsFztDjjuiawWM2NWI6erHu3i7YyLX7F15KVpgBXSndDotaPFNwwPMtrvylwC3Uicyh9dYYjWwkHIV4uxSX1x/IV0vG8P2b3H2OygFddexDGhBmNI23mgRLycNEGchbBXKLuSxstCQvVPVXSIyN85kEL6lHOJ8TeuSg/pz0ZNm0sZRgyvDv7BUg+SKjdioOv3864cSZArzwJSGCXnzzOtZE4P2lkrFEW9Kp3GPd1ECgYEAyfyPQjLlwRmttuS5MqrZYHCoIUwUC1Fc8FM6rQ1qWrZmlIuJOGMNz8k4OueoPOCYCssOmYkVjdj4sQ9fAKIjQRvEk/551fwth1v6oygp3CoIRMnTMEV3aw2lrR6t5Kdi8go6CYGdHGqNg86KsPdh5vHt3X6tt9VEmnOCaYKtNm0CgYEAyJ7Z3eVpsVOymihPRri4YUktxkucsuLZDVQiPy1VZ13lHbupEX9HdndjnCzW38znub6QXZZFAmBzlybjyGqBj3GkfYLbRsN1rfxAz2EBVCHNw5vsbT3Y3H5ecJF4sKSqvYdEYjh1ZXt36+gQ4cFVnDTcKbCUikIKcyp7gpdxWMkCgYASE3kamoV3L34dVQDB6QpFGC/jsvREQwbHNxuUKxQuhA5QEuC33B17T+2pT9v9frf8l0l6Mgtsw1MWPWLqIph3Fi4UdHygys2kRHX8rfZ5DoXo6grtUfoq6XC/OnSjdtti/8lL2n6d8WcyuYQt7CWlzlbUMKcJQZu/GsE8AgbQTQKBgBlxA6H/MNsbCSBBkmQekstLk3F9Lz8EQ5BMJheq8RSttJuDq9obMP3WABzTFuDExHIqt3YW02OMxbvaY7KLxyhgugJssNAVg3vv7GwgGiIIM2bmBCKKYFD9+Y3+9LY5+6kTWVm3XTPkZ33ULSfj865eibkDuqKZ42ddcNJL0ET5AoGAVX9lVCbLJQeiZz3rnmpFyu4gyt/CdtxXtGvjK1Rz6z0VDWa9GL1nkTlndZx4scisr4qTD5mVKH346+vnbpAq60RhwNoIPc9Pl7gqjqSwtZvwfe2oxXqm96cpObQRXtwgC+78L8ZoSijv4SL6s72EcOkU2gVBUlQ/ChZZbQDrh18="], "keyUse": ["ENC"], "certificate": ["MIICmzCCAYMCBgGPubuIYTANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZtYXN0ZXIwHhcNMjQwNTI3MTEwNTI2WhcNMzQwNTI3MTEwNzA2WjARMQ8wDQYDVQQDDAZtYXN0ZXIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCeSqWyTBgCmpEfa/SX6iAuTDbGj5MzK8u3CnF413W+nz1ZxPofSCofqbRxnpHM4eiV3vgA9vvKJ34M43vMGyCRkVUH9owLxFTRzVNhM7ytmMs7umDDuiVqJDHupdRtOTgBWywhu9vMc7M5k38RYAUFGRmM9eccKEjjKQy0z3K/hkrBhaUyHf14LIpYhQV/kIMYvUra8vy/CX+rdBMR20YSHVGWZr700pcW5ZUKkA3+KaQNozRyEV+KkslHw+KPEPSdWpgOXtF0EYv86YpneLGiElTeMYveEVkg7YNaavZCFX4qxG7xSmy0t5mTacdQOdopxc9JB+q8W0Aqhax10jOVAgMBAAEwDQYJKoZIhvcNAQELBQADggEBADf0CW7OLAh/C28AqndqFAVz6ll3n4w3M1B7sSR61tKkmTeiLC//pyJ360DL3iO03GTQvEkaDYP0iTsNLkqHaFrRS9WuLU0zL/womuD57JGgeRVad7d++t+1fRhq9+Zc8D3e/LZPr2NBlzBKFUmnBX6g8+AJASLsl5ynIeNZmDGPSmD5AawbD6CnNUprMbCsIdXJ3RPBLlUnDLB5RETCtF25O/9eDzlAkiFTEO/7hR7nsKAKKBdavlCOoqCuTHbtEa6G3FW13iSLQk+i2eFP3JXO+J7Iuy4DJ4wlNQhfH9UPrltNzx821Ki5sekI0O68GE/xlYWgZj/lHf/YX4AJyy0="], "priority": ["100"], "algorithm": ["RSA-OAEP"]}}, {"id": "4f2ad66e-df0e-435d-939e-00b114452456", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEogIBAAKCAQEAi5AmcMlP4EO+TlygAkxzl/vjEamb0ctstpM7OJtzLvvLn48xuaEhcncmW8y0pvle4FEjaJWtk8i0VEblKeqWggeBcmowp7hE8pyaZjEs/CvH35D9WEagCyZLcvd4LBJytHECcH2MmcwD/yuoD9QSyf94IxMT/KGelUfb65qbERggRv+ycM2A5vW7FEw6xUYLZgBgQwi9H3pQuXKk7JYlN+TVQdWX5OiuSeLmJji3ARLivS+QYhRFokyDrFEo8QSC6ojgs5U177PCmk/SM7wck/gele8NLTdKkZUD7RyAfACgN3c1OwsH90Yp9iobhjs73M4WAa3hv7SNlaztyR//nwIDAQABAoIBADcqXpd8a49ZF7M/15uw/owX67SCl24A+9LbNbB1eb0bmGxUnLIkZ+UoBciJjOrnbUI7sERZzce5sNYTQ+giqyVwtBE2uk7BfrdrgXhdfcubvqC3XfzzxbVCiNZDzYOWMzLx7KXsapQsWXQWnJLNIDQqQCGVG/RjKh0Vz288qIjDrAidEdGXAyeP0kYicAE23SHCVCc45+BW4JVZyD34O2ZpLd4u2Qa3r70fUvLJbD9ZZTov2xDZZpMgaakO+ekfapVd7XjRe7v0iI0IuyAkukZpR+YxUTajkgdeNfBNHpa8L0LTEbn68Zw8Vw7SXJdWk32MFDPV7u7sk0ZDf22tm8kCgYEAxOAPHEJ/c2+XNpOovqGYjd83HJk/6XDQCaAaoIGNypn1m3rhWIhbTd6vvRANXTzZkbG5YTdBXgPdWfcVOrOZpvJyNUcCwTjL1MohWcuVoGUcujXIvLSOX+jGMfp4OyrwY9/C+xcFHYAB+4H4S+1gtvyMdnsR+gbOWAsjLovoW4UCgYEAtXniELlrTn7rZA85BZ1tr+FfUUQBqKcJ2Y/PCxJBbiXW6Z5ZxprGGKfYvzoJzM7j8CIwpnBwlPprH4jRIiF1D4bpbiMHW4zGdm4MQaF1gmhRHc06Wf+OCkiDRGYstNRchFW0XCoz6XdL5Yf0d7eTbYDT3QObv9Q3+59w+alMndMCgYA2c3n6aPHzLXP3JlPPlcYpxwjpIgGpQQhpAR0cM3jSBk8F17Cn2d7W5LZaZiwsE35ha3RITQ4JQKxiC5KVj8L5h6jKz38Vje3iRp1+xm311MFjRyjj+FRAdpMKk6nZQ4EFPoghBjbl99eUhlf0OA09zw/SXSuVqXg8pafvYca8OQKBgCzW5b2eIt5tS7oYbM1vXYfElumcdRB33V4tQ0oIA7S0ksp8ftAss9+psUtmir9ZK0bNG9o0FA0rZsaO5qWZQLRKcGjvLMU5Hlxqyop423ESV2CWseFE3JK74ftHh6pSgl2MU5kmMFdyS4V2NzhRWpMG7tJALv6R8BczCRYW98BTAoGABVRPGHdvh588j2DO7WQ6Lj3PiKu6LOha1IeUz+aiqhBKJXOJ+JHQja5LGLLRrCW5r2P0zptecK9Mx+v/QmK6mR2fd2HcjFTMv2K7zDJQYUs8JuyCgc4/ljwkm8yzEMVRJQurvDrrg9gQrFX4TPkt3DIR0DbM3qj1Y6krm9VG/ig="], "keyUse": ["SIG"], "certificate": ["MIICmzCCAYMCBgGPubuH1jANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZtYXN0ZXIwHhcNMjQwNTI3MTEwNTI2WhcNMzQwNTI3MTEwNzA2WjARMQ8wDQYDVQQDDAZtYXN0ZXIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCLkCZwyU/gQ75OXKACTHOX++MRqZvRy2y2kzs4m3Mu+8ufjzG5oSFydyZbzLSm+V7gUSNola2TyLRURuUp6paCB4FyajCnuETynJpmMSz8K8ffkP1YRqALJkty93gsEnK0cQJwfYyZzAP/K6gP1BLJ/3gjExP8oZ6VR9vrmpsRGCBG/7JwzYDm9bsUTDrFRgtmAGBDCL0felC5cqTsliU35NVB1Zfk6K5J4uYmOLcBEuK9L5BiFEWiTIOsUSjxBILqiOCzlTXvs8KaT9IzvByT+B6V7w0tN0qRlQPtHIB8AKA3dzU7Cwf3Rin2KhuGOzvczhYBreG/tI2VrO3JH/+fAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAArnm7thZbzIj9mO9AQ1WnHOsRkwQUKT8/3PyyxsTSmG1wqFfnoL1Oijn3N1wZWM12vlI363C3WjatmmO3KiycJ0Ti2rGFJfe8FAxZJVEQDLrQdcIpuZ9fp63CubVB3RLtTRGIehT5UYPYhdYXMY6HfAIWVtM7nd0XsE3QLbaFzFvSWlExXQq8Ffav7l1nR5g2CAosmHMON6/bfy9yAKPwxXty3rP8lSAEWP2oeZp/jJNOBLtj/CQ8xhgKqbiVpmixi9uMlR4B8QzDeDNXI5OQOsr/6bdKMAb3SplFjS6DIaP5AFvbT60n659Rfw3r9MrPcy12s8H8n1EfFw5KPa1n0="], "priority": ["100"]}}, {"id": "53c3a1ea-09a0-4ed3-ac51-e570e9729f81", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["e338ebed-c36b-41c9-be80-d83ae394499a"], "secret": ["qeWnIteF9-muoUy_YEBuIQ"], "priority": ["100"]}}, {"id": "b0bb3105-5681-4129-adae-d10f97525498", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["e2d45b76-8def-47a2-b814-de6bffc79d59"], "secret": ["bdDepVDOx9cZitL7n8FQq_gIU7i-gyPAbSiORG-ov8oJAceZNoiZGxULi7BG204Yv-cHZVQ3ywjjIBiPyyB80w"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "71815286-d5fd-4ba4-aca3-0d987ac194b5", "name": "hmac-generated-hs512", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["585ed32b-049d-45de-b99a-b7338c4ef476"], "secret": ["XE6yiOkM6ypHeCmC-gYHoItZBi34HGDyMrl2vDtHtMeqmG1P23YdaSmVQuCEVFOGRUoBvtBATsH82hl103N-ZCenYZnF6-1mvNRMqvQIrTDAB1EvvOrXO9hbRb6Px87bBBB4cslJCWaeFMEGhza49_L1Cw4kxGwwrmr4H_3gjD8"], "priority": ["100"], "algorithm": ["HS512"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "9eae1882-a82f-4213-b8dc-b6403aaa8bb9", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "3d896751-d40e-4944-a715-e75dc22c5cf7", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "11c4181f-fe71-4128-b354-00b687a268b7", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "a4a2af23-c817-4f43-8e44-94b80979e1d2", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "70ec056f-536f-4ba7-a401-453a559804e6", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "c17eb4f1-cb22-490c-89d9-4d8245578ea0", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "f7a15041-2da2-40f0-b9dc-7d798da2c97b", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "585cda3a-1f5f-4f02-a7d7-1d24e1f30b3c", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "45011fa2-3e31-47be-92ca-122f05d07698", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "9c474c7b-e1cd-4c34-b0ab-88c90e01c1ed", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "1ce3c82f-7a3e-47a0-ac01-2814496d0f3f", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "d4537087-38f6-4a67-90ec-eb3e27bf7036", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "ef71e92e-b104-42de-a7d3-90d35c39bf8c", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "438808ed-a20a-493c-852e-34f8ec9429fd", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "72b6d209-342e-41da-952c-08171e3a3ae1", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "9a2f87c0-df9c-4e4f-959d-297c6c97d809", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "f202b482-0eae-41fb-931a-34ac109c3596", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "fff9e180-d446-42d9-845b-9bde31dcee99", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "e61e7b6f-2c10-4d79-9cb2-4ae45d886c47", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "34b873f9-8b6d-4bf9-aa53-837b0216d671", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": true, "defaultAction": false, "priority": 80, "config": {}}, {"alias": "delete_credential", "name": "Delete Credential", "providerId": "delete_credential", "enabled": true, "defaultAction": false, "priority": 100, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "firstBrokerLoginFlow": "first broker login", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "parRequestUriLifespan": "60", "cibaInterval": "5"}, "keycloakVersion": "24.0.4", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}, {"id": "Migration", "realm": "Migration", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": true, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxTemporaryLockouts": 0, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "af4f1ab6-338f-4688-a754-a21adacbfb58", "name": "default-roles-migration", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["migration-test-realm-role", "offline_access", "uma_authorization"], "client": {"account": ["view-profile", "manage-account"]}}, "clientRole": false, "containerId": "Migration", "attributes": {}}, {"id": "4cf00611-63ad-4798-8f27-35870c948c17", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "Migration", "attributes": {}}, {"id": "423043d7-ec2b-4975-abd7-ddf80486689f", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "Migration", "attributes": {}}, {"id": "eab5811a-e514-4092-9c11-d902514146e0", "name": "migration-test-realm-role", "composite": false, "clientRole": false, "containerId": "Migration", "attributes": {}}], "client": {"migration-test-client": [{"id": "f64ae467-4f51-4023-87e9-865da81c29cc", "name": "migration-test-client-role", "composite": false, "clientRole": true, "containerId": "0e3543fa-6d38-4a9f-8810-151adab26f7c", "attributes": {}}], "realm-management": [{"id": "253d025a-1e56-4a5f-97a3-5d4e2a00b0a4", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "8efba442-2cf2-4337-93d2-4642ccfae50d", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "69fb62e2-a68f-4583-a8b5-5e0cc497d1e5", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "bf23f119-4b81-4f1b-833f-2d0f01b26284", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["view-identity-providers", "query-clients", "create-client", "manage-events", "manage-users", "view-users", "query-realms", "manage-realm", "manage-clients", "view-events", "impersonation", "view-clients", "query-groups", "view-realm", "manage-authorization", "manage-identity-providers", "query-users", "view-authorization"]}}, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "91ca08c7-2adf-466a-ae24-2278fec0fb71", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "e46d5ce0-fcfc-4410-860a-168f9accbd28", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "459ff65b-72ae-4910-9185-3fbf61aad194", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "c5abd11c-39ac-4b1f-83ce-2a665a3f0908", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "b9d97a25-180a-49f5-b4c6-ae93d31cd563", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "1a1fa059-1bda-43cf-a80f-ef3e242e9bde", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "88d06055-70a2-43c8-a8a2-e2b4410b4aea", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "7d14a29c-242c-4213-9126-375e84f2b3fd", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "36fe9f13-3f71-465c-9139-59191622bcf8", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "ffc7db1a-f81d-434e-94fb-4a391fc18f7f", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "5e84e256-b2fd-43f4-9424-933411d46f6f", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "ca85a3af-3c77-472a-9204-0194a0177a5b", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "30ebf8da-dde2-48f3-843a-519d3b865cb5", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "2ed65f06-f886-4281-9af1-feebb9af165b", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "d2536399-6d26-42b8-afdb-a7ad0f560907", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}], "migration-saml-client": [], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "17da1ee6-9e13-4497-8364-7a287320a9a1", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "e6071292-ce71-4916-81ee-3956635dce3b", "attributes": {}}], "account": [{"id": "aac16cb7-d1b3-4667-969f-942bc397b7d2", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}, {"id": "dc5ebb15-4d78-4d53-9498-9173217a549c", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}, {"id": "da95329d-53aa-484b-90d6-ff590e4f7b4e", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}, {"id": "9700a00a-bf98-4e68-b9ab-f57d0b9c1769", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}, {"id": "6d284670-2ad0-41a2-80e4-5343d5fff85b", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}, {"id": "938898e5-2ff7-4024-9bd0-71b62eeb6d8c", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}, {"id": "1bf1fbea-18f0-495f-b5be-8343b7cc86ea", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}, {"id": "4eebe6a3-b78b-4933-9944-d743df578a37", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}]}}, "groups": [{"id": "69a9acb5-68eb-405c-9717-3d2fab0d6e6e", "name": "migration-test-group", "path": "/migration-test-group", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {}}], "defaultRole": {"id": "af4f1ab6-338f-4688-a754-a21adacbfb58", "name": "default-roles-migration", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "Migration"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "users": [{"id": "cf47dd8b-3719-449f-9892-bac9f8ae7ef7", "username": "migration-test-user", "emailVerified": false, "createdTimestamp": *************, "enabled": true, "totp": false, "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}, {"id": "47611b1e-6e38-415f-99b1-8babab008505", "username": "offline-test-user", "emailVerified": false, "createdTimestamp": *************, "enabled": true, "totp": false, "credentials": [{"id": "b22fe342-e3b6-4e31-a76c-7f7ffdeb83ab", "type": "password", "createdDate": *************, "secretData": "{\"value\":\"kNwotFPNeuwelpT1HWt+E4ONXFK6wjd+h0zbzNBRGwOqacAjeY7vYN9QZQ46DlEKSdn04cEU/3RvX8WPcRegxg==\",\"salt\":\"rEIJDbs+BQqpx31v8mONWA==\"}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\",\"additionalParameters\":{}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "d17942d8-a654-4901-8e62-0ca7341a4c63", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Migration/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/Migration/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "ac9ecafa-e49b-4f88-a97b-3f7d8445e3dd", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Migration/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/Migration/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "3bf98f71-3180-4f73-a659-d7f1dfe8ff47", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "631c208b-3f72-419a-9059-de2f4e74b111", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "e6071292-ce71-4916-81ee-3956635dce3b", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "b14fa52b-4a72-46fd-ab5d-40e4cf76471d", "clientId": "migration-saml-client", "baseUrl": "http://localhost:8080/sales-post", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["http://localhost:8080/sales-post/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": true, "protocol": "saml", "attributes": {"saml.force.post.binding": "true", "saml.multivalued.roles": "false", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature.keyinfo.ext": "false", "use.refresh.tokens": "true", "extremely_long_attribute": "     00000     00010     00020     00030     00040     00050     00060     00070     00080     00090     00100     00110     00120     00130     00140     00150     00160     00170     00180     00190     00200     00210     00220     00230     00240     00250     00260     00270     00280     00290     00300     00310     00320     00330     00340     00350     00360     00370     00380     00390     00400     00410     00420     00430     00440     00450     00460     00470     00480     00490     00500     00510     00520     00530     00540     00550     00560     00570     00580     00590     00600     00610     00620     00630     00640     00650     00660     00670     00680     00690     00700     00710     00720     00730     00740     00750     00760     00770     00780     00790     00800     00810     00820     00830     00840     00850     00860     00870     00880     00890     00900     00910     00920     00930     00940     00950     00960     00970     00980     00990     01000     01010     01020     01030     01040     01050     01060     01070     01080     01090     01100     01110     01120     01130     01140     01150     01160     01170     01180     01190     01200     01210     01220     01230     01240     01250     01260     01270     01280     01290     01300     01310     01320     01330     01340     01350     01360     01370     01380     01390     01400     01410     01420     01430     01440     01450     01460     01470     01480     01490     01500     01510     01520     01530     01540     01550     01560     01570     01580     01590     01600     01610     01620     01630     01640     01650     01660     01670     01680     01690     01700     01710     01720     01730     01740     01750     01760     01770     01780     01790     01800     01810     01820     01830     01840     01850     01860     01870     01880     01890     01900     01910     01920     01930     01940     01950     01960     01970     01980     01990     02000     02010     02020     02030     02040     02050     02060     02070     02080     02090     02100     02110     02120     02130     02140     02150     02160     02170     02180     02190     02200     02210     02220     02230     02240     02250     02260     02270     02280     02290     02300     02310     02320     02330     02340     02350     02360     02370     02380     02390     02400     02410     02420     02430     02440     02450     02460     02470     02480     02490     02500     02510     02520     02530     02540     02550     02560     02570     02580     02590     02600     02610     02620     02630     02640     02650     02660     02670     02680     02690     02700     02710     02720     02730     02740     02750     02760     02770     02780     02790     02800     02810     02820     02830     02840     02850     02860     02870     02880     02890     02900     02910     02920     02930     02940     02950     02960     02970     02980     02990     03000     03010     03020     03030     03040     03050     03060     03070     03080     03090     03100     03110     03120     03130     03140     03150     03160     03170     03180     03190     03200     03210     03220     03230     03240     03250     03260     03270     03280     03290     03300     03310     03320     03330     03340     03350     03360     03370     03380     03390     03400     03410     03420     03430     03440     03450     03460     03470     03480     03490     03500     03510     03520     03530     03540     03550     03560     03570     03580     03590     03600     03610     03620     03630     03640     03650     03660     03670     03680     03690     03700     03710     03720     03730     03740     03750     03760     03770     03780     03790     03800     03810     03820     03830     03840     03850     03860     03870     03880     03890     03900     03910     03920     03930     03940     03950     03960     03970     03980", "saml.signing.certificate": "MIICuTCCAaECBgGAay77dDANBgkqhkiG9w0BAQsFADAgMR4wHAYDVQQDDBVtaWdyYXRpb24tc2FtbC1jbGllbnQwHhcNMjIwNDI3MTMxOTQyWhcNMzIwNDI3MTMyMTIyWjAgMR4wHAYDVQQDDBVtaWdyYXRpb24tc2FtbC1jbGllbnQwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQChHhg8hfuVngRkyMW7LzBTjL2TKBBV7biXEKRFfoJwTwXPMjRyqvzfbTcb7gsh/B1w1kkmN9Gzigy1YVe3JC01AUGZ9/a6net+q5dxFosBv0frO20tC6/60nIO3kuCmmbYVBJNWFrBXtrioaBWkhUaOoTxkzpveJkE+Yr2ViSfxhJjFD35g7oMuBao3AkYIgDCfra9Y1mL7GHzwGxrpdGDc5ojQG2yJ8IuIyPD79OpJdQ98UoKBs5/odZmF9TRuogFSjgUqMRDw7Z7HiDChBXC9ezRp4MDNFR8hSPZRf7dTqXLH3bpx7BMf3b3g0lveN8nWw/BOrawl7MH14S/peF/AgMBAAEwDQYJKoZIhvcNAQELBQADggEBAJuNkA3YcHIzMx87yaAMnZvwqST7x2++PxoOiPS9+TA7rHg/2JdC0bSmga0Zw4MI9mPGExDCVVmjH3pW3J0K+f5McNQQeUC5LVVx5cEt8m0p8Ci5dyF3binBMVJR6F7oh39N12CRyyfV0wSIhZWzFwDbd58eNHJz/ie/Jae3o2aUsH4O+ifoBykl5BAeMyn2Q/glMejt9xL/QL/gf3HZyX5FnEFlPpPFUAd/ixIglVmVTqH4Ozhnu+GMm446ArpozD9d4yXJ+UyD/FDXuB3AAFOgclAXhLzSGLQHsN/a817L2p28m20PADiv6YtdJ3vDd6Hr9SUrlurmg70gr3RuH+4=", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "false", "client_credentials.use_refresh_token": "false", "saml.signature.algorithm": "RSA_SHA256", "require.pushed.authorization.requests": "false", "saml.client.signature": "true", "saml.signing.private.key": "MIIEowIBAAKCAQEAoR4YPIX7lZ4EZMjFuy8wU4y9kygQVe24lxCkRX6CcE8FzzI0cqr83203G+4LIfwdcNZJJjfRs4oMtWFXtyQtNQFBmff2up3rfquXcRaLAb9H6zttLQuv+tJyDt5Lgppm2FQSTVhawV7a4qGgVpIVGjqE8ZM6b3iZBPmK9lYkn8YSYxQ9+YO6DLgWqNwJGCIAwn62vWNZi+xh88Bsa6XRg3OaI0BtsifCLiMjw+/TqSXUPfFKCgbOf6HWZhfU0bqIBUo4FKjEQ8O2ex4gwoQVwvXs0aeDAzRUfIUj2UX+3U6lyx926cewTH9294NJb3jfJ1sPwTq2sJezB9eEv6XhfwIDAQABAoIBAA14ji81Rpk2WoiwrrOdWO2LZWhR37fHhfj/d7SlxLMuMD6oJHF38Wee4o4vw3eGdFG0YO5UmXBo+PILXwGdgTge0ETW0ia9QGzZXPKkH2A4hYNZ9yFeikqu4MF0fZzAWxv4P4V+/yuOyj9LozPJyADm5qmqFScvzp93W9KH1k0TaOh7diJacjMY4XiDKwV3/+g85+W/rgyfEQ1FjEJWxyQ5EwBrHrn7zlktQZCKDWNVKsHDc32+dub+SZZsrolgDEmsfShjo14PFghS87N626Fcz+DgNv1p14DqLgwA/y7sAyEGckk4u0xLKZ8MCcfAZc8KKgu1Nw4ySiBJDvFHJqUCgYEAuUnAeiWrL5BFgBw5QrJ44jKYs0zOxWi/2y7ofqpFU0d5iG+Zhoxdp5P2w6J+np0rTHeV3qX9+iYpAgKm1ixi4eih33ty1AyuC12nUd21sJtyru0HqrJPAQTb9ZjSP9vXJ0h+UE3gab8KMGTsLo/YR9B93KofHHDh0fvkPAZbtU0CgYEA3pru9kUdH3MTmp5z7+CyGGhJ/K568GT/FWNVxDoZPyOyKD6LKwa+Jsv+siIRRZ1AKWeNp451PEd54IaP2tUn6s2AbpvuElWqL4vFD/Q4j2ze3vkzNHDoFhzUU/ggz2J+hengUxQExzGb9qYijDOk6PAhAlIsLPnTQCoNb7gMG/sCgYBS3nBucPB6Kl1bKcRWVzoHeLeg4YZM9kdwgS9Mj1zIHVx2r+sLKTYt39hqsP9Oc17NKWLnHHhV68FFe+ggO+PRw9i5+h1mR3GD9dQColVDcpK9N5fJOUxqWUEDO+E03F8C28JaqinnQaEN+eVEAeOqYs4X5iJZ9waYgr8WNYXItQKBgFMoCZCR7PksoewmuSSpCZ64hrsP6vkWoXu56fYtxfSiPxFtm8ts72fK2NBujYk13xwYhQBM5VqpUMwhOGgFLK44KnwnX94VhkfQrMLy9pYNk4w0B37Vlr17842SqZ8PKjiYT6Z5WfeXfq27DjjVqgsieRJG5B3BDbtweih71no1AoGBAKddnbLbLY3Znp9ihL85w2WwY2bobABgwee9sTvuQuXkO+19Mm0ER1D3XbtWSIuWgPYzRnUTsgYe/P9qVr1Wrhyf2DC82szbfIa0vez4tN+8oINjsNJhW8gUpPB0TrtoMkCYjw8sJ4BxCgVZaYE50+G+cf+SvNr3Fg5GhfEPAFw5", "id.token.as.detached.signature": "false", "saml.assertion.signature": "false", "saml_single_logout_service_url_post": "http://localhost:8080/sales-post/saml", "saml.encrypt": "false", "saml_assertion_consumer_url_post": "http://localhost:8080/sales-post/saml", "saml.server.signature": "true", "saml_idp_initiated_sso_url_name": "sales-post-very-long-name-greater-than-255-characters-0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901", "exclude.session.state.from.auth.response": "false", "saml.artifact.binding.identifier": "ZDisLXkadz6IlDoL8l343V44KP0=", "saml.artifact.binding": "false", "saml_force_name_id_format": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "true", "display.on.consent.screen": "false", "saml_name_id_format": "username", "saml.onetimeuse.condition": "false", "saml_signature_canonicalization_method": "http://www.w3.org/2001/10/xml-exc-c14n#"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["role_list"], "optionalClientScopes": []}, {"id": "0e3543fa-6d38-4a9f-8810-151adab26f7c", "clientId": "migration-test-client", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "backchannel.logout.session.required": "true", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["acr", "web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "3fcb104b-2354-428b-abbd-9ba9ea5e744b", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/Migration/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/Migration/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "dd1bfe11-ae96-4408-a3ac-33ebb265e876", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "adef1610-70ec-4282-88ef-bcb26b1f5edf", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "57b24be6-e4a9-4a26-8ef8-f04f0caca9a8", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "bb39a0b6-af13-42bb-9919-f332cb052bcd", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "4a915c07-ffd3-476b-9831-d35bf9dcfca9", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "e3027f70-ee80-4368-b16d-496208eb9c5d", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "93395631-9a98-4545-87e0-4f1887613846", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "236c6d56-04e4-49b7-ba9d-b8c54d19460c", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "51faf2f1-3d0d-4633-9cdb-2d3524059a22", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "6a7ce404-535e-4ab2-85c5-f328d6fcaa5b", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "f2d58749-6fb1-43dc-bb74-32bb744a58d5", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "155da808-0318-498a-ad75-1a912b79665b", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "ef75d675-b321-4b37-947c-1d4b2978c27b", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "fc2cd4d1-769c-43aa-b0ee-1cad01e42f05", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "47a9e7bb-e320-4b07-8423-80939e4b87fe", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "2cccf8b0-f67a-4ecf-b15d-a5fe75d73e3f", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "94c91679-3f64-4d4f-9641-4590b950f06b", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "fa7ba608-da25-4a3b-9dfc-b959b983755f", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "4fe835b4-4920-4c54-9c43-1b295fcf72df", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "cbead812-740c-44de-b943-f910e2609556", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "2225ae16-b3e6-4034-b63f-56c4a342e69b", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "115ec7bf-3c90-4770-b76d-1e77d189a65c", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "08a01457-386a-497b-b1f3-4e5c60f1c339", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "1228a28d-a688-46bf-a3cb-4ae2ca8b107d", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}]}, {"id": "69fe9567-58ba-485d-8c53-95ee1126f698", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "87ae4fd7-ee81-4227-b318-d20221793e33", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "e8124dfa-07f9-4944-9a43-c135fea9b3ef", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "4e4c0885-8508-4c6c-815b-0f6384486ba4", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "6e26ea6e-8d9e-4bb4-a335-df23baea8a89", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "7b7b4a03-9b67-4a0c-a154-e36d8b2e251c", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}]}, {"id": "42440d09-d43b-4ffd-82e1-d646915227dc", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "93956d53-de49-4939-9f06-24c9b1837eb0", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}, {"id": "83c65b82-1422-4057-98cc-548d021ff543", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}]}, {"id": "e2dd44b1-d42e-4357-9481-f9d74fc2eaf6", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "4d6c37f2-d14b-4f4d-9e54-82382e0f2b52", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "97fac94d-e9b5-420e-8af6-cf9b754224a7", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "f84a21a0-6286-43ee-8fbb-2a150e70521b", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "2853551e-ee98-48da-aefe-7ea60ccc56c8", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["roles", "profile", "role_list", "email", "web-origins", "acr"], "defaultOptionalClientScopes": ["phone", "offline_access", "address", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=********; includeSubDomains"}, "smtpServer": {}, "accountTheme": "keycloak.v2", "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "119720bf-d07a-48c9-88fd-cc6800b90b70", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "6b9af018-656c-4dad-a602-e5239eab39ab", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-full-name-mapper", "saml-user-property-mapper", "saml-user-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-address-mapper", "saml-role-list-mapper", "oidc-usermodel-property-mapper", "oidc-usermodel-attribute-mapper"]}}, {"id": "93d30870-683a-4e2b-88b3-af5366c43b05", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "e3475a40-1377-4376-94c5-cc0e51858abc", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "5f2a9e9d-1dd9-4181-bf38-c5d91e560d08", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "6501d527-b9d2-4c7a-8425-281357563d19", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "f64c4e61-1bd0-470f-965d-2818f86893c2", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "ab149dc9-2675-4070-84ea-5bd4399dfee1", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-full-name-mapper", "oidc-usermodel-property-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "oidc-address-mapper", "saml-user-attribute-mapper", "saml-role-list-mapper"]}}], "org.keycloak.userprofile.UserProfileProvider": [{"id": "ffbd35ed-cdfc-4b25-977d-470bd989865c", "providerId": "declarative-user-profile", "subComponents": {}, "config": {"kc.user.profile.config": ["{\"attributes\":[{\"name\":\"username\",\"displayName\":\"${username}\",\"validations\":{\"length\":{\"min\":3,\"max\":255},\"username-prohibited-characters\":{},\"up-username-not-idn-homograph\":{}},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"email\",\"displayName\":\"${email}\",\"validations\":{\"email\":{},\"length\":{\"max\":255}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"firstName\",\"displayName\":\"${firstName}\",\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"lastName\",\"displayName\":\"${lastName}\",\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false}],\"groups\":[{\"name\":\"user-metadata\",\"displayHeader\":\"User metadata\",\"displayDescription\":\"Attributes, which refer to user metadata\"}],\"unmanagedAttributePolicy\":\"ENABLED\"}"]}}], "org.keycloak.keys.KeyProvider": [{"id": "a5da6470-4334-43ec-be81-9759b1c531a3", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["76340352-dab2-4aa3-bc18-44266977c8fa"], "secret": ["hRLdio4uEBkmzJEfHH5Miw"], "priority": ["100"]}}, {"id": "2cf43435-0a7c-43e9-a59d-711edeb5701a", "name": "hmac-generated-hs512", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["62386e4c-09d3-477c-b432-b7111c061ac0"], "secret": ["XDB2wLdPykSLg-hAxIMFaWQvsJjHEAu7nsO0wTSHir2lgjxhAKyfcGuwB85lt9pVYPpUBWzIOM_P0fXUvBh2HWHUeNaCaeAS1ac5C-9Xh3wb4ifuNbKKXe-uD6O69OuHrjP_Naps00SFB5tUvxvIsMmTkEhBhIxCVlJg9yKMqqI"], "priority": ["100"], "algorithm": ["HS512"]}}, {"id": "b9e22c38-bde6-464b-b121-bcd5dae6f615", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["c8a977cc-63ef-4fec-ac74-0c03bbde0288"], "secret": ["3RkkhRqk4AkTm1opSIe4AsM-X5azpUQ5RDA-3fpAjs5a7Mn7S-r5ON5u6tJIdRcOFfe81Rodq8uE5IBqmxJvsg"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "51cbfea7-ca8b-479d-aa9c-9df7df9ccb26", "name": "rsa-enc-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpAIBAAKCAQEAlr7S8QcT9p5rwbJCm8HktgcUVVSSkCfZGB8Qo390pxoiaSYEVy2ROZBP5q/OMZ1dT4ztYCyOeanfk92kTE/xU66f8gGnx+hi0ZS4T95yXFebo+kfELGCYTkicg3ruO93CJRRnLHtskDBOPBXMNj/bM3TLszogdg8XPQPYC9LbMp4iKAPLk0L6avxQYhjxj90VMLg1AlSUQa/ePUZRmUGSgIGFWoVs72jiMFnLcA1UyqrobkRcL6tgUXd1XZBbv4g98UYw+Q5RXmAr4CmzgNgeLwyWfJPh9VPjCkpuIijG8w5948hEMsQJ/UoSWF4C61V94UVHCGiMwmgba36xUXeHwIDAQABAoIBAQCAsZzIpPBIJWt9S8BN1TR6qoSTbMZLR5mxXCQKUS+30Tn7Zrdh7ccDy85K9WAlP2GAHqIw7xkbiQkU5a30cHNoq7ZlF0C0DBMBNeQ8W35qk2iOgOFCkSjr86kSkgviMJ4Atw8NpWF2Qo4zHAxz6W3256kZVVpSL9cozDusM/RChSy6XCzpYcpr39ifTeoghKtDc4Zx/FySKnkxrNEzptRJEdIIh/F0XYpNRglkEzbySyay6ziLtqBMcVb6tT/QoPUoH/RkZlqivxCNdqyo/f37j6RF+Vw/oiKrRLY5CAMgsiNiqiYR5/j7fnUhnl6P69pjA/G1kxAe5N3BzNrySNtRAoGBAOZub+w5ghD4KIREnoXReoa/A4tj550tlO2YWqGq3wkCMQ9K26sXbzFZiIhF7t74Y5vxhIlhbr+f4jFEb+1cfZ2AGC/88UozLONzw2SF4hiNt/7FTklk2R6JzOKqq77xbteTcRsUYyjPegEQAK671xjTpa1ZDphwUuR+OWUOPTpdAoGBAKd42e2UjrZlBDS0MBg3/4qZBRVWWXsEkss9/6xvKxCnWj4hi8ygNZkSlkgIM1ANQj900xWq7eKS8somzG1uKhCUnXNp4S0XlK6GwPlCEayJHtTslXIDefPfugXkD1S8+gywIxJGicSAPq4sip1pvdTllIKO9KrRonYsjdQ/dUqrAoGASEW9Dt8en0nmE04eViRUhKymZPKps0+XlUmiq3AXUhEA6CX3bdsEwfqt6ufcOsy3GTMP+bK4dJul6wPnAxBuSZbifzKbT0Vrv88QKpgfSMoyOlMR8c0SmBRqUXFsOpAozq5s3CxxyVwBjRHC1QsPNLc1EICNJlyccxPv1nU32PUCgYAO5yo9giLRCFw/dGlzPkh3RPa212BPGjEV9QZzidEDQmE9di7Cnt8mKAlHj7NBRGzIyNSf8P4KU5lXc+xtMIh9wTvhsWJgaODKRyOVCtQXznv35rXF15laomYjcBANMmPuDR6R2cUYv3DUfxI2v9osv+FfXOlY6h7S9VQJtzvw0QKBgQCwHQIo96V5lcIQlDsZd8b18X3HChfO9EIJkSm3q2uxkQWoFPLSDqRjoBOn/5lTHKuKOtU/ahYL0Ae3e4FCvYRfwmruZdZ5kV6PNV3pHJXOCwXvFhiSKUpcDlNwG2gwk/w2WDM3KAzxQpXDSKBvk5xJiSy3nfR0mBll190/VWRdhw=="], "keyUse": ["enc"], "certificate": ["MIICoTCCAYkCBgGBi/E7BjANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAlNaWdyYXRpb24wHhcNMjIwNjIyMTUwMjMxWhcNMzIwNjIyMTUwNDExWjAUMRIwEAYDVQQDDAlNaWdyYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCWvtLxBxP2nmvBskKbweS2BxRVVJKQJ9kYHxCjf3SnGiJpJgRXLZE5kE/mr84xnV1PjO1gLI55qd+T3aRMT/FTrp/yAafH6GLRlLhP3nJcV5uj6R8QsYJhOSJyDeu473cIlFGcse2yQME48Fcw2P9szdMuzOiB2Dxc9A9gL0tsyniIoA8uTQvpq/FBiGPGP3RUwuDUCVJRBr949RlGZQZKAgYVahWzvaOIwWctwDVTKquhuRFwvq2BRd3VdkFu/iD3xRjD5DlFeYCvgKbOA2B4vDJZ8k+H1U+MKSm4iKMbzDn3jyEQyxAn9ShJYXgLrVX3hRUcIaIzCaBtrfrFRd4fAgMBAAEwDQYJKoZIhvcNAQELBQADggEBABuVb4TSURUMZMyCc1kHO9L/sE9brYK561hqZsXyBKK6RHQUHvB10oc3D0V/Fi84LiUYVvqwHHARBc1ZRntqSqOOf6ImAp3sSULWVDSE49RSFHG03VjhRRhKBbsPPAy5LODukiRo5HXSr+bYbIJICeTfXKkDKyhj9prgrJJ1RyMP4dyVFGg1l0dUhmlISy69X6MaMiL025479KYaXEA4KydwEL3oGoGjnT4r8JIKga1DQTDFKWFHKncfVr7Rr8Rw0Ycs8BdTJxiWJFOZ4aX5EyKDxP1mrMMhmlpgkQs6FIoiPmzG+uj63u+QzscHYtmA4WRrq2LkTZKQmLon4tNd7i0="], "priority": ["100"]}}, {"id": "6f87c426-a7ac-4c17-891f-e7f53eb2c7fe", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpAIBAAKCAQEA+uG0ozYC3VW05488M1FHZLwRcxcdG0iTTvQl6vF3tTKP/pFuCeK93In2nsuEyeYkxTn5KVevKMqdQ1YezcUwfp0RBdT+aBEFTaOAnEABKeOmKq242dBlv7j7+TaiJy11ETVkP4eB+00z+S3HiPoffFrvoEEujqBAdnmOkc3pJi76j1ZlQGrOWYIUIO8ei7fRmPONcGQSPlruyVPvou2Jp0EsrylyumqMuI+18ch/PKCLOEYarml1Xw/1m/YPtlG8lKMAnnOOiLyrnySl4pIYUYJgUHZImUVYincKpP3lX6zFskGwntnXVUrNsrsE8gww6z6qwk4IX39Wj3wbSVTFswIDAQABAoIBAQDI860K5iJZQ2NJ7xMbT8lNyM55T1RDHFjR7wHING3cN4nbMIXhaTsGwYNHImFUjwF48LzA/rkQhWp+GydFTuNPJ2QRNdApbyi+TW6guSu1b+ETYl8kKKa3cOZzce1kUCbrB8cqk9HrhqVQu/iNoTtDElvKEyvFllrMCwXFiBduOrD+Lylq4ea09e8dM1x8UanNGvr0X2RtpzNumDrJ5crQCulHgCNVGCGUO0R4fedDWEK+8dOVuvCwUAMHVHgRmuYbtFOhEFiIqccncdb5wzW7vVROV9mAcKLptjEkEBQQOv0NUPlwx6AaExkMVRfGVAoYKdVfMtKvS8NGAIfvlNYBAoGBAP4sHDWlEVI7shkfhjPRUNFdt156AbOzcL5RvdP0Q2yep8e4nyOASqpCixIYHVVYz9QNv29UtdE/096hEOCkNBpyk7t1IRg7dJi+PvbmdP4nLzWnaKOldiVFcBLZQeDgJKWMBhw0D+Gc0NBZNrVW5j+b86xGszu18J8Ynq0bFwKBAoGBAPyvibLKh9DhP/XDKSPsl9YItUrU0h722Up9jqFVdMmfXD5HV1BrtEVhEr/7A5SMTKwXC6fZ3q5lZeCAD+q3yK6JW1dG0Rp8wBJnhRU2kML5ZWpLNFPUxeiJYlMlVXf84OfIS/PCPkkVz8aipdpLOB7+fs6RkZA/pGBbY+vxEEYzAoGAeSWDKJqN3/kuEUVyTN52u+QEU5apmwJrOXtylET3oh0Y8VvxFhW/ANoIuU4fJR/Yp0UKOsFQi/T1QkbY6Ym6f+Sev5m7dXZZQ7hiAIKlvsHkgZlSibWC047+NS2ydcHAFdvgya6S50E8UiJDrjlGM4lcon9sTIiSsPImJ3IjDIECgYEAsdQvyh3ymzQKuUDRsTCOYtjaDYHdJmF+5oeaWzwy5ro6RrIaZsSFp9X7RhS3nlFmnC3TzBruNjmyCt7VVNBmcT7Fg5SZEFT/L3SfGiC+nBi8IKQwfVbeuRoGkpFRpxHWjghOO4xWHUU2ZoqMwiXxpw5f26xijuvbGNz4Y1HRrDUCgYB4jIVzx8b4F2c2EmpDlE6YRzTzrilmFoGjGkm0Ntdh060WknQCJ6VZWcGs9PheKyK5ssMRR/2PBlHJosMIJm4kkwF1R0eSXlpjTUAP2UJiWeurNJkSjKVblJBz0roMk1HHSyeZcHi8qLsfpJo2z+PTxewULHjvk0jGXMFII40djQ=="], "keyUse": ["sig"], "certificate": ["MIICoTCCAYkCBgGBi/E7KzANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAlNaWdyYXRpb24wHhcNMjIwNjIyMTUwMjMxWhcNMzIwNjIyMTUwNDExWjAUMRIwEAYDVQQDDAlNaWdyYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQD64bSjNgLdVbTnjzwzUUdkvBFzFx0bSJNO9CXq8Xe1Mo/+kW4J4r3cifaey4TJ5iTFOfkpV68oyp1DVh7NxTB+nREF1P5oEQVNo4CcQAEp46YqrbjZ0GW/uPv5NqInLXURNWQ/h4H7TTP5LceI+h98Wu+gQS6OoEB2eY6RzekmLvqPVmVAas5ZghQg7x6Lt9GY841wZBI+Wu7JU++i7YmnQSyvKXK6aoy4j7XxyH88oIs4RhquaXVfD/Wb9g+2UbyUowCec46IvKufJKXikhhRgmBQdkiZRViKdwqk/eVfrMWyQbCe2ddVSs2yuwTyDDDrPqrCTghff1aPfBtJVMWzAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAHsNYa+/NP4o0XmP0Er1Q4ca0tErlV6DDIiQMB+M/+urIDMtRblN5sg/IGzzqK/RtXYnKB86q9F6qSaGryeYGz7w3Dg4xcBGUYsPSXPWt8egKniOsTBeQSBZHx6yO2rBJt/uvapZX8kfRYQDaJDh3JIqCp+UlSbTyGpxh9M3nuv6kKi8k/eO8bDw9X97I0XUaVD8H3LIKUa5VKoavJ86e063ouvE+qukL84reWitAssyDbEtxuQTxfeBubAl7QfFDHmmLyt77vcVgNvMHjMyFSxq/hvGOxMq3i5T9p5ARjRuHb9OIZ2iFWc00q5+87TythXeRwJAIZpqcC6YuXv3DAM="], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "54ae9dac-3216-46c2-a77e-df95b9fa5780", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "080277d0-d160-4fc8-a741-fca40733d025", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "********-373e-449d-812b-df907265ed17", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "270edbd9-160c-48f0-b28c-f1dedd055460", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "0e8fc156-03b2-4eec-920b-8cec1ee7492a", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "95eee741-4755-4d91-8d24-bac0af0c2264", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "a74775bc-878a-48ff-a272-2d3c5a433b09", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "58fe4650-20ab-4c62-860b-ed9ec220009e", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "1ea91397-6f0f-44e8-bffb-5434db694281", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "764dffd9-962f-49d1-b590-484dadd968ae", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "a411a24f-c794-4a51-b46c-24a07b34af8b", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "c98db00e-9f17-42a7-b945-a85397d78664", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "bac7619b-9fda-46f9-8f60-2102db5e8120", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "6e5120fe-e969-45bd-aa3a-f8b37cc0fc20", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "3cdb281a-4049-446b-8f76-68dd2711a95c", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "80e20771-0604-4478-b961-4583d368323e", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "c622d7e6-c67c-4fb2-8180-d1be42fbfe1b", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "6bf21dce-554f-40d6-aa87-f686c170f37f", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "bfceba3d-b90e-4a42-b30f-f6f8c816463d", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "0fa31614-d9a1-4904-9184-4573ea5012db", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "delete_credential", "name": "Delete Credential", "providerId": "delete_credential", "enabled": true, "defaultAction": false, "priority": 100, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "firstBrokerLoginFlow": "first broker login", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "clientOfflineSessionMaxLifespan": "0", "oauth2DevicePollingInterval": "5", "clientSessionIdleTimeout": "0", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "clientOfflineSessionIdleTimeout": "0", "cibaInterval": "5"}, "keycloakVersion": "24.0.4", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}, {"id": "Migration2", "realm": "Migration2", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxTemporaryLockouts": 0, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "fe0dfd49-a60d-4d36-8469-6c396b2b2613", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "Migration2", "attributes": {}}, {"id": "6fd8d647-4472-4f29-bd09-143a2f8b4570", "name": "default-roles-migration2", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["view-profile", "manage-account"]}}, "clientRole": false, "containerId": "Migration2", "attributes": {}}, {"id": "a6931711-21ec-4454-b6e0-f7a2322a6167", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "Migration2", "attributes": {}}], "client": {"realm-management": [{"id": "ee8650e5-297c-49dc-a312-c4b04a3bcd39", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "ccffb6c5-63ff-4e2d-b053-3e508ae5cbcf", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "b878e1c3-a61d-46e5-9ae1-a01a17994408", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "7a3cdc1a-4b84-413e-9ccf-c57fcf3317ab", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "9e48e608-ebef-45bb-8c17-e68145627474", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "8397f0bb-623f-48ab-9611-3e3784ff22dc", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "4945c292-b655-47bf-b853-6a2e864f670d", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "b8c50598-541c-4be5-ae10-c7ab25e857e2", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "de622240-0b70-4871-99cb-24b30c9b7784", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "f29033c0-ab26-4add-9ca3-bf303cad4bd8", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "f0c2f20e-7650-44ba-9ef7-8cae3467e768", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "4b1e22d1-f58a-48dc-84de-9271b948635e", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "49fc51d9-957a-4c1d-9aca-3b85a851f4c6", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "3ab4ee5a-9470-4098-b0c5-c6d71e6f2e27", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "f89758a7-3bb5-4c4a-9242-26f03c783524", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "c6447c99-40f7-4e83-bb32-44456966a025", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "75ba2077-4460-4880-8324-6138698e22dc", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["view-events", "manage-realm", "manage-authorization", "view-users", "view-clients", "query-realms", "view-identity-providers", "query-groups", "query-users", "manage-identity-providers", "view-authorization", "impersonation", "manage-events", "view-realm", "create-client", "query-clients", "manage-users", "manage-clients"]}}, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "62ef0926-17c9-4179-947b-8da243a3ff03", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "********-e752-4d24-b6d8-2ba48280b769", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "364a543b-2337-498b-bc1b-ae007fef08ec", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "7eb37807-722f-4091-b8bd-cf126c8a6050", "attributes": {}}], "account": [{"id": "caf46d94-fb53-4e2b-b745-a495d5a9bb7b", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}, {"id": "464b8bb9-55ff-4cbc-8f61-aba1a108e40e", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}, {"id": "56ac9513-0e0c-4081-aefa-5958676a3bc4", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}, {"id": "79b46149-3a45-4ed5-b3af-cdef7af7c2c3", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}, {"id": "dd3013ac-134c-4020-8727-bbaf29fc127f", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}, {"id": "cb9aec3f-5d60-4314-903b-30c00c55ab80", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}, {"id": "f4542b38-e64a-4dcc-aa82-ebdd158129d3", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}, {"id": "7e55c038-99f1-4a4d-b041-a25661cb80b3", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}]}}, "groups": [], "defaultRole": {"id": "6fd8d647-4472-4f29-bd09-143a2f8b4570", "name": "default-roles-migration2", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "Migration2"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Migration2/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/Migration2/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "96a9ce83-aefa-4ba8-9e19-6b838b552800", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Migration2/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/Migration2/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "4a46da78-f5cf-423a-b3f2-bb07da3f2fbe", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "bab37f84-a2f8-436a-bca6-e966121068db", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "7eb37807-722f-4091-b8bd-cf126c8a6050", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "********-3010-4d8d-90a6-db9990449b23", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "0e80ad6e-0584-4a43-a50e-66c62618cbf0", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/Migration2/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/Migration2/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "c7c9d434-1f13-497e-95e1-84673fd35f32", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "0d154891-3b7c-48f4-92ad-c97df27e20bf", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "d37fc0fd-e2d7-4134-a4d4-bfec108e477e", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "acccd5aa-07d2-4883-8d78-fd55f709bcf1", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "d3b5637b-41cf-4e80-9955-ee66615e06ce", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "56b65c6d-be0c-4b03-b204-5bffe6885375", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "699e8ead-f48e-4138-9361-46f284ef0f48", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}, {"id": "f60da5fe-5595-454d-a40a-fd4eadb5c9a0", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "3728ab78-90ce-4079-a9b5-ecffdb42d329", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "da85fe38-b7f8-4005-b4bc-79c80a577854", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "62991bca-db4f-40ff-b223-4480a697c0b3", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "02006ccb-726f-47d4-878d-5f9223c7e6ce", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "aa118395-ea3a-43a4-a2e3-c921f3261929", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "9345a54b-7aa1-4fab-864e-6be04fc2ccc0", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "fa8c87d1-9b2d-42f6-8295-7e9f32747651", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "348cd00e-7464-4444-b11c-5a07caed9701", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}, {"id": "c600ad07-fc7a-4bb5-9264-264b41d713d5", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "f79c2403-8831-4681-b32d-62507bc17758", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "6bcf684b-8a89-447c-a52a-f2721e2c3954", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "fab89da6-1006-426f-8009-3f011f73f759", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "83a79e37-7142-41b9-a2fe-d168360b502e", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "f39f986f-fb20-47f8-969d-255e74472f19", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "37dc25a0-5929-497b-8ca9-c3ace626eed7", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "d6d4b369-bb9a-4e95-b64c-ffe4c0d8061a", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "cdaa5ca4-0684-45ae-a6e4-a08e5d0da654", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "6a0b3841-d31d-4175-a062-e94534b330cb", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "bfb7b26b-94bb-491e-bd77-492dfdc18ae4", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "6480bbfc-bf69-43d1-aef1-a3d86f8bb2c2", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "ff50f17e-2b22-4752-96d2-7e49ddb5de18", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "3e999ace-5e6f-4ca9-bef0-1ea23a094151", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "4af1af16-08b8-4f3f-adf0-8af7e7c82e3a", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "efbd2e65-9ccb-44aa-943e-3fe2cc065f3c", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "cad4c792-f2bd-451a-92f8-1b182087135d", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "8183099f-1d4c-447e-8e41-1da7b57bfc1e", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}]}, {"id": "1823f7f9-8e23-440e-a25f-800f43203960", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "4d3bef63-2dff-4dd5-97b2-2026252aa589", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["web-origins", "roles", "role_list", "email", "profile"], "defaultOptionalClientScopes": ["address", "offline_access", "microprofile-jwt", "phone"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=********; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [{"alias": "gitlab", "internalId": "6ff5ce97-5018-**********************", "providerId": "gitlab", "enabled": true, "updateProfileFirstLoginMode": "on", "trustEmail": false, "storeToken": false, "addReadTokenRoleOnCreate": false, "authenticateByDefault": false, "linkOnly": false, "config": {"hideOnLoginPage": "true", "clientId": "gitlab-client", "acceptsPromptNoneForwardFromClient": "false", "disableUserInfo": "false", "syncMode": "LEGACY", "filteredByClaim": "false", "clientSecret": "secret", "caseSensitiveOriginalUsername": "false"}}], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "cff606fb-0e86-47c9-84f7-62bce4f09837", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "e45930ae-f2bc-41f8-a7fd-e5d7ad7362f1", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "7f27b976-84ba-4cd5-8777-fe340741ccd0", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "13113f59-7119-4d6e-88ae-fbd4498b5cea", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "5406436b-03ba-4c14-9821-268e7eadb666", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-property-mapper", "saml-role-list-mapper", "oidc-usermodel-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-attribute-mapper", "oidc-full-name-mapper", "oidc-address-mapper", "saml-user-property-mapper"]}}, {"id": "d79b1499-24de-4963-9ef4-6c8ce075a923", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "55d8aaa7-2307-4e3f-9b49-4a5cf7f0980c", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-sha256-pairwise-sub-mapper", "saml-user-attribute-mapper", "oidc-address-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "oidc-full-name-mapper", "oidc-usermodel-property-mapper"]}}, {"id": "b5ca9baa-fcc5-40bd-abff-e5274f94c0e4", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}], "org.keycloak.storage.UserStorageProvider": [{"id": "307bba27-f8da-4c4d-a1b9-3c1bf49f824e", "name": "ldap", "providerId": "ldap", "subComponents": {}, "config": {"fullSyncPeriod": ["-1"], "pagination": ["false"], "startTls": ["false"], "usersDn": ["ou=People,dc=keycloak,dc=org"], "connectionPooling": ["false"], "cachePolicy": ["DEFAULT"], "useKerberosForPasswordAuthentication": ["false"], "importEnabled": ["true"], "enabled": ["false"], "changedSyncPeriod": ["-1"], "usernameLDAPAttribute": ["uid"], "bindCredential": ["anything"], "bindDn": ["uid=admin,ou=system"], "vendor": ["other"], "uuidLDAPAttribute": ["entryUUID"], "allowKerberosAuthentication": ["false"], "connectionUrl": ["ldap://localhost:10389"], "syncRegistrations": ["true"], "authType": ["simple"], "useTruststoreSpi": ["always"], "usePasswordModifyExtendedOp": ["false"], "trustEmail": ["false"], "userObjectClasses": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, organizational<PERSON>erson"], "rdnLDAPAttribute": ["uid"], "editMode": ["WRITABLE"], "validatePasswordPolicy": ["false"]}}], "org.keycloak.userprofile.UserProfileProvider": [{"id": "88cef18c-bcd8-40d2-9e7d-d257298317f2", "providerId": "declarative-user-profile", "subComponents": {}, "config": {"kc.user.profile.config": ["{\"attributes\":[{\"name\":\"username\",\"displayName\":\"${username}\",\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"validations\":{\"length\":{\"min\":3,\"max\":255},\"username-prohibited-characters\":{},\"up-username-not-idn-homograph\":{}}},{\"name\":\"email\",\"displayName\":\"${email}\",\"permissions\":{\"edit\":[\"admin\",\"user\"],\"view\":[\"admin\",\"user\"]},\"validations\":{\"email\":{},\"length\":{\"max\":255},\"pattern\":{\"pattern\":\"[a-zA-Z0-9!#$%&'*+/=?^_`{|}~.-]+@example.nl\",\"error-message\":\"Invalid domain selected\"}},\"annotations\":{\"\":\"\"},\"required\":{\"roles\":[\"user\"]},\"group\":null},{\"name\":\"firstName\",\"displayName\":\"${firstName}\",\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}}},{\"name\":\"lastName\",\"displayName\":\"${lastName}\",\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}}}]}"]}}], "org.keycloak.keys.KeyProvider": [{"id": "0ad3f11c-b781-48ce-8ab4-3f3dfdb1f149", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["aba12ef6-6a76-4958-a542-32de274a755b"], "secret": ["pxKMHb3Z6LDkDfAR31mL3w"], "priority": ["100"]}}, {"id": "012412ec-78c2-4c8d-8ab7-6dc6e62ef61d", "name": "rsa-enc-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAtqlQVdrbBrs9IVX7brNrki4GnvXb97g4DZx1+9UVzDVgo1DWxO65Xty+8onE/0ERlKh1+30UDCtmAw+mRX34jMYGPi/p6Y1lbH9Xt7Bz4vbIEhMzfTF+udEQVxY17ZyChp0aZLNTpqoZGFlulQbCEK5gqCTuMFdglRHeNdXkrsZOYfGWFalkHqBichh4Br8XQBVXNPvdm5Hde1cnPnYy0j1Stq9sqUR1zhsbS9oagGstDeXVby4RukJctWoY1/r17ZFXXdzEgIIw3AfEaNI723bRM98EcPJ8xyCDetpWFJoGlqbGnIo1ISP9nQUmwFeIgEYO5S+gpb2k4FgHDCuCIwIDAQABAoIBAA1NlXk7QDpkRpZ38aDlVk9GJNtM4qFO/F4ZaApYQYcm2Avs5kdfb4a2Iv39BKjmb0ZZYoZXXK9qNbrBWAW1n/V4spmy2aFV1+4n9BZ/tmDwQi/20AgtI6Ka1ErTZkgQ3vy40mRFHzGDL+KzGLenz1hxqr50OhixNGqG650WH9qOnTqvXiUh5IMrGQOlsD7odAF/xr1Mkjsvh9EW6Wui3+JS9hRNlGYex/eWwdyUGVwZlYEjKr2WGq/+QTqVqN+jZLIGwCG4ufqySDtAVC+PS/wT4Mxuk56p8IWbV9o0ZCldiWHAHmVIXEGTBlrlZk6MZokswDKsBtOCQ9CMKurJu+kCgYEA45xsOT9sP6kGQBY2aQVyz8D3QpIXli/dnAls8lGeZRX9vQ4YPYEAAwvqocES7d9aGP5cjGDDNxKakrCj+lmMAPA0uZXdSTwQLAPubVb2leG8TLbrWBRntO2kxZYFW+ifnGc51kgOGEDpl57agxf5tYAJF3BUxU4ajnQH7YkVoJ0CgYEAzXGoScUFHOTBOW+Hh0bFf8MjbmrTke7NCO8dFDtnCPARb9TpUL+5GYsssjdgrlyT6lj68c4rw79dfiI5u06F5RdZ0Z75i0gUG5xEEFTExUc9EmPG4qCTMP+gBy7/AeiFgQS0iKHJNgjC2YblOYRJK5M8hH5xi2W8+dh+5erjUb8CgYBsss00YceOd2gWvwTpDsMP2HW9Awvkm6NX22B+MOBWIjR1Tv/gj3Sz/8Lj0NL7askaWo9UMvnHmUjceSM2cgFciVqErr9pNBf9DTuWWAuW9KD6efjWrZgBXeRswAJ7LBLdnKaQYQTqLEXiNOjh5ldIiIbO/MRfHbetBFis5eTAOQKBgH4rJubnEFTGR8Q2Mk1psTDdbo0JHhWpSlIKRxl9wbFDffHoF8F3fP2q8zAfwkhSoqRxMOv/afE1PP2Rl1LozmH7DTYnI6neAjtSpJpC9PUS4vbAQAw+ASc+VdETk5hgbFznvteKRD/i8YcVyQChtS4FpkzYOnMZcA0gYA5jSmeRAoGBAKRrjJauGx3UczhWmIp1fCbqfuEiaCyfp0FotuiWJzcpXFZoNsYDvO+uC3DmniTaydwKug7JwWbQ7xECcgPkZzZrCIIz5JR9OwfTsEy89dDTQSmYpS5+0tA9BAaEZXupequYqd7oJV1UEZQxdK1Vm//g52EOXD/ozHx0z8uYAI2p"], "keyUse": ["enc"], "certificate": ["MIICozCCAYsCBgGBi/E+CjANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApNaWdyYXRpb24yMB4XDTIyMDYyMjE1MDIzMloXDTMyMDYyMjE1MDQxMlowFTETMBEGA1UEAwwKTWlncmF0aW9uMjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALapUFXa2wa7PSFV+26za5IuBp712/e4OA2cdfvVFcw1YKNQ1sTuuV7cvvKJxP9BEZSodft9FAwrZgMPpkV9+IzGBj4v6emNZWx/V7ewc+L2yBITM30xfrnREFcWNe2cgoadGmSzU6aqGRhZbpUGwhCuYKgk7jBXYJUR3jXV5K7GTmHxlhWpZB6gYnIYeAa/F0AVVzT73ZuR3XtXJz52MtI9UravbKlEdc4bG0vaGoBrLQ3l1W8uEbpCXLVqGNf69e2RV13cxICCMNwHxGjSO9t20TPfBHDyfMcgg3raVhSaBpamxpyKNSEj/Z0FJsBXiIBGDuUvoKW9pOBYBwwrgiMCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEArX8U0Dg+LpC3d3/aIrP2JbGu+KsF7qJEXZTVxHzra9lFomtAuYoIMucSGqpupnmORFwgKPY+R2r2L0qfbCN57G/tjxqi1X+z/aVJP6bebetsnXCjQtI1xUf2GnaKRpEnbMRnF7R396oVCcXbgF0wTTuMMmer5KfQlNDKRhC/mTtAMfRFHVI8RBOtQYSxART2hT6UuKYNHr8rYJCmDzqnifqA5pQ999FB+QveS+mF1rNIbqh2Fbguo2Okyj3uuRRxYu+NvqoKcMlpcU2+6QhQUW+jtmcUJOFIusHgLs73hsA+H557tlRryA073q54Kw7RgQtPN85CsiepCFEYEZ6aIw=="], "priority": ["100"]}}, {"id": "10e64137-5377-4e5f-a935-908741d18bdc", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["e07b429d-76d2-4f9a-b2e7-220670c87ca3"], "secret": ["SPoR1vrlZ5lO6fOAMI6pJ5coDFJ2U9SAGiN0ruN9leYAL3Ib3El-NeA1VtLTFYS9ObtJkNP4llBHKnT8ZlaJ_Q"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "f2e19e56-94ce-4a7e-82b2-117407051e61", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpQIBAAKCAQEAt4yvJyMjIwmfXsyqgJdebEYhwOu9dAen6Kz3qMKqhJ5mF2QGHZCksang9li9OfNAG4yCl1jRltWjS5pWV4eghEb9aXOfwAr3z4XUVoeL974x5YpEH1CjY+ahX4UL5+p0CIQhRsFkrUumT0+5IdrBE9phncQMJ3m5ZMieTcOIT9nB9KFeCnPpw8RbBDyKE8/2elEg3M/OG1jBJ1fMZu8YV4f3ml4Gak63z31osdcMFFjbxJCBvZw10gktHcfgEdvlClppYZ5MHJPcRaMroA/zKW4y6nCIK3wJrmSBHCyIj3iwKN4uzI4VfkcIlZDGLF/nb8ohg0EQ1EbVbOSpT4sDOQIDAQABAoIBAQCBH584USeuxl4vOpvXyqTKVqv4I98reRdOQCAZwJhko2n+mocS1WaG9ZMkulikI9O9jaNxBTKsga5WyURkXy004Rv0yzpex1h9zRALr19v8YT4jcROo1POO7jakaZGiFEIjKs8M2C36Lgeo18Byfp6FO8HJZvHSYJ6vAdhfWSvhrf9+sgDeT8iHGSB2DbUHq5RCjbi2nfk8RZDkGQcRliav0g69jm/iO62VLhF73ssSliK5VjCvwP9qtxTT5oi3B/ZtCvFDk++OxzjYz0b45/ZZIJ5OZGtmFoPoYsDpJUjQmDv8pl+1GOWsnXkLjibpqooniqWE8RkcgpNQlV449eBAoGBAOJJdFjXquAmJVyJGPyUcobUdyLzr1BojbKFVplwagLSp7zqRuD5YbvOi7psvO+1bfE4NB9ukW+gRZ5FNvE9t3bdwil1dkDMV+fGnWkrSvaY0XdQHLuFcmo2Gn9wE4i6R8XrvB8Kx3HS3bPyrgE8UmWNvDSl5bEiypEVfDFUXOLNAoGBAM+moSYUcETxpYgbprmg0/uqVctgH8PXymrevG/LVCYrxHMLyNmJm+zcMEcyn8S04vt89VnHg5AQMBF0azBhgCUzuWbq5qx+PSMaOtcd6/e9Uf6lrT9wGU45MNlxMn4q+KeBTNTNSvT85BXrwiMwd7saBgAetbaJKNnHUvQe7podAoGAA0CnCXfgHuSteM0HrOm9U67OH5R9GpzGHKDKA96Xx36bpp1/c1BiVT68x2s0tAVZlTQHm8QNFxuopIO+LG2Eq5K4bbwgfQHy6YPzU0WqCC1eJWbjYtaiNGCetOI5haeXzIJ8671N7JbpVOLSGXZygL8uy1rvGEK1wqwaYuMhiqkCgYEAlUz4lxmg82FPpIJFKTjC0jNRxo3RqQ1xw89sa3EliuBzOkjh289+ZhqXN8nlaBirDyl0e13hcWbCOPqcy0d/TMBY8O+6dZG6wY9teRhRfmoIF3DmFTOzbjPetKkYJfEdocptBKCq4WUnVb+ScZy6qbN3VwS9oIYT8inHsjC/W90CgYEAs/h4YQUhVWjWN1i1U/HY2X0EM0E3BRW6VbyL5QE62afy4TGMg6T71SzRZpumLMq5BTm0r8t8rnWaghDXEWwpUkrBrFtJfCkRiw+FcBECr79ECEgHzmBNCPvq8UPi9ewgOYV2F6anQDc+Q1kuo6C4hmBJ0ls51ddmBp4q2AuZsAo="], "keyUse": ["sig"], "certificate": ["MIICozCCAYsCBgGBi/E93DANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApNaWdyYXRpb24yMB4XDTIyMDYyMjE1MDIzMVoXDTMyMDYyMjE1MDQxMVowFTETMBEGA1UEAwwKTWlncmF0aW9uMjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALeMrycjIyMJn17MqoCXXmxGIcDrvXQHp+is96jCqoSeZhdkBh2QpLGp4PZYvTnzQBuMgpdY0ZbVo0uaVleHoIRG/Wlzn8AK98+F1FaHi/e+MeWKRB9Qo2PmoV+FC+fqdAiEIUbBZK1Lpk9PuSHawRPaYZ3EDCd5uWTInk3DiE/ZwfShXgpz6cPEWwQ8ihPP9npRINzPzhtYwSdXzGbvGFeH95peBmpOt899aLHXDBRY28SQgb2cNdIJLR3H4BHb5QpaaWGeTByT3EWjK6AP8yluMupwiCt8Ca5kgRwsiI94sCjeLsyOFX5HCJWQxixf52/KIYNBENRG1WzkqU+LAzkCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEARG0ZsJuL6vAvQNmjmPda1moVpTj5sQ3fe8BrtfDAd+Wfz1K/BZfYZrMuE9r0JSrc2t/yI4DjaRl3wlibfT/WrRLe/duUoVwn15kJ5tBqAheyy2sGwLqA0OaT7ltSaHqZ0xCx8RA9uoE25VHj9Ho22B9WQ2Tj1Y/oB3L03SAyVfFFEXZgq6w8zjRuTp6sf3SEfehGQ0nZz8+LtsSrw2GcQjj8grzVCAO3ckDPRZPW6gBBts8KJdXy5+N+6emunq9imQC2OvZaCzSHZBs73I5fsvW+ffIiagIXHXytsWfelK7LBy0ggzprR2M+qgdnu3Z6FMPeZ1CUzsP1V5iMOYIDwA=="], "priority": ["100"]}}, {"id": "960cab4c-a964-41ae-825f-30954a6a3ce2", "name": "hmac-generated-hs512", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["70977eff-55c2-4327-897c-1d4a5da78244"], "secret": ["8LyGloi9XEa7Aa9FrhVHfj2zZsclQ3rnbJpCRS_OqKM0s7pD4kmwWT9F83OGv9I8Jd6Ob-yWJ80i-_aTULVz6mFDRSsGI-KEUVx7D0INWGr_CmCoCAPY0vJd4EqNYlW4bcHiZWZMfCT2mUy4wS6mRyaZUax7UDlH-oXlJh72S2k"], "priority": ["100"], "algorithm": ["HS512"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "070258ab-09e2-4ebc-af4e-2cc5e2e07fab", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "40dd0282-c511-4832-9abb-7f8e68f70499", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "1d69ffb5-f02a-4f60-8604-96c4e8a9702e", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "74d41d49-d5c3-4a43-8d4f-6d2ae6a77d69", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "639fb835-e491-49a1-afd8-cff2322adfae", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "39accd20-7563-4540-aff7-48eea7840afe", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "da2d541e-95f3-4240-8d58-807799736f93", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "f83e1dec-fff5-4660-9365-41042498e296", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "fa59b97f-3b60-4c1c-b11e-0216852eb496", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "bbef0ceb-638e-4ab3-866a-8f9b6d67bc69", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "3e47f2d3-dfd5-4886-aa32-8ce6bc051e94", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "c5beccf2-fa4c-4a31-b5d8-13aae74a26fd", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "0b3581ab-d9b2-4614-ad0f-11963d9adae3", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "68566c6c-6044-43df-9749-4de0e0fd19c5", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "c65a446d-738c-488a-b82a-d65fc93302c0", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "46e305b8-bcf1-4607-b4e0-38d2085e5a82", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "bb502534-32f9-4eb9-af54-573fff919200", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "4fb25433-3ccd-4444-a6c1-f6ce66d12d97", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "889ebaf4-1b81-4041-a954-2105e0aaa36e", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "4aafe830-1107-4a5e-9390-11f5ed23f1f5", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "delete_credential", "name": "Delete Credential", "providerId": "delete_credential", "enabled": true, "defaultAction": false, "priority": 100, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "firstBrokerLoginFlow": "first broker login", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "clientOfflineSessionMaxLifespan": "0", "oauth2DevicePollingInterval": "5", "clientSessionIdleTimeout": "0", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "clientOfflineSessionIdleTimeout": "0", "cibaInterval": "5"}, "keycloakVersion": "24.0.4", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}]