#
# Copyright 2016 Red Hat, Inc. and/or its affiliates
# and other contributors as indicated by the <AUTHOR>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

log4j.rootLogger=info, stdout

log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{HH:mm:ss,SSS} %-5p %t [%c] %m%n

# For debug, run KeycloakServer with -Dkeycloak.logging.level=debug
keycloak.logging.level=info
log4j.logger.org.keycloak=${keycloak.logging.level}


# Enable to view events
# log4j.logger.org.keycloak.events=debug

# Enable to view loaded SPI and Providers
# log4j.logger.org.keycloak.services.DefaultKeycloakSessionFactory=debug
# log4j.logger.org.keycloak.provider.ProviderManager=debug
# log4j.logger.org.keycloak.provider.FileSystemProviderLoaderFactory=debug
#log4j.logger.org.infinispan.transaction.impl.TransactionCoordinator=OFF
#log4j.logger.org.infinispan.transaction.tm.DummyTransaction=OFF
#log4j.logger.org.infinispan.container.entries.RepeatableReadEntry=OFF
# Broker logging
keycloak.testsuite.logging.level=info
log4j.logger.org.keycloak.testsuite=${keycloak.testsuite.logging.level}

# Liquibase updates logged with "info" by default. Logging level can be changed by system property "keycloak.liquibase.logging.level"
keycloak.liquibase.logging.level=info
log4j.logger.org.keycloak.connections.jpa.updater.liquibase=${keycloak.liquibase.logging.level}

# Enable to view infinispan initialization
# log4j.logger.org.keycloak.models.sessions.infinispan.initializer=trace

# Enable to view cache activity
#log4j.logger.org.keycloak.cluster.infinispan=trace
#log4j.logger.org.keycloak.models.cache.infinispan=debug

# Enable to view database updates
log4j.logger.org.keycloak.connections.jpa.DefaultJpaConnectionProviderFactory=${keycloak.liquibase.logging.level}
# log4j.logger.org.keycloak.migration.MigrationModelManager=debug

# Enable to view hibernate statistics
log4j.logger.org.keycloak.connections.jpa.HibernateStatsReporter=debug

keycloak.infinispan.logging.level=info
log4j.logger.org.keycloak.cluster.infinispan=${keycloak.infinispan.logging.level}
log4j.logger.org.keycloak.connections.infinispan=${keycloak.infinispan.logging.level}
log4j.logger.org.keycloak.keys.infinispan=${keycloak.infinispan.logging.level}
log4j.logger.org.keycloak.models.cache.infinispan=${keycloak.infinispan.logging.level}
log4j.logger.org.keycloak.models.sessions.infinispan=${keycloak.infinispan.logging.level}

log4j.logger.org.infinispan.client.hotrod.impl.query.RemoteQuery=error

# Enable to view ldap logging
# log4j.logger.org.keycloak.storage.ldap=trace

# Enable to view queries to LDAP
# log4j.logger.org.keycloak.storage.ldap.idm.store.ldap.LDAPIdentityStore=trace

# Enable to view details about LDAP performance operations
# log4j.logger.org.keycloak.storage.ldap.idm.store.ldap.LDAPOperationManager.perf=trace

# Enable to view MSAD mapper logging
#log4j.logger.org.keycloak.storage.ldap.mappers.msad.MSADUserAccountControlStorageMapper=trace

# Enable to view kerberos/spnego logging
# log4j.logger.org.keycloak.federation.kerberos=trace

# Enable to view detailed AS REQ and TGS REQ requests to embedded Kerberos server
# log4j.logger.org.apache.directory.server.kerberos=debug
#log4j.logger.org.keycloak.saml=debug

log4j.logger.org.xnio=off
log4j.logger.org.hibernate=off
log4j.logger.org.jboss.resteasy=warn
log4j.logger.org.apache.directory.api=warn
log4j.logger.org.apache.directory.server.core=warn
log4j.logger.org.apache.directory.server.ldap.LdapProtocolHandler=error

# Enable to view HttpClient connection pool activity
#log4j.logger.org.apache.http.impl.conn=debug

# Enable to view details from identity provider authenticator
#log4j.logger.org.keycloak.authentication.authenticators.browser.IdentityProviderAuthenticator=trace
#log4j.logger.org.keycloak.services.resources.IdentityBrokerService=trace
#log4j.logger.org.keycloak.broker=trace

#log4j.logger.io.undertow=trace

#log4j.logger.org.keycloak.protocol=debug
#log4j.logger.org.keycloak.services.resources.LoginActionsService=debug
#log4j.logger.org.keycloak.services.managers=debug
#log4j.logger.org.keycloak.services.resources.SessionCodeChecks=debug
#log4j.logger.org.keycloak.authentication=debug

# Enable to view WebAuthn debug logging
#log4j.logger.org.keycloak.credential.WebAuthnCredentialProvider=debug
#log4j.logger.org.keycloak.authentication.requiredactions.WebAuthnRegister=debug
#log4j.logger.org.keycloak.authentication.authenticators.browser.WebAuthnAuthenticator=debug

# Client policies
#log4j.logger.org.keycloak.services.clientpolicy=trace

#log4j.logger.org.keycloak.STACK_TRACE=trace

# Enable logs the SQL statements
#log4j.logger.org.hibernate.SQL=debug
# Enable logs the JDBC parameters passed to a query
#log4j.logger.org.hibernate.orm.jdbc.bind=trace
