doLogIn=登录
doRegister=注册
doRegisterSecurityKey=注册
doCancel=取消
doSubmit=提交
doBack=返回
doYes=是
doNo=否
doContinue=继续
doIgnore=忽略
doAccept=接受
doDecline=拒绝
doForgotPassword=忘记密码?
doClickHere=点击这里
doImpersonate=模拟
doTryAgain=重试
doTryAnotherWay=尝试其他方法
doConfirmDelete=确认删除
errorDeletingAccount=删除账户时遇到错误
deletingAccountForbidden=您没有权限删除您的账户，请联系管理员。
kerberosNotConfigured=Kerberos 没有配置
kerberosNotConfiguredTitle=Kerberos 没有配置
bypassKerberosDetail=您没有通过 Kerberos 登录 或者您的浏览器没有设置 Kerberos 登录. 请点击继续通过其他途径登录。
kerberosNotSetUp=Kerberos 没有配置，您无法登录。
registerTitle=注册
loginAccountTitle=登录到您的账户
loginTitle=登录到 {0}
loginTitleHtml={0}
impersonateTitle={0} 模拟用户
impersonateTitleHtml=<strong>{0}</strong>模拟用户
realmChoice=域
unknownUser=未知用户
loginTotpTitle=配置验证器
loginProfileTitle=更新账户信息
loginIdpReviewProfileTitle=更新账户信息
loginTimeout=您的登录已超时，请重新开始登录。
reauthenticate=请重新授权以继续
oauthGrantTitle=授权给 {0}
oauthGrantTitleHtml={0}
oauthGrantInformation=通过了解 {0} 将如何处理您的数据，确保您信任 {0} 。
oauthGrantReview=您可以审阅
oauthGrantTos=服务条款。
oauthGrantPolicy=隐私政策。
errorTitle=很抱歉...
errorTitleHtml=我们<strong>很抱歉</strong> ...
emailVerifyTitle=验证电子邮箱地址
emailForgotTitle=忘记密码了吗?
updateEmailTitle=更新点在邮箱
emailUpdateConfirmationSentTitle=已发送确认邮件。
emailUpdateConfirmationSent=已向 {0} 发送了一封确认邮件. 请按照前面的指示来更新电子邮箱。
emailUpdatedTitle=电子邮箱已更新。
emailUpdated=您的账户的电子邮箱已经成功更新为 {0}.
updatePasswordTitle=更新密码
codeSuccessTitle=成功代码
codeErrorTitle=错误代码\: {0}
displayUnsupported=不支持请求的显示类型
browserRequired=需要浏览器来登录
browserContinue=需要浏览器来完成登录
browserContinuePrompt=打开浏览器以继续？ [y/n]:
browserContinueAnswer=y

# Transports
usb=USB
nfc=NFC
bluetooth=蓝牙
internal=内部
unknown=未知

termsTitle=条款
termsText=<p>需要确定的条款</p>
termsPlainText=需要确定的条款。
termsAcceptanceRequired=您必须同意我们的的服务条款。
acceptTerms=我同意服务条款

recaptchaFailed=无效的验证码
recaptchaNotConfigured=需要验证码，但是没有配置
consentDenied=许可被拒绝。

noAccount=新用户?
username=用户名
usernameOrEmail=用户名 或 电子邮箱地址
firstName=名
givenName=姓
fullName=全名
lastName=姓
familyName=姓
email=Email
password=密码
passwordConfirm=确认密码
passwordNew=新密码
passwordNewConfirm=确认新密码
hidePassword=隐藏密码
showPassword=显示密码
rememberMe=记住我
authenticatorCode=一次性验证码
address=地址
street=街道
locality=市
region=省，自治区，直辖市
postal_code=邮政编码
country=国家
emailVerified=已验证电子邮件
website=网址
phoneNumber=电话号码
phoneNumberVerified=已验证电话号码
gender=性别
birthday=生日
zoneinfo=时区
gssDelegationCredential=GSS Delegation Credential
logoutOtherSessions=从其他设备中登出

profileScopeConsentText=用户资料
emailScopeConsentText=电子邮箱地址
addressScopeConsentText=地址
phoneScopeConsentText=电话号码
offlineAccessScopeConsentText=离线访问
samlRoleListScopeConsentText=我的角色
rolesScopeConsentText=用户角色

restartLoginTooltip=重新开始登录

loginTotpIntro=您需要设置一个一次性密码生成器来访问您的账户
loginTotpStep1=在您的移动设备中安装以下任意一个应用：
loginTotpStep2=打开该应用来扫描条码
loginTotpStep3=输入该应用提供的一次性代码并点击提交来完成配置。
loginTotpStep3DeviceName=请提供一个设备名称以方便管理您的OTP设备
loginTotpManualStep2=打开应用并输入密钥：
loginTotpManualStep3=如果应用程序允许设置，请使用以下配置值：
loginTotpUnableToScan=无法扫描？
loginTotpScanBarcode=扫描条码？
loginCredential=凭证
loginOtpOneTime=一次性代码
loginTotpType=类型
loginTotpAlgorithm=算法
loginTotpDigits=位数
loginTotpInterval=间隔
loginTotpCounter=计数
loginTotpDeviceName=设备名称

loginTotp.totp=基于时间
loginTotp.hotp=给予计数器

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

loginChooseAuthenticator=选择登录方式

oauthGrantRequest=您是否授予这些访问权限?
inResource=in

oauth2DeviceVerificationTitle=设备登录
verifyOAuth2DeviceUserCode=输入设备提供的代码，然后单击提交
oauth2DeviceInvalidUserCodeMessage=错误代码，请重试。
oauth2DeviceExpiredUserCodeMessage=代码已过期。请返回您的设备并尝试重新连接。
oauth2DeviceVerificationCompleteHeader=设备登录成功
oauth2DeviceVerificationCompleteMessage=您可以关闭此浏览器窗口并返回您的设备。
oauth2DeviceVerificationFailedHeader=设备登录失败。
oauth2DeviceVerificationFailedMessage=您可以关闭此浏览器窗口，然后返回到您的设备并尝试重新连接。
oauth2DeviceConsentDeniedMessage=连接设备的同意被拒绝。
oauth2DeviceAuthorizationGrantDisabledMessage=不允许此客户端启动 OAuth 2.0 设备授权授予。该流程已对此客户端禁用。

emailVerifyInstruction1=一封包含验证邮箱具体步骤的邮件已经发送到您的邮箱。
emailVerifyInstruction2=邮箱没有收到验证码?
emailVerifyInstruction3=重新发送电子邮件

emailLinkIdpTitle=链接 {0}
emailLinkIdp1=一封包含链接账户 {0} 和账户 {1} 到账户 {2} 的邮件已经发送到您的邮箱。
emailLinkIdp2=邮箱没有收到验证码邮件?
emailLinkIdp3=重新发送电子邮件
emailLinkIdp4=如果您已经在不同的浏览器中验证了电子邮件
emailLinkIdp5=来继续.

backToLogin=&laquo; 回到登录

emailInstruction=输入您的用户名或邮箱，我们会发送一封带有设置新密码步骤的邮件到您的邮箱。
emailInstructionUsername=输入您的用户名，我们将向您发送如何创建新密码的说明。

copyCodeInstruction=请复制这段验证码并粘贴到您的应用：

pageExpiredTitle=页面已过期
pageExpiredMsg1=重启登录流程
pageExpiredMsg2=继续登录流程

personalInfo=个人信息\:
role_admin=管理员
role_realm-admin=域管理员
role_create-realm=创建域
role_create-client=创建客户端
role_view-realm=查看域
role_view-users=查看用户
role_view-applications=查看应用
role_view-clients=查看客户端
role_view-events=查看时间
role_view-identity-providers=查看身份提供者
role_manage-realm=管理域
role_manage-users=管理用户
role_manage-applications=管理应用
role_manage-identity-providers=管理身份提供者
role_manage-clients=管理客户
role_manage-events=管理事件
role_view-profile=查看用户信息
role_manage-account=管理账户
role_manage-account-links=管理账户链接
role_read-token=读取令牌
role_offline-access=离线访问
client_account=账户
client_account-console=账户控制台
client_security-admin-console=安全管理控制台
client_admin-cli=管理命令行工具
client_realm-management=域管理
client_broker=经纪人

requiredFields=必填字段

invalidUserMessage=无效的用户名或密码。
invalidUsernameMessage=无效的用户名。
invalidUsernameOrEmailMessage=无效的用户名或电子邮箱。
invalidPasswordMessage=无效的密码.
invalidEmailMessage=无效的电子邮件地址
accountDisabledMessage=账户已被禁用，请联系您的管理员。
accountTemporarilyDisabledMessage=无效的用户名或密码。
accountPermanentlyDisabledMessage=无效的用户名或密码。
accountTemporarilyDisabledMessageTotp=无效的验证码
accountPermanentlyDisabledMessageTotp=无效的验证码
expiredCodeMessage=登录超时，请重新登录。
expiredActionMessage=操作已过期。请登录后继续。
expiredActionTokenNoSessionMessage=操作已过期。
expiredActionTokenSessionExistsMessage=操作已过期。请重新开始。
sessionLimitExceeded=已有过多会话。

missingFirstNameMessage=请输入名。
missingLastNameMessage=请输入姓。
missingEmailMessage=请输入电子邮箱.
missingUsernameMessage=请输入用户名。
missingPasswordMessage=请输入密码。
missingTotpMessage=请输入验证码
missingTotpDeviceNameMessage=请指定设备名称。
notMatchPasswordMessage=密码不匹配。

error-invalid-value=无效值。
error-invalid-blank=请输入值。
error-empty=请输入值。
error-invalid-length=长度必须在 {1} 和 {2} 之间。
error-invalid-length-too-short=最小长度为 {1}。
error-invalid-length-too-long= 最大长度为 {2}。
error-invalid-email=无效电子邮件地址。
error-invalid-number=无效号码。
error-number-out-of-range=号码必须介于 {1} 和 {2} 之间。
error-number-out-of-range-too-small= 数字的最小值必须是 {1}。
error-number-out-of-range-too-big= 数值的最大值必须为 {2}。
error-pattern-no-match=无效值。
error-invalid-uri=无效 URL。
error-invalid-uri-scheme=无效 URL 方案。
error-invalid-uri-fragment=无效 URL 片段。
error-user-attribute-required=请指定此字段。
error-invalid-date=无效日期。
error-user-attribute-read-only=此字段只读。
error-username-invalid-character=值包含无效字符。
error-person-name-invalid-character=值包含无效字符。
error-reset-otp-missing-id=请选择 OTP 配置。

invalidPasswordExistingMessage=无效的旧密码
invalidPasswordBlacklistedMessage=无效密码：该密码已经被拉黑。
invalidPasswordConfirmMessage=确认密码不相同
invalidTotpMessage=无效的验证码

usernameExistsMessage=用户名已被占用
emailExistsMessage=电子邮件已存在。

federatedIdentityExistsMessage=用户 {0} {1} 已存在. 请登录账户管理界面链接账户.
federatedIdentityUnavailableMessage=使用身份提供者 {1} 进行身份验证的用户 {0} 不存在。请联系管理员。
federatedIdentityUnmatchedEssentialClaimMessage=身份提供者签发的 ID 令牌与配置的基本要求不匹配。请联系管理员。

confirmLinkIdpTitle=账户已存在
federatedIdentityConfirmLinkMessage=用户{0} {1} 已存在. 怎么继续?
federatedIdentityConfirmReauthenticateMessage=以 {0} 登录来将 {1} 连接到您的账户
nestedFirstBrokerFlowMessage={0} 用户 {1} 与任何已知用户都没有关联。
confirmLinkIdpReviewProfile=审查您的信息
confirmLinkIdpContinue=添加到已知账户

configureTotpMessage=您需要设置移动验证码来激活您的账户。
configureBackupCodesMessage=您需要设置备份代码来激活您的账户。
updateProfileMessage=您需要更新您的用户资料来激活您的账户。
updatePasswordMessage=您需要更新您的密码来激活您的账户。
updateEmailMessage=您需要更新您的电子邮箱地址来激活您的账户。
resetPasswordMessage=您需要修改您的密码。
verifyEmailMessage=您需要验证您的电子邮箱来激活您的账户
linkIdpMessage=您需要验证您的电子邮箱来连接到账户{0}.

emailSentMessage=您很快会收到一封关于接下来操作的邮件。
emailSendErrorMessage=无法发送邮件，请稍后再试

accountUpdatedMessage=您的账户已经更新。
accountPasswordUpdatedMessage=您的密码已经被更新。

delegationCompleteHeader=登录成功
delegationCompleteMessage=您可以关闭浏览器窗口，返回控制台应用程序。
delegationFailedHeader=登录失败
delegationFailedMessage=您可以关闭此浏览器窗口，返回控制台应用程序并尝试重新登录。

noAccessMessage=无权限

invalidPasswordMinLengthMessage=无效的密码：最短长度 {0}.
invalidPasswordMaxLengthMessage=无效的密码：最大长度 {0}.
invalidPasswordMinDigitsMessage=无效的密码： 至少包含{0} 个数字
invalidPasswordMinLowerCaseCharsMessage=无效的密码：至少包含 {0} 小写字母.
invalidPasswordMinUpperCaseCharsMessage=无效的密码：至少包含 {0} 大写字母.
invalidPasswordMinSpecialCharsMessage=无效的密码：至少包含 {0} 特殊字符.
invalidPasswordNotUsernameMessage=无效的密码： 不能与用户名相同.
invalidPasswordNotEmailMessage=无效的密码：不能与电子邮箱地址相同。
invalidPasswordRegexPatternMessage=无效的密码： 无法与正则表达式匹配.
invalidPasswordHistoryMessage=无效的密码： 不能与前 {0} 个旧密码相同.
invalidPasswordGenericMessage=无效的密码：新密码不符合密码规则。

failedToProcessResponseMessage=无法处理回复
httpsRequiredMessage=需要HTTPS
realmNotEnabledMessage=域未启用
invalidRequestMessage=非法的请求
successLogout=您已被登出
failedLogout=无法登出
unknownLoginRequesterMessage=未知的登录请求发起方
loginRequesterNotEnabledMessage=登录请求发起方未启用
bearerOnlyMessage=Bearer-only 的应用不允许通过浏览器登录
standardFlowDisabledMessage=客户端不允许发起指定返回类型的浏览器登录. 此客户端已禁用标准流程。
implicitFlowDisabledMessage=客户端不允许发起指定返回类型的浏览器登录. 此客户端已禁用隐式流程。
invalidRedirectUriMessage=无效的跳转链接
unsupportedNameIdFormatMessage=不支持的 NameIDFormat
invalidRequesterMessage=无效的发起者
registrationNotAllowedMessage=不允许注册
resetCredentialNotAllowedMessage=不允许重置凭证

permissionNotApprovedMessage=许可没有批准
noRelayStateInResponseMessage=身份提供者没有返回中继状态信息
insufficientPermissionMessage=权限不足以链接新的身份
couldNotProceedWithAuthenticationRequestMessage=无法与身份提供者处理认证请求
couldNotObtainTokenMessage=未从身份提供者获得令牌
unexpectedErrorRetrievingTokenMessage=从身份提供者获得令牌时遇到未知错误
unexpectedErrorHandlingResponseMessage=从身份提供者获得回复时遇到未知错误
identityProviderAuthenticationFailedMessage=认证失败，无法通过身份提供者认证
couldNotSendAuthenticationRequestMessage=无法向身份提供方发送认证请求
unexpectedErrorHandlingRequestMessage=在处理发向认证提供方的请求时，出现未知错误。
invalidAccessCodeMessage=无效的访问代码
sessionNotActiveMessage=会话不在活动状态
invalidCodeMessage=发生错误，请通过应用程序重新登录。
cookieNotFoundMessage=未找到 cookie。请确保浏览器已启用 cookie。
insufficientLevelOfAuthentication=未满足所请求的身份验证级别。
identityProviderUnexpectedErrorMessage=与身份提供程序进行身份验证时出现意外错误
identityProviderMissingStateMessage=身份提供程序的响应中缺少状态参数。
identityProviderMissingCodeOrErrorMessage=身份提供程序的响应中缺少代码或错误参数。
identityProviderInvalidResponseMessage=身份提供程序的无效响应。
identityProviderInvalidSignatureMessage=身份提供程序响应中的无效签名。
identityProviderNotFoundMessage=找不到具有标识符的身份提供程序。
identityProviderLinkSuccess=您已成功验证电子邮件。请回到原来的浏览器继续登录。
staleCodeMessage=此页面已失效，请返回应用程序并重新登录
realmSupportsNoCredentialsMessage=域不支持任何凭证类型。
credentialSetupRequired=无法登录，需要设置凭证。
identityProviderNotUniqueMessage=该域支持多个身份提供程序。无法确定应使用哪个身份提供程序进行身份验证。
emailVerifiedMessage=您的电子邮件地址已通过验证。
emailVerifiedAlreadyMessage=您的电子邮件地址已被验证通过。
staleEmailVerificationLink=您点击的链接是旧的过期链接，已经不再有效。 也许您已经验证了您的电子邮件。
identityProviderAlreadyLinkedMessage={0} 返回的联合身份已链接到另一个用户。
confirmAccountLinking=确认将身份提供者 {1} 的账户 {0} 与您的账户链接。
confirmEmailAddressVerification=确认电子邮件地址 {0} 的有效性。
confirmExecutionOfActions=执行以下操作

backToApplication=&laquo; 回到应用
missingParameterMessage=缺少参数 \: {0}
clientNotFoundMessage=客户端未找到
clientDisabledMessage=客户端已禁用
invalidParameterMessage=无效的参数 \: {0}
alreadyLoggedIn=您已经登录。
differentUserAuthenticated=您已经以不同用户 ''{0}'' 的身份在此会话中进行了身份验证。请先注销。
brokerLinkingSessionExpired=已请求与经纪人帐户链接，但当前会话不再有效。
proceedWithAction=&raquo; 点击此处继续
acrNotFulfilled=未满足身份验证要求

requiredAction.CONFIGURE_TOTP=配置OTP
requiredAction.TERMS_AND_CONDITIONS=条款和条件
requiredAction.UPDATE_PASSWORD=更新密码
requiredAction.UPDATE_PROFILE=更新个人资料
requiredAction.VERIFY_EMAIL=验证电子邮箱
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=生成恢复代码
requiredAction.webauthn-register-passwordless=Webauthn注册免密码

invalidTokenRequiredActions=链接中包含的必需操作无效

doX509Login=您将被登录为\：
clientCertificate=X509客户端证书\：
noCertificate=[No Certificate]


pageNotFound=页面未找到
internalServerError=发生内部服务器错误

console-username=用户名：
console-password=密码：
console-otp=一次性密码：
console-new-password=新密码：
console-confirm-password=确认密码：
console-update-password=需要更新您的密码。
console-verify-email=您需要验证您的电子邮件地址。我们已向 {0} 发送了一封包含验证码的电子邮件。请将此代码输入下面的输入框中。
console-email-code=电子邮件验证码：
console-accept-terms=接受条款？[y/n]：
console-accept=y

# Openshift messages
openshift.scope.user_info=用户信息
openshift.scope.user_check-access=用户访问信息
openshift.scope.user_full=完全访问
openshift.scope.list-projects=列出项目

# SAML authentication
saml.post-form.title=身份验证重定向
saml.post-form.message=重定向中，请稍候。
saml.post-form.js-disabled=JavaScript已被禁用。我们强烈建议启用它。点击下面的按钮继续。
saml.artifactResolutionServiceInvalidResponse=无法解析构造。

# Authenticators
otp-display-name=验证器应用程序
otp-help-text=从验证器应用输入验证码。
otp-reset-description=应删除哪个OTP配置？
password-display-name=密码
password-help-text=通过输入密码登录。
auth-username-form-display-name=用户名
auth-username-form-help-text=输入用户名以开始登录
auth-username-password-form-display-name=用户名和密码
auth-username-password-form-help-text=输入用户名和密码以登录。

# Recovery Codes
auth-recovery-authn-code-form-display-name=恢复代码
auth-recovery-authn-code-form-help-text=输入先前生成的恢复代码。
auth-recovery-code-info-message=输入指定的恢复代码。
auth-recovery-code-prompt=恢复代码 #{0}
auth-recovery-code-header=使用恢复代码登录
recovery-codes-error-invalid=无效的代码
recovery-code-config-header=恢复代码
recovery-code-config-warning-title=离开此页面后，这些恢复代码将不再显示
recovery-code-config-warning-message=确保打印、下载或将它们复制到密码管理器，并将其保存在安全的地方。取消此设置将从您的帐户中删除这些恢复代码。
recovery-codes-print=打印
recovery-codes-download=下载
recovery-codes-copy=复制
recovery-codes-copied=已复制
recovery-codes-confirmation-message=我已将这些代码保存在安全的地方
recovery-codes-action-complete=完成设置
recovery-codes-action-cancel=取消设置
recovery-codes-download-file-header=将这些恢复代码保存在安全的地方。
recovery-codes-download-file-description=恢复代码是一次性验证码，如果您无法访问您的验证器，可以使用它们登录到您的帐户。
recovery-codes-download-file-date=这些代码生成于
recovery-codes-label-default=恢复代码

# WebAuthn
webauthn-display-name=安全密钥
webauthn-help-text=使用您的安全密钥登录。
webauthn-passwordless-display-name=安全密钥
webauthn-passwordless-help-text=使用您的安全密钥进行无密码登录。
webauthn-login-title=安全密钥登录
webauthn-registration-title=安全密钥注册
webauthn-available-authenticators=可用的安全密钥
webauthn-unsupported-browser-text=此浏览器不支持 WebAuthn。请尝试其他浏览器或联系管理员。
webauthn-doAuthenticate=使用安全密钥登录
webauthn-createdAt-label=创建于

# WebAuthn Error
webauthn-error-title=安全密钥错误
webauthn-error-registration=注册您的安全密钥失败。<br /> {0}
webauthn-error-api-get=通过安全密钥进行身份验证失败。<br /> {0}
webauthn-error-different-user=首次经过身份验证的用户不是通过安全密钥经过身份验证的用户。
webauthn-error-auth-verification=安全密钥身份验证结果无效。<br /> {0}
webauthn-error-register-verification=安全密钥注册结果无效。<br /> {0}
webauthn-error-user-not-found=通过安全密钥进行身份验证的用户未知。

# Identity provider
identity-provider-redirector=连接到另一个身份提供者
identity-provider-login-label=或使用以下方式登录
idp-email-verification-display-name=电子邮件验证
idp-email-verification-help-text=通过验证您的电子邮件来链接您的帐户。
idp-username-password-form-display-name=用户名和密码
idp-username-password-form-help-text=通过登录来链接您的帐户。

finalDeletionConfirmation=如果将您的帐户删除，将无法恢复。如果要保留您的帐户，请点击取消。
irreversibleAction=此操作不可逆转
deleteAccountConfirm=删除帐户确认

deletingImplies=删除您的帐户意味着：
errasingData=删除您的所有数据
loggingOutImmediately=立即将您注销
accountUnusable=此帐户将无法再次使用应用程序
userDeletedSuccessfully=用户删除成功

access-denied=访问被拒绝
access-denied-when-idp-auth=使用 {0} 进行身份验证时被拒绝访问

frontchannel-logout.title=正在注销
frontchannel-logout.message=您正在从以下应用程序注销
logoutConfirmTitle=注销
logoutConfirmHeader=您确定要注销吗？
doLogout=注销

readOnlyUsernameMessage=由于用户名是只读的，您无法更新用户名。
