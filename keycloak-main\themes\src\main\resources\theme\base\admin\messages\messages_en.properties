invalidPasswordMinLengthMessage=Invalid password: minimum length {0}.
invalidPasswordMaxLengthMessage=Invalid password: maximum length {0}.
invalidPasswordMinLowerCaseCharsMessage=Invalid password: must contain at least {0} lower case characters.
invalidPasswordMinDigitsMessage=Invalid password: must contain at least {0} numerical digits.
invalidPasswordMinUpperCaseCharsMessage=Invalid password: must contain at least {0} upper case characters.
invalidPasswordMinSpecialCharsMessage=Invalid password: must contain at least {0} special characters.
invalidPasswordNotUsernameMessage=Invalid password: must not be equal to the username.
invalidPasswordNotContainsUsernameMessage=Invalid password: Can not contain the username.
invalidPasswordNotEmailMessage=Invalid password: must not be equal to the email.
invalidPasswordRegexPatternMessage=Invalid password: fails to match regex pattern(s).
invalidPasswordHistoryMessage=Invalid password: must not be equal to any of last {0} passwords.
invalidPasswordBlacklistedMessage=Invalid password: password is blacklisted.
invalidPasswordGenericMessage=Invalid password: new password does not match password policies.

ldapErrorEditModeMandatory=Edit Mode is mandatory
ldapErrorInvalidCustomFilter=Custom configured LDAP filter does not start with "(" or does not end with ")".
ldapErrorConnectionTimeoutNotNumber=Connection Timeout must be a number
ldapErrorReadTimeoutNotNumber=Read Timeout must be a number
ldapErrorMissingClientId=Client ID needs to be provided in config when Realm Roles Mapping is not used.
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Not possible to preserve group inheritance and use UID membership type together.
ldapErrorCantWriteOnlyForReadOnlyLdap=Can not set write only when LDAP provider mode is not WRITABLE
ldapErrorCantWriteOnlyAndReadOnly=Can not set write-only and read-only together
ldapErrorCantEnableStartTlsAndConnectionPooling=Can not enable both StartTLS and connection pooling.
ldapErrorCantEnableUnsyncedAndImportOff=Can not disable Importing users when LDAP provider mode is UNSYNCED
ldapErrorMissingGroupsPathGroup=Groups path group does not exist - please create the group on specified path first
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=Validate Password Policy is applicable only with WRITABLE edit mode

clientRedirectURIsFragmentError=Redirect URIs must not contain an URI fragment
clientRootURLFragmentError=Root URL must not contain an URL fragment
clientRootURLIllegalSchemeError=Root URL uses an illegal scheme
clientBaseURLIllegalSchemeError=Base URL uses an illegal scheme
backchannelLogoutUrlIllegalSchemeError=Backchannel logout URL uses an illegal scheme
clientRedirectURIsIllegalSchemeError=A redirect URI uses an illegal scheme
clientBaseURLInvalid=Base URL is not a valid URL
clientRootURLInvalid=Root URL is not a valid URL
clientRedirectURIsInvalid=A redirect URI is not a valid URI
backchannelLogoutUrlIsInvalid=Backchannel logout URL is not a valid URL


pairwiseMalformedClientRedirectURI=Client contained an invalid redirect URI.
pairwiseClientRedirectURIsMissingHost=Client redirect URIs must contain a valid host component.
pairwiseClientRedirectURIsMultipleHosts=Without a configured Sector Identifier URI, client redirect URIs must not contain multiple host components.
pairwiseMalformedSectorIdentifierURI=Malformed Sector Identifier URI.
pairwiseFailedToGetRedirectURIs=Failed to get redirect URIs from the Sector Identifier URI.
pairwiseRedirectURIsMismatch=Client redirect URIs does not match redirect URIs fetched from the Sector Identifier URI.

duplicatedJwksSettings=The "Use JWKS" switch and the switch "Use JWKS URL" cannot be ON at the same time.

error-invalid-value=Invalid value.
error-invalid-blank=Please specify value.
error-empty=Please specify value.
error-invalid-length=Attribute {0} must have a length between {1} and {2}.
error-invalid-length-too-short=Attribute {0} must have minimal length of {1}.
error-invalid-length-too-long=Attribute {0} must have maximal length of {2}.
error-invalid-email=Invalid email address.
error-invalid-number=Invalid number.
error-number-out-of-range=Attribute {0} must be a number between {1} and {2}.
error-number-out-of-range-too-small=Attribute {0} must have minimal value of {1}.
error-number-out-of-range-too-big=Attribute {0} must have maximal value of {2}.
error-pattern-no-match=Invalid value.
error-invalid-uri=Invalid URL.
error-invalid-uri-scheme=Invalid URL scheme.
error-invalid-uri-fragment=Invalid URL fragment.
error-user-attribute-required=Please specify attribute {0}.
error-invalid-date=Attribute {0} is invalid date.
error-user-attribute-read-only=Attribute {0} is read only.
error-username-invalid-character={0} contains invalid character.
error-person-name-invalid-character={0} contains invalid character.
error-invalid-multivalued-size=Attribute {0} must have at least {1} and at most {2} {2,choice,0#values|1#value|1<values}.

client_account=Account
client_account-console=Account Console
client_security-admin-console=Security Admin Console
client_admin-cli=Admin CLI
client_realm-management=Realm Management
client_broker=Broker