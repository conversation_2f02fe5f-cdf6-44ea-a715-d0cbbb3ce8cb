#
# Copyright 2016 Red Hat, Inc. and/or its affiliates
# and other contributors as indicated by the <AUTHOR>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

log4j.rootLogger=info, keycloak

log4j.appender.keycloak=org.apache.log4j.ConsoleAppender
log4j.appender.keycloak.layout=org.apache.log4j.EnhancedPatternLayout
keycloak.testsuite.logging.pattern=%d{HH:mm:ss,SSS} %-5p [%c] (%t) %m%n
log4j.appender.keycloak.layout.ConversionPattern=${keycloak.testsuite.logging.pattern}

# Logging with "info" when running test from IDE, but disabled when running test with "mvn" . Both cases can be overriden by use system property "keycloak.logging.level" (eg. -Dkeycloak.logging.level=debug )
log4j.logger.org.keycloak=${keycloak.logging.level:info}

keycloak.testsuite.logging.level=debug
log4j.logger.org.keycloak.testsuite=${keycloak.testsuite.logging.level}

# Logging with "info" when running test from IDE, but disabled when running test with "mvn" . Both cases can be overriden by use system property "keycloak.logging.level" (eg. -Dkeycloak.logging.level=debug )
# log4j.logger.org.hibernate=debug

# Enable to view loaded SPI and Providers
 log4j.logger.org.keycloak.services.DefaultKeycloakSessionFactory=debug
 log4j.logger.org.keycloak.provider.ProviderManager=debug
# log4j.logger.org.keycloak.provider.FileSystemProviderLoaderFactory=debug

# Liquibase updates logged with "info" by default. Logging level can be changed by system property "keycloak.liquibase.logging.level"
keycloak.liquibase.logging.level=info
log4j.logger.org.keycloak.connections.jpa.updater.liquibase=${keycloak.liquibase.logging.level}
log4j.logger.org.keycloak.connections.jpa.DefaultJpaConnectionProviderFactory=debug

# Enable to log short stack traces for log entries enabled with StackUtil.getShortStackTrace() calls
#log4j.logger.org.keycloak.STACK_TRACE=trace

#log4j.logger.org.keycloak.models.sessions.infinispan=trace
keycloak.infinispan.logging.level=info
log4j.logger.org.keycloak.cluster.infinispan=${keycloak.infinispan.logging.level}
log4j.logger.org.keycloak.connections.infinispan=${keycloak.infinispan.logging.level}
log4j.logger.org.keycloak.keys.infinispan=${keycloak.infinispan.logging.level}
log4j.logger.org.keycloak.models.cache.infinispan=${keycloak.infinispan.logging.level}
log4j.logger.org.keycloak.models.sessions.infinispan=${keycloak.infinispan.logging.level}

log4j.logger.org.infinispan.CLUSTER=warn
log4j.logger.org.infinispan.server.hotrod=info
log4j.logger.org.infinispan.client.hotrod.impl=info
log4j.logger.org.infinispan.client.hotrod.event.impl=info

log4j.logger.org.infinispan.client.hotrod.impl.query.RemoteQuery=error

# avoid logging INFO-message "ignoring the message MessageType : UNBIND_REQUEST" very often
log4j.logger.org.apache.directory.server.ldap.handlers.LdapRequestHandler=warn

log4j.logger.org.keycloak.executors=info

#log4j.logger.org.infinispan.expiration.impl.ClusterExpirationManager=trace

## Enable SQL debugging
# Enable logs the SQL statements
#log4j.logger.org.hibernate.SQL=debug

# Enable logs the JDBC parameters passed to a query
#log4j.logger.org.hibernate.orm.jdbc.bind=trace

