[{"id": "Migration", "realm": "Migration", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "offlineSessionIdleTimeout": 2592000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "2c0f874e-eace-4569-9244-b2ac5c0c1d7c", "name": "offline_access", "description": "${role_offline-access}", "scopeParamRequired": true, "composite": false, "clientRole": false, "containerId": "Migration"}, {"id": "b8250e64-0bb3-4324-b6bf-f02cb0f4d7ff", "name": "uma_authorization", "description": "${role_uma_authorization}", "scopeParamRequired": false, "composite": false, "clientRole": false, "containerId": "Migration"}, {"id": "a1c163f3-80a7-4900-a4dd-c66df26748a6", "name": "migration-test-realm-role", "scopeParamRequired": false, "composite": false, "clientRole": false, "containerId": "Migration"}], "client": {"migration-test-client": [{"id": "d89f0132-0165-42b4-8a95-1b7570464279", "name": "migration-test-client-role", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "e1f5a06c-1159-4740-8dfa-3ca59cd03d4e"}], "realm-management": [{"id": "3cd37ba2-9279-49c1-b5f8-6055bc55e17b", "name": "view-clients", "description": "${role_view-clients}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "b0737101-9dca-4349-808b-a93d29cfafe4", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "4f0254c5-c826-4a7a-93fa-bf029827b0db", "name": "query-groups", "description": "${role_query-groups}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "d682a71a-2aaa-4ba5-ab14-05cd5daaa7bc", "name": "query-realms", "description": "${role_query-realms}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "b7eae89d-358b-4509-9c02-3bb94e32e3e3", "name": "manage-users", "description": "${role_manage-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "8bf0dc11-5639-4f42-babb-359473ac8b4d", "name": "view-users", "description": "${role_view-users}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "a68e40d7-f4df-4253-ad2f-641a5f730420", "name": "view-events", "description": "${role_view-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "e7857c73-5a15-48d5-8dea-b2921c1184cc", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "67ce1a07-af71-4d75-8dba-454395258ccc", "name": "manage-clients", "description": "${role_manage-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "5be36132-0a3c-47b7-80d3-7ce17e7d118d", "name": "query-clients", "description": "${role_query-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "896bbdda-9722-4bfe-ae14-ee2d0f6e7c85", "name": "impersonation", "description": "${role_impersonation}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "a59dedda-eb79-47e4-8fe7-7b7d63e4cc52", "name": "create-client", "description": "${role_create-client}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "05acb92e-f007-4622-9fd7-9106b8fdd32b", "name": "view-authorization", "description": "${role_view-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "ce8a037a-8853-4757-a7ed-eac87e014085", "name": "query-users", "description": "${role_query-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "1d939019-cc1a-4bd1-9ff6-2a9201f59a50", "name": "realm-admin", "description": "${role_realm-admin}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"realm-management": ["view-clients", "query-groups", "view-identity-providers", "query-realms", "manage-users", "view-users", "view-events", "manage-identity-providers", "query-clients", "manage-clients", "impersonation", "create-client", "view-authorization", "query-users", "manage-authorization", "view-realm", "manage-events", "manage-realm"]}}, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "7df36b2e-0328-4ec0-abe1-3616d5895841", "name": "manage-authorization", "description": "${role_manage-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "4cee5d8c-990b-4d69-ba3e-6b428e9470ed", "name": "view-realm", "description": "${role_view-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "711fad68-820e-4355-8a5f-2b6f9f064d55", "name": "manage-realm", "description": "${role_manage-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}, {"id": "2a7fd66c-988f-4e21-9c2d-1923eca88610", "name": "manage-events", "description": "${role_manage-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5"}], "security-admin-console": [], "admin-cli": [], "broker": [{"id": "f79e9adc-9faa-47b7-919f-a16a035c4872", "name": "read-token", "description": "${role_read-token}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "5ce15296-898b-49f8-be5d-f29f76600236"}], "account": [{"id": "c66b5836-88c8-47fd-a099-1920ae7a5aca", "name": "manage-account", "description": "${role_manage-account}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "6b9ba4ca-fb7c-4e17-a3e6-88f3a17397cc"}, {"id": "441f0f69-c559-45ca-8009-9edc508d5e24", "name": "manage-account-links", "description": "${role_manage-account-links}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "6b9ba4ca-fb7c-4e17-a3e6-88f3a17397cc"}, {"id": "eca5b168-dbca-4c24-b0d8-8e8ada647e41", "name": "view-profile", "description": "${role_view-profile}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "6b9ba4ca-fb7c-4e17-a3e6-88f3a17397cc"}]}}, "groups": [{"id": "b686ddd7-7a9e-4645-9e7b-e1219993b068", "name": "migration-test-group", "path": "/migration-test-group", "attributes": {}, "realmRoles": [], "clientRoles": {}, "subGroups": []}], "defaultRoles": ["offline_access", "uma_authorization", "migration-test-realm-role"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "users": [{"id": "2a15a4f3-0e14-4b57-8753-2d774ef02fce", "createdTimestamp": *************, "username": "migration-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"type": "totp", "hashedSaltedValue": "dSdmuHLQhkm54oIm0A0S", "hashIterations": 0, "counter": 0, "algorithm": "HmacSHA1", "digits": 8, "period": 40, "createdDate": *************, "config": {}}, {"type": "password", "hashedSaltedValue": "kNwotFPNeuwelpT1HWt+E4ONXFK6wjd+h0zbzNBRGwOqacAjeY7vYN9QZQ46DlEKSdn04cEU/3RvX8WPcRegxg==", "salt": "rEIJDbs+BQqpx31v8mONWA==", "hashIterations": 27500, "counter": 0, "algorithm": "pbkdf2-sha256", "digits": 0, "period": 0, "createdDate": *************, "config": {}}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["offline_access", "uma_authorization"], "clientRoles": {"account": ["manage-account", "view-profile"]}, "notBefore": 0, "groups": []}, {"id": "3a15a4f3-0e14-4b57-8753-2d774ef02fce", "createdTimestamp": *************, "username": "offline-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"type": "password", "hashedSaltedValue": "kNwotFPNeuwelpT1HWt+E4ONXFK6wjd+h0zbzNBRGwOqacAjeY7vYN9QZQ46DlEKSdn04cEU/3RvX8WPcRegxg==", "salt": "rEIJDbs+BQqpx31v8mONWA==", "hashIterations": 27500, "counter": 0, "algorithm": "pbkdf2-sha256", "digits": 0, "period": 0, "createdDate": *************, "config": {}}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["offline_access", "uma_authorization"], "clientRoles": {"account": ["manage-account", "view-profile"]}, "notBefore": 0, "groups": []}], "clientScopeMappings": {"migration-test-client": [{"clientTemplate": "Default test template", "roles": ["migration-test-client-role"]}]}, "clients": [{"id": "6b9ba4ca-fb7c-4e17-a3e6-88f3a17397cc", "clientId": "account", "name": "${client_account}", "baseUrl": "/auth/realms/Migration/account", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "7ea0b316-8e4d-42cb-a365-bf84db0b2175", "defaultRoles": ["manage-account", "view-profile"], "redirectUris": ["/auth/realms/Migration/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "8a7289de-a332-4348-843f-a42f0a9f54cd", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "7bacf9e4-0ccf-4548-befc-8b77f549260e", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "d84d5416-c78f-4ad7-ae0f-3a1547846976", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "4603f9a2-1d2e-48a4-99e9-139e39faab70", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "eedfc75c-8a36-4597-a2a1-5ee605d99e48", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "d9f71e51-94a5-4e1b-ac63-9ef25411b176", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "113e78fc-21a5-4429-bd7a-4edb146ca781", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "ed2d7452-4d63-48e4-8024-f9b85cf455e6", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "c497bffa-3f62-4c1f-97bf-ff70daeb4284", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "3cb76619-430f-4c6a-afdf-92a92e8dd4b0", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "f492f4ae-7cc5-48a7-a955-5f59409b2c60", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "ed22259e-579d-40ee-ad7e-57ab7027a36c", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "81809aae-cba5-4612-9534-c43690d63265", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "a11c23ff-3c6c-446b-b6d1-938f1ca50d13", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "5ce15296-898b-49f8-be5d-f29f76600236", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "1da7093f-951c-441f-a71e-6b3f49546bf4", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "bdb67013-74ea-448e-98f4-a867adba51b1", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "32dad368-4a75-4e9f-b050-ab9f8b1cb152", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "285d596c-8d1d-4228-af21-fc27fe8852fe", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "74adc77a-b362-4a02-88f5-18f7dd54c553", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "191eb51a-3609-4565-a54c-a07678ab92dc", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "cada07f7-e5b7-4e83-a34d-61f7770d5195", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "e1f5a06c-1159-4740-8dfa-3ca59cd03d4e", "clientId": "migration-test-client", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "secret", "defaultRoles": ["migration-test-client-role"], "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "2444afa8-451b-45df-b330-286dc5caec78", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "5ed3d87d-3de8-462d-9c30-05afb4998762", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "2821f747-81fe-4c44-b2ba-bdaeea4ceafa", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "d1a94889-b857-4630-a99c-360a1038ecfa", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "b0ea6149-aa12-442e-955b-81bdae7edebf", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "03bd5103-703f-4880-adf2-44b613961098", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"clientId": "migration-saml-client", "enabled": true, "fullScopeAllowed": true, "protocol": "saml", "baseUrl": "http://localhost:8080/sales-post", "redirectUris": ["http://localhost:8080/sales-post/*"], "attributes": {"saml_assertion_consumer_url_post": "http://localhost:8080/sales-post/saml", "saml_single_logout_service_url_post": "http://localhost:8080/sales-post/saml", "saml.authnstatement": "true", "saml_idp_initiated_sso_url_name": "sales-post"}}, {"id": "9a37d2c5-6a36-4a2c-b837-f2ea846fb0d5", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "9a06cf68-06a5-4495-b494-2eab86d07cd1", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "0553fd78-f19e-4fb2-8146-46a53fed7bf0", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "fa4a0e6b-d94a-4d28-9f36-22739acd4283", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "b91b5219-bb18-47d8-a83a-96dd36ef6d07", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "ee28144b-b96e-43a6-bba4-1ee9b6c9ac46", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "af98c7c7-a76b-46f7-bfdc-0bfb23aaddff", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "9df6bb64-092d-47c9-a791-72f75969807b", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false, "authorizationServicesEnabled": true, "authorizationSettings": {"resources": [{"name": "group.resource.a", "scopes": ["view-members"]}, {"name": "group.resource.b", "scopes": ["view-members"]}, {"name": "group.resource.c", "scopes": ["view-members"]}, {"name": "group.resource.d", "scopes": ["view-members"]}, {"name": "group.resource.e", "scopes": ["view-members"]}]}}, {"id": "35d39054-e7e5-4dbb-8948-1738094679e8", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "baseUrl": "/auth/admin/Migration/console/index.html", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "cbfacf31-e796-4e09-a2d4-77b6b76bc0a2", "redirectUris": ["/auth/admin/Migration/console/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "3365e7fd-b705-41b4-b98a-7f6d10082990", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "cb4ff0ac-3c54-4410-a09c-3084333e709a", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "2797ff83-98b1-445f-b446-9c1fa51b5297", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "761620ff-7f06-40a1-9fb2-5c446b494369", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "consentText": "${locale}", "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "86febf77-b65a-407b-bb4d-4bc0c6e1b486", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "059f78ba-fe3f-4b1a-9e7e-927acde76108", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "f3dd4325-ea15-4f93-b9c8-1c35a429a484", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "********-3cd4-4f2a-974b-e1447907834a", "clientId": "client-with-template", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": -1, "clientTemplate": "Default test template", "useTemplateConfig": false, "useTemplateScope": true, "useTemplateMappers": true}, {"id": "70e8e897-82d4-49ab-82c9-c37e1a48b6bb", "clientId": "authz-servlet", "adminUrl": "http://localhost:8080/authz-servlet", "baseUrl": "http://localhost:8080/authz-servlet", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["http://localhost:8080/authz-servlet/*"], "webOrigins": ["http://localhost:8080"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "authorizationSettings": {"resources": [{"name": "Admin Resource", "uri": "/protected/admin/*", "type": "http://servlet-authz/protected/admin", "_id": "af06c58d-32b6-44d2-9057-2673ced120eb"}, {"name": "Protected Resource", "uri": "/*", "type": "http://servlet-authz/protected/resource", "_id": "d8ec89d2-7fc3-416c-9584-f242e8a6f827"}, {"name": "Premium Resource", "uri": "/protected/premium/*", "type": "urn:servlet-authz:protected:resource", "_id": "9c4dd55d-b7a1-45a5-a379-d2ae48b7b309"}, {"name": "Main Page", "type": "urn:servlet-authz:protected:resource", "_id": "01394f0e-8b06-4ae8-a1cb-9f6ff7eeb6b4"}]}}], "clientTemplates": [{"id": "d43ae38d-80a1-471a-9a80-5f9a0d34d7a4", "name": "Default test template", "description": "Test client template", "protocol": "openid-connect", "fullScopeAllowed": false}], "browserSecurityHeaders": {"xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "xXSSProtection": "1; mode=block", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "1b2a8f5e-b286-43e8-b4c3-8f6ec04a038c", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {}}, {"id": "5788c32d-5c4b-4c50-83ea-53aff46f9b21", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "1f2a4b71-2578-476e-9b68-e3015037bef6", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-full-name-mapper", "saml-role-list-mapper", "oidc-usermodel-attribute-mapper", "saml-user-attribute-mapper", "oidc-address-mapper", "oidc-usermodel-property-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-property-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "c42c701b-9e1b-435c-8fa5-9d5cb6d7c395", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "5c0906f4-311f-4e77-8e7c-b9c1ca970c9c", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-full-name-mapper", "oidc-address-mapper", "oidc-usermodel-attribute-mapper", "saml-user-attribute-mapper", "oidc-usermodel-property-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-property-mapper", "saml-role-list-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "b029adef-ac18-42ef-83e3-0e560bd359bc", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "cf14c2b6-8ae0-4b52-8f6d-a0bd720b5a4a", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "082df100-9aa5-4359-971a-b9e629312771", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}], "org.keycloak.keys.KeyProvider": [{"id": "f230f584-4012-4e97-ae44-cb08dcbeb6e8", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["24cd7703-6bdb-4866-94cb-4c49b7c5c34e"], "secret": ["0cNaBzfhwAlyT9IWJyWxYTwIoaLWBBf99yRNVbm8Vp8"], "priority": ["100"]}}, {"id": "3cb6eb81-5261-471a-8c39-47f2bc2c3569", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["a022834b-6eb6-4fae-95c9-325b4bd63af6"], "secret": ["IZLgFXROqrJy_HWC-b4bCg"], "priority": ["100"]}}, {"id": "7e90169d-aa73-442d-9b59-e58b0bccb197", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpQIBAAKCAQEAvuMLlQSWJpBdHkmAWSnbon1pb3Lv8KVLxIaDnyjkbZYogPdR1IYCKkKk4jhnPwZ5Bgxo/Xp+yqtqXR6Y/YvorhDwpSrabL0ZeCleiFASTiKl40HFH7O8IiG5tp3qa3+qazOw2NZVeq9zWSgy2PdKviWfClnneII+9JJ35MLfcD7Gu7iRlfZpvcIrkoZR1n3lPIQJdW+2qKoDNG0QK/VlfCY9tesoFyMdmil6SZCyIyNGXXDoELw+iOyau63VdSHfyPbHCgz2BheVrumrtp7MaUmS6JQ2oyQ9x3zH6ciO8hJp04CW2sgy45J/zwqYAvrfeA5EhMqUGwjU4nW31qw29QIDAQABAoIBAQCfxtvC9/vWe6/10gAkoey9rcu7BbHg8/rfnNg/OLM7JxzwFhPgl+roBLCOyjdhgcF7Vpfq08WdTq3RE/NrBtjUGknTdnMt4XqYIciGwL8Rt96ywjKFvqQSzbftK3E0WcADy1iTp5QVBRa/4e3tvdBKKKNevrIJJLwdaxv/1z2/bNenRXXpYCn8oPRdBAZab9XEIRjskWUeD5zrKhynMcmEP7fXTcd0rHPw2GxmIQAnX7avNrlTxLxkHiVpdQFg0t+y+7ZmI2j22o2pj8aYR7/LEnl8AdYZTkxjsHeineq94MP/MTJpJwLiADRkqc9UyjItslI5K672VELpfFfzwZ8BAoGBAPkSi+N8eFvFNeOZ8Z7Zn9L8iFFdAHevrUv6idW9dH39Bb3hEhOcybkqFVVwWiqmhnknG5FywIj8Ijfj1Zf8Jy4BRJtqT38YZt6PU70AureS18PgqGGuNP0Uhk0CbJ0ksp9Eb5tahgl1czr+hDBhLv84bm9dXiuZtVKrmherPxPtAoGBAMQyNEWk6C4EvhSIud5D4qmVe9OCc94X1yTJ7Vi9qLyIwN4ayS5zdiMA87alG1IY5Mp0W372srhiJKchC6xdcbaRnsmdAZp/UHJoz1BzcabpsslAUUvQXSzT+xgQ4thSmm8z1bnIMdP3uKUTleE8WjxaL2uE/hXWi4EHtVn7JV4pAoGBAOEkCp8PHByYHyYlOWtFfFrTOsWRM6L8gcF0LrOuFWbtGAuTLyvbRdzCVZZtgJOdA2DwINNQzwkEIEo1ABXqL+A99S4m4sUX4aS8Gg12Y1EcwzykDpB9UBiUPPHPhy5kxbGBwwDCyvYx3H8vIX452qmI8Qppc34l6ulXhGpwuQvhAoGAKRdSt1tofWrk3KweMZ0KNfZm7s+dsOBRYRUhrrpaQtJpgpsdk6r/DSm/4XsHE9S+5mAkYZ0NpWx4vMBBD6KqlFA/b36AvXj4wl2NPg5PuWOs3FWqMTS8rwzexBWVTTVPvwaeNsDlHr39YCWP+JKJW6aP0/cQuEYtBaM1qljdZLECgYEA8qgB8JrWrLDtmatZoFRrv0Db6DBAUTTCi3lC80ZKILsd8OQLz46T+l0rRFVkyP3+EocEXc/3vr+WxeRHLM+2SIAHQkSIYT0z0UYD7miFxQVwEBx/NZCDh6an1qxBBDOC4egaVw/Nuxy0mSY1N0SSOrAKyI5WT/fYZJYaEvy4Vqg="], "certificate": ["MIICoTCCAYkCBgFkrlCj7TANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAlNaWdyYXRpb24wHhcNMTgwNzE4MTY1MjU3WhcNMjgwNzE4MTY1NDM3WjAUMRIwEAYDVQQDDAlNaWdyYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC+4wuVBJYmkF0eSYBZKduifWlvcu/wpUvEhoOfKORtliiA91HUhgIqQqTiOGc/BnkGDGj9en7Kq2pdHpj9i+iuEPClKtpsvRl4KV6IUBJOIqXjQcUfs7wiIbm2neprf6prM7DY1lV6r3NZKDLY90q+JZ8KWed4gj70knfkwt9wPsa7uJGV9mm9wiuShlHWfeU8hAl1b7aoqgM0bRAr9WV8Jj216ygXIx2aKXpJkLIjI0ZdcOgQvD6I7Jq7rdV1Id/I9scKDPYGF5Wu6au2nsxpSZLolDajJD3HfMfpyI7yEmnTgJbayDLjkn/PCpgC+t94DkSEypQbCNTidbfWrDb1AgMBAAEwDQYJKoZIhvcNAQELBQADggEBAGyAqUD+9FmtU2Ov5qr4tZjYu1hAo/UQyDtRI1QIQcS03B7DdDjWvbsRwKaV+QcrDEXTkdXJ3ilnOKIPsagDFl/S9Tm6sa6Q13+9zmennVTGBp1wMDCMAs1KUdblCCqZvW3+C4XCROnIJOIWpJG7pUPlqRQdPEy6j9kP04OPucG83LMz+6VrMPTVBMWaStx6XV1aV5l+cP1L0EWUqtR6Nq/NltzM5+mSqIfAhVSNOFztygpSQSkmoYhsHenQHJMyKlUboEP7P29NoNEwd1ZPQBLRMK4BXhIUWWXtjOCQkrPOU15oaWxY1elSv6QNyckj1N9YJDXS5j68IZX7uPM5JRM="], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "4aeec93f-04c0-4ad9-af2e-60251ab1808a", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "9b588373-78e4-4fe0-9515-d19ec9b2e92d", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "cd8a5152-8d10-4388-bee4-291b9c15c5c4", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "e442a70c-a726-472a-92a4-a9235c1d8cd1", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "f0879c76-b610-4b86-80e4-23698d9eed0b", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "OPTIONAL", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "8dba30bb-d318-45ed-9949-cf6f9f688c93", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "0ac533bc-3180-473e-81e8-4a48d0db8526", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "b2411d19-99da-4c93-a314-c594777c61c1", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "3087ad57-34f9-44a5-a9b5-c4adf59a8b45", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "66e876e9-c789-4e2b-8433-47511d08b427", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "1b3757fc-c94e-4aa5-a014-0cd4edcbb01b", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "OPTIONAL", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "0cca99b7-2770-4c77-a784-c21ec1194636", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "e60b32ee-cf7c-42d9-92e4-f02786a46eec", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "6e8ad006-1e0c-4269-888f-09c719588227", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"_browser_header.xXSSProtection": "1; mode=block", "_browser_header.xFrameOptions": "SAMEORIGIN", "_browser_header.strictTransportSecurity": "max-age=31536000; includeSubDomains", "permanentLockout": "false", "quickLoginCheckMilliSeconds": "1000", "_browser_header.xRobotsTag": "none", "maxFailureWaitSeconds": "900", "minimumQuickLoginWaitSeconds": "60", "failureFactor": "30", "actionTokenGeneratedByUserLifespan": "300", "maxDeltaTimeSeconds": "43200", "_browser_header.xContentTypeOptions": "nosniff", "actionTokenGeneratedByAdminLifespan": "43200", "bruteForceProtected": "false", "_browser_header.contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "waitIncrementSeconds": "60", "custom_attribute": "custom_value"}, "keycloakVersion": "7.2.0.GA"}, {"id": "Migration2", "realm": "Migration2", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "offlineSessionIdleTimeout": 2592000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "4964ebad-22de-4a75-89a0-929b5536c895", "name": "uma_authorization", "description": "${role_uma_authorization}", "scopeParamRequired": false, "composite": false, "clientRole": false, "containerId": "Migration2"}, {"id": "52aa5665-13b7-4ce6-b486-451bd84e5589", "name": "offline_access", "description": "${role_offline-access}", "scopeParamRequired": true, "composite": false, "clientRole": false, "containerId": "Migration2"}, {"id": "a495da40-f44c-4e28-8f82-75bb5677e597", "name": "default-roles-migration2", "description": "${role_default-roles}", "scopeParamRequired": true, "composite": false}], "client": {"realm-management": [{"id": "a3fa8d19-c5f6-4257-b5cf-09d44dab2b64", "name": "query-realms", "description": "${role_query-realms}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "8cdacaa0-32af-4675-a86c-f884b193e006", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "a9a5385e-be6e-4322-bd77-084c03eb05db", "name": "create-client", "description": "${role_create-client}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "ee81f037-037a-4a9d-ad7d-33e8cbde0812", "name": "realm-admin", "description": "${role_realm-admin}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"realm-management": ["query-realms", "manage-identity-providers", "create-client", "view-clients", "view-identity-providers", "view-realm", "view-authorization", "manage-clients", "query-clients", "query-users", "manage-realm", "view-events", "query-groups", "view-users", "manage-authorization", "impersonation", "manage-events", "manage-users"]}}, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "981f86cb-b5a2-4e25-a611-b1db07338112", "name": "view-clients", "description": "${role_view-clients}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "3c59f1ab-e017-4a84-a917-7aab15d778e5", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "05d49384-7dd7-4a76-9621-b20dab496ed4", "name": "view-realm", "description": "${role_view-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "06bce59c-3530-441a-bfc0-166f0c934d05", "name": "view-authorization", "description": "${role_view-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "31ff5f25-abbe-4f6d-9df1-8e3a1e05f418", "name": "manage-clients", "description": "${role_manage-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "547cb7ba-48c3-49b0-ab5e-d26ac4785e6d", "name": "query-clients", "description": "${role_query-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "1a8ae70a-512a-4852-8d92-2126807012e0", "name": "query-users", "description": "${role_query-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "958491d4-d245-4d20-8247-c75a638143b4", "name": "view-events", "description": "${role_view-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "138cd3bc-b216-4b6c-b20b-c3546134433b", "name": "manage-realm", "description": "${role_manage-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "af32b6c7-3a6d-40fc-81d9-0dec1da63c94", "name": "view-users", "description": "${role_view-users}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "74dafba6-f16d-4d96-86f6-c4a008586e69", "name": "query-groups", "description": "${role_query-groups}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "cb6edf78-a174-4133-82d5-7ddda57796b1", "name": "manage-authorization", "description": "${role_manage-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "ef93b4d5-0d62-42cb-b94b-ca39a878846b", "name": "manage-events", "description": "${role_manage-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "eeb0ebe9-667e-42aa-b184-ce13d9f6cc29", "name": "impersonation", "description": "${role_impersonation}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}, {"id": "b4894919-b766-498c-b581-ac41e85cf073", "name": "manage-users", "description": "${role_manage-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "156f2936-f142-4794-85f4-4bd1f574c186"}], "security-admin-console": [], "admin-cli": [], "broker": [{"id": "4d4207af-f814-4b92-977f-540cdb5c51d3", "name": "read-token", "description": "${role_read-token}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "b50b31d0-d448-4c13-be2e-58238c5265b1"}], "account": [{"id": "7d9e5449-1fee-400e-b86e-6cc38c198f2e", "name": "view-profile", "description": "${role_view-profile}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "941b7d5a-a9ff-4b74-b6dc-9d709d1af361"}, {"id": "f643824c-dd0d-4c37-99a6-4b0acdee1c4e", "name": "manage-account", "description": "${role_manage-account}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "941b7d5a-a9ff-4b74-b6dc-9d709d1af361"}, {"id": "92ac0557-2e4e-4152-9aa8-ceaacac7724d", "name": "manage-account-links", "description": "${role_manage-account-links}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "941b7d5a-a9ff-4b74-b6dc-9d709d1af361"}]}}, "groups": [], "defaultRoles": ["uma_authorization", "offline_access"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "clients": [{"id": "941b7d5a-a9ff-4b74-b6dc-9d709d1af361", "clientId": "account", "name": "${client_account}", "baseUrl": "/auth/realms/Migration2/account", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "2ac5a2b3-1f28-4bca-9203-d7ba3175a7f2", "defaultRoles": ["view-profile", "manage-account"], "redirectUris": ["/auth/realms/Migration2/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "5f42539b-2d21-4b5d-87b9-db29fe46f3a8", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "850679f9-5504-4a90-b2c2-f9c570c6bd89", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "826a7201-0065-4d3f-878b-ca8dd640e70d", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "8b00b7cc-03d8-4425-a5cb-f0d05dcf7698", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "9469d99a-c246-4569-8071-b7020fe9d005", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "4c1a6bc5-ede9-4599-8376-b36611e43b1e", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "3b1c295f-6925-4c3c-83df-50de74a5e5cb", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "********-bc35-499f-aa70-ea581cf2296d", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "7a74eab8-b021-456f-a0d1-97c02b50e757", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "d9cfeaf1-0f9c-47f5-852d-5830e9de3b35", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "1b100b73-66af-4bfd-a465-e93727c255a6", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "b7106dc4-d42a-4bd5-aba4-4a9110832c5e", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "eafcfbfc-ad79-4025-8397-9a60e25aaa71", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "58bafbfa-28b7-4df7-8a33-058c0fd1a8ca", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "b50b31d0-d448-4c13-be2e-58238c5265b1", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "87e3ecfd-403f-4588-a05d-67bf146a4e66", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "d56a39d1-73f6-4bc7-8f60-b7db498e95a0", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "b6a31338-109a-454d-a4f5-f1dca4d2f31d", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "36d7bc16-81b2-4b10-8349-e2a3fa873198", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "28205124-2380-404a-97a6-8df83a19280b", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "05a85d5d-b259-4306-8f7a-56dc26e46834", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "802fd97a-32af-440f-ae65-8677eacd4a02", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "156f2936-f142-4794-85f4-4bd1f574c186", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "5afb05d6-ec9c-475c-9223-a1f763231849", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "37d72504-0251-4cdb-bd91-8a9e2f91209f", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "835a6fe0-4592-4fb4-81a9-a8493a19396b", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "8aff01b9-3f1b-4b6f-b345-3b53f8a3a741", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "70da980a-78d7-4693-b500-eedab812d2a2", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "ec69626c-f60d-4570-b674-a24a90364037", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "263ee03e-8ab9-40ff-95a9-8009a1d2e964", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "fd7d7b95-d481-4968-8f0d-55fc52648905", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "baseUrl": "/auth/admin/Migration2/console/index.html", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "d8a2115b-d828-4df7-8ea2-5a7bfdcc5fec", "redirectUris": ["/auth/admin/Migration2/console/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "02790f80-9c12-4a94-b2d0-f30200708566", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "4d66c016-88ae-453b-acb1-fa9b05f32165", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "2202ef3b-4287-44b4-83ce-e88636f29c2d", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "consentText": "${locale}", "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "90cef744-49d2-4cda-aabe-24d543f54bcb", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "9060d721-9052-4f77-8e0a-68017fa7aff8", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "33014831-fde3-4419-b9a6-fef673610aba", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "953fe292-c2b1-420d-a493-99d8639a4771", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}], "clientTemplates": [], "browserSecurityHeaders": {"xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "xXSSProtection": "1; mode=block", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "b309fc08-f29a-4708-ac92-8788db40b8fb", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "898ed21e-bdbe-414c-8f2a-6643d9555260", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {}}, {"id": "f3d788b0-b8b8-4869-a18a-b13261999775", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "oidc-address-mapper", "oidc-usermodel-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-full-name-mapper", "oidc-usermodel-property-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "d337f79c-e149-4aa1-b4b3-b25d0bd56f8a", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-property-mapper", "saml-user-attribute-mapper", "oidc-address-mapper", "oidc-full-name-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "oidc-usermodel-attribute-mapper", "oidc-sha256-pairwise-sub-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "3c8873de-b6b2-4117-a1e4-fb1f7d23c9cd", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "0259e98d-e83f-4dd0-8e15-940e253e1e9e", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "10bbb530-4e05-41a5-acac-ab7180985308", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "47f3a124-faab-439f-9679-9c1e9e0e06f6", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}], "org.keycloak.keys.KeyProvider": [{"id": "6109a0c5-4fa3-418c-8d28-d25da283ca7d", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["745c3015-c13d-40f7-a62b-50e5df91146a"], "secret": ["qCt9nPZQyaEuRM7O8C2KqMMGJHa_Wny4gtkrUdspuXk"], "priority": ["100"]}}, {"id": "6986f815-7865-4faf-b7f9-71a51c8f64cf", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAlDwYLnMoGdjKdaiomgvYiUxIeKuCEHCNSDrpkxwEPYUJvmXX6WZokZxjv43b5NSuKb0Z83VtMMRgX+DzDeVeDTyji82BCBR7tvBbmNpaXKNWtczURUHqmmh44WmS9njAtiGZsMdVlTsVs9pcfk5UgFdjq92ov9rLL5SLyBR13vebWEeeD9lVlIef43D8JSXptCyDUxAejWjHouYVWtJXq9dMxEJc5+biv6gtZeLflDeJAvfi8iPn8LqsVmMDvCjV+aqB5Yk+BsPC/rbh75amZoJ4Mo9jiEsQnte74eWY8tWc1UlbJaAoDw1JoroRVgpXmdvb26vebfxRmWbU/m5bYQIDAQABAoIBACdUOmY4j28sIatN8E3Ozsi+wIujlYKfGh0L5GTvjgB0h71IcByw2xWnFKACF1GDTCIu1e/OPAYwVBGcMWCCdo20hP/WqmFmbdx/sQ3BATCK9bqOedNWRdRp24iS5jcWmqq4B3rrqD1Ly9pIGTo5thnHFd7HlLoyZq5dDykqpItfZlDbprvIGi3lpc/fRMLQiZk3ca48zqtCtAJbNXEcRSs/BFdvz75mIw2f3BQ8Myhh5EfLfUaeHH7Uw17ElKZSc83RcALuzCiOnbCVEG3MEl6D32OGDQte1V/0qLGcxkmas2eaAyL+AuCSL7pS/rJippfustYHY8MTwo0aR0sUT7ECgYEA3Z1RslavS9MFDYP07xfdaHdtzhpzNaV+zH6FIUuLsvgODsqm18RWbVDFe1Tm1M/NALX1wk8JHdixOuTKss22j7wR1EZe/6W1su8SATxnQv8MmZ3/KMboqabLskwfcRwk1zELbKGcLdLu1Oew5Ki7uz+KjC3myb0tEucbiYVexpcCgYEAqzwUfcgjxEszP+cnaj734HBCZ6L+IDQoapuG8O8JiiZEkPm5GHO/FEQlgxHdIhRf+yw1lSbabzX+dPvuXV+mAiiN0LcmuTxTKvx2wESFbQYytJmMRoNBh6ve6s93Pi6ozo8jVNqRlVDzZvFLHhxMEGOpjGKIdJaNFgyA0jIpZMcCgYAKax/xG2QoU2ZUaVS1fNC63Yp6+wFZj0szv+rwUsXhhwnaJirLz/4kK3ztPGORs6c2lldUaDTMg2hplWH2H/eqMDRCX4CN0jOFkvmstzM/kg/8lNHvD9LWTvIakruTrLgKqdKYq8xU1yrFNZM6XLheqDESQAIuj8czsUu5Hl0DRwKBgQCYtmxQAT5kwexjAXRVfPL0ZynR47tgXSPub+ZY2dZYLujXot9Av3maSWxrCHN/AUzYZQUkkBIfPLhC/KwI+lTDeAeCLNWMPhCuNZpyvqmAIhb2mjpQaJ74sH+OSpi+DD7geSe5dXSBBkENfgTQRcxQkZGUcgvgbqRFFcGNtM7V4QKBgC0SQc20ydyHV3M2jd51/aDphhlCHYbOyRHRCLacutqRa0T+sor+vvfBJs4HaIMHx09v3gvng2KCw+8G+CbEYZjPZhog4zmODysBxodo66bH/Lhf38XIO/i+2KjuvRot43oqKuOziyWZjxdfptQMVHdPm0juCrzs5CmxhQE2Tk3L"], "certificate": ["MIICozCCAYsCBgFkrloSnjANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApNaWdyYXRpb24yMB4XDTE4MDcxODE3MDMxNVoXDTI4MDcxODE3MDQ1NVowFTETMBEGA1UEAwwKTWlncmF0aW9uMjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJQ8GC5zKBnYynWoqJoL2IlMSHirghBwjUg66ZMcBD2FCb5l1+lmaJGcY7+N2+TUrim9GfN1bTDEYF/g8w3lXg08o4vNgQgUe7bwW5jaWlyjVrXM1EVB6ppoeOFpkvZ4wLYhmbDHVZU7FbPaXH5OVIBXY6vdqL/ayy+Ui8gUdd73m1hHng/ZVZSHn+Nw/CUl6bQsg1MQHo1ox6LmFVrSV6vXTMRCXOfm4r+oLWXi35Q3iQL34vIj5/C6rFZjA7wo1fmqgeWJPgbDwv624e+WpmaCeDKPY4hLEJ7Xu+HlmPLVnNVJWyWgKA8NSaK6EVYKV5nb29ur3m38UZlm1P5uW2ECAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAS2UFr1Bjlz5nD7bm9/kyRMjY7u2JIwS1LdZPS4JtsofeN0nvRe416JKv2qfWHfRVXHR5i7axaADMN8s9rPl+1AdWHK4MOUKn5j3b9uEHi+2ipTijHBn3oUUgC5f6Dn3bgbUlKcHYaBUpUf+MGiGNMJegIEblgXKnnD6lGe4dphwEJuZJ56P/DzR1GGLY+m8RF84v1p8jSt+n4pKHIGLtcaHDiOYEiFHQXli8tYtPmXhO/9BZjNFKjaOgBrjO4pmqqZovEwbza3Rh7m5AVtmbEqUjynJyChc4zn8Ba77U3hta8lhJj+aL+xpxrQnWpzgRC7KxC78yCjXtYIQ52lJYuQ=="], "priority": ["100"]}}, {"id": "21b61c4d-9912-4ef5-9d2e-e8af323f9ba8", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["35ccdfd3-9965-460f-88d7-bce2e2ebe171"], "secret": ["d_pFytrgOeyD3NY_Jza01g"], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "5aa30cef-ab4a-495d-a517-54e8f7f8e593", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "f5abece4-a6aa-46cb-9358-051f83373bcc", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "31ddf121-be50-4105-b746-5e08ecca509b", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "26fbfddc-3c47-41fa-a4d9-82bdff8851c4", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "c32b3d17-ee1f-45cb-a180-70664b62def7", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "OPTIONAL", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "d4658790-1613-4b09-a2c1-96e6c55496da", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "dc9fed88-2f45-4645-b263-89bece6303e7", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "ea8fa13d-ab5a-466b-9fb3-15c3ee46ef22", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "eaca49d0-bd77-4b4a-94bc-02828c281fe5", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "9501658c-7048-4748-91f4-b23300d2bdee", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "ec7915cd-3ee8-4c78-80c3-6da2db83d5a2", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "OPTIONAL", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "751eba34-3ff2-48d1-9cb2-908907dba8b1", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "0f4612d2-6134-4cf4-a3f0-e9122df417f0", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "73fba97e-add0-40f4-8343-be601b3f9821", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"_browser_header.xXSSProtection": "1; mode=block", "_browser_header.xFrameOptions": "SAMEORIGIN", "_browser_header.strictTransportSecurity": "max-age=31536000; includeSubDomains", "permanentLockout": "false", "quickLoginCheckMilliSeconds": "1000", "_browser_header.xRobotsTag": "none", "maxFailureWaitSeconds": "900", "minimumQuickLoginWaitSeconds": "60", "failureFactor": "30", "actionTokenGeneratedByUserLifespan": "300", "maxDeltaTimeSeconds": "43200", "_browser_header.xContentTypeOptions": "nosniff", "actionTokenGeneratedByAdminLifespan": "43200", "bruteForceProtected": "false", "_browser_header.contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "waitIncrementSeconds": "60"}, "keycloakVersion": "7.2.0.GA"}, {"id": "master", "realm": "master", "displayName": "rh-sso", "displayNameHtml": "<strong>Red Hat</strong><sup>®</sup> Single Sign On", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 60, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "offlineSessionIdleTimeout": 2592000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "1f241def-c321-48dd-bccd-24f0e9d12d53", "name": "offline_access", "description": "${role_offline-access}", "scopeParamRequired": true, "composite": false, "clientRole": false, "containerId": "master"}, {"id": "5bc76fc4-2285-4bbd-b9e1-d64891f920e7", "name": "master-test-realm-role", "scopeParamRequired": false, "composite": false, "clientRole": false, "containerId": "master"}, {"id": "bf15b2b8-a97b-413f-a0a9-87cb064af13a", "name": "create-realm", "description": "${role_create-realm}", "scopeParamRequired": false, "composite": false, "clientRole": false, "containerId": "master"}, {"id": "9b987a92-def0-4541-9fc3-efdd9aa952b0", "name": "uma_authorization", "description": "${role_uma_authorization}", "scopeParamRequired": false, "composite": false, "clientRole": false, "containerId": "master"}, {"id": "c9c57cc5-ee50-4122-b87f-24b31638b0db", "name": "admin", "description": "${role_admin}", "scopeParamRequired": false, "composite": true, "composites": {"realm": ["create-realm"], "client": {"Migration-realm": ["manage-clients", "manage-authorization", "query-groups", "view-events", "manage-users", "query-realms", "view-authorization", "view-identity-providers", "query-clients", "view-clients", "impersonation", "manage-realm", "view-realm", "manage-events", "create-client", "query-users", "manage-identity-providers", "view-users"], "master-realm": ["query-users", "manage-authorization", "query-realms", "manage-events", "view-users", "query-clients", "manage-identity-providers", "manage-clients", "create-client", "view-events", "manage-realm", "manage-users", "view-authorization", "view-identity-providers", "view-clients", "query-groups", "view-realm", "impersonation"], "Migration2-realm": ["manage-identity-providers", "view-clients", "view-events", "impersonation", "view-realm", "create-client", "query-clients", "query-realms", "manage-events", "manage-clients", "manage-authorization", "view-identity-providers", "view-users", "manage-users", "query-groups", "query-users", "view-authorization", "manage-realm"]}}, "clientRole": false, "containerId": "master"}], "client": {"security-admin-console": [], "master-test-client": [{"id": "c20a33ec-cbd3-4d2b-bdb5-5fb4cfa50906", "name": "master-test-client-role", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "2dabff6e-9c44-4209-b2ec-bd7ff163c3d2"}], "admin-cli": [], "Migration-realm": [{"id": "e9be8e81-de84-4454-80e9-d7766d68daf5", "name": "view-events", "description": "${role_view-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "69d94e18-f063-4ed2-b686-32b9198195e6", "name": "manage-clients", "description": "${role_manage-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "76f659bd-e3dd-4f14-8185-b0d215acf4f9", "name": "manage-authorization", "description": "${role_manage-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "1ae6e8c8-66d0-42c0-8cd2-c307a8c92386", "name": "manage-users", "description": "${role_manage-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "80e02a58-9c0a-4049-a7ea-b222f13a456c", "name": "query-realms", "description": "${role_query-realms}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "e4993b2f-2fe1-46d5-b1f6-f4269b5a4bd3", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "454389f7-6392-4e20-b93d-02516dff79ae", "name": "view-authorization", "description": "${role_view-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "41d9fec3-985f-482a-a37f-9954dd922209", "name": "view-users", "description": "${role_view-users}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"Migration-realm": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "49e0d5ab-fbf2-4386-8df7-03ebec381dae", "name": "create-client", "description": "${role_create-client}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "772a64b9-45d6-4c28-a868-bfa219254166", "name": "query-clients", "description": "${role_query-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "901dda4d-dbbe-48ef-bf2d-01f5f5b188c1", "name": "view-clients", "description": "${role_view-clients}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"Migration-realm": ["query-clients"]}}, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "ee2eff52-f086-4636-97ab-1a5594fcc34d", "name": "query-users", "description": "${role_query-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "99f3686a-8a22-4080-877a-5614e6f5e975", "name": "impersonation", "description": "${role_impersonation}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "a96a42ce-2ae7-4a3f-b92a-ba4bebdc0966", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "44279f21-d51b-4d62-b484-367f12e2cc45", "name": "query-groups", "description": "${role_query-groups}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "95b10a48-8f93-47c3-98e7-c405da5e4c52", "name": "manage-realm", "description": "${role_manage-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "40ab143c-db70-4e9d-9bb5-9ff5bf489408", "name": "view-realm", "description": "${role_view-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}, {"id": "ce7eb4ac-5c24-48f2-8b02-6e4ac457efaa", "name": "manage-events", "description": "${role_manage-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62ee653d-e689-4b6e-9749-8a40ed646bad"}], "broker": [{"id": "68e4bdee-e652-4205-bfd4-f8d9c923c73d", "name": "read-token", "description": "${role_read-token}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "1502ad94-2ca7-4c4b-9ed9-3f4e8bb79eee"}], "master-realm": [{"id": "3a995cb4-c237-4b49-83ff-6c28cf529625", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "9bbfdf90-3570-4009-af6e-36a2cb505963", "name": "view-clients", "description": "${role_view-clients}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"master-realm": ["query-clients"]}}, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "9fafd3f0-5ea1-4ec3-bb24-4d94ae07693a", "name": "manage-clients", "description": "${role_manage-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "31fee9d3-2d8b-4bbe-96e4-1d5823eda6dd", "name": "query-groups", "description": "${role_query-groups}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "74aaefc0-5ba2-41e7-a394-e1ea8982ebc9", "name": "query-users", "description": "${role_query-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "b26779f5-d9d8-4fad-8b6a-802f1463acc9", "name": "view-events", "description": "${role_view-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "fba6e8bc-e5d0-47fe-9b4a-1167ba8da55b", "name": "manage-authorization", "description": "${role_manage-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "6bf436e2-bb31-452b-93ac-255ecbfa7b10", "name": "manage-realm", "description": "${role_manage-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "b5ee4352-cb26-4f61-9580-452f03f9052d", "name": "create-client", "description": "${role_create-client}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "33614767-2e13-4bd0-835e-8cd053d40da3", "name": "manage-users", "description": "${role_manage-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "fa271fa6-11ff-4e1f-b18c-333e29d80470", "name": "query-realms", "description": "${role_query-realms}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "a7fbb877-e684-491d-9e90-19df6f73f2e0", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "016a37e5-1aac-485b-8893-1196cb7008b6", "name": "view-authorization", "description": "${role_view-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "9906e4ed-39f3-41c7-b263-2b351d42f698", "name": "view-realm", "description": "${role_view-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "81ef7683-f0a6-49f2-8c13-65b2d2098cc7", "name": "manage-events", "description": "${role_manage-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "195a589d-25e9-40f7-9e3e-36eb74e01f64", "name": "impersonation", "description": "${role_impersonation}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "95efbc80-3a84-47d1-857a-47c882a9591e", "name": "view-users", "description": "${role_view-users}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"master-realm": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}, {"id": "c29ba7c8-2c02-4e74-9e59-5a35f489a066", "name": "query-clients", "description": "${role_query-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "0be91c31-dad5-4451-b0db-a3296cf0705d"}], "Migration2-realm": [{"id": "1f8fff99-5f93-42dd-b34e-65ff901034d8", "name": "view-events", "description": "${role_view-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "780d0701-508c-463c-b32e-b1f73051e62f", "name": "manage-events", "description": "${role_manage-events}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "e5f9afbb-b8ce-47bd-8501-cfcb18c32880", "name": "query-groups", "description": "${role_query-groups}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "98634468-7715-4c64-9bb3-417c1429cb94", "name": "impersonation", "description": "${role_impersonation}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "5c52d194-c82a-4d56-9e81-d71c01839500", "name": "view-realm", "description": "${role_view-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "ec8bf9ab-fa8f-463d-9a99-74d91219f369", "name": "manage-clients", "description": "${role_manage-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "3ad587f2-11c3-4c1c-bb24-792a9dd792e0", "name": "query-users", "description": "${role_query-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "78733970-6c2d-4bd7-8fc9-d90f6926d6d5", "name": "manage-authorization", "description": "${role_manage-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "06ec1d2c-690f-473d-96e8-7b3747523255", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "033efd6b-603c-4669-941c-ce243c9846fb", "name": "create-client", "description": "${role_create-client}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "962145a9-0c57-48db-a7a2-c02edfe75d28", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "014d0a44-20e0-4d22-8a5c-794d706015b1", "name": "view-users", "description": "${role_view-users}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"Migration2-realm": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "8639a93f-6cb1-4db7-ad0d-2e1f0413c5a1", "name": "manage-users", "description": "${role_manage-users}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "59a966da-2142-4e1e-82ff-b90bb6d1024d", "name": "view-authorization", "description": "${role_view-authorization}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "203ebcde-7321-4dd2-922b-0bd61e9aa0cf", "name": "query-clients", "description": "${role_query-clients}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "c3dd556e-d72c-4043-a140-6beadc714d12", "name": "view-clients", "description": "${role_view-clients}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"Migration2-realm": ["query-clients"]}}, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "2bd182b5-b9a5-4f5d-a849-9661bf68be41", "name": "manage-realm", "description": "${role_manage-realm}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}, {"id": "d4942c2e-b919-43cf-a796-2ed66cbf4c7e", "name": "query-realms", "description": "${role_query-realms}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "a99b1d3e-df30-4017-aadb-343d69ce2455"}], "account": [{"id": "9d920c0f-399d-4af5-beb8-216a876df62f", "name": "view-profile", "description": "${role_view-profile}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62aa9d9f-13fc-491e-93b2-35e7d43bc7c9"}, {"id": "2f34fceb-6258-49e4-b150-fe363c84c190", "name": "manage-account-links", "description": "${role_manage-account-links}", "scopeParamRequired": false, "composite": false, "clientRole": true, "containerId": "62aa9d9f-13fc-491e-93b2-35e7d43bc7c9"}, {"id": "8a978fcf-d007-48d2-a2af-cf95ba23992c", "name": "manage-account", "description": "${role_manage-account}", "scopeParamRequired": false, "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "62aa9d9f-13fc-491e-93b2-35e7d43bc7c9"}]}}, "groups": [{"id": "e82cd044-f53b-4404-bc0b-816b8ed66663", "name": "master-test-group", "path": "/master-test-group", "attributes": {}, "realmRoles": [], "clientRoles": {}, "subGroups": []}], "defaultRoles": ["offline_access", "uma_authorization", "master-test-realm-role"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "users": [{"id": "c02438db-d977-458d-9eb3-0b95015bcc05", "createdTimestamp": *************, "username": "admin", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"type": "password", "hashedSaltedValue": "JOrh81WYkyhsdJSYiQZEpbtV4FQYEMmqRxncHiBZKunm8g0zNqQOezqEF20IJZWvxvudT6wAcmTzWsv4Qd1tvg==", "salt": "no71Rq8NWrZUpRY8cPxo2Q==", "hashIterations": 27500, "counter": 0, "algorithm": "pbkdf2-sha256", "digits": 0, "period": 0, "createdDate": *************, "config": {}}], "disableableCredentialTypes": ["password"], "requiredActions": [], "realmRoles": ["offline_access", "uma_authorization", "admin"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}, {"id": "cd8942ab-f0a8-453e-a869-c64660c1e951", "createdTimestamp": *************, "username": "master-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["offline_access", "uma_authorization"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}], "clients": [{"id": "62ee653d-e689-4b6e-9749-8a40ed646bad", "clientId": "Migration-realm", "name": "Migration Realm", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "a60f5e9c-f06e-4368-ba28-1db56d705964", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "4e7b2e63-e100-4591-b1c1-cfebd836d152", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "b444cf05-3cdc-4f1f-a3b4-4fd592b7b032", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "25ec79e6-2d8c-467f-a313-32ec71b03f2a", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "a149528d-b6fb-43c8-961a-ce0d47ce1c4d", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "25f393ab-3688-4726-95ae-1171076899b2", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "eaded6d8-09a4-453f-97ba-b1fe1c476d99", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "a99b1d3e-df30-4017-aadb-343d69ce2455", "clientId": "Migration2-realm", "name": "Migration2 Realm", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "********-99c8-4c13-b107-426c0f2d3564", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "8912a270-9a3d-42d2-bba5-4ae303a36823", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "0c79bf3d-8662-4e03-87b4-8ed940129e91", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "d5ba1a77-07e4-4ed7-a2e6-5d65f14eff1e", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "c858b6e5-2a0f-4642-9863-814e1660db9e", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "210b828b-4a2d-487b-8300-78a1720f8224", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "f70f2c7b-fcc3-4520-9f63-5100e66f333a", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "62aa9d9f-13fc-491e-93b2-35e7d43bc7c9", "clientId": "account", "name": "${client_account}", "baseUrl": "/auth/realms/master/account", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "8a5cb743-0bb4-419a-8865-07e098f3422b", "defaultRoles": ["manage-account", "view-profile"], "redirectUris": ["/auth/realms/master/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "5e43974d-443f-4044-95cf-a6bcc6cf1190", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "2c08ad6d-db09-48a0-b005-833a18bd0eb8", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "a91a2dd6-42fd-4e7e-9645-1f3fd113c1d8", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "df05ddca-912a-45f9-962c-bb27977c5457", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "55677383-ac15-4baa-bce2-cd74db3e0bdf", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "a7e85bc0-a423-413c-b6d3-b1c67c5e3165", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "a26af91f-5627-468b-9dd8-5fd5c23e1e60", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "8adf97b4-8bfc-48ba-a1e3-b54aa2e96c7a", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "43ece8df-7404-480e-bd67-ee643006ecd6", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "badd4293-cf66-4086-a347-1829e05b98d3", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "c5590293-091f-49cf-a6a0-5e22fea164f9", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "c2498730-2893-458c-b390-8632bce80e98", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "d892b485-5a8c-4435-a148-f7efdf31ae74", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "396c2b2e-5964-4be5-9e64-41b83c41c83c", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "1502ad94-2ca7-4c4b-9ed9-3f4e8bb79eee", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "167d3095-7849-4754-afb4-0289d13a6f5a", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "3b5a7c7e-9409-4256-a6f4-caee2cac297b", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "5e2972ba-5863-449c-9901-495749afb4eb", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "d3ab076c-edea-4a3e-8866-1d1b140c5ef0", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "242a20aa-689e-4647-894f-1ed332aa596c", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "e6466411-4795-4bcd-a204-a1c81c51e161", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "b1c296cd-2d2f-4e40-9227-a0b8bba64102", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "0be91c31-dad5-4451-b0db-a3296cf0705d", "clientId": "master-realm", "name": "master Realm", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "56262a8e-9ae8-4dca-b8c2-bf692a8d809f", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "8f53ef4a-025a-470f-b17f-1edeeed3f5bf", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "baab765b-49e3-4f42-b2a7-5da9d4502e61", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "13886d71-8a63-4daa-b38f-bd1031ace75e", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "ae3ffcb3-8aec-4e06-81b0-8fccd2b34997", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "08224daf-d596-4dd7-a737-252e1baa4f1b", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "b7452afe-3432-47d7-9517-c72ecfc89210", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "2dabff6e-9c44-4209-b2ec-bd7ff163c3d2", "clientId": "master-test-client", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "932dd4b7-42e8-44d6-9791-651bec2a757b", "defaultRoles": ["master-test-client-role"], "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "9e048073-2155-4570-bc44-a95c71c4649b", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "33ba7776-f989-4293-a9d0-98f818d9f0fe", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "aed8a845-37d4-44d7-a937-a2127bc8a4a9", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "76c49323-f40f-41ed-a9ed-2e003e1d81aa", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "02a428f2-e354-4de1-b748-2dec9ebafc61", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "e5045347-d4c2-4910-8cec-4286540f3ffb", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "2719b88a-55a7-41c0-9656-994a7877a2fa", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "baseUrl": "/auth/admin/master/console/index.html", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "3f055f68-af42-4833-82f4-37db64f998c2", "redirectUris": ["/auth/admin/master/console/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "4424d248-1025-436a-b924-4f40ec2513fe", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "d6ad7923-bf33-4d1e-b7ce-3ec18adcee14", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "3ee0f495-703b-4a23-ac0b-899da340560e", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "7160a43a-ed4e-4ce6-8aa0-e9e3e7296e98", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "45d3d48e-b21a-404a-a9c9-8952f65c9a55", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "consentText": "${locale}", "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "9d084123-56fe-4456-b0cd-508532461d43", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "0a90de95-7e20-4d1f-ba26-eb1efaaff76e", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}], "clientTemplates": [], "browserSecurityHeaders": {"xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "xXSSProtection": "1; mode=block", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "9bd2b50e-9c26-483d-93cf-9543fb775b80", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "199f8220-ceb7-4e16-b1e6-cf5ed072efbb", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "oidc-full-name-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "oidc-usermodel-property-mapper", "oidc-address-mapper", "saml-user-attribute-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "1c6036e3-e32b-416e-8efd-3f1b8f038ce5", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "3b21beb1-7dfa-4ebd-9ef8-08f1dbfff488", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "saml-user-property-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-role-list-mapper", "oidc-full-name-mapper", "oidc-address-mapper", "oidc-usermodel-property-mapper", "oidc-usermodel-attribute-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "05defa1e-5899-4fb6-acae-7736153545bd", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "ab481c86-8e30-4783-b009-51d3c58e55b0", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "e62dbfb3-f912-41b6-9462-d6cabf4d55cd", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "507f81ae-ea28-49aa-9669-bcea2c2e595e", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {}}], "org.keycloak.keys.KeyProvider": [{"id": "a9dc1039-bcb1-4c89-9df9-87c9b481b07e", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpAIBAAKCAQEAujBrGJFngOcesU65PVgHPOyNu5zh6QktkkBBuEsfuHvW2t/inXW8XBZ1QwMk6YGV/4ogNqiHMKaDCOewCBSHiX2HiJh5PXDGQHQgqLzeeQHfndGIk7JaDrlWjEEeE7tx430u+R+/leCHjNTH8FmBIg777OXnFRDWh9JabHrLTkZU+9vLLUosH1LDI6ICgeHPUunb4GMcAoOcccOPc0IHUbBc1OdrpVRl/Q0EgRp9Y/rce8DgrB8rJbxTDMNedh5vOwayFr0kVsDhioxX7o1SFKIz6Kx+yRGNjaUVWTUxoOC9PsgU9n7S2fbh9OHmUJ1yXAFCWzPiMlJo3ehscr45YQIDAQABAoIBAQC58STjckM13psuY/pnIwSHeY7KcRDEFCBo0LdRf+T77uFR0QmWS0CDjwIonHk+oeXVQHKPY5svoPt35zpLt4TscGrspalfDMNRFyiOm6BqpM5X5CxLpHCAB+RBSb7p4ecJU7mXnndNDOESamYKyLhH9ULvAWLYDOrS1ebsM23ainp4O49qm0p+eT35kvzwB5tH/4zJIlOvZRoYpqslie6EU12bxvViD1uejoh0vJ5++05qYowbBv2Qg8wTfx4up0bt54pkhgDZ6J2x+WZhwygdZuP5oZUtU4oWZWV7AaWUpYs8pFNJGP9PxE9uZbuKv8SAhhGAbGNWJhDdVemzSO7hAoGBAOI4dP36yDKJ1VlVlpCze+ypWXoApo7o7NWP+OLc59BntotUfIRO7otO1y/NImn+28hoZBbmL4s0M+o4ncZW/jSQPn9UdQl7FWalC3kADZuhS9YVrOkstVc2tVa5mSBtNq1VMIXS1ZhzEd8NdAy1QlXqbQxnTnYwTa3arqZzdN5NAoGBANKy6zwJk4RgWdNms8PW8DXR5RmMwY/21FAQH3lG6HKjyFrzcvrj8qpOC19tTULHcE33HlZNkhfuVflLYnTjhL7aAZe+JFF2GeRiWbdsEFsqHMMYf1+7WegmvBFjApUNqiwQ3TCaetxqjp6vDb9iEI4lfBPR8B/X5S1mQEMkIRllAoGBALN8k+aHuZvPnIU60TGSFrsZSrLwHTa6PF7d0l09ZHgIMd+ucvUPI1lIfpegrLNo0Lzbhr5ceFB8ZMxri1M45oyR/eRNAGc7TymXvdiNYD6g5WjA3eZL4L2H3K0oqQP/Yyh3tP4Y3/wLIJzXMcJ7dlxwmYED3k8L2DYekf/jD1DpAoGAMkgwiytd4QF9bk3cKKhquu1UVzyYFNs638B5QmABqRoc/leK4QT1LF9jp2pWPdGKo5CN1Nq+OqfOLKgo3uxNCZ7NMoFV+XrNwwsVjghj/t2V75kxDI+RbS2RRDT0sM2KBuofXzH+kAt97bCo/ztP6BxZ+ADp5S+IqcT1IRDGNWECgYBlRFj5fSMZoBDfZSoMFMKQh0jgy9vrF/6rwHkXeNLvgJdjgvg948VZZQDTjFFb18Gu7TJa4cjrnkXU6EMoQrTAaZEQ5uYviyjfnn/1liy8dEMAk4xQssZxk5Nepy2S2qzP2OLG3XxQVHAteVmF9j4vN53RKDUYHKopdeUW2KCIMw=="], "certificate": ["MIICmzCCAYMCBgFkrkIIUzANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZtYXN0ZXIwHhcNMTgwNzE4MTYzNzAwWhcNMjgwNzE4MTYzODQwWjARMQ8wDQYDVQQDDAZtYXN0ZXIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC6MGsYkWeA5x6xTrk9WAc87I27nOHpCS2SQEG4Sx+4e9ba3+KddbxcFnVDAyTpgZX/iiA2qIcwpoMI57AIFIeJfYeImHk9cMZAdCCovN55Ad+d0YiTsloOuVaMQR4Tu3HjfS75H7+V4IeM1MfwWYEiDvvs5ecVENaH0lpsestORlT728stSiwfUsMjogKB4c9S6dvgYxwCg5xxw49zQgdRsFzU52ulVGX9DQSBGn1j+tx7wOCsHyslvFMMw152Hm87BrIWvSRWwOGKjFfujVIUojPorH7JEY2NpRVZNTGg4L0+yBT2ftLZ9uH04eZQnXJcAUJbM+IyUmjd6GxyvjlhAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAEKMR8MW33UP6YGUnh3HjEuw44MkrXR11OJlMoxyg8MKhJTkdl3Z8ssIn31YEAzlLZzMfaOPCLZBdlkhGophlPAJeyJt7niO8tfJlpZqQVUGJyzfRdK5lI+B4qp2Sl9pKxbMKs2fqrkYDv6fr81PNM/7vig+oLqlSkumPCa8/bcaxuP192nAWn0I2kRJd4g0yX606p0w0A4mRTrkcXH2MHNo+xuE+dV9ldjBYxrt42kZtiX2k7Crt7b5Bhs2ulJK+th6heWincJKITea8rSX4DBPqBo8dLkjuNbj7EbzSZ06vXsjL3+n2pIsVGQGTFmApt3aYCPjTXN8ty+Pc1OruBc="], "priority": ["100"]}}, {"id": "4ea1ff32-5300-40ad-8412-5c008d3958e1", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["d09adb46-2241-4d31-a095-9328c851815c"], "secret": ["AkfkOtoY5J_M0bqgm-yI68zLJhdgdpC2tQ5Hl7_qfpI"], "priority": ["100"]}}, {"id": "b50521ec-f3a8-4df0-9117-91a1151e7da4", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["23fd47dd-a9c5-4b3d-913e-622adfb43af6"], "secret": ["fdzNlH5OwaZlnDvCzShngQ"], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "ac803bd7-dcd6-446c-9486-a0916aa8a181", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "797a416b-bf36-4fd7-9a72-69d2796d179e", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "cf0a6db1-0b4b-4851-9ba2-f1cc0ebd9c92", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "3e8214b2-8cc5-4ab1-af89-79ca2b3b6cc6", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "fb6fb236-90f5-481f-a96f-9645fecdeb96", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "OPTIONAL", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "2131e7a3-69b6-47fe-a0de-86f1335fc7d2", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "3ec206f3-2fc3-4f60-a47f-f84d2c83e583", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "fddab4e6-59a9-4cb0-83fe-fbf341ad8897", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "32c3c60a-ba69-45be-b544-59daa07e260a", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "e692f812-8366-4d82-b2ea-ca5e9ecad634", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "a6a9438f-239d-431a-aeda-4569600291e5", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "OPTIONAL", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "f31b9762-88cf-448e-abde-46565179e151", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "0fe75ee6-b72b-4682-82e2-f296c6d0ea65", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "f3806ce9-6b99-4069-b76b-005f0850b37c", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"_browser_header.xXSSProtection": "1; mode=block", "_browser_header.xFrameOptions": "SAMEORIGIN", "_browser_header.strictTransportSecurity": "max-age=31536000; includeSubDomains", "permanentLockout": "false", "quickLoginCheckMilliSeconds": "1000", "displayName": "rh-sso", "_browser_header.xRobotsTag": "none", "maxFailureWaitSeconds": "900", "minimumQuickLoginWaitSeconds": "60", "displayNameHtml": "<strong>Red Hat</strong><sup>®</sup> Single Sign On", "failureFactor": "30", "maxDeltaTimeSeconds": "43200", "_browser_header.xContentTypeOptions": "nosniff", "bruteForceProtected": "false", "_browser_header.contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "waitIncrementSeconds": "60"}, "keycloakVersion": "3.4.3.Final"}]