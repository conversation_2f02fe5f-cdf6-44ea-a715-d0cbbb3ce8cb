parent=base
import=common/keycloak

styles=css/login.css
stylesCommon=vendor/patternfly-v4/patternfly.min.css vendor/patternfly-v3/css/patternfly.min.css vendor/patternfly-v3/css/patternfly-additions.min.css lib/pficon/pficon.css

meta=viewport==width=device-width,initial-scale=1

kcHtmlClass=login-pf
kcLoginClass=login-pf-page

kcLogoLink=http://www.keycloak.org

kcLogoClass=login-pf-brand

kcContainerClass=container-fluid
kcContentClass=col-sm-8 col-sm-offset-2 col-md-6 col-md-offset-3 col-lg-6 col-lg-offset-3

kcHeaderClass=login-pf-page-header
kcFeedbackAreaClass=col-md-12
kcLocaleClass=col-xs-12 col-sm-1

## Locale
kcLocaleMainClass=pf-c-dropdown
kcLocaleListClass=pf-c-dropdown__menu pf-m-align-right
kcLocaleItemClass=pf-c-dropdown__menu-item

## Alert
kcAlertClass=pf-c-alert pf-m-inline
kcAlertTitleClass=pf-c-alert__title kc-feedback-text

kcFormAreaClass=col-sm-10 col-sm-offset-1 col-md-8 col-md-offset-2 col-lg-8 col-lg-offset-2
kcFormCardClass=card-pf

### Social providers
kcFormSocialAccountListClass=pf-c-login__main-footer-links kc-social-links
kcFormSocialAccountListGridClass=pf-l-grid kc-social-grid
kcFormSocialAccountListButtonClass=pf-c-button pf-m-control pf-m-block kc-social-item kc-social-gray
kcFormSocialAccountListButtonDisabledClass=pf-m-aria-disabled
kcFormSocialAccountGridItem=pf-l-grid__item

kcFormSocialAccountNameClass=kc-social-provider-name
kcFormSocialAccountLinkClass=pf-c-login__main-footer-links-item-link
kcFormSocialAccountSectionClass=kc-social-section kc-social-gray
kcFormHeaderClass=login-pf-header

kcFeedbackErrorIcon=fa fa-fw fa-exclamation-circle
kcFeedbackWarningIcon=fa fa-fw fa-exclamation-triangle
kcFeedbackSuccessIcon=fa fa-fw fa-check-circle
kcFeedbackInfoIcon=fa fa-fw fa-info-circle

kcResetFlowIcon=pficon pficon-arrow fa

# WebAuthn icons
kcWebAuthnKeyIcon=pficon pficon-key
kcWebAuthnDefaultIcon=pficon pficon-key
kcWebAuthnUnknownIcon=pficon pficon-key unknown-transport-class
kcWebAuthnUSB=fa fa-usb
kcWebAuthnNFC=fa fa-wifi
kcWebAuthnBLE=fa fa-bluetooth-b
kcWebAuthnInternal=pficon pficon-key

kcFormClass=form-horizontal
kcFormGroupClass=form-group
kcFormGroupErrorClass=has-error
kcLabelClass=pf-c-form__label pf-c-form__label-text
kcLabelWrapperClass=col-xs-12 col-sm-12 col-md-12 col-lg-12
kcInputClass=pf-c-form-control
kcInputHelperTextBeforeClass=pf-c-form__helper-text pf-c-form__helper-text-before
kcInputHelperTextAfterClass=pf-c-form__helper-text pf-c-form__helper-text-after
kcInputClassRadio=pf-c-radio
kcInputClassRadioInput=pf-c-radio__input
kcInputClassRadioLabel=pf-c-radio__label
kcInputClassCheckbox=pf-c-check
kcInputClassCheckboxInput=pf-c-check__input
kcInputClassCheckboxLabel=pf-c-check__label
kcInputClassRadioCheckboxLabelDisabled=pf-m-disabled
kcInputErrorMessageClass=pf-c-form__helper-text pf-m-error required kc-feedback-text
kcInputGroup=pf-c-input-group
kcInputWrapperClass=col-xs-12 col-sm-12 col-md-12 col-lg-12
kcFormOptionsClass=col-xs-12 col-sm-12 col-md-12 col-lg-12
kcFormButtonsClass=col-xs-12 col-sm-12 col-md-12 col-lg-12
kcFormSettingClass=login-pf-settings
kcTextareaClass=form-control
kcSignUpClass=login-pf-signup


kcInfoAreaClass=col-xs-12 col-sm-4 col-md-4 col-lg-5 details

### user-profile grouping
kcFormGroupHeader=pf-c-form__group

##### css classes for form buttons
# main class used for all buttons
kcButtonClass=pf-c-button
# classes defining priority of the button - primary or default (there is typically only one priority button for the form)
kcButtonPrimaryClass=pf-m-primary
kcButtonDefaultClass=btn-default
# classes defining size of the button
kcButtonLargeClass=btn-lg
kcButtonBlockClass=pf-m-block

##### css classes for input
kcInputLargeClass=input-lg

##### css classes for form accessibility
kcSrOnlyClass=sr-only

##### css classes for select-authenticator form
kcSelectAuthListClass=pf-l-stack select-auth-container
kcSelectAuthListItemClass=pf-l-stack__item select-auth-box-parent pf-l-split
kcSelectAuthListItemIconClass=pf-l-split__item select-auth-box-icon
kcSelectAuthListItemIconPropertyClass=fa-2x select-auth-box-icon-properties
kcSelectAuthListItemBodyClass=pf-l-split__item pf-l-stack
kcSelectAuthListItemHeadingClass=pf-l-stack__item select-auth-box-headline pf-c-title
kcSelectAuthListItemDescriptionClass=pf-l-stack__item select-auth-box-desc
kcSelectAuthListItemFillClass=pf-l-split__item pf-m-fill
kcSelectAuthListItemArrowClass=pf-l-split__item select-auth-box-arrow
kcSelectAuthListItemArrowIconClass=fa fa-angle-right fa-lg
kcSelectAuthListItemTitle=select-auth-box-paragraph

##### css classes for the authenticators
kcAuthenticatorDefaultClass=fa fa-list list-view-pf-icon-lg
kcAuthenticatorPasswordClass=fa fa-unlock list-view-pf-icon-lg
kcAuthenticatorOTPClass=fa fa-mobile list-view-pf-icon-lg
kcAuthenticatorWebAuthnClass=fa fa-key list-view-pf-icon-lg
kcAuthenticatorWebAuthnPasswordlessClass=fa fa-key list-view-pf-icon-lg

##### css classes for the OTP Login Form
kcLoginOTPListClass=pf-c-tile
kcLoginOTPListInputClass=pf-c-tile__input
kcLoginOTPListItemHeaderClass=pf-c-tile__header
kcLoginOTPListItemIconBodyClass=pf-c-tile__icon
kcLoginOTPListItemIconClass=fa fa-mobile
kcLoginOTPListItemTitleClass=pf-c-tile__title

##### css classes for identity providers logos
kcCommonLogoIdP=kc-social-provider-logo kc-social-gray

## Social
kcLogoIdP-facebook=fa fa-facebook
kcLogoIdP-google=fa fa-google
kcLogoIdP-github=fa fa-github
kcLogoIdP-linkedin=fa fa-linkedin
kcLogoIdP-instagram=fa fa-instagram
## windows instead of microsoft - not included in PF4
kcLogoIdP-microsoft=fa fa-windows
kcLogoIdP-bitbucket=fa fa-bitbucket
kcLogoIdP-gitlab=fa fa-gitlab
kcLogoIdP-paypal=fa fa-paypal
kcLogoIdP-stackoverflow=fa fa-stack-overflow
kcLogoIdP-twitter=fa fa-twitter
kcLogoIdP-openshift-v4=pf-icon pf-icon-openshift

## Recovery codes
kcRecoveryCodesWarning=kc-recovery-codes-warning
kcRecoveryCodesList=kc-recovery-codes-list
kcRecoveryCodesActions=kc-recovery-codes-actions
kcRecoveryCodesConfirmation=kc-recovery-codes-confirmation
kcCheckClass=pf-c-check
kcCheckInputClass=pf-c-check__input
kcCheckLabelClass=pf-c-check__label

## Password visibility
kcFormPasswordVisibilityButtonClass=pf-c-button pf-m-control
kcFormPasswordVisibilityIconShow=fa fa-eye
kcFormPasswordVisibilityIconHide=fa fa-eye-slash
