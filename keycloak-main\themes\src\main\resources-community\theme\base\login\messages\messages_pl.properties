doLogIn=Logowanie
doRegister=Rejestracja
doRegisterSecurityKey=Rejestracja
doCancel=Anuluj
doSubmit=Zatwierdź
doBack=Cofnij
doYes=Tak
doNo=Nie
doContinue=Kontynuuj
doIgnore=Ignoruj
doAccept=Akceptuj
doDecline=<PERSON><PERSON><PERSON><PERSON>
doForgotPassword=Nie pamię<PERSON> hasła?
doClickHere=Kliknij tutaj
doImpersonate=Wciel się
doTryAgain=Spróbuj ponownie
doTryAnotherWay=Spróbuj inną metodą
doConfirmDelete=Potwierdź usunięcie
errorDeletingAccount=Wystąpił błąd podczas usuwania konta
deletingAccountForbidden=Nie masz uprawnień do usunięcia własnego konta. Skontaktuj się z administratorem.
kerberosNotConfigured=Kerberos nie jest skonfigurowany
kerberosNotConfiguredTitle=Kerberos nie jest skonfigurowany
bypassKerberosDetail=Albo nie jesteś zalogowany przez Kerberos albo twoja przeglądarka nie jest skonfigurowana do logowania Kerberos. Kliknij kontynuuj by zalogować się w inny sposób.
kerberosNotSetUp=Kerberos nie jest skonfigurowany. Nie można się zalogować.
registerTitle=Rejestracja
loginAccountTitle=Zaloguj się na swoje konto
loginTitle=Zaloguj się do {0}
loginTitleHtml={0}
impersonateTitle=Wcielenie {0}
impersonateTitleHtml=Wcielenie <strong>{0}</strong>
realmChoice=Strefa
unknownUser=Nieznany użytkownik
loginTotpTitle=Konfiguracja dla Mobile Authenticator
loginProfileTitle=Zaktualizuj informacje konta
loginIdpReviewProfileTitle=Aktualizuj informacje o koncie
loginTimeout=Zbyt dużo czasu zajęło logowanie. Proces logowania rozpocznie się od nowa.
reauthenticate=Proszę ponownie się uwierzytelnić, aby kontynuować
authenticateStrong=Wymagane silne uwierzytelnienie, aby kontynuować
oauthGrantTitle=Przydziel dostęp dla {0}
oauthGrantTitleHtml={0}
oauthGrantInformation=Upewnij się, że ufasz {0}, dowiedz się, jak {0} będzie przetwarzać Twoje dane.
oauthGrantReview=Możesz przejrzeć
oauthGrantTos=warunki usługi.
oauthGrantPolicy=politykę prywatności.
errorTitle=Przykro nam...
errorTitleHtml=<strong>Przykro</strong> nam...
emailVerifyTitle=Weryfikacja e-maila
emailForgotTitle=Nie pamiętasz hasła?
updateEmailTitle=Zaktualizuj adres e-mail
emailUpdateConfirmationSentTitle=Wysłano e-mail z potwierdzeniem
emailUpdateConfirmationSent=E-mail z potwierdzeniem został wysłany na adres {0}. Musisz postępować zgodnie z instrukcjami, aby zakończyć aktualizację adresu e-mail.
emailUpdatedTitle=E-mail zaktualizowany
emailUpdated=Adres e-mail konta został pomyślnie zaktualizowany na {0}.
updatePasswordTitle=Aktualizacja hasła
codeSuccessTitle=Kod sukcesu
codeErrorTitle=Kod błędu\: {0}
displayUnsupported=Żądany typ wyświetlania jest nieobsługiwany
browserRequired=Do zalogowania wymagana jest przeglądarka
browserContinue=Przeglądarka jest wymagana by dokończyć logowanie
browserContinuePrompt=Otworzyć przeglądarkę i kontynuować logowanie? [t/n]:
browserContinueAnswer=t

# Transports
usb=USB
nfc=NFC
bluetooth=Bluetooth
internal=Wewnętrzny
unknown=Nieznany

termsTitle=Regulamin
termsText=<p>Regulamin, który należy zdefiniować</p>
termsPlainText=Regulamin, który należy zdefiniować.
termsAcceptanceRequired=Musisz zgodzić się z naszymi warunkami.
acceptTerms=Zgadzam się na regulamin

deleteCredentialTitle=Usuń {0}
deleteCredentialMessage=Czy chcesz usunąć {0}?

linkIdpActionTitle=Łączenie {0}
linkIdpActionMessage=Czy chcesz połączyć swoje konto z {0}?

recaptchaFailed=Błędna Recaptcha
recaptchaNotConfigured=Recaptcha jest wymagana, ale nie skonfigurowana
consentDenied=Zgoda odrzucona.

noAccount=Nie masz konta?
username=Nazwa użytkownika
usernameOrEmail=Nazwa użytkownika lub e-mail
firstName=Imię
givenName=Nadane imię
fullName=Imię i nazwisko
lastName=Nazwisko
familyName=Nazwisko rodowe
email=E-mail
password=Hasło
passwordConfirm=Potwierdź hasło
passwordNew=Nowe hasło
passwordNewConfirm=Potwierdzenie nowego hasła
hidePassword=Ukryj hasło
showPassword=Pokaż hasło
rememberMe=Zapamiętaj mnie
authenticatorCode=Kod jednorazowy
address=Adres
street=Ulica
locality=Miejscowość
region=Województwo
postal_code=Kod pocztowy
country=Państwo
emailVerified=E-mail zweryfikowany
website=Strona internetowa
phoneNumber=Nr telefonu
phoneNumberVerified=Nr telefonu zweryfikowany
gender=Płeć
birthday=Data urodzenia
zoneinfo=Strefa czasowa
gssDelegationCredential=Świadectwo przekazania uprawnień GSS
logoutOtherSessions=Wyloguj z innych urządzeń

profileScopeConsentText=Profil użytkownika
emailScopeConsentText=Adres e-mail
addressScopeConsentText=Adres
phoneScopeConsentText=Numer telefonu
offlineAccessScopeConsentText=Dostęp offline
samlRoleListScopeConsentText=Moje role
rolesScopeConsentText=Role użytkownika
organizationScopeConsentText=Organizacja

restartLoginTooltip=Restart logowania

loginTotpIntro=Aby uzyskać dostęp do tego konta, musisz skonfigurować generator haseł jednorazowych
loginTotpStep1=Zainstaluj jedną z następujących aplikacji na telefonie komórkowym
loginTotpStep2=Otwórz aplikację i zeskanuj kod kreskowy
loginTotpStep3=Wprowadź jednorazowy kod podany przez aplikację i kliknij Prześlij, aby zakończyć konfigurację
loginTotpStep3DeviceName=Podaj nazwę urządzenia, aby pomóc w zarządzaniu urządzeniami OTP.
loginTotpManualStep2=Otwórz aplikację i wprowadź klucz
loginTotpManualStep3=Użyj poniższych wartości konfiguracji, jeśli aplikacja pozwala na ich ustawienie
loginTotpUnableToScan=Nie możesz zeskanować?
loginTotpScanBarcode=Zeskanować kod QR?
loginCredential=Poświadczenia
loginOtpOneTime=Kod jednorazowy
loginTotpType=Typ
loginTotpAlgorithm=Algorytm
loginTotpDigits=Cyfry
loginTotpInterval=Interwał
loginTotpCounter=Licznik
loginTotpDeviceName=Nazwa urządzenia

loginTotp.totp=Oparte o czas
loginTotp.hotp=Oparte o licznik

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

loginChooseAuthenticator=Wybierz metodę logowania

oauthGrantRequest=Czy przyznajesz te uprawnienia dostępu?
inResource=w

oauth2DeviceVerificationTitle=Logowanie urządzeniem
verifyOAuth2DeviceUserCode=Wprowadź kod podany przez Twoje urządzenie i kliknij Zatwierdź
oauth2DeviceInvalidUserCodeMessage=Nieprawidłowy kod, spróbuj ponownie.
oauth2DeviceExpiredUserCodeMessage=Kod wygasł. Wróć do swojego urządzenia i spróbuj ponownie się połączyć.
oauth2DeviceVerificationCompleteHeader=Logowanie urządzenia zakończone pomyślnie
oauth2DeviceVerificationCompleteMessage=Możesz zamknąć to okno przeglądarki i wrócić do swojego urządzenia.
oauth2DeviceVerificationFailedHeader=Logowanie urządzenia nie powiodło się
oauth2DeviceVerificationFailedMessage=Możesz zamknąć to okno przeglądarki i wrócić do swojego urządzenia i spróbować ponownie się zalogować.
oauth2DeviceConsentDeniedMessage=Odmowa zgody na połączenie urządzenia.
oauth2DeviceAuthorizationGrantDisabledMessage=Klient nie ma uprawnień do inicjowania udzielenia autoryzacji urządzenia OAuth 2.0. Przepływ jest wyłączony dla klienta.

emailVerifyInstruction1=Została wysłana do Ciebie wiadomość e-mail z instrukcjami jak zweryfikować swój adres e-mail.
emailVerifyInstruction2=Nie otrzymałem kodu weryfikacyjnego w wiadomości e-mail?
emailVerifyInstruction3=aby ponownie wysłać wiadomość e-mail.
emailVerifyInstruction4=Aby zweryfikować swój adres e-mail, wyślemy Ci wiadomość e-mail z instrukcjami na adres {0}.
emailVerifyResend=Wyślij ponownie e-mail weryfikacyjny
emailVerifySend=Wyślij e-mail weryfikacyjny

emailLinkIdpTitle=Link {0}
emailLinkIdp1=Wiadomość e-mail z instrukcjami, aby powiązać konto {0} {1} z kontem {2} została wysłana do Ciebie.
emailLinkIdp2=Nie otrzymałem kodu weryfikacyjnego w wiadomości e-mail?
emailLinkIdp3=aby ponownie wysłać wiadomość e-mail.
emailLinkIdp4=Jeśli już zweryfikowana e-mail w innej przeglądarce
emailLinkIdp5=aby kontynuować.

backToLogin=&laquo; Powrót do logowania

emailInstruction=Wpisz swój adres e-mail lub nazwę użytkownika, a wyślemy do Ciebie instrukcje, jak utworzyć nowe hasło.
emailInstructionUsername=Wprowadź swoją nazwę użytkownika, a wyślemy Ci instrukcje dotyczące tworzenia nowego hasła.

copyCodeInstruction=Proszę skopiować ten kod i wklej go do aplikacji:

pageExpiredTitle=Strona wygasła
pageExpiredMsg1=Aby ponownie uruchomić proces logowania
pageExpiredMsg2=Aby kontynuować proces logowania

personalInfo=Informacje osobiste:
role_admin=Admin
role_realm-admin=Strefa Admin
role_create-realm=Utwórz strefę
role_create-client=Utwórz klienta
role_view-realm=Wyświetl strefę
role_view-users=Wyświetl użytkowników
role_view-applications=Wyświetl aplikacje
role_view-clients=Wyświetl klientów
role_view-events=Wyświetl zdarzenia
role_view-identity-providers=Wyświetl dostawców tożsamości
role_manage-realm=Zarządzaj strefą
role_manage-users=Zarządzaj użytkownikami
role_manage-applications=Zarządzaj aplikacjami
role_manage-identity-providers=Zarządzaj dostawcami tożsamości
role_manage-clients=Zarządzaj klientami
role_manage-events=Zarządzaj zdarzeniami
role_view-profile=Zobacz profil
role_manage-account=Zarządzaj kontem
role_manage-account-links=Zarządzanie łączami konta
role_read-token=Odczytu tokenu
role_offline-access=Dostęp offline
client_account=Konto
client_account-console=Konsola konta
client_security-admin-console=Konsola administratora bezpieczeństwa
client_admin-cli=Admin CLI
client_realm-management=Zarządzanie strefą
client_broker=Broker

requiredFields=Wymagane pola

invalidUserMessage=Nieprawidłowa nazwa użytkownika lub hasło.
invalidUsernameMessage=Nieprawidłowa nazwa użytkownika.
invalidUsernameOrEmailMessage=Nieprawidłowa nazwa użytkownika lub hasło.
invalidPasswordMessage=Nieprawidłowe hasło.
invalidEmailMessage=Nieprawidłowy adres e-mail.
accountDisabledMessage=Konto jest wyłączone, skontaktuj się z administratorem.
# These properties are deliberately the same as "invalidUsernameMessage", so by default, it is not possible to recognize the reason of failed authentication is temporarily disabled account
accountTemporarilyDisabledMessage=Nieprawidłowa nazwa użytkownika lub hasło.
accountPermanentlyDisabledMessage=Nieprawidłowa nazwa użytkownika lub hasło.
# These properties are deliberately the same as "invalidTotpMessage", so by default, it is not possible to recognize the reason of failed authentication is temporarily disabled account
accountTemporarilyDisabledMessageTotp=Nieprawidłowy kod uwierzytelnienia.
accountPermanentlyDisabledMessageTotp=Nieprawidłowy kod uwierzytelnienia.
expiredCodeMessage=Przekroczono limit czasu logowania. Proszę Zaloguj się ponownie.
expiredActionMessage=Akcja wygasła. Proszę kontynuować logowanie.
expiredActionTokenNoSessionMessage=Akcja wygasła.
expiredActionTokenSessionExistsMessage=Akcja wygasła. Proszę uruchomić ponownie.
sessionLimitExceeded=Zbyt wiele sesji
identityProviderLogoutFailure=Błąd wylogowania dostawcy tożsamości SAML

missingFirstNameMessage=Proszę podać imię.
missingLastNameMessage=Proszę podać nazwisko.
missingEmailMessage=Proszę podać e-mail.
missingUsernameMessage=Proszę podać nazwę użytkownika.
missingPasswordMessage=Proszę podać hasło.
missingTotpMessage=Proszę podać kod uwierzytelniający.
missingTotpDeviceNameMessage=Proszę podać nazwę urządzenia.
notMatchPasswordMessage=Hasła nie są zgodne.

error-invalid-value=Nieprawidłowa wartość.
error-invalid-blank=Proszę podać wartość.
error-empty=Proszę podać wartość.
error-invalid-length=Długość musi wynosić od {1} do {2}.
error-invalid-length-too-short=Minimalna długość to {1}.
error-invalid-length-too-long=Maksymalna długość to {2}.
error-invalid-email=Nieprawidłowy adres e-mail.
error-invalid-number=Nieprawidłowa liczba.
error-number-out-of-range=Liczba musi być w zakresie od {1} do {2}.
error-number-out-of-range-too-small=Liczba musi mieć minimalną wartość {1}.
error-number-out-of-range-too-big=Liczba musi mieć maksymalną wartość {2}.
error-pattern-no-match=Nieprawidłowa wartość.
error-invalid-uri=Nieprawidłowy adres URL.
error-invalid-uri-scheme=Nieprawidłowy schemat URL.
error-invalid-uri-fragment=Nieprawidłowy fragment URL.
error-user-attribute-required=Proszę wypełnić to pole.
error-invalid-date=Nieprawidłowa data.
error-user-attribute-read-only=To pole jest tylko do odczytu.
error-username-invalid-character=Wartość zawiera niedozwolony znak.
error-person-name-invalid-character=Wartość zawiera niedozwolony znak.
error-reset-otp-missing-id=Proszę wybrać konfigurację OTP.

invalidPasswordExistingMessage=Nieprawidłowe istniejące hasło.
invalidPasswordBlacklistedMessage=Nieprawidłowe hasło: hasło jest na czarnej liście.
invalidPasswordConfirmMessage=Potwierdzenie hasła nie pasuje.
invalidTotpMessage=Nieprawidłowy kod uwierzytelnienia.

usernameExistsMessage=Nazwa użytkownika już istnieje.
emailExistsMessage=E-mail już istnieje.

federatedIdentityExistsMessage=Użytkownik z {0} {1} już istnieje. Zaloguj się do zarządzania kontem, aby połączyć konto.
federatedIdentityUnavailableMessage=Użytkownik {0} uwierzytelniony u dostawcy tożsamości {1} nie istnieje. Skontaktuj się z administratorem.
federatedIdentityUnmatchedEssentialClaimMessage=Token ID wydany przez dostawcę tożsamości nie pasuje do skonfigurowanego kluczowego roszczenia. Skontaktuj się z administratorem.

confirmLinkIdpTitle=Konto już istnieje
confirmOverrideIdpTitle=Połączenie z brokerem już istnieje
federatedIdentityConfirmLinkMessage=Użytkownik z {0} {1} już istnieje. Co chcesz zrobić?
federatedIdentityConfirmOverrideMessage=Próbujesz połączyć swoje konto {0} z kontem {1} {2}. Ale Twoje konto jest już połączone z innym kontem {3} {4}. Czy potwierdzasz, że chcesz zastąpić istniejące połączenie nowym kontem?
federatedIdentityConfirmReauthenticateMessage=Uwierzytelnij się, aby połączyć swoje konto z {0}
nestedFirstBrokerFlowMessage=Użytkownik {0} {1} nie jest powiązany z żadnym znanym użytkownikiem.
confirmLinkIdpReviewProfile=Przejrzyj profil
confirmLinkIdpContinue=Dodaj do istniejącego konta
confirmOverrideIdpContinue=Tak, zastąp połączenie bieżącym kontem

configureTotpMessage=Musisz skonfigurować aplikację uwierzytelniającą, aby aktywować swoje konto.
configureBackupCodesMessage=Musisz skonfigurować Kody Zapasowe, aby aktywować swoje konto.
updateProfileMessage=Musisz zaktualizować profilu użytkownika, aby aktywować swoje konto.
updatePasswordMessage=Musisz zmienić swoje hasło, aby aktywować swoje konto.
updateEmailMessage=Musisz zaktualizować swój adres e-mail, aby aktywować swoje konto.
resetPasswordMessage=Musisz zmienić swoje hasło.
verifyEmailMessage=Musisz zweryfikować swój adres e-mail, aby aktywować swoje konto.
linkIdpMessage=Musisz zweryfikować swój adres e-mail, aby połączyć swoje konto z {0}.

emailSentMessage=Powinieneś otrzymywać wkrótce pocztę z dalszymi instrukcjami.
emailSendErrorMessage=Nie można wysłać wiadomości e-mail, proszę spróbować ponownie później.

accountUpdatedMessage=Twoje konto zostało zaktualizowane.
accountPasswordUpdatedMessage=Twoje hasło zostało zaktualizowane.

delegationCompleteHeader=Logowanie udane
delegationCompleteMessage=Możesz zamknąć okno przeglądarki i przejść wstecz do aplikacji konsoli.
delegationFailedHeader=Logowanie nie powiodło się
delegationFailedMessage=Możesz zamknąć okno przeglądarki, wrócić do aplikacji konsoli i spróbować zalogować się ponownie.

noAccessMessage=Brak dostępu

invalidPasswordMinLengthMessage=Nieprawidłowe hasło: minimalna długość {0}.
invalidPasswordMaxLengthMessage=Nieprawidłowe hasło: maksymalna długość {0}.
invalidPasswordMinDigitsMessage=Nieprawidłowe hasło: musi zawierać przynajmniej {0} cyfr.
invalidPasswordMinLowerCaseCharsMessage=Nieprawidłowe hasło: musi zawierać co najmniej {0} małych liter.
invalidPasswordMinUpperCaseCharsMessage=Nieprawidłowe hasło: musi zawierać co najmniej {0} wielkich liter.
invalidPasswordMinSpecialCharsMessage=Nieprawidłowe hasło: musi zawierać przynajmniej {0} znaków specjalnych.
invalidPasswordNotUsernameMessage=Nieprawidłowe hasło: nie może być nazwą użytkownika.
invalidPasswordNotContainsUsernameMessage=Nieprawidłowe hasło: nie może zawierać nazwy użytkownika.
invalidPasswordNotEmailMessage=Nieprawidłowe hasło: nie może być takie samo jak adres e-mail.
invalidPasswordRegexPatternMessage=Nieprawidłowe hasło: brak zgodności z wyrażeniem regularnym.
invalidPasswordHistoryMessage=Nieprawidłowe hasło: nie może być takie jak {0} ostatnich haseł.
invalidPasswordGenericMessage=Nieprawidłowe hasło: nowe hasło nie jest zgodne z zasadami haseł.

failedToProcessResponseMessage=Nie można przetworzyć odpowiedzi
httpsRequiredMessage=Wymagany HTTPS
realmNotEnabledMessage=Strefa nie jest aktywna
invalidRequestMessage=Nieprawidłowe żądanie
successLogout=Jesteś wylogowany
failedLogout=Wylogowanie nie powiodło się
unknownLoginRequesterMessage=Nieznany żądający logowania
loginRequesterNotEnabledMessage=Żądający logowania nie jest aktywny
bearerOnlyMessage=Klienci bearer-only nie mogą inicjować logowania przez przeglądarkę
standardFlowDisabledMessage=Klient nie może zainicjować logowania przez przeglądarkę z podanym response_type. Standardowy przepływ jest wyłączony dla klienta.
implicitFlowDisabledMessage=Klient nie może zainicjować logowania przez przeglądarkę z podanym response_type. Niejawny przepływ jest wyłączony dla klienta.
invalidRedirectUriMessage=Nieprawidłowy uri przekierowania
unsupportedNameIdFormatMessage=Nieobsługiwany NameIDFormat
invalidRequesterMessage=Nieprawidłowy żądający
registrationNotAllowedMessage=Rejestracja nie jest dozwolona
resetCredentialNotAllowedMessage=Zresetowanie poświadczeń nie jest dozwolone

permissionNotApprovedMessage=Uprawnienie nie zatwierdzone.
noRelayStateInResponseMessage=Brak przekazanego stanu w odpowiedzi dostawcy tożsamości.
insufficientPermissionMessage=Niewystarczające uprawnienia do łączenia tożsamości.
couldNotProceedWithAuthenticationRequestMessage=Nie można kontynuować żądania uwierzytelnienia do dostawcy tożsamości.
couldNotObtainTokenMessage=Nie można uzyskać tokenu od dostawcy tożsamości.
unexpectedErrorRetrievingTokenMessage=Nieoczekiwany błąd podczas pobierania tokenu od dostawcy tożsamości.
unexpectedErrorHandlingResponseMessage=Nieoczekiwany błąd podczas obsługi odpowiedzi od dostawcy tożsamości.
identityProviderAuthenticationFailedMessage=Uwierzytelnianie nie powiodło się. Nie można uwierzytelnić za pomocą dostawcy tożsamości.
couldNotSendAuthenticationRequestMessage=Nie może wysyłać żądania uwierzytelniania do dostawcy tożsamości.
unexpectedErrorHandlingRequestMessage=Nieoczekiwany błąd podczas obsługi żądania uwierzytelnienia do dostawcy tożsamości.
invalidAccessCodeMessage=Nieprawidłowy kod dostępu.
sessionNotActiveMessage=Sesja nie jest aktywna.
invalidCodeMessage=Wystąpił błąd, zaloguj się ponownie za pośrednictwem aplikacji.
cookieNotFoundMessage=Nie znaleziono pliku cookie do ponownego logowania. Mógł on wygasnąć, zostać usunięty lub masz wyłączone pliki cookie w przeglądarce. Jeśli pliki cookie są wyłączone, włącz je. Kliknij ''Powrót do aplikacji'', aby zalogować się ponownie
insufficientLevelOfAuthentication=Żądany poziom uwierzytelnienia nie został spełniony.
identityProviderUnexpectedErrorMessage=Nieoczekiwany błąd podczas uwierzytelniania u dostawcy tożsamości
identityProviderMissingStateMessage=Brak parametru stanu w odpowiedzi od dostawcy tożsamości.
identityProviderMissingCodeOrErrorMessage=Brak kodu lub parametru błędu w odpowiedzi od dostawcy tożsamości.
identityProviderInvalidResponseMessage=Nieprawidłowa odpowiedź od dostawcy tożsamości.
identityProviderInvalidSignatureMessage=Nieprawidłowy podpis w odpowiedzi od dostawcy tożsamości.
identityProviderNotFoundMessage=Nie można odnaleźć dostawcy tożsamości z tym identyfikatorem.
identityProviderLinkSuccess=Pomyślnie zweryfikowano e-mail. Wróć do oryginalnej przeglądarki i tam kontynuuj logowanie.
staleCodeMessage=Ta strona nie jest już ważna, proszę wrócić do aplikacji i zalogować się ponownie
realmSupportsNoCredentialsMessage=Strefa nie obsługuje dowolnego typu poświadczeń.
credentialSetupRequired=Nie można się zalogować, wymagana jest konfiguracja danych uwierzytelniających.
identityProviderNotUniqueMessage=Strefa obsługuje wielu dostawców tożsamości. Nie można określić dostawcy tożsamości, który powinien być używany do uwierzytelniania.
emailVerifiedMessage=Twój adres e-mail został zweryfikowany.
emailVerifiedAlreadyMessage=Twój adres e-mail został już zweryfikowany.
staleEmailVerificationLink=Użyto nieaktualny link stanu, który stracił ważność. Może e-mail został już zweryfikowany?
identityProviderAlreadyLinkedMessage=Stowarzyszona tożsamość, zwrócona przez {0} jest już połączona z innym użytkownikiem.
confirmAccountLinking=Potwierdź powiązanie konta {0} dostawcy tożsamości {1} z twoim kontem.
confirmEmailAddressVerification=Potwierdź ważność adresu e-mail {0}.
confirmExecutionOfActions=Wykonaj następujące akcje

backToApplication=&laquo; Powrót do aplikacji
missingParameterMessage=Brakujące parametry\: {0}
clientNotFoundMessage=Klient nie znaleziony.
clientDisabledMessage=Klient nieaktywny.
invalidParameterMessage=Nieprawidłowy parametr\: {0}
alreadyLoggedIn=Jesteś już zalogowany.
differentUserAuthenticated=Jesteś już uwierzytelniona/y jako inny użytkownik ''{0}'' w tej sesji. Najpierw się wyloguj.
brokerLinkingSessionExpired=Żądano łączenia kont brokera, ale bieżąca sesja już jest nieważna.
proceedWithAction=&raquo; kliknij tutaj, aby przejść
acrNotFulfilled=Wymagania uwierzytelniania nie zostały spełnione

requiredAction.CONFIGURE_TOTP=Skonfiguruj OTP
requiredAction.TERMS_AND_CONDITIONS=Regulamin
requiredAction.UPDATE_PASSWORD=Zaktualizuj hasło
requiredAction.UPDATE_PROFILE=Zaktualizuj profil
requiredAction.VERIFY_EMAIL=Zweryfikuj adres e-mail
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Generuj kody odzyskania
requiredAction.webauthn-register-passwordless=Rejestr Webauthn bez hasła
requiredAction.webauthn-register=Rejestr Webauthn

invalidTokenRequiredActions=Wymagane działania zawarte w linku są nieprawidłowe

doX509Login=Użytkownik będzie zalogowany jako:
clientCertificate=X509 certyfikat klienta:
noCertificate=[Brak certyfikatu]


pageNotFound=Nie znaleziono strony
internalServerError=Wystąpił błąd wewnętrzny serwera

console-username=Nazwa użytkownika:
console-password=Hasło:
console-otp=Hasło jednorazowe:
console-new-password=Nowe hasło:
console-confirm-password=Potwierdź hasło:
console-update-password=Aktualizacja hasła jest wymagana.
console-verify-email=Musisz zweryfikować swój adres e-mail. Wiadomość e-mail z kodem weryfikacyjnym została wysłana do {0}. Podaj ten kod poniżej.
console-email-code=Kod z e-maila:
console-accept-terms=Akceptujesz warunki? [t/n]:
console-accept=t

# Openshift messages
openshift.scope.user_info=Informacje o użytkowniku
openshift.scope.user_check-access=Informacje o dostępie użytkownika
openshift.scope.user_full=Pełny dostęp
openshift.scope.list-projects=Wyświetl projekty

# SAML authentication
saml.post-form.title=Przekierowanie uwierzytelnienia
saml.post-form.message=Przekierowywanie, proszę czekać.
saml.post-form.js-disabled=JavaScript jest wyłączony. Zdecydowanie zalecamy jej włączenie. Kliknij przycisk poniżej, aby kontynuować.
saml.artifactResolutionServiceInvalidResponse=Nie udało się przetworzyć artefaktu.

#authenticators
otp-display-name=Aplikacja uwierzytelniająca
otp-help-text=Wprowadź kod weryfikacyjny z aplikacji uwierzytelniającej.
otp-reset-description=Którą konfigurację OTP należy usunąć?
password-display-name=Hasło
password-help-text=Zaloguj się wpisując hasło
auth-username-form-display-name=Nazwa użytkownika
auth-username-form-help-text=Rozpocznij logowanie, wprowadzając swoją nazwę użytkownika
auth-username-password-form-display-name=Nazwa użytkownika i hasło
auth-username-password-form-help-text=Zaloguj się, wprowadzając swoją nazwę użytkownika i hasło.
auth-x509-client-username-form-display-name=Certyfikat X509
auth-x509-client-username-form-help-text=Zaloguj się przy użyciu certyfikatu klienta X509.

# Recovery Codes
auth-recovery-authn-code-form-display-name=Kod uwierzytelniający odzyskiwanie
auth-recovery-authn-code-form-help-text=Wpisz kod uwierzytelniający odzyskiwanie z poprzednio wygenerowanej listy.
auth-recovery-code-info-message=Wprowadź podany kod odzyskiwania.
auth-recovery-code-prompt=Kod odzyskiwania #{0}
auth-recovery-code-header=Zaloguj z kodem uwierzytelniającym odzyskiwanie
recovery-codes-error-invalid=Niepoprawny kod uwierzytelniający odzyskiwanie
recovery-code-config-header=Kody uwierzytelniające odzyskiwanie
recovery-code-config-warning-title=Te kody odzyskiwania nie pojawią się ponownie po opuszczeniu tej strony
recovery-code-config-warning-message=Należy je wydrukować, pobrać lub skopiować do menedżera haseł i trzymać je w bezpiecznym miejscu. Anulowanie tej konfiguracji spowoduje usunięcie kodów odzyskiwania z konta.
recovery-codes-print=Drukuj
recovery-codes-download=Pobierz
recovery-codes-copy=Kopiuj
recovery-codes-copied=Skopiowane
recovery-codes-confirmation-message=Zapisałem te kody gdzieś w bezpiecznym miejscu
recovery-codes-action-complete=Zakończ konfigurację
recovery-codes-action-cancel=Anuluj konfigurację
recovery-codes-download-file-header=Przechowuj te kody w bezpiecznym miejscu
recovery-codes-download-file-description=Kody odzyskiwania to jednorazowe kody dostępu, które umożliwiają zalogowanie się na konto, jeśli nie masz dostępu do narzędzia uwierzytelniającego.
recovery-codes-download-file-date=Te kody zostały wygenerowane
recovery-codes-label-default=Kody odzyskiwania

# WebAuthn
webauthn-display-name=Passkey
webauthn-help-text=Użyj Passkey do zalogowania się
webauthn-passwordless-display-name=Passkey
webauthn-passwordless-help-text=Użyj Passkey do logowania bez hasła.
webauthn-login-title=Logowanie za pomocą Passkey
webauthn-registration-title=Rejestracja Passkey
webauthn-available-authenticators=Dostępne Passkeys
webauthn-unsupported-browser-text=WebAuthn nie jest obsługiwany przez tę przeglądarkę. Wypróbuj inną lub skontaktuj się z administratorem.
webauthn-doAuthenticate=Zaloguj z użyciem Passkey
webauthn-createdAt-label=Utworzony
webauthn-registration-init-label=Passkey (Domyślna etykieta)
webauthn-registration-init-label-prompt=Wprowadź etykietę zarejestrowanego Passkey


# WebAuthn Error
webauthn-error-title=Błąd Passkey
webauthn-error-registration=Nie udało się zarejestrować Twojego Passkey.<br /> {0}
webauthn-error-api-get=Nie udało się uwierzytelnić za pomocą Passkey.<br /> {0}
webauthn-error-different-user=Pierwszy uwierzytelniony użytkownik nie jest tym który został uwierzytelniony przez Passkey
webauthn-error-auth-verification=Wynik autoryzacji za pomocą Passkey jest niepoprawny.<br /> {0}
webauthn-error-register-verification=Wynik rejestracji za pomocą Passkey jest niepoprawny.<br /> {0}
webauthn-error-user-not-found=Nieznany użytkownik uwierzytelniony przez Passkey.

# Passkey
passkey-login-title=Logowanie za pomocą Passkey
passkey-available-authenticators=Dostępne Passkey
passkey-unsupported-browser-text=Passkey nie jest obsługiwany przez tę przeglądarkę. Wypróbuj inną lub skontaktuj się z administratorem.
passkey-doAuthenticate=Zaloguj się przy użyciu Passkey
passkey-createdAt-label=Utworzony
passkey-autofill-select=Wybierz swój Passkey

# Identity provider
identity-provider-redirector=Połącz się z innym dostawcą tożsamości
identity-provider-login-label=Lub zaloguj się za pomocą
idp-email-verification-display-name=Weryfikacja adresu e-mail
idp-email-verification-help-text=Połącz swoje konto, weryfikując swój e-mail.
idp-username-password-form-display-name=Nazwa użytkownika i hasło
idp-username-password-form-help-text=Połącz swoje konto logując się.

finalDeletionConfirmation=Jeśli usuniesz swoje konto, nie będzie można go przywrócić. Aby zachować swoje konto, kliknij Anuluj.
irreversibleAction=Ta akcja jest nieodwracalna
deleteAccountConfirm=Potwierdzenie usunięcia konta

deletingImplies=Usunięcie konta oznacza:
errasingData=Usuwanie wszystkich danych
loggingOutImmediately=Natychmiastowe wylogowywanie
accountUnusable=Kolejne użycie aplikacji nie będzie możliwe przy użyciu tego konta
userDeletedSuccessfully=Użytkownik został usunięty

access-denied=Odmowa dostępu
access-denied-when-idp-auth=Odmowa dostępu podczas uwierzytelniania za pomocą {0}

frontchannel-logout.title=Wylogowywanie
frontchannel-logout.message=Wylogowujesz się z następujących aplikacji
logoutConfirmTitle=Wylogowywanie
logoutConfirmHeader=Czy chcesz się wylogować?
doLogout=Wyloguj

readOnlyUsernameMessage=Zmiana nazwy użytkownika nie jest możliwa, ponieważ edycja konta jest zablokowana.
error-invalid-multivalued-size=Atrybut {0} musi mieć co najmniej {1} i najwyżej {2} {2,choice,0#wartości|1#wartość|1<wartości}.

organization.confirm-membership.title=Zaraz dołączysz do organizacji ${kc.org.name}
organization.confirm-membership=Klikając na poniższy link, zostaniesz członkiem organizacji {0}:
organization.member.register.title=Utwórz konto, aby dołączyć do organizacji ${kc.org.name}
organization.select=Wybierz organizację, aby kontynuuować
notMemberOfOrganization=Użytkownik nie jest członkiem organizacji {0}
notMemberOfAnyOrganization=Użytkownik nie jest członkiem żadnej organizacji
