doSave=Sauvegarder
doCancel=Annuler
doLogOutAllSessions=Déconnexion de toutes les sessions
doRemove=Supprimer
doAdd=Ajouter
doSignOut=Déconnexion
editAccountHtmlTitle=Édition du compte
federatedIdentitiesHtmlTitle=Identités fédérées
accountLogHtmlTitle=Accès au compte
changePasswordHtmlTitle=Changer de mot de passe
sessionsHtmlTitle=Sessions
accountManagementTitle=Gestion du compte Keycloak
authenticatorTitle=Authentification
applicationsHtmlTitle=Applications
authenticatorCode=Code à usage unique
email=Courriel
firstName=Prénom
givenName=Prénom
fullName=Nom complet
lastName=Nom
familyName=Nom de famille
password=Mot de passe
passwordConfirm=Confirmation
passwordNew=Nouveau mot de passe
username=Nom d''utilisateur
address=Adresse
street=Rue
locality=Ville ou Localité
region=État, Province ou Région
postal_code=Code Postal
country=Pays
emailVerified=Courriel vérifié
gssDelegationCredential=Accréditation de délégation GSS
role_admin=Administrateur
role_realm-admin=Administrateur du domaine
role_create-realm=Créer un domaine
role_view-realm=Voir un domaine
role_view-users=Voir les utilisateurs
role_view-applications=Voir les applications
role_view-clients=Voir les clients
role_view-events=Voir les événements
role_view-identity-providers=Voir les fournisseurs d''identités
role_manage-realm=Gérer le domaine
role_manage-users=Gérer les utilisateurs
role_manage-applications=Gérer les applications
role_manage-identity-providers=Gérer les fournisseurs d''identités
role_manage-clients=Gérer les clients
role_manage-events=Gérer les événements
role_view-profile=Voir le profil
role_manage-account=Gérer le compte
role_read-token=Lire le jeton d''authentification
role_offline-access=Accès hors-ligne
client_account=Compte
client_security-admin-console=Console d''Administration de la Sécurité
client_admin-cli=Admin CLI
client_realm-management=Gestion du domaine
client_broker=Broker


requiredFields=Champs obligatoires
allFieldsRequired=Tous les champs sont obligatoires
backToApplication=&laquo; Revenir à l''application
backTo=Revenir à {0}
date=Date
event=Evénement
ip=IP
client=Client
clients=Clients
details=Détails
started=Début
lastAccess=Dernier accès
expires=Expiration
applications=Applications
account=Compte
federatedIdentity=Identité fédérée
authenticator=Authentification
sessions=Sessions
log=Connexion
application=Application
grantedPermissions=Permissions accordées
grantedPersonalInfo=Informations personnelles accordées
additionalGrants=Droits additionnels
action=Action
inResource=dans
fullAccess=Accès complet
offlineToken=Jeton d''authentification hors-ligne
revoke=Révoquer un droit
configureAuthenticators=Authentifications configurées
mobile=Téléphone mobile
totpStep1=Installez l''une des applications suivantes sur votre mobile
totpStep2=Ouvrez l''application et scannez le code-barres :
totpStep3=Entrez le code à usage unique fourni par l''application et cliquez sur Sauvegarder pour terminer.
totpManualStep2=Ouvrez l''application et entrez la clef :
totpManualStep3=Utilisez les valeurs de configuration suivantes si l''application les autorise :
totpUnableToScan=Impossible de scanner ?
totpScanBarcode=Scanner le code-barre ?
totp.totp=Basé sur le temps
totp.hotp=Basé sur un compteur
totpType=Type
totpAlgorithm=Algorithme
totpDigits=Chiffres
totpInterval=Intervalle
totpCounter=Compteur
missingUsernameMessage=Veuillez entrer votre nom d''utilisateur.
missingFirstNameMessage=Veuillez entrer votre prénom.
invalidEmailMessage=Courriel invalide.
missingLastNameMessage=Veuillez entrer votre nom.
missingEmailMessage=Veuillez entrer votre courriel.
missingPasswordMessage=Veuillez entrer votre mot de passe.
notMatchPasswordMessage=Les mots de passe ne sont pas identiques
missingTotpMessage=Veuillez entrer le code d''authentification.
invalidPasswordExistingMessage=Mot de passe existant invalide.
invalidPasswordConfirmMessage=Le mot de passe de confirmation ne correspond pas.
invalidTotpMessage=Le code d''authentification est invalide.
usernameExistsMessage=Le nom d''utilisateur existe déjà.
emailExistsMessage=Le courriel existe déjà.
readOnlyUserMessage=Vous ne pouvez pas mettre à jour votre compte car il est en lecture seule.
readOnlyPasswordMessage=Vous ne pouvez pas mettre à jour votre mot de passe car votre compte est en lecture seule.
successTotpMessage=L''authentification via téléphone mobile est configurée.
successTotpRemovedMessage=L''authentification via téléphone mobile est supprimée.
successGrantRevokedMessage=Droit révoqué avec succès.
accountUpdatedMessage=Votre compte a été mis à jour.
accountPasswordUpdatedMessage=Votre mot de passe a été mis à jour.
missingIdentityProviderMessage=Le fournisseur d''identité n''est pas spécifié.
invalidFederatedIdentityActionMessage=Action manquante ou invalide.
identityProviderNotFoundMessage=Le fournisseur d''identité spécifié n''est pas trouvé.
federatedIdentityLinkNotActiveMessage=Cette identité n''est plus active dorénavant.
federatedIdentityRemovingLastProviderMessage=Vous ne pouvez pas supprimer votre dernière fédération d''identité sans mot de passe spécifié.
identityProviderRedirectErrorMessage=Erreur de redirection vers le fournisseur d''identité.
identityProviderRemovedMessage=Le fournisseur d''identité a été supprimé correctement.
identityProviderAlreadyLinkedMessage=Le fournisseur d''identité retourné par {0} est déjà lié à un autre utilisateur.
accountDisabledMessage=Ce compte est désactivé, veuillez contacter votre administrateur.
accountTemporarilyDisabledMessage=Ce compte est temporairement désactivé, veuillez contacter votre administrateur ou réessayez plus tard.
invalidPasswordMinLengthMessage=Mot de passe invalide : longueur minimale {0}.
invalidPasswordMinLowerCaseCharsMessage=Mot de passe invalide : doit contenir au moins {0} lettre(s) en minuscule.
invalidPasswordMinDigitsMessage=Mot de passe invalide : doit contenir au moins {0} chiffre(s).
invalidPasswordMinUpperCaseCharsMessage=Mot de passe invalide : doit contenir au moins {0} lettre(s) en majuscule.
invalidPasswordMinSpecialCharsMessage=Mot de passe invalide : doit contenir au moins {0} caractère(s) spéciaux.
invalidPasswordNotUsernameMessage=Mot de passe invalide : ne doit pas être identique au nom d''utilisateur.
invalidPasswordRegexPatternMessage=Mot de passe invalide : ne valide pas l''expression rationnelle.
invalidPasswordHistoryMessage=Mot de passe invalide : ne doit pas être égal aux {0} derniers mots de passe.
applicationName=Nom de l''application
authenticatorActionSetup=Configurer
device-activity=Activité des Appareils
accountSecurityTitle=Sécurité du Compte
accountManagementWelcomeMessage=Bienvenue dans la Gestion de Compte Keycloak
personalInfoSidebarTitle=Informations personnelles
accountSecuritySidebarTitle=Sécurité du compte
signingInSidebarTitle=Authentification
deviceActivitySidebarTitle=Activité des appareils
linkedAccountsSidebarTitle=Comptes liés
personalInfoHtmlTitle=Informations Personnelles
personalInfoIntroMessage=Gérez vos informations de base
personalSubMessage=Gérez vos informations de base.
accountSecurityIntroMessage=Gérez votre mot de passe et l''accès à votre compte
applicationsIntroMessage=Auditez et gérez les permissions d''accès des applications aux données de votre compte
applicationType=Type d''application
doLink=Lien
linkedAccountsHtmlTitle=Comptes liés
personalSubTitle=Vos informations personnelles
currentPassword=Mot de passe actuel
phoneNumberVerified=Numéro de téléphone vérifié
gender=Genre
zoneinfo=Fuseau horaire
addressScopeConsentText=Adresse
role_view-consent=Voir les consentements
role_uma_authorization=Obtenir des permissions
deletingImplies=Supprimer votre compte implique :
errasingData=Effacer toutes vos données
doLogIn=Connexion
noAccessMessage=Accès non autorisé
deviceActivityHtmlTitle=Activité des appareils
accountManagementBaseThemeCannotBeUsedDirectly=Le thème du compte de base ne contient que des traductions pour la console de compte. Pour afficher la console de compte, vous devez soit définir le parent de votre thème à un autre thème de compte, soit fournir votre propre fichier index.ftl. Veuillez consulter la documentation pour de plus amples renseignements.
resourceIntroMessage=Partagez vos ressources avec les membres de votre équipe
updatePasswordTitle=Mettre à jour le mot de passe
updatePasswordMessageTitle=Assurez-vous de choisir un mot de passe fort
updatePasswordMessage=Un mot de passe fort contient un mélange de chiffres, de lettres et de symboles. Il est difficile à deviner, ne ressemble pas à un mot réel et n’est utilisé que pour ce compte.
website=Page Web
birthday=Date de naissance
profileScopeConsentText=Profil utilisateur
phoneNumber=Numéro de téléphone
samlRoleListScopeConsentText=Mes rôles
emailScopeConsentText=Courriel
offlineAccessScopeConsentText=Accès hors ligne
phoneScopeConsentText=Numéro de téléphone
role_view-groups=Voir les groupes
role_manage-consent=Gérer les consentements
rolesScopeConsentText=Rôles utilisateur
organizationScopeConsentText=Organisation
role_manage-account-links=Gérer les liens de compte
client_account-console=Console de gestion du compte
totpStep3DeviceName=Renseignez un nom d''appareil pour vous aider à gérer vos a appareils OTP.
availableRoles=Rôles disponibles
totpDeviceName=Nom du dispositif
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator
irreversibleAction=Cette action est irréversible
passwordLastUpdateMessage=Votre mot de passe a été modifié le
totpAppFreeOTPName=FreeOTP
loggingOutImmediately=Déconnexion immédiate
accountUnusable=Tout usage futur de l''application ne sera plus possible avec ce compte
invalidUserMessage=Utilisateur invalide
updateReadOnlyAttributesRejectedMessage=Mise à jour de l''attribut en lecture seule rejetée
missingTotpDeviceNameMessage=Merci de renseigner un nom pour cet appareil.
readOnlyUsernameMessage=Vous ne pouvez pas mettre à jour votre nom d''utilisateur car il est en lecture seule.
staleCodeAccountMessage=La page a expiré. Merci d''essayer à nouveau.
consentDenied=Consentement refusé.
access-denied-when-idp-auth=Acres refusé lors de l''authentification avec {0}
invalidPasswordMaxLengthMessage=Mot de passe invalide : longueur maximale {0}.
invalidPasswordNotContainsUsernameMessage=Mot de passe invalide : il ne peut pas contenir le nom d''utilisateur.
invalidPasswordNotEmailMessage=Mot de passe invalide : il ne doit pas correspondre au courriel.
invalidPasswordBlacklistedMessage=Mot de passe invalide : ce mot de passe est non autorisé.
invalidPasswordGenericMessage=Mot de passe invalide : le niveau mot de passe ne respecte pas la politique de mot de passe.
myResources=Mes ressources
myResourcesSub=Mes ressources
doDeny=Refuser
doRevoke=Révoquer
doApprove=Approuver
doRemoveSharing=Supprimer le partage
doRemoveRequest=Supprimer la demande
peopleAccessResource=Personne ayant accès à cette ressource
description=Description
name=Nom
scopes=Périmètres
resource=Ressource
user=Utilisateur
peopleSharingThisResource=Personne partageant cette ressource
needMyApproval=Nécessite mon approbation
icon=Icône
requestor=Demandeur
owner=Propriétaire
resourcesSharedWithMe=Ressources partagées avec moi
shares=partage(s)
notBeingShared=Cette ressource n''est pas partagée.
notHaveAnyResource=Vous n''avez aucune ressource
noResourcesSharedWithYou=Aucune ressource partagée avec vous
clickHereForDetails=Cliquer ici pour plus d''informations.
resourceIsNotBeingShared=Cette ressource n''est pas partagée
clearAllFilter=Réinitialiser les filtres
activeFilters=Filtres actifs
filterByName=Filtrer par nom ...
allApps=Toutes les applications
internalApps=Applications internes
thirdpartyApps=Applications tierces
appResults=Résultats
identityProvider=Fournisseur d''identités
userDefined=Défini par l''utilisateur
removeAccess=Supprimer l''accès
authenticatorStatusMessage=L''authentification à deux facteurs est
authenticatorFinishSetUpTitle=Votre authentification à deux facteurs
resourceManagedPolicies=Autorisation donnant accès à cette ressource
resourceNoPermissionsGrantingAccess=Pas d''autorisation donnent accès à cette ressource
anyAction=Toute action
shareWithOthers=Partager avec d''autres
requestsWaitingApproval=Vos demandes en attente d''approbation
permissionRequestion=Demandes d''autorisation
permission=Autorisation
havePermissionRequestsWaitingForApproval=Vous avez {0} demande(s) d''autorisation en attente d''approbation.
authorizedProviderMessage=Fournisseurs autorisés liés à votre compte
authorizedProvider=Fournisseur autorisé
federatedIdentityBoundOrganization=Vous ne pouvez pas supprimer le lien avec un fournisseur d''identités associé à une organisation.
authenticatorSubTitle=Paramétrer l''authentification à deux facteurs
authenticatorSubMessage=Pour renforcer la sécurité de votre compte, activez au moins une méthode disponible d''authentification à deux facteurs.
clientNotFoundMessage=Client non trouvé.
enterBarCode=Renseignez le code à usage unique
doCopy=Copier
doFinish=Terminer
chooseYourCountry=Choisissez votre pays
sendVerficationCode=Envoyez le code de vérification
enterYourVerficationCode=Renseignez le code de vérification
realmName=Domaine
generateNewBackupCodes=Générer de nouveaux codes de récupération d''authentification
sharedwithMe=Partagé avec moi
share=Partage
sharedwith=Partager avec
accessPermissions=Autorisations d''accès
permissionRequests=Demandes d''autorisation
approve=Approuver
approveAll=Tout approuver
people=personnes
perPage=par page
currentPage=Page actuelle
group=Groupe
addTeam=Ajouter des équipes pour partager vos ressources avec elles
myPermissions=Mes autorisations
waitingforApproval=En attente d''approbation
anyPermission=Toute autorisation
openshift.scope.user_info=Informations de l''utilisateur
openshift.scope.user_check-access=Informations sur les accès de l''utilisateur
openshift.scope.user_full=Accès complet
error-invalid-blank=Merci de renseigner une valeur.
error-invalid-length-too-short=L''attribut {0} doit avoir une longueur minimale de {1}.
error-invalid-length-too-long=L''attribut {0} doit avec une longueur maximale de {2}.
error-invalid-email=Adresse de courriel non valide.
error-invalid-number=Nombre non valide.
error-number-out-of-range-too-small=L''attribut {0} doit avec une valeur minimale de {1}.
error-number-out-of-range-too-big=L''attribut {0} doit avoir une valeur maximale de {2}.
error-invalid-uri-scheme=Schéma d''URL non valide.
error-user-attribute-required=Merci de renseigner l''attribut {0}.
error-invalid-date=Date non valide.
error-user-attribute-read-only=Le champ {0} est en lecture seule.
error-username-invalid-character=Le nom d''utilisateur contient un caractère non valide.
addPeople=Ajouter des personnes pour partager vos ressources avec elles
error-person-name-invalid-character=Le nom contient un caractère non valide.
error-invalid-uri-fragment=Fragment d''URL non valide.
sharetheResource=Partager la ressource
openshift.scope.list-projects=Lister les projets
error-invalid-length=L''attribut {0} doit avoir une longueur comprise entre {1} et {2}.
scanBarCode=Voulez-vous scanner le code-barres ?
enterYourPhoneNumber=Renseignez vote numéro de téléphone
doDownload=Télécharger
doPrint=Imprimer
error-number-out-of-range=L''attribut {0} doit être un nombre compris entre {1} et {2}.
resources=Ressources
error-invalid-uri=URL non valide.
selectPermission=Sélectionner l''autorisation
error-invalid-value=Valeur non valide.
error-empty=Merci de renseigner une valeur.
error-pattern-no-match=Valeur non valide.
authenticatorMobileMessage=Utiliser un authentificateur sur smartphone pour obtenir des codes de vérification pour l''authentification à deux facteurs.
mobileSetupStep3=Renseignez le code à usage unique fourni par l''application et cliquez sur Sauvegarder pour terminer le paramétrage.
applicationInUse=Utilisation uniquement dans l''application
removeAccessMessage=Vous devrez redonner accès à nouveau si vous voulez utiliser ce compte applicatif.
identityProviderMessage=Pour lier votre compte avec un fournisseur d''identités déjà configuré
socialLogin=Nom sur les réseaux sociaux
authenticatorFinishSetUpMessage=Chaque fois que vous vous connecterez à votre compte Keycloak, il vous sera demandé de fournir un code d''authentification à deux facteurs.
authenticatorMobileTitle=Authentificateur sur smartphone
authenticatorMobileFinishSetUpMessage=Cet authentificateur a été relié à votre téléphone.
authenticatorSMSTitle=Code par SMS
authenticatorMobileSetupTitle=Paramétrage de l''authentificateur sur smartphone
smscodeIntroMessage=Renseignez votre numéro de téléphone et un code de vérification sera envoyé à votre téléphone.
mobileSetupStep1=Installez une application d''authentificateur sur votre téléphone. Les applications suivantes sont supportées.
mobileSetupStep2=Ouvrez l''application et scannez le code-barres :
authenticatorSMSMessage=Keycloak enverra le code de vérification sur votre téléphone en tant qu''authentification à deux facteurs.
authenticatorDefaultStatus=Par défaut
authenticatorChangePhone=Changer le numéro de téléphone
authenticatorSMSFinishSetUpMessage=Les messages texte sont envoyés sur
authenticatorSMSCodeSetupTitle=Paramétrage du code par SMS
authenticatorBackupCodesSetupTitle=Paramétrage des codes de récupération de l''authentification
backtoAuthenticatorPage=Retour à la page de l''authentificateur
