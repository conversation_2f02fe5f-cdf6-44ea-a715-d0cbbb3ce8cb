# Keycloak SSO Realm Configuration

This repository contains a complete, production-ready Keycloak realm configuration for Single Sign-On (SSO) authentication, built step by step with detailed explanations.

## 📁 Files Overview

| File | Description |
|------|-------------|
| `realm-config.json` | Complete realm configuration with SSO settings |
| `realm-config-guide.md` | Detailed explanation of each configuration section |
| `deploy-realm.sh` | Automated deployment script |
| `validate-realm-config.sh` | Configuration validation script |
| `README.md` | This file - getting started guide |

## 🚀 Quick Start

### 1. Prerequisites

- Keycloak server running (version 20+)
- `jq` installed for JSON processing
- `curl` for API calls
- Admin access to Keycloak

### 2. Customize Configuration

Before deployment, update the placeholder values in `realm-config.json`:

```bash
# Required changes:
- Replace "my-sso-realm" with your realm name
- Update all client secrets
- Configure identity provider credentials
- Set proper redirect URIs for your applications
- Update SMTP server settings
- Replace example domains with your actual domains
```

### 3. Validate Configuration

Run the validation script to check for issues:

```bash
# Linux/Mac
./validate-realm-config.sh realm-config.json

# Windows
bash validate-realm-config.sh realm-config.json
```

### 4. Deploy Realm

Use the deployment script for automated deployment:

```bash
# Linux/Mac
./deploy-realm.sh

# Windows
bash deploy-realm.sh

# Or set custom parameters
KEYCLOAK_URL=https://your-keycloak.com \
ADMIN_USER=admin \
REALM_FILE=realm-config.json \
./deploy-realm.sh
```

### 5. Manual Deployment (Alternative)

If you prefer manual deployment:

```bash
# Get admin token
TOKEN=$(curl -s -X POST \
  "http://localhost:8080/realms/master/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin" \
  -d "password=admin" \
  -d "grant_type=password" \
  -d "client_id=admin-cli" | jq -r '.access_token')

# Create realm
curl -X POST \
  "http://localhost:8080/admin/realms" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d @realm-config.json
```

## 🔧 Configuration Sections Explained

### Basic Realm Settings
- **Realm name and display settings**
- **SSL/TLS requirements**
- **Token lifespans and session management**
- **Security algorithms**

### Authentication & Authorization
- **User registration and login settings**
- **Brute force protection**
- **Required credentials**
- **Default roles and permissions**

### Client Applications
- **OpenID Connect web application**
- **SAML application**
- **Client authentication methods**
- **Redirect URIs and CORS settings**

### Identity Providers
- **Google OAuth2/OIDC**
- **Corporate SAML IdP**
- **Microsoft Azure AD**
- **Custom identity provider configurations**

### Security Features
- **Browser security headers**
- **Token validation settings**
- **Session management**
- **Logout configuration**

## 🔐 Security Best Practices Implemented

### Token Security
- ✅ Short access token lifespan (5 minutes)
- ✅ Appropriate refresh token settings
- ✅ Secure signature algorithms (RS256)
- ✅ Proper session timeouts

### Client Security
- ✅ Client secrets for confidential clients
- ✅ Strict redirect URI validation
- ✅ Disabled deprecated flows (implicit)
- ✅ CORS configuration

### Identity Provider Security
- ✅ Signature validation for SAML
- ✅ Encrypted assertions where needed
- ✅ Proper attribute mapping
- ✅ Trust email verification

### Infrastructure Security
- ✅ SSL/TLS requirements
- ✅ Security headers configuration
- ✅ Brute force protection
- ✅ Email verification

## 🧪 Testing Your Configuration

### 1. Basic Functionality Test
```bash
# Test realm accessibility
curl -s "http://localhost:8080/realms/my-sso-realm/.well-known/openid-configuration"

# Test SAML metadata
curl -s "http://localhost:8080/realms/my-sso-realm/protocol/saml/descriptor"
```

### 2. Client Authentication Test
```bash
# Test OIDC client
curl -X POST \
  "http://localhost:8080/realms/my-sso-realm/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials" \
  -d "client_id=my-web-app" \
  -d "client_secret=your-client-secret"
```

### 3. Identity Provider Test
- Navigate to: `http://localhost:8080/realms/my-sso-realm/account`
- Test login with each configured identity provider
- Verify user attributes are properly mapped

## 📋 Post-Deployment Checklist

- [ ] Realm is accessible via admin console
- [ ] All clients are properly configured
- [ ] Identity providers are working
- [ ] SMTP configuration is functional
- [ ] Test users can authenticate
- [ ] SSO flow works end-to-end
- [ ] Logout functionality works
- [ ] Token refresh works properly
- [ ] Security headers are present
- [ ] Brute force protection is active

## 🔧 Customization Guide

### Adding New Clients

1. Add to the `clients` array in `realm-config.json`:
```json
{
  "clientId": "new-app",
  "name": "New Application",
  "protocol": "openid-connect",
  "enabled": true,
  "standardFlowEnabled": true,
  "redirectUris": ["https://newapp.com/*"]
}
```

### Adding New Identity Providers

1. Add to the `identityProviders` array:
```json
{
  "alias": "new-idp",
  "providerId": "oidc",
  "enabled": true,
  "config": {
    "clientId": "your-client-id",
    "clientSecret": "your-client-secret",
    "authorizationUrl": "https://idp.com/auth",
    "tokenUrl": "https://idp.com/token"
  }
}
```

### Modifying Token Lifespans

Update these values based on your security requirements:
```json
{
  "accessTokenLifespan": 300,
  "ssoSessionIdleTimeout": 1800,
  "ssoSessionMaxLifespan": 36000
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Invalid JSON**: Run `jq . realm-config.json` to validate
2. **Authentication Failed**: Check admin credentials
3. **Client Not Found**: Verify client ID matches exactly
4. **Redirect URI Mismatch**: Ensure URIs match exactly (no wildcards in production)
5. **Identity Provider Error**: Check client ID/secret and endpoints

### Debug Mode

Enable debug logging in Keycloak:
```bash
# Add to Keycloak startup
--log-level=DEBUG
```

### Validation Errors

Run the validation script for detailed error analysis:
```bash
./validate-realm-config.sh realm-config.json
```

## 📚 Additional Resources

- [Keycloak Documentation](https://www.keycloak.org/documentation)
- [OpenID Connect Specification](https://openid.net/connect/)
- [SAML 2.0 Specification](https://docs.oasis-open.org/security/saml/v2.0/)
- [OAuth 2.0 Security Best Practices](https://datatracker.ietf.org/doc/html/draft-ietf-oauth-security-topics)

## 🤝 Contributing

1. Fork the repository
2. Make your changes
3. Test with the validation script
4. Submit a pull request

## 📄 License

This configuration is provided as-is for educational and production use. Modify according to your security requirements.

---

**⚠️ Security Notice**: Always review and customize this configuration for your specific environment. Never use default passwords or placeholder values in production!
