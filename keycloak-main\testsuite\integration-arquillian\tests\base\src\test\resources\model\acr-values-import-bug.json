{"id": "8d394374-7fc5-4ae5-bcb1-ba72a952209c", "realm": "acr-import-bug", "displayName": "", "displayNameHtml": "", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxTemporaryLockouts": 0, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "d0d2b9f8-a226-4582-a748-38e54ac32a4f", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "8d394374-7fc5-4ae5-bcb1-ba72a952209c", "attributes": {}}, {"id": "754de213-de0c-4e76-9451-0174cedfc9c6", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "8d394374-7fc5-4ae5-bcb1-ba72a952209c", "attributes": {}}, {"id": "9f20c552-0506-4887-8816-d83dc7580a18", "name": "default-roles-acr-import-bug", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["manage-account", "view-profile"]}}, "clientRole": false, "containerId": "8d394374-7fc5-4ae5-bcb1-ba72a952209c", "attributes": {}}], "client": {"realm-management": [{"id": "8f28c84d-d4b7-42bc-9ef0-fe89247367e5", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "4607df14-62c9-4b36-8fcf-af813913187d", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "b1850915-c491-47cc-abdd-dbe1f6955e75", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "afacfde7-c0e3-4546-8853-8c09576a40ea", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "bd0d084e-1064-4c6c-b6d0-9c8e34f620c7", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "980763a4-ef76-4c7d-be38-6765e7f22418", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "1ef54cfc-44a3-4615-bb76-987512b75329", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "cf733c34-278b-4228-a7c0-751c8759375c", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "bab1f8a5-bcaa-4795-b76c-e79b875c4a9e", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "e09bbbf3-c3c1-4ee2-8b0b-9a6e694fbc9b", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "d6f2fff2-967e-4909-b843-6d61b186df1d", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "97186017-ea03-465a-ab44-cd302b86ef33", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "c185dec0-88b9-472a-81b4-1908746924b0", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "e7e2db56-a2ec-4d17-b225-a4e45a7299db", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "48f553f3-8b79-4d1a-b88d-1ce0bc7ce3f4", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "1d40d093-72b7-4b47-b8ef-763fe10bf619", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "ee17b494-02e3-481e-9a5d-a272ff9db358", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "366fffa4-4c02-4576-b73e-e0d92c786e68", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["view-authorization", "view-events", "manage-authorization", "view-users", "query-users", "manage-realm", "view-clients", "query-realms", "query-clients", "manage-users", "manage-events", "impersonation", "view-identity-providers", "query-groups", "manage-identity-providers", "create-client", "manage-clients", "view-realm"]}}, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}, {"id": "74050f82-d3d8-468d-8477-1b8e606d49ee", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "account-console": [], "client-silver": [], "broker": [{"id": "9086f505-8017-44c7-95f7-2ee0c24546db", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "e3c286f0-ca98-44e8-a1b5-7f900c01aa61", "attributes": {}}], "account": [{"id": "cfc25994-fc1d-4a67-b2cb-620ebbd776f7", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "1cb83137-4919-473b-9942-0ef60ec26d83", "attributes": {}}, {"id": "bc91a821-7800-4c2a-85c5-5a29af0d42de", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "1cb83137-4919-473b-9942-0ef60ec26d83", "attributes": {}}, {"id": "9ce1df70-b199-46ee-868e-5c071eeff6c1", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "1cb83137-4919-473b-9942-0ef60ec26d83", "attributes": {}}, {"id": "d184ab72-2f25-42bf-aef4-0248176a44ff", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "1cb83137-4919-473b-9942-0ef60ec26d83", "attributes": {}}, {"id": "2f701f59-f624-4508-8a9e-dc2fe7ecd48c", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "1cb83137-4919-473b-9942-0ef60ec26d83", "attributes": {}}, {"id": "c5703f5e-e92f-4e2d-a15b-8d7c523134de", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "1cb83137-4919-473b-9942-0ef60ec26d83", "attributes": {}}, {"id": "f4716f59-8d75-40cf-aa1e-830662ae0125", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "1cb83137-4919-473b-9942-0ef60ec26d83", "attributes": {}}, {"id": "911a753c-21a1-4596-a7ca-d432943ea7fa", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "1cb83137-4919-473b-9942-0ef60ec26d83", "attributes": {}}]}}, "groups": [], "defaultRole": {"id": "9f20c552-0506-4887-8816-d83dc7580a18", "name": "default-roles-acr-import-bug", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "8d394374-7fc5-4ae5-bcb1-ba72a952209c"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256", "RS256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256", "RS256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "1cb83137-4919-473b-9942-0ef60ec26d83", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/acr-import-bug/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/acr-import-bug/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "aee0e27c-5021-4dc2-b04e-f38570841568", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/acr-import-bug/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/acr-import-bug/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "f1f46590-cf76-48b6-8318-3e6a21b47950", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "7351d53b-c091-4b62-9d3a-8704e7<PERSON>bee", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "client.use.lightweight.access.token.enabled": "true"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "e3c286f0-ca98-44e8-a1b5-7f900c01aa61", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "true"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "9966d6c1-e60c-4c97-b21b-fc866e60bd4e", "clientId": "client-silver", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"client.introspection.response.allow.jwt.claim.enabled": "false", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "use.refresh.tokens": "true", "realm_client": "false", "oidc.ciba.grant.enabled": "false", "client.use.lightweight.access.token.enabled": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "tls.client.certificate.bound.access.tokens": "false", "require.pushed.authorization.requests": "false", "acr.loa.map": "{}", "display.on.consent.screen": "false", "token.response.type.bearer.lower-case": "false", "dpop.bound.access.tokens": "false", "default.acr.values": "silver"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "2772d77b-5efe-46f1-a685-aca8417f8bb0", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "true"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "15f73142-0795-43b2-99bc-a7f3c7408cce", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/acr-import-bug/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/acr-import-bug/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "client.use.lightweight.access.token.enabled": "true", "post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "5b285615-ec56-47e6-a446-c804472ce980", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}], "clientScopes": [{"id": "234a32f8-4535-42bf-847f-b96b7efa72bd", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "2d7a67dd-6e94-4a02-9e41-5c254ff2456a", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "265c5976-b829-4166-a128-14e2da37037d", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "eff36ec9-3948-4710-b153-cd72ecd7e121", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "8e4c9a43-9bc0-48cb-8ebe-baa1d2172636", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "1590dd23-3158-4913-900e-6c476783fa48", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "6a984246-49d1-45d8-ad2a-706321b18935", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String"}}, {"id": "a49e96ed-377f-442f-ba75-4b1bd7a2115f", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String"}}, {"id": "12339061-a6af-4539-8d98-9fbd06d6bf64", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "f4535162-2b92-4eba-bc8d-2c82c18e978f", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "b185d8d0-bea2-4c19-8f80-43e59b9ec27f", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "96f1737b-bce1-46c3-a07b-220e85c70a74", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "23667228-97e3-488d-80a3-10727887ef85", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "b87664e9-6e38-4355-916d-16bed6f815a9", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "c61839d9-100f-4703-bced-772a7a4fa9d0", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "15db93e7-6e77-4210-b1ab-dbdbe55fe6ba", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "e7233a3e-151a-4ff6-847f-37bf73e73209", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "b61a70f4-1858-445b-8e26-c2c9f797794f", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "c30c2f50-e605-4c56-9c34-3f4b3e11cf9b", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "495a47f0-7b81-48f5-a874-6a3b28d0dd58", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "99907319-c966-487e-9167-78cbe60ac9c8", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "4e5fb425-11c9-443c-8509-d5a13046a293", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "d015c37f-160d-4059-bddf-744403dd7ed2", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "long"}}, {"id": "a7e11613-eaa8-4139-9a80-e975fec38559", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}]}, {"id": "6d278ebc-5eee-4a2f-9f1a-4bc15451abd4", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "b6d27bd5-c123-46d4-87d6-156522593aac", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "37ba8d17-2081-488e-a2f9-421a437477da", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "26fba604-c193-45c1-860d-63bdf9390120", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "0235b84a-f247-4790-b45c-df733b857f38", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "450ba32f-96d2-4b6d-8749-b4d4fce40364", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "2cedb7e4-8a0e-448b-b73d-e31ba6b160a9", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "0fd36890-f22f-4fa0-a215-9cba15a20e82", "name": "oid4vc_natural_person", "description": "OIDC$VP Scope, that adds all properties required for a natural person.", "protocol": "oid4vc", "attributes": {}, "protocolMappers": [{"id": "438583b0-43e2-43c6-8ddb-18b22c0ef985", "name": "subject id", "protocol": "oid4vc", "protocolMapper": "oid4vc-subject-id-mapper", "consentRequired": false, "config": {"supportedCredentialTypes": "VerifiableCredential", "subjectIdProperty": "id"}}, {"id": "76eb9240-19b3-40ef-9c15-94cb65e6d7e1", "name": "email", "protocol": "oid4vc", "protocolMapper": "oid4vc-user-attribute-mapper", "consentRequired": false, "config": {"subjectProperty": "email", "userAttribute": "email", "aggregateAttributes": "false"}}, {"id": "263382a8-1e81-456b-a19d-167f9450017a", "name": "last-name", "protocol": "oid4vc", "protocolMapper": "oid4vc-user-attribute-mapper", "consentRequired": false, "config": {"subjectProperty": "<PERSON><PERSON>ame", "userAttribute": "lastName", "aggregateAttributes": "false"}}, {"id": "30ea0545-b322-4df9-9868-318f209f3b73", "name": "client roles", "protocol": "oid4vc", "protocolMapper": "oid4vc-target-role-mapper", "consentRequired": false, "config": {"subjectProperty": "roles", "clientId": "id"}}, {"id": "8619e003-8417-4a32-a5dc-e16cc03d6e12", "name": "first-name", "protocol": "oid4vc", "protocolMapper": "oid4vc-user-attribute-mapper", "consentRequired": false, "config": {"subjectProperty": "firstName", "userAttribute": "firstName", "aggregateAttributes": "false"}}]}, {"id": "fa4e5311-3220-491c-b15d-bb4b0f371bec", "name": "basic", "description": "OpenID Connect scope for add all basic claims to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "f0a1a22a-a69a-471e-8198-e2ce693bfdc2", "name": "sub", "protocol": "openid-connect", "protocolMapper": "oidc-sub-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}, {"id": "2ed370f9-6a11-4ac6-adb0-6450f8ca1c79", "name": "auth_time", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "AUTH_TIME", "introspection.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "auth_time", "jsonType.label": "long"}}]}, {"id": "e757c34a-847e-46df-ba53-304d833a69a9", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "19dc48ba-ece2-4e84-8286-e65a7af4fb15", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "8193c3ea-4d7c-4da3-9f26-f066e87b0902", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "introspection.token.claim": "true", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "226e734c-1d2d-4c38-a9f2-f5bad803343f", "name": "saml_organization", "description": "Organization Membership", "protocol": "saml", "attributes": {"display.on.consent.screen": "false"}, "protocolMappers": [{"id": "fe40e896-741a-4870-a6ab-2b83f691022e", "name": "organization", "protocol": "saml", "protocolMapper": "saml-organization-membership-mapper", "consentRequired": false, "config": {}}]}, {"id": "f3763c4f-7995-4605-8a57-4cef9711a6af", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "827d84b8-431b-4048-9e84-27204e0212ed", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "0df26e68-6617-42bc-bae4-8831ec0072e3", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "fb6cf36e-9b2c-4d94-82b4-ee09f9f74d51", "name": "organization", "description": "Additional claims about the organization a subject belongs to", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${organizationScopeConsentText}"}, "protocolMappers": [{"id": "b4b47251-38ae-4b97-b8ff-588ca425f7da", "name": "organization", "protocol": "openid-connect", "protocolMapper": "oidc-organization-membership-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "organization", "jsonType.label": "String"}}]}], "defaultDefaultClientScopes": ["oid4vc_natural_person", "role_list", "saml_organization", "profile", "email", "roles", "web-origins", "acr", "basic"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt", "organization"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "referrerPolicy": "no-referrer", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "e3d463ef-f05a-456d-a732-98f59d371399", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "e3dd4c42-7bbd-4452-b342-a9674c27a2df", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-attribute-mapper", "oidc-address-mapper", "saml-user-property-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "oidc-full-name-mapper", "oidc-usermodel-property-mapper"]}}, {"id": "0cdfe2e9-af05-4d60-a9eb-92b790dda77a", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-attribute-mapper", "saml-user-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-property-mapper", "oidc-address-mapper", "oidc-full-name-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper"]}}, {"id": "5328265d-4485-4729-b0a3-59ea4a4b3e24", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "56b10355-63e6-4650-9db9-1618ff4e62ed", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "84b5a91c-ec17-4abd-bbb2-f01fe03ca415", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "3cfad80f-4154-455c-a071-9c2795656f3e", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "25d549f2-919f-46f1-989d-c73db6fd373d", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}], "org.keycloak.keys.KeyProvider": [{"id": "ea72c3b7-2108-4711-b489-ea36594daea0", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "d2e08eb7-c1bb-4eea-aeef-d6c6af9e53fa", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "202efc16-687d-471f-a616-5c36a8f5fc42", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["RSA-OAEP"]}}, {"id": "1df3044d-5bcc-41ce-80d5-1eaafef53261", "name": "hmac-generated-hs512", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["HS512"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "296c34f9-a695-4746-90b2-f984cc1b93e3", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "6fab4fe3-3f6c-4654-aef4-467e32371a06", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "b7ea2253-4c91-4189-ab39-431b47dcf1d8", "alias": "Browser - Conditional Organization", "description": "Flow to determine if the organization identity-first login is to be used", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "organization", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "3f99a74c-a0bb-4280-90d6-2768adfd467e", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "5cc8c49d-1f96-4876-9a9b-2eb2622f3d26", "alias": "First Broker Login - Conditional Organization", "description": "Flow to determine if the authenticator that adds organization members is to be used", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "idp-add-organization-member", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "72d55a7d-1566-4bd5-8b47-e769dc2b4ac8", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "0d3d4cb4-7295-4037-ba65-1824f150efdc", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "221d5591-f06c-4ab6-b503-04897f797731", "alias": "Organization", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 10, "autheticatorFlow": true, "flowAlias": "Browser - Conditional Organization", "userSetupAllowed": false}]}, {"id": "ac00ca2b-de46-4803-adf9-e4390c2989c5", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "2dee9ecb-f179-4705-ba52-92137f967750", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "59ae1063-71f0-4f2e-944d-4661da47bc3c", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "868b6609-d5df-4140-9178-731eb780e90b", "alias": "browser", "description": "Browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 26, "autheticatorFlow": true, "flowAlias": "Organization", "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "27fb37a8-8662-4ba6-b5f8-4166b7698a6a", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "13817cbc-25c3-417e-bea4-c45ec00652d0", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "85f6274d-a488-4f9b-8ba7-d0aa21eff00d", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "39f60a11-f1ba-42af-8c06-c6a7968f1a5d", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 50, "autheticatorFlow": true, "flowAlias": "First Broker Login - Conditional Organization", "userSetupAllowed": false}]}, {"id": "704224fa-1f8d-4b56-93d6-a751b0e9fced", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "25214fd0-ae24-42f3-9fe3-b84e444174ef", "alias": "registration", "description": "Registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "b8810eda-ca51-4f46-85ef-fdd4e1d3f9de", "alias": "registration form", "description": "Registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-terms-and-conditions", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 70, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "e01e969c-e4a8-4251-9dd8-a357f89eb478", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "560b9e58-a89b-4ed6-9c33-8366ddbfe663", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "e88048d5-5970-4a4a-b607-7d2ad2b409f8", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "4028cf4f-c3aa-4464-89ae-5b8a2ce6207e", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "CONFIGURE_RECOVERY_AUTHN_CODES", "name": "Recovery Authentication Codes", "providerId": "CONFIGURE_RECOVERY_AUTHN_CODES", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "UPDATE_EMAIL", "name": "Update Email", "providerId": "UPDATE_EMAIL", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": true, "defaultAction": false, "priority": 80, "config": {}}, {"alias": "VERIFY_PROFILE", "name": "Verify Profile", "providerId": "VERIFY_PROFILE", "enabled": true, "defaultAction": false, "priority": 90, "config": {}}, {"alias": "delete_credential", "name": "Delete Credential", "providerId": "delete_credential", "enabled": true, "defaultAction": false, "priority": 100, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "firstBrokerLoginFlow": "first broker login", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaAuthRequestedUserHint": "login_hint", "oauth2DevicePollingInterval": "5", "clientOfflineSessionMaxLifespan": "0", "clientSessionIdleTimeout": "0", "clientOfflineSessionIdleTimeout": "0", "cibaInterval": "5", "realmReusableOtpCode": "false", "cibaExpiresIn": "120", "oauth2DeviceCodeLifespan": "600", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "frontendUrl": "", "organizationsEnabled": "false", "acr.loa.map": "{\"silver\":\"1\",\"gold\":\"2\"}"}, "keycloakVersion": "999.0.0-SNAPSHOT", "userManagedAccessAllowed": false, "organizationsEnabled": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}