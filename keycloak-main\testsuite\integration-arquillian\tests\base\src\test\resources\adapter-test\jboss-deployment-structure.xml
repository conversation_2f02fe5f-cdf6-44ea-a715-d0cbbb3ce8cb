<!--
  ~ Copyright 2016 Red Hat, Inc. and/or its affiliates
  ~ and other contributors as indicated by the <AUTHOR>
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<jboss-deployment-structure>
    <deployment>
        <dependencies>

            <!-- the Demo code uses classes in these modules.  These are optional to import if you are not using
            Apache Http Client or the HttpClientBuilder that comes with the adapter core -->
            <module name="org.apache.httpcomponents"/>

            <!--required by SAML test servlets-->
            <module name="org.keycloak.keycloak-adapter-spi" />
            <module name="org.keycloak.keycloak-saml-core" />
            <module name="org.keycloak.keycloak-saml-core-public" />
            
        </dependencies>
    </deployment>
</jboss-deployment-structure>
