{"id": "test-user-null-attr", "realm": "test-user-null-attr", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "e161bb37-bffb-44a8-8d39-c0de5e378cd5", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "test-user-null-attr", "attributes": {}}, {"id": "6aec0f95-55b4-4fe7-87b1-73c8c1e28556", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "test-user-null-attr", "attributes": {}}], "client": {"realm-management": [{"id": "47ad2786-2ed4-4614-bfdb-7cb3d0d6d3af", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "77637fb4-2159-47d9-bd02-bbea227f8182", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "fcc75dc3-c08f-4ea9-af33-a94c215bf28e", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "6cb02fc0-d0d6-4b83-bbf3-9fe48844507d", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "d251255e-99ab-4c6d-a53a-f16905ce88b5", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "3e08c460-ac98-4b6d-a895-516583a9daaf", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "7a8b6fe6-7ac6-4683-a964-c58ea38991f0", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "ad54ac19-edf8-4955-9b92-d667b0b7fe36", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "4ebe00a1-327c-47c6-ab57-dd63f2d2029e", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "ee4c2b4e-4322-4d3c-a402-8825fcb84fab", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "77bc5018-fdda-4cae-b2c5-86e78d957f92", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "bee6e333-08c1-478f-be83-a2f5d6b1f4d9", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["view-identity-providers", "view-clients", "manage-clients", "manage-identity-providers", "manage-users", "create-client", "query-groups", "manage-authorization", "view-authorization", "impersonation", "view-realm", "manage-events", "view-events", "view-users", "manage-realm", "query-clients", "query-users", "query-realms"]}}, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "b931dcee-095e-4092-b3da-207efd2af07c", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "865df2a3-9908-4562-89a2-7abf97bacc58", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "24ad1a73-4ac4-47aa-a773-9433c212ab29", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "414dbb00-16e9-4ed4-a8f1-041370e3e069", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "af0d82f1-f438-4a37-a37a-e866597a2126", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "a64c5a34-71ad-4f93-aab3-246c93b7c038", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}, {"id": "b3a25a3a-63e4-4d2c-8902-8cbe1de3e30e", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "********-2d4d-4b67-8158-afc00a300b3c", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "********-aeb8-4ec3-8371-fcc847e15d20", "attributes": {}}], "account": [{"id": "8e2532f1-c892-47f9-9125-d122756d850b", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "618d62a1-1d11-48e5-b14b-41de66877629", "attributes": {}}, {"id": "8ab03779-c159-4ae0-aa26-2ef93e0d939c", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "618d62a1-1d11-48e5-b14b-41de66877629", "attributes": {}}, {"id": "a1e8df41-0da8-475e-b96e-165b364979ec", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "618d62a1-1d11-48e5-b14b-41de66877629", "attributes": {}}]}}, "groups": [], "defaultRoles": ["uma_authorization", "offline_access"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "users": [{"id": "269d52af-f3c3-48f6-841e-75dc7b808831", "createdTimestamp": *************, "username": "testuser", "enabled": true, "totp": false, "emailVerified": false, "attributes": {"key1": ["value1"], "key2": ["value2"], "key3": [null]}, "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["offline_access", "uma_authorization"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "618d62a1-1d11-48e5-b14b-41de66877629", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/test-user-null-attr/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "8766f356-181b-4365-966e-759b37d19ba0", "defaultRoles": ["view-profile", "manage-account"], "redirectUris": ["/realms/test-user-null-attr/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "c49a7747-3f56-4709-a0cc-248ceb67e199", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/test-user-null-attr/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "315f51c2-bbb6-426a-8b74-777cf7423248", "redirectUris": ["/realms/test-user-null-attr/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "598ed852-1b50-4083-be76-55be502977c5", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "be3a4e6b-523c-46fe-a75a-885191aee1cb", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "804b52c0-5b35-4066-9617-e97b5d801052", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "********-aeb8-4ec3-8371-fcc847e15d20", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "c24b44a6-cdfc-43e2-8416-9fb2178dda93", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "759d0356-3c2b-4192-90b2-63b9ebcf46cb", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "f60a8b90-1d0d-4ec1-b95e-c97067d16182", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "03235ba2-ad66-4f53-97f9-0bf8069619aa", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/test-user-null-attr/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "89ce17a6-2fbf-4b94-b09d-0e172a68776d", "redirectUris": ["/admin/test-user-null-attr/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "9e277beb-7ab1-40e1-8537-7efe9206a737", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "1ac2e110-441c-45c4-8e2e-c50d7b2b8994", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "809e4a60-387c-4417-b50d-7ddb03eb0449", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "4cecd387-2fe4-4b0b-baf2-f77ec8810099", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "238514d4-859c-44c8-90ca-a1497473435f", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}, {"id": "76f40b9a-af6c-46a8-8fc6-0687da2d3553", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "5e619211-3659-4121-bebc-42ab0078c428", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "6dd05ee2-ea89-48b4-877e-d5578dcf37a3", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "93e6f241-846c-48af-ba78-7c8266c7e558", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}, {"id": "f436fe9e-485c-4b74-a1db-19108a78de34", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "5d41409d-0253-4fac-a4c9-7f76a377f44f", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "1a978dec-da66-4bf1-b773-b009e23e4725", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}, {"id": "7248b788-835f-489d-a14b-ab4498db7007", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}]}, {"id": "fd4aac27-cc9d-49e2-aa48-41695aa8eb40", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "4a176ee7-dab6-49bd-b644-ee359f7966ff", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "85ec37c1-8430-4984-ad49-cfb01e0780b5", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "032520c0-4356-4c4e-bab6-7b10696b2dd6", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "72a91f75-7a09-4e46-a204-bb11c6093474", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "67dc078b-09fc-405f-8905-70aa32d3532b", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "a7ffb672-9bc7-408d-9301-a872fbe6d670", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "d4a1feb7-1727-40f9-a732-676463d54889", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "2ed6d2e2-0c65-4508-aecb-80b32bb5ced6", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "4e68813a-84be-4e9e-ba83-dcb8d773e4d4", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "755b29e5-1df9-4c5c-8796-788c4a39d164", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "6a4872d3-b9d2-4932-827f-11c04bae18d3", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "649cc53c-6ae1-44e1-850a-ecf9afb7eaa3", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "4bff897e-c5ff-46a4-b010-a81f3fa3152c", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "00d048d6-8c89-4a29-843a-020edada37fc", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}]}, {"id": "4de348f5-b362-448d-809e-edc2a70261fa", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "62cb2960-6885-4a98-b4d0-8987c44e7bec", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "54de406a-cfac-42ae-8d97-f149ad1043b8", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "7bdd6fb0-1055-4c66-8409-34d6d11b6be4", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "f6181634-4eb6-436f-ad95-6d287f260380", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "80ca5dfb-d1f9-4c2f-8f1f-4056b9248259", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "0875318f-0ebd-4e91-9a44-f12a16d6d966", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "01139d93-185a-4635-a81b-1b6a2e66f1a0", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "a5172cf0-6b05-41ee-870a-fd25e6c2e498", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "5bf6b838-1d41-4370-b61f-c51faab98eb7", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "f0de671e-3389-4533-acc9-ca3e3ff7716d", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-sha256-pairwise-sub-mapper", "saml-user-attribute-mapper", "oidc-address-mapper", "oidc-usermodel-attribute-mapper", "oidc-full-name-mapper", "saml-user-property-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper"]}}, {"id": "927fb360-0adc-4c91-b480-d003768df18a", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "f429d8bf-d042-4b13-aa70-e1b09ef6a3a4", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "2e5317ec-a8c7-47f5-aa3c-de12a99c13ff", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper", "oidc-address-mapper"]}}, {"id": "9c089afc-4d32-47e7-bf2d-62ce27d08cb7", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "41f6398e-d9db-4756-b376-0ca589e95bdb", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}], "org.keycloak.keys.KeyProvider": [{"id": "6106a56b-47e9-47d0-a3f6-59952d5e597b", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAhSjJajn/bcNGK49W9tCFVIFGL+MYsAotNGE389X0xnGsWzRI0+4pOF8R+6HlXP3wKOlUdNckCgu7HvzJ/N+mTl6CTVWdnQKQVhRY0NcqccK8iMWkRtL3sqJKbNHZ0iSug6Wccx71AcS8ieEUy7zRB5fdylRR30NUeDhMKlFAVyxMGfWYwISr1Mqx2cq1BPByzyi1+AAkrka9cs58JdcFDzUGxy2yXmxJB5Uuqi5wheFOTVY6mGeZIA1r8ADIrwD3mByYzGVRNI/UB8dN3X7iZgKss2TtDhLKSURwohwRJiXvBZg7VpwVoz4M0oitEHfvb8LTIWCpN5OY2d8+goS19QIDAQABAoIBAHdSExozGmXbA7fo2/6S38bXiHNExkyI8fTr4N2IraxdFBsuAMyXrywqVMztR8BKdLfUTa/dURgHZwffcYg9SKMVISH3RCL7tybLWMLa69fArnzIzeoBBaB2uMZGTS93W0HwVv75kIajKmdK3/2pFo39UesKH8s2ZCzOFcIdyM/TQQHkzQgz9d6xQt9i3XjjZiixSDtWoi6WUixur6LM3rFIdbGke1VzaCFFnzM9maBqtjYLYXDEuiJXsp4Gr5CvwU0doyKyizuZlfUqEPKtlRwpLRQorDRP0P5j0el6TUBYPolW+oxlI/aCy8Iva73GyouGi92rz21MTcYNqX772EECgYEAuNKB2A/4Lk0Hg9SPm0WnsVJlhhtN6qTlVmQE5u9UTGzcqHXt6YPi5COINZmnCbZ3yDsmk0R5iTObIjT0/ydAy7MitlBuIrYjU8PKiABRcbqmk9xjXSPUsOFxR+9I1rfQIQKMRnPFv0YMMOGwGIOTyFfPa7dL9Uw+9cuVCKACvs0CgYEAuHDdm68kCv49xZi/jmssaFUAALxzmqhwEomrmYxZr4gLmSGyrBDMEOKzTTGjW5/ViK7raqEa3wIn97waoLjP+CfDnHk88m6JBWJn7lK1bDuYvLk70Tq4+LFp7QhRPdrClMA7Byvhv43A1PhxgJauAnu/BrDeDzkKFKxU76eig8kCgYEAt0BhZa1P0fimPtv/F2FVB8g+yV1BQCoHCkVZZvBdkPlPP+jN3/7YdIOWhi63JDY7RdkAQnxeVN9KLfx7/pEY+d+/xyywRtJ47JDwuzA1kKIUj/6wtqTUOh0NiNmESwEt58zy8NfRdfkqFT1wsJ2lZbtK+e5f7fOPaX5VYpvknPECgYAqroO6CVev65Hj7is2C/sk0bbEdNfTzHLS92TsjZwbkMIOV8v/IYv3xF512KzTATPrA3+bF1kejmMtYyxOUTZfWORdi3jdKVMwGcuvTRiKyWfZFIyRKKOxeWzn22rhg4RP5ARE7pS5PVaIck3h0fzGulhEdh2NLEf27MJjC0oCcQKBgDOPxaI45A+aQR4NskZvlnuT86+f8JOI950xsO8ZmFmqFngy518a6TnykLl1WA4E8TS+inlCFEiduj1btsccvCBp9qNVRX6I93mbemQFlbg6oQQdj7GrVJVl/uZLY7WaZsGVfy1xpGXKRQHBstWRfKvO6vD88pz+co5UK9Ab9K+e"], "certificate": ["MIICtTCCAZ0CBgFverl9mDANBgkqhkiG9w0BAQsFADAeMRwwGgYDVQQDDBN0ZXN0LXVzZXItbnVsbC1hdHRyMB4XDTIwMDEwNjExNTc1NFoXDTMwMDEwNjExNTkzNFowHjEcMBoGA1UEAwwTdGVzdC11c2VyLW51bGwtYXR0cjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAIUoyWo5/23DRiuPVvbQhVSBRi/jGLAKLTRhN/PV9MZxrFs0SNPuKThfEfuh5Vz98CjpVHTXJAoLux78yfzfpk5egk1VnZ0CkFYUWNDXKnHCvIjFpEbS97KiSmzR2dIkroOlnHMe9QHEvInhFMu80QeX3cpUUd9DVHg4TCpRQFcsTBn1mMCEq9TKsdnKtQTwcs8otfgAJK5GvXLOfCXXBQ81Bsctsl5sSQeVLqoucIXhTk1WOphnmSANa/AAyK8A95gcmMxlUTSP1AfHTd1+4mYCrLNk7Q4SyklEcKIcESYl7wWYO1acFaM+DNKIrRB372/C0yFgqTeTmNnfPoKEtfUCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAFrTjRzrYbbkC+G7ASxkmHmvM2mdD92bUS1jwTvdJMrF0tFcZTtRD//Wbg+6GvRtkgimI5EM0Q4LflL4BfGKn9bA6wgXOlnXWaQzRnpmfcoa7/BHvOgvZITcuHUNrbgQhuBtNh/jaDrRr8u4fMwqwvGPe5JcesfZkC9siztl7RO8IAHCgai0NUCKcr9/eFNffGSlU23dLrBME7ZVpMBZxPvVH4zMBzK0FLOCRJAG5yInLxzoOBAhgKmsfnLnwSKOS6AudnsIjmulyO/rLEt8FOtKoKx7GQmtzoJX8qqt2gElQojlff3/LJ+oOgYnrdA2pDVmHKSWZ6pf/pEy6S7TiZg=="], "priority": ["100"]}}, {"id": "2b06c73a-fe38-413d-9fee-626980685c3d", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["694a4045-7b63-402b-8a9f-84a0d8c0b6b5"], "secret": ["p_QQsM00mP7tJCEW7rTCew"], "priority": ["100"]}}, {"id": "9010959e-17bf-4bc9-9a16-9e6a7c230e4b", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["9d547d80-029d-44c5-a60b-cf3b4b311375"], "secret": ["4JFv7rRBmdmBofkkILKx69GpWvckjkya2KBf_V8FfuhuvO_BEXSCRHp9n76bFyexJ6DmK6pKdmgZX__oIHEJ7w"], "priority": ["100"], "algorithm": ["HS256"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "4924886a-22ef-4b6d-992e-6d269cb57081", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "d3ea3a35-074b-4827-b3b5-e064ce740df6", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth-otp", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "1dc9bf38-729b-4ee7-9a44-5d9f4cd73f35", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "fac6529b-4550-45ec-9094-099b6e99a64e", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "76ef3b04-bb13-4271-922e-cda0762c0d3b", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "77bfe71a-4f2f-4acd-ad61-e6ca0df64d89", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "Account verification options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "18c36a31-72fc-461f-b0c8-524e5167de17", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "5c613aa7-a5e6-42d6-8ce8-6fd122a15e5e", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "87d8d3b1-016d-49e2-831b-d311a7040bcb", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 20, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "782f6021-4cc7-4704-8a42-91fcbfdf5508", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "f21c1bce-dcb7-4049-bf7d-c9ab144d917a", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-x509", "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "c2ca35fa-6c53-41ce-8000-346300a0b381", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 30, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "cca40f39-c6a8-4c98-b8c7-5b416f05c093", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "10f6ff01-24c3-4aff-9040-fa4e12f79ae9", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "User creation or linking", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "0f6bbf42-7926-4a7c-8167-46331f3cfde4", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 20, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "0dd2f1c3-a3ce-4380-8ef0-4dc7bdb4297e", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "Authentication Options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "5645a178-2e60-42ba-8e55-eb52d0577fa3", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "9c8992a2-422a-4624-bb3b-86af4cc99915", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "d6844f7e-5593-4af4-896a-db5784ca3643", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 40, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "b278f188-d1d5-406f-bb45-0cef0f00c459", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "7ab6e1d9-dda7-4d5b-866b-bb638c965b9b", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "324810cf-c770-4e41-9338-cfae24fff093", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"webAuthnPolicyAuthenticatorAttachment": "not specified", "_browser_header.xRobotsTag": "none", "webAuthnPolicyRpEntityName": "keycloak", "failureFactor": "30", "actionTokenGeneratedByUserLifespan": "300", "maxDeltaTimeSeconds": "43200", "webAuthnPolicySignatureAlgorithms": "ES256", "offlineSessionMaxLifespan": "5184000", "_browser_header.contentSecurityPolicyReportOnly": "", "bruteForceProtected": "false", "_browser_header.contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "_browser_header.xFrameOptions": "SAMEORIGIN", "_browser_header.strictTransportSecurity": "max-age=31536000; includeSubDomains", "webAuthnPolicyUserVerificationRequirement": "not specified", "permanentLockout": "false", "quickLoginCheckMilliSeconds": "1000", "webAuthnPolicyCreateTimeout": "0", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "maxFailureWaitSeconds": "900", "minimumQuickLoginWaitSeconds": "60", "webAuthnPolicyAvoidSameAuthenticatorRegister": "false", "_browser_header.xContentTypeOptions": "nosniff", "actionTokenGeneratedByAdminLifespan": "43200", "waitIncrementSeconds": "60", "offlineSessionMaxLifespanEnabled": "false"}, "keycloakVersion": "9.0.0-SNAPSHOT", "userManagedAccessAllowed": false}