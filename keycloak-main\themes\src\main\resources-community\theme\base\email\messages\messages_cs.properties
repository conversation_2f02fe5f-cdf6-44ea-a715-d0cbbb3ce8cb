emailVerificationSubject=Ověření e-mailu
emailVerificationBody=Někdo vytvořil účet {2} s touto e-mailovou adresou. Pokud jste to vy, klikněte na níže uvedený odkaz a ověřte svou e-mailovou adresu \n\n{0}\n\nTento odkaz vyprší za {3}.\n\nPokud jste tento účet nevytvořili, tuto zprávu ignorujte.
emailVerificationBodyHtml=<p>Někdo vytvořil účet {2} s touto e-mailovou adresou. Pokud jste to vy, klikněte na níže uvedený odkaz a ověřte svou e-mailovou adresu. </p><p><a href="{0}">Odkaz na ověření e-mailové adresy</a></p><p>Platnost odkazu vyprší za {3}.</p><p>Pokud jste tento účet nevytvořili, tuto zprávu ignorujte.</p>
orgInviteSubject=Pozvánka do organizace {0}
orgInviteBody=Někdo Vás pozval do organizace "{3}". Pro přidání se klikněte na odkaz níže.\n\n{0}\n\nTento odkaz vyprší za {4}.\n\nPokud se do organizace nechcete přidat, ignorujte tuto zprávu.
orgInviteBodyHtml=<p>Někdo Vás pozval do organizace {3}. Pro přidání se klikněte na odkaz níže. </p><p><a href="{0}">Odkaz pro přidání se do organizace</a></p><p>Tento odkaz vyprší za {4}.</p><p>Pokud se do organizace nechcete přidat, ignorujte tuto zprávu.</p>
orgInviteBodyPersonalized=Dobrý den, "{5}" "{6}".\n\n Někdo Vás pozval do organizace "{3}". Pro přidání se klikněte na odkaz níže.\n\n{0}\n\nTento odkaz vyprší za {4}.\n\nPokud se do organizace nechcete přidat, ignorujte tuto zprávu.
orgInviteBodyPersonalizedHtml=<p>Dobrý den, {5} {6}.</p><p>Někdo Vás pozval do organizace {3}. Pro přidání se klikněte na odkaz níže. </p><p><a href="{0}">Odkaz pro přidání se do organizace</a></p><p>Tento odkaz vyprší za {4}.</p><p>Pokud se do organizace nechcete přidat, ignorujte tuto zprávu.</p>
emailUpdateConfirmationSubject=Ověření nového e-mailu
emailUpdateConfirmationBody=Pro aktualizaci e-mailové adresy {1} u Vašeho účtu {2} klikněte na odkaz níže\n\n{0}\n\nTento odkaz expiruje za {3}.\n\nPokud tuto změnu nechcete provést, ignorujte tuto zprávu.
emailUpdateConfirmationBodyHtml=<p>Pro aktualizaci e-mailové adresy {1} u Vašeho účtu {2} klikněte na odkaz níže</p><p><a href="{0}">{0}</a></p><p>Tento odkaz expiruje za {3}.</p><p>Pokud tuto změnu nechcete provést, ignorujte tuto zprávu.</p>
emailTestSubject=[KEYCLOAK] - testovací zpráva
emailTestBody=Toto je testovací zpráva
emailTestBodyHtml=<p>Toto je testovací zpráva </p>
identityProviderLinkSubject=Odkaz {0}
identityProviderLinkBody=Někdo chce propojit Váš účet "{1}" s účtem "{0}" uživatele {2}. Pokud jste to vy, klikněte na níže uvedený odkaz a propojte účty. \n\n{3}\n\nPlatnost tohoto odkazu je {5}.\n\nPokud nechcete propojit účet, tuto zprávu ignorujte. Pokud propojíte účty, budete se moci přihlásit jako {1} pomocí {0}.
identityProviderLinkBodyHtml=<p>Někdo chce propojit Váš účet <b>{1}</b> s účtem <b>{0}</b> uživatele {2}. Pokud jste to vy, klikněte na níže uvedený odkaz a propojte účty.</p><p><a href="{3}">Odkaz na propojení účtů.</a></p><p> Platnost tohoto odkazu je {5}. </p><p> Pokud nechcete propojit účet, tuto zprávu ignorujte. Pokud propojíte účty, budete se moci přihlásit jako {1} pomocí {0}.</p>
passwordResetSubject=Zapomenuté heslo
passwordResetBody=Někdo právě požádal o změnu hesla u Vašeho účtu {2}. Pokud jste to vy, pro jeho změnu klikněte na odkaz níže.\n\n{0}\n\nPlatnost tohoto odkazu je {3}.\n\nPokud heslo změnit nechcete, tuto zprávu ignorujte a nic se nezmění.
passwordResetBodyHtml=<p>Někdo právě požádal o změnu pověření Vašeho účtu {2}. Pokud jste to vy, klikněte na odkaz níže, abyste je resetovali.</p><p><a href="{0}">Odkaz na obnovení pověření </a></p><p> Platnost tohoto odkazu vyprší během {3}.</p><p> Pokud nechcete obnovit Vaše pověření, ignorujte tuto zprávu a nic se nezmění.</p>
executeActionsSubject=Aktualizujte svůj účet
executeActionsBody=Váš administrátor Vás požádal o provedení následujících akcí u účtu {2}: {3}. Začněte kliknutím na níže uvedený odkaz.\n\n{0}\n\nPlatnost tohoto odkazu je {4}.\n\nPokud si nejste jisti, zda je tento požadavek v pořádku, ignorujte tuto zprávu.
executeActionsBodyHtml=<p>Váš administrátor Vás požádal o provedení následujících akcí u účtu {2}: {3}. Začněte kliknutím na níže uvedený odkaz.</p><p><a href="{0}">Odkaz na aktualizaci účtu.</a></p><p>Platnost tohoto odkazu je {4}.</p><p>Pokud si nejste jisti, zda je tento požadavek v pořádku, ignorujte tuto zprávu.</p>
eventLoginErrorSubject=Chyba přihlášení
eventLoginErrorBody=Někdo se neúspěšně pokusil přihlásit k účtu {0} z {1}. Pokud jste to nebyli vy, kontaktujte administrátora.
eventLoginErrorBodyHtml=<p>Někdo se neúspěšně pokusil přihlásit k účtu {0} z {1}. Pokud jste to nebyli vy, kontaktujte administrátora.</p>
eventRemoveTotpSubject=Odebrat TOTP
eventRemoveTotpBody=V účtu {0} bylo odebráno nastavení OTP z {1}. Pokud jste to nebyli vy, kontaktujte administrátora.
eventRemoveTotpBodyHtml=<p>V účtu {0} bylo odebráno nastavení OTP z {1}. Pokud jste to nebyli vy, kontaktujte administrátora.</p>
eventUpdatePasswordSubject=Aktualizace hesla
eventUpdatePasswordBody=V účtu {0} bylo změněno heslo z {1}. Pokud jste to nebyli vy, kontaktujte administrátora.
eventUpdatePasswordBodyHtml=<p>V účtu {0} bylo změněno heslo z {1}. Pokud jste to nebyli vy, kontaktujte administrátora.</p>
eventUpdateTotpSubject=Aktualizace OTP
eventUpdateTotpBody=V účtu {0} bylo změněno nastavení OTP z {1}. Pokud jste to nebyli vy, kontaktujte administrátora.
eventUpdateTotpBodyHtml=<p>V účtu {0} bylo změněno nastavení OTP z {1}. Pokud jste to nebyli vy, kontaktujte administrátora.</p>
eventUpdateCredentialSubject=Aktualizace přihlašovacího údaje
eventUpdateCredentialBody=Váš přihlašovací údaj {0} se změnil na {1} z {2}. Pokud jste to nebyli vy, kontaktujte administrátora.
eventUpdateCredentialBodyHtml=<p>Váš přihlašovací údaj {0} se změnil na {1} z {2}. Pokud jste to nebyli vy, kontaktujte administrátora.</p>
eventRemoveCredentialSubject=Odstranění přihlašovacího údaje
eventRemoveCredentialBody=Přihlašovací údaj {0} byl odstraněn z Vašeho účtu na {1} z {2}. Pokud jste to nebyli vy, kontaktujte administrátora.
eventRemoveCredentialBodyHtml=<p>Přihlašovací údaj {0} byl odstraněn z Vašeho účtu na {1} z {2}. Pokud jste to nebyli vy, kontaktujte administrátora.</p>
eventUserDisabledByTemporaryLockoutSubject=Uživatel dočasně zablokován
eventUserDisabledByTemporaryLockoutBody=Váš uživatel byl dočasně zablokován kvůli několika špatným pokusům na {0}. Kontaktujte prosím administrátora, pokud je to potřeba.
eventUserDisabledByTemporaryLockoutHtml=<p>Váš uživatel byl dočasně zablokován kvůli několika špatným pokusům na {0}. Kontaktujte prosím administrátora, pokud je to potřeba.</p>
eventUserDisabledByPermanentLockoutSubject=Uživatel trvale zablokován
eventUserDisabledByPermanentLockoutBody=Váš uživatel byl trvale zablokován kvůli několika špatným pokusům na {0}. Kontaktujte prosím administrátora, pokud je to potřeba.
eventUserDisabledByPermanentLockoutHtml=<p>Váš uživatel byl trvale zablokován kvůli několika špatným pokusům na {0}. Kontaktujte prosím administrátora, pokud je to potřeba.</p>

requiredAction.CONFIGURE_TOTP=Konfigurace OTP
requiredAction.TERMS_AND_CONDITIONS=Smluvní podmínky
requiredAction.UPDATE_PASSWORD=Aktualizace hesla
requiredAction.UPDATE_PROFILE=Aktualizace profilu
requiredAction.VERIFY_EMAIL=Ověření e-mailu
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Generování kódů pro obnovu

# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be '{0,choice,0#minut|1#minuta|2#minuty|2<minut}'
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#sekund|1#sekunda|2#sekundy|4<sekund}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#minut|1#minuta|2#minuty|4<minut}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#hodin|1#hodina|2#hodiny|4<hodin}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#dní|1#den|2#dny|4<dní}

emailVerificationBodyCode=Ověřte prosím svou e-mailovou adresu zadáním následujícího kódu.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Ověřte prosím svou e-mailovou adresu zadáním následujícího kódu.</p><p><b>{0}</b></p>
