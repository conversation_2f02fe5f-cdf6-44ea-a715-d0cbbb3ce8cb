[libdefaults]
    default_realm = KEYCLOAK.ORG
    default_tgs_enctypes = des3-cbc-sha1-kd aes256-cts-hmac-sha1-96 rc4-hmac aes128-cts-hmac-sha1-96
    default_tkt_enctypes = des3-cbc-sha1-kd aes256-cts-hmac-sha1-96 rc4-hmac aes128-cts-hmac-sha1-96
    permitted_enctypes = des3-cbc-sha1-kd aes256-cts-hmac-sha1-96 rc4-hmac aes128-cts-hmac-sha1-96
    kdc_timeout = 30000
    dns_lookup_realm = false
    dns_lookup_kdc = false
    dns_canonicalize_hostname = false
    ignore_acceptor_hostname = true
    forwardable = true

[realms]
    KEYCLOAK.ORG = {
        kdc = localhost:6088
    }
    KC2.COM = {
        kdc = localhost:7088
    }

[domain_realm]
    localhost = KEYCLOAK.ORG