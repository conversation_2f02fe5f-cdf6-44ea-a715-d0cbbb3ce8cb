doLogIn=Iniciar sesión
doRegister=Registrarse
doRegisterSecurityKey=Registrarse
doCancel=Cancelar
doSubmit=Enviar
doYes=Sí
doNo=No
doContinue=Continuar
doAccept=Aceptar
doDecline=Declinar
doForgotPassword=¿Has olvidado tu contraseña?
doClickHere=Haz clic aquí
doImpersonate=Suplantar
kerberosNotConfigured=Kerberos no configurado
kerberosNotConfiguredTitle=Kerberos no configurado
bypassKerberosDetail=O bien no está identificado mediante Kerberos o su navegador no está configurado para identificarse mediante Kerberos. Por favor haga clic para identificarse por otro medio
kerberosNotSetUp=Kerberos no está configurado. No puede identificarse.
loginAccountTitle=Acceder a tu cuenta
loginTitle=Inicia sesión en {0}
loginTitleHtml={0}
impersonateTitle={0} Suplantar Usuario
impersonateTitleHtml=<strong>{0}</strong> Suplantar Usuario
realmChoice=Realm
unknownUser=Usuario desconocido
loginTotpTitle=Configura tu aplicación de identificación móvil
loginProfileTitle=Actualiza la información de tu cuenta
loginTimeout=Ha tardado demasiado en identificarse. Inicie de nuevo la identificación.
oauthGrantTitle=Conceder acceso a {0}
oauthGrantTitleHtml={0}
errorTitle=Lo sentimos...
errorTitleHtml=Lo <strong>sentimos</strong>...
emailVerifyTitle=Verificación del email
emailForgotTitle=¿Ha olvidado su contraseña?
updatePasswordTitle=Modificar contraseña
codeSuccessTitle=Código de éxito
codeErrorTitle=Código de error: {0}
termsTitle=Términos y Condiciones
termsText=<p>Términos y condiciones a definir</p>
recaptchaFailed=Reconocimiento de texto inválido
recaptchaNotConfigured=El reconocimiento de texto es obligatorio pero no está configurado
consentDenied=Consentimiento rechazado.
noAccount=¿Usuario nuevo?
username=Usuario
usernameOrEmail=Usuario o email
firstName=Nombre
givenName=Nombre de pila
fullName=Nombre completo
lastName=Apellido
familyName=Apellido
email=Email
password=Contraseña
passwordConfirm=Confirme la contraseña
passwordNew=Nueva contraseña
passwordNewConfirm=Confirma la nueva contraseña
rememberMe=Seguir conectado
authenticatorCode=Código de un solo uso
address=Dirección
street=Calle
locality=Ciudad o Localidad
region=Estado, Provincia, o Región
postal_code=Código Postal
country=País
emailVerified=Email verificado
gssDelegationCredential=Credencial de delegación GSS
loginTotpIntro=Es necesario configurar un generador de claves de un sólo uso para acceder a esta cuenta
loginTotpStep1=Instala una de las siguientes aplicaciones en tu teléfono móvil:
loginTotpStep2=Abre la aplicación y escanea el código de barras:
loginTotpStep3=Introduzca el código único que le muestra la aplicación de autenticación y haga clic en Enviar para finalizar la configuración.
loginTotpStep3DeviceName=Introduce un nombre de dispositivo para que te ayude a gestionar tus dispositivos OTP.
loginTotpManualStep2=Abre la aplicación e introduce la clave:
loginTotpManualStep3=Usa los siguientes parámetros de configuración si la aplicación te permite introducirlos:
loginTotpUnableToScan=¿No consigues escanear?
loginTotpScanBarcode=¿Escanear el código de barras?
loginCredential=Credenciales
loginOtpOneTime=Código de un solo uso
loginTotpType=Tipo
loginTotpAlgorithm=Algoritmo
loginTotpDigits=Dígitos
loginTotpInterval=Intervalo
loginTotpCounter=Contador
loginTotpDeviceName=Nombre del dispositivo
oauthGrantRequest=¿Quiere permitir estos privilegios de acceso?
inResource=en
emailVerifyInstruction1=Se ha enviado un correo electrónico con instrucciones para verificar tu dirección de correo a {0}.
emailVerifyInstruction2=¿No ha recibido un código de verificación en su email?
emailVerifyInstruction3=para reenviar el email.
backToLogin=Volver a la identificación
emailInstruction=Indique su usuario o email y le enviaremos instrucciones indicando cómo generar una nueva contraseña.
copyCodeInstruction=Por favor, copie y pegue este código en su aplicación:
personalInfo=Información personal:
role_admin=Administrador
role_realm-admin=Administrador del realm
role_create-realm=Crear realm
role_create-client=Crear cliente
role_view-realm=Ver realm
role_view-users=Ver usuarios
role_view-applications=Ver aplicaciones
role_view-clients=Ver clientes
role_view-events=Ver eventos
role_view-identity-providers=Ver proveedores de identidad
role_manage-realm=Gestionar realm
role_manage-users=Gestionar usuarios
role_manage-applications=Gestionar aplicaciones
role_manage-identity-providers=Gestionar proveedores de identidad
role_manage-clients=Gestionar clientes
role_manage-events=Gestionar eventos
role_view-profile=Ver perfil
role_manage-account=Gestionar cuenta
role_read-token=Leer token
role_offline-access=Acceso sin conexión
client_account=Cuenta
client_security-admin-console=Consola de Administración de Seguridad
client_realm-management=Gestión del realm
client_broker=Broker
invalidUserMessage=Usuario o contraseña incorrectos.
invalidEmailMessage=Email no válido.
accountDisabledMessage=La cuenta está desactivada, contacte con el administrador.
accountTemporarilyDisabledMessage=Usuario o contraseña incorrectos.
accountPermanentlyDisabledMessage=Usuario o contraseña incorrectos.
accountTemporarilyDisabledMessageTotp=El código de autenticación no es válido.
accountPermanentlyDisabledMessageTotp=El código de autenticación no es válido.
expiredCodeMessage=Se agotó el tiempo máximo para la identificación. Por favor identificate de nuevo.
expiredActionMessage=Acción caducada. Continúe con el inicio de sesión ahora.
sessionLimitExceeded=Hay demasiadas sesiones
missingFirstNameMessage=Por favor indique su nombre.
missingLastNameMessage=Por favor indique su apellido.
missingEmailMessage=Por favor indique su email.
missingUsernameMessage=Por favor indique su usuario.
missingPasswordMessage=Por favor indique su contraseña.
missingTotpMessage=Por favor indique su código de autenticación.
notMatchPasswordMessage=Las contraseñas no coinciden.
invalidPasswordExistingMessage=La contraseña actual no es correcta.
invalidPasswordBlacklistedMessage=Contraseña no válida: la contraseña está en la lista negra.
invalidPasswordConfirmMessage=La confirmación de contraseña no coincide.
invalidTotpMessage=El código de autenticación no es válido.
usernameExistsMessage=El nombre de usuario ya existe.
emailExistsMessage=El email ya existe.
federatedIdentityExistsMessage=El usuario con {0} {1} ya existe. Inicie sesión en la administración de cuentas para vincular la cuenta.
federatedIdentityUnavailableMessage=El usuario {0} autenticado con el proveedor de identidad {1} no existe. Póngase en contacto con su administrador.
configureTotpMessage=Tiene que configurar la aplicación móvil de identificación para activar su cuenta.
configureBackupCodesMessage=Tiene que configurar códigos de respaldo para activar su cuenta.
updateProfileMessage=Tiene que actualizar su perfil de usuario para activar su cuenta.
updatePasswordMessage=Tiene que cambiar su contraseña para activar su cuenta.
updateEmailMessage=Tiene que actualizar su dirección de correo electrónico para activar su cuenta.
resetPasswordMessage=Tiene que cambiar su contraseña.
verifyEmailMessage=Tiene que verificar su email para activar su cuenta.
linkIdpMessage=Debe verificar su dirección de correo electrónico para vincular su cuenta con {0}.
emailSentMessage=En breve deberías recibir un mensaje con más instrucciones.
emailSendErrorMessage=Falló el envío del email, por favor inténtalo de nuevo más tarde.
accountUpdatedMessage=Tu cuenta se ha actualizado.
accountPasswordUpdatedMessage=Tu contraseña se ha actualizado.
noAccessMessage=Sin acceso
invalidPasswordMinLengthMessage=Contraseña incorrecta: longitud mínima {0}.
invalidPasswordMinDigitsMessage=Contraseña incorrecta: debe contener al menos {0} caracteres numéricos.
invalidPasswordMinLowerCaseCharsMessage=Contraseña incorrecta: debe contener al menos {0} letras minúsculas.
invalidPasswordMinUpperCaseCharsMessage=Contraseña incorrecta: debe contener al menos {0} letras mayúsculas.
invalidPasswordMinSpecialCharsMessage=Contraseña incorrecta: debe contener al menos {0} caracteres especiales.
invalidPasswordNotUsernameMessage=Contraseña incorrecta: no puede ser igual al nombre de usuario.
invalidPasswordRegexPatternMessage=Contraseña incorrecta: no cumple la expresión regular.
invalidPasswordHistoryMessage=Contraseña incorrecta: no puede ser igual a ninguna de las últimas {0} contraseñas.
failedToProcessResponseMessage=Fallo al procesar la respuesta
httpsRequiredMessage=HTTPS obligatorio
realmNotEnabledMessage=El realm no está activado
invalidRequestMessage=Petición incorrecta
failedLogout=Falló la desconexión
unknownLoginRequesterMessage=Solicitante de identificación desconocido
loginRequesterNotEnabledMessage=El solicitante de inicio de sesión está desactivado
bearerOnlyMessage=Las aplicaciones Bearer-only no pueden iniciar sesión desde el navegador
invalidRedirectUriMessage=La URI de redirección no es correcta
unsupportedNameIdFormatMessage=NameIDFormat no soportado
invalidRequesterMessage=Solicitante no válido
registrationNotAllowedMessage=El registro no está permitido
resetCredentialNotAllowedMessage=El reinicio de las credenciales no está permitido
permissionNotApprovedMessage=Permiso no aprobado.
noRelayStateInResponseMessage=Sin estado de retransmisión en la respuesta del proveedor de identidad.
identityProviderAlreadyLinkedMessage=La identidad federada devuelta por {0} ya está vinculada a otro usuario.
insufficientPermissionMessage=Permisos insuficientes para enlazar identidades.
couldNotProceedWithAuthenticationRequestMessage=No se pudo continuar con la petición de autenticación al proveedor de identidad.
couldNotObtainTokenMessage=No se pudo obtener el código del proveedor de identidad.
unexpectedErrorRetrievingTokenMessage=Error inesperado obteniendo el token del proveedor de identidad.
unexpectedErrorHandlingResponseMessage=Error inesperado procesando la respuesta del proveedor de identidad.
identityProviderAuthenticationFailedMessage=Falló la autenticación. No fue posible autenticarse en el proveedor de identidad.
couldNotSendAuthenticationRequestMessage=No se pudo enviar la petición de identificación al proveedor de identidad.
unexpectedErrorHandlingRequestMessage=Error inesperado durante la petición de identificación al proveedor de identidad.
invalidAccessCodeMessage=Código de acceso no válido.
sessionNotActiveMessage=La sesión no está activa.
invalidCodeMessage=Ha ocurrido un error, por favor identificate de nuevo desde tu aplicación.
identityProviderUnexpectedErrorMessage=Error no esperado intentado autenticar en el proveedor de identidad
identityProviderNotFoundMessage=No se pudo encontrar un proveedor de identidad con el identificador.
realmSupportsNoCredentialsMessage=El realm no soporta ningún tipo de credenciales.
identityProviderNotUniqueMessage=El realm soporta múltiples proveedores de identidad. No se pudo determinar el proveedor de identidad que debería ser utilizado para identificarse.
emailVerifiedMessage=Tu email ha sido verificado.
staleEmailVerificationLink=El enlace en el que hizo clic es un enlace obsoleto antiguo y ya no es válido. Tal vez ya haya verificado su correo electrónico.
identityProviderInvalidResponseMessage=Respuesta no válida del proveedor de identidad.
confirmAccountLinking=Confirme la vinculación de la cuenta {0} del proveedor de identidad {1} con su cuenta.
identityProviderMissingStateMessage=Falta el parámetro de estado en respuesta del proveedor de identidad.
confirmEmailAddressVerification=Confirme la validez de la dirección de correo electrónico {0}.
confirmExecutionOfActions=Realice las siguientes acciones
backToApplication=Volver a la aplicación
missingParameterMessage=Parámetros que faltan: {0}
clientNotFoundMessage=Cliente no encontrado.
invalidParameterMessage=Parámetro no válido: {0}
alreadyLoggedIn=Ya se ha autentificado.
proceedWithAction=» Haz click aquí para proceder
requiredAction.CONFIGURE_TOTP=Configurar OTP
requiredAction.TERMS_AND_CONDITIONS=Términos y condiciones
requiredAction.UPDATE_PASSWORD=Actualización de contraseña
requiredAction.UPDATE_PROFILE=Actualización del perfil
requiredAction.VERIFY_EMAIL=Verificar correo electrónico
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Generar códigos de recuperación
requiredAction.webauthn-register-passwordless=Webauthn Registro sin contraseña
pageNotFound=Página no encontrada
internalServerError=Se ha producido un error interno del servidor

# Identity provider
identity-provider-redirector=Inicie sesión con otro proveedor de identidad
identity-provider-login-label=O inicie sesión con
idp-email-verification-display-name=Verificacion de email
idp-email-verification-help-text=Vincule su cuenta validando su correo electrónico.
idp-username-password-form-display-name=Usuario y contraseña
idp-username-password-form-help-text=Vincule su cuenta iniciando sesión.
access-denied=Acceso denegado
logoutConfirmTitle=Cerrando sesión
logoutConfirmHeader=¿Quiere cerrar sesión?
doLogout=Cerrar sesión
doBack=Atrás
doIgnore=Ignorar
doTryAgain=Intentar otra vez
doTryAnotherWay=Pruebe de otra manera
doConfirmDelete=Confirmar la eliminación
errorDeletingAccount=Ocurrió un error al intentar eliminar la cuenta
deletingAccountForbidden=No tiene suficientes permisos para eliminar su propia cuenta, comuníquese con un administrador.
registerTitle=Registro
loginIdpReviewProfileTitle=Actualizar información de la cuenta
reauthenticate=Vuelva a autenticarse para continuar
oauthGrantInformation=Asegúrese que confía en {0} aprendiendo cómo {0} manejará sus datos.
oauthGrantReview=Podría revisar el
oauthGrantTos=términos de servicio.
oauthGrantPolicy=política de privacidad.
updateEmailTitle=Actualizar correo electrónico
emailUpdateConfirmationSentTitle=Correo de confirmación enviado
emailUpdateConfirmationSent=Se ha enviado un correo electrónico de confirmación a {0}. Debe seguir las instrucciones de este para completar la actualización del correo electrónico.
emailUpdatedTitle=Correo electrónico actualizado
emailUpdated=El correo electrónico de la cuenta se ha actualizado correctamente a {0}.
displayUnsupported=Tipo de visualización solicitado no soportado
browserRequired=Se requiere un navegador para iniciar sesión
browserContinue=Se requiere un navegador para completar el inicio de sesión
browserContinuePrompt=¿Abrir el navegador y continuar iniciando sesión?[s/n]:
browserContinueAnswer=s

# Transports
usb=USB
nfc=NFC
bluetooth=Bluetooth
internal=Interna
unknown=Desconocida
termsPlainText=Términos y condiciones a definir.
termsAcceptanceRequired=Debe aceptar nuestros términos y condiciones.
acceptTerms=Estoy de acuerdo con los términos y condiciones


hidePassword=Ocultar contraseña
showPassword=Mostrar contraseña
website=Página web
phoneNumber=Número de teléfono
phoneNumberVerified=Número de teléfono verificado
gender=Género
birthday=Fecha de nacimiento
zoneinfo=Zona horaria
logoutOtherSessions=Cerrar sesión en otros dispositivos
profileScopeConsentText=Perfil del usuario
emailScopeConsentText=Dirección de correo electrónico
addressScopeConsentText=Dirección
phoneScopeConsentText=Número de teléfono
offlineAccessScopeConsentText=Acceso fuera de línea
samlRoleListScopeConsentText=Mis roles
rolesScopeConsentText=Roles del usuario
restartLoginTooltip=Reiniciar el inicio de sesión
loginTotp.totp=Basado en el tiempo
loginTotp.hotp=Basado en contador
totpAppFreeOTPName=FreeOtp
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator
loginChooseAuthenticator=Seleccione el método de inicio de sesión


oauth2DeviceVerificationTitle=Inicio de sesión del dispositivo
verifyOAuth2DeviceUserCode=Ingrese el código proporcionado por su dispositivo y haga clic en Enviar
oauth2DeviceInvalidUserCodeMessage=Código no válido, por favor vuelva a intentarlo.
oauth2DeviceExpiredUserCodeMessage=El código ha expirado. Vuelva a su dispositivo e intente conectarse nuevamente.
oauth2DeviceVerificationCompleteHeader=Inicio de sesión del dispositivo exitoso
oauth2DeviceVerificationCompleteMessage=Puede cerrar esta ventana del navegador y volver a su dispositivo.
oauth2DeviceVerificationFailedHeader=Falló el inicio de sesión del dispositivo
oauth2DeviceVerificationFailedMessage=Puede cerrar esta ventana del navegador y volver a su dispositivo e intentar conectarse nuevamente.
oauth2DeviceConsentDeniedMessage=Consentimiento denegado por conectar el dispositivo.
oauth2DeviceAuthorizationGrantDisabledMessage=El cliente no puede iniciar la petición de autorización del dispositivo OAuth 2.0. El flujo está deshabilitado para el cliente.
emailLinkIdpTitle=Enlace {0}
emailLinkIdp1=Se le ha enviado un correo electrónico con instrucciones para vincular {0} cuenta {1} con su cuenta {2}.
emailLinkIdp2=¿No ha recibido un código de verificación en su correo electrónico?
emailLinkIdp3=para volver a enviar el correo electrónico.
emailLinkIdp4=Si ya verificó el correo electrónico en un navegador diferente
emailLinkIdp5=continuar.
emailInstructionUsername=Ingrese su nombre de usuario y le enviaremos instrucciones sobre cómo crear una nueva contraseña.
pageExpiredTitle=La página ha expirado
pageExpiredMsg1=Para reiniciar el proceso de inicio de sesión
pageExpiredMsg2=Para continuar el proceso de inicio de sesión
role_manage-account-links=Administrar enlaces de cuenta
client_account-console=Consola de cuentas
client_admin-cli=Administrador cli
requiredFields=Campos requeridos
invalidUsernameMessage=Nombre de usuario no válido.
invalidUsernameOrEmailMessage=Nombre de usuario o correo electrónico no válidos.
invalidPasswordMessage=Contraseña inválida.
expiredActionTokenNoSessionMessage=Acción expirada.
expiredActionTokenSessionExistsMessage=Acción expirada. Comience de nuevo.
missingTotpDeviceNameMessage=Especifique el nombre del dispositivo.
error-invalid-value=Valor no válido.
error-invalid-blank=Especifique el valor.
error-empty=Especifique el valor.
error-invalid-length=La longitud debe estar entre {1} y {2}.
error-invalid-length-too-short=La longitud mínima es {1}.
error-invalid-length-too-long=La longitud máxima es {2}.
error-invalid-email=Dirección de correo electrónico no válida.
error-invalid-number=Número invalido.
error-number-out-of-range=El número debe estar entre {1} y {2}.
error-number-out-of-range-too-small=El número debe tener un valor mínimo de {1}.
error-number-out-of-range-too-big=El número debe tener un valor máximo de {2}.
error-pattern-no-match=Valor no válido.
error-invalid-uri=URL invalida.
error-invalid-uri-scheme=Esquema de URL no válido.
error-invalid-uri-fragment=Fragmento de URL no válido.
error-user-attribute-required=Especifique este campo.
error-invalid-date=Fecha inválida.
error-user-attribute-read-only=Este campo es de solo lectura.
error-username-invalid-character=El valor contiene carácter inválido.
error-person-name-invalid-character=El valor contiene carácter inválido.
error-reset-otp-missing-id=Elija una configuración OTP.
federatedIdentityUnmatchedEssentialClaimMessage=El token de identificación emitido por el proveedor de identidad no coincide con el reclamo esencial configurado. Póngase en contacto con su administrador.
confirmLinkIdpTitle=La cuenta ya existe
federatedIdentityConfirmLinkMessage=El usuario con {0} {1} ya existe. ¿Cómo quiere continuar?
federatedIdentityConfirmReauthenticateMessage=Autenticar para vincular su cuenta con {0}
nestedFirstBrokerFlowMessage=El usuario {0} {1} no está vinculado a ningún usuario conocido.
confirmLinkIdpReviewProfile=Perfil de revisión
confirmLinkIdpContinue=Agregar a la cuenta existente
delegationCompleteHeader=Inicio de sesión correcto
delegationCompleteMessage=Puede cerrar esta ventana del navegador y volver a la aplicación de su consola.
delegationFailedHeader=Error de inicio de sesión
delegationFailedMessage=Puede cerrar esta ventana del navegador, volver a la aplicación de su consola e intentar iniciar sesión nuevamente.
invalidPasswordMaxLengthMessage=Contraseña no válida: longitud máxima {0}.
invalidPasswordNotEmailMessage=Contraseña no válida: no debe ser igual al correo electrónico.
invalidPasswordGenericMessage=Contraseña no válida: la nueva contraseña no coincide con las políticas de contraseña.
successLogout=Está desconectado
standardFlowDisabledMessage=El cliente no puede iniciar el inicio de sesión del navegador con el tipo de respuesta dado. El flujo estándar está deshabilitado para el cliente.
implicitFlowDisabledMessage=El cliente no puede iniciar el inicio de sesión del navegador con el tipo de respuesta dado. El flujo implícito está deshabilitado para el cliente.
cookieNotFoundMessage=No se encontró la cookie de reinicio de inicio de sesión. Puede haber expirado, haber sido eliminada o las cookies pueden estar deshabilitadas en su navegador. Si las cookies están deshabilitadas, habilítelas. Haga clic en Volver a la aplicación para iniciar sesión nuevamente.
insufficientLevelOfAuthentication=El nivel de autenticación solicitado no se ha cumplido.
identityProviderMissingCodeOrErrorMessage=Código faltante o parámetro de error en respuesta del proveedor de identidad.
identityProviderInvalidSignatureMessage=Firma no válida en respuesta del proveedor de identidad.
identityProviderLinkSuccess=Verificó con éxito su correo electrónico. Vuelva a su navegador original y continúe allí con el inicio de sesión.
staleCodeMessage=Esta página ya no es válida, vuelva a su solicitud e inicie sesión nuevamente
credentialSetupRequired=No se puede iniciar sesión, se requiere configuración de credencial.
clientDisabledMessage=Cliente deshabilitado.
differentUserAuthenticated=Ya está autenticado como un usuario diferente ''{0}'' en esta sesión. Por favor cierre sesión primero.
brokerLinkingSessionExpired=Vinculación de la cuenta del corredor solicitada, pero la sesión actual ya no es válida.
acrNotFulfilled=Requisitos de autenticación no cumplidos
invalidTokenRequiredActions=Las acciones requeridas incluidas en el enlace no son válidas
doX509Login=Se registrará como:
clientCertificate=Certificado de cliente X509:
noCertificate=[Sin certificado]


console-username=Nombre de usuario:
console-password=Contraseña:
console-otp=Contraseña de una sola vez:
console-new-password=Nueva contraseña:
console-confirm-password=Confirmar Contraseña:
console-update-password=Se requiere la actualización de su contraseña.
console-verify-email=Debe verificar su dirección de correo electrónico. Enviamos un correo electrónico a {0} que contiene un código de verificación. Ingrese este código en la entrada a continuación.
console-email-code=Código de correo electrónico:
console-accept-terms=¿Aceptar los terminos?[y/n]:
console-accept=Y

# Openshift messages
openshift.scope.user_info=Informacion del usuario
openshift.scope.user_check-access=Información de acceso de usuario
openshift.scope.user_full=Acceso completo
openshift.scope.list-projects=Listar proyectos

# SAML authentication
saml.post-form.title=Redirección de autenticación
saml.post-form.message=Redireccionando, por favor espere.
saml.post-form.js-disabled=JavaScript está deshabilitado. Recomendamos encarecidamente habilitarlo. Haga clic en el botón de abajo para continuar.
saml.artifactResolutionServiceInvalidResponse=Incapaz de resolver artefactos.

#authenticators
otp-display-name=Aplicación de autenticador
otp-help-text=Ingrese un código de verificación de la aplicación Authenticator.
otp-reset-description=¿Qué configuración de OTP debe eliminarse?
password-display-name=Contraseña
password-help-text=Inicie sesión ingresando su contraseña.
auth-username-form-display-name=Nombre de usuario
auth-username-form-help-text=Comience el inicio de sesión ingresando su nombre de usuario
auth-username-password-form-display-name=Nombre de usuario y contraseña
auth-username-password-form-help-text=Inicie sesión ingresando su nombre de usuario y contraseña.

# Recovery Codes
auth-recovery-authn-code-form-display-name=Código de autenticación de recuperación
auth-recovery-authn-code-form-help-text=Ingrese un código de autenticación de recuperación desde una lista generada anteriormente.
auth-recovery-code-info-message=Ingrese el código de recuperación especificado.
auth-recovery-code-prompt=Código de recuperación #{0}
auth-recovery-code-header=Iniciar sesión con un código de autenticación de recuperación
recovery-codes-error-invalid=Código de autenticación de recuperación no válida
recovery-code-config-header=Códigos de autenticación de recuperación
recovery-code-config-warning-title=Estos códigos de recuperación no volverán a aparecer después de salir de esta página
recovery-code-config-warning-message=Asegúrese de imprimir, descargarlos o copiarlos a un administrador de contraseñas y mantenerlos seguros. Cancelar esta configuración eliminará estos códigos de recuperación de su cuenta.
recovery-codes-print=Imprimir
recovery-codes-download=Descargar
recovery-codes-copy=Copiar
recovery-codes-copied=Copiada
recovery-codes-confirmation-message=He guardado estos códigos en algún lugar seguro
recovery-codes-action-complete=Configuración completa
recovery-codes-action-cancel=Cancelar configuración
recovery-codes-download-file-header=Mantenga estos códigos de recuperación en algún lugar seguros.
recovery-codes-download-file-description=Los códigos de recuperación son códigos de acceso de un solo uso que le permiten iniciar sesión en su cuenta si no tiene acceso a su autenticador.
recovery-codes-download-file-date=Estos códigos se generaron en
recovery-codes-label-default=Códigos de recuperación

# WebAuthn
webauthn-display-name=Passkey
webauthn-help-text=Use su Passkey para iniciar sesión.
webauthn-passwordless-display-name=Passkey
webauthn-passwordless-help-text=Use su Passkey para iniciar sesión sin contraseña.
webauthn-login-title=Inicio de sesión con Passkey
webauthn-registration-title=Registro de Passkey
webauthn-available-authenticators=Passkeys disponibles
webauthn-unsupported-browser-text=WebAuthn no es compatible con este navegador. Pruebe con otro o comuníquese con su administrador.
webauthn-doAuthenticate=Iniciar sesión con Passkey
webauthn-createdAt-label=Creada

# WebAuthn Error
webauthn-error-title=Error de Passkey
webauthn-error-registration=No se pudo registrar su Passkey. <br /> {0}
webauthn-error-api-get=No se pudo autenticar con el Passkey. <br /> {0}
webauthn-error-different-user=El primer usuario autenticado no es el autenticado por el Passkey.
webauthn-error-auth-verification=El resultado de la autenticación con el Passkey no es válido. <br /> {0}
webauthn-error-register-verification=El resultado de registro del Passkey no es válido. <br /> {0}
webauthn-error-user-not-found=Usuario desconocido autenticado por el Passkey.


finalDeletionConfirmation=Si elimina su cuenta, no se puede restaurar. Para mantener su cuenta, haga clic en Cancelar.
irreversibleAction=Esta acción es irreversible
deleteAccountConfirm=Eliminar la confirmación de la cuenta
deletingImplies=Eliminar su cuenta implica:
errasingData=Borrando todos sus datos
loggingOutImmediately=Registrándole inmediatamente
accountUnusable=Cualquier uso posterior de la aplicación no será posible con esta cuenta
userDeletedSuccessfully=Usuario eliminado con éxito
access-denied-when-idp-auth=Acceso negado al autenticar con {0}
frontchannel-logout.title=Saliendo de su cuenta
frontchannel-logout.message=Está cerrando las siguientes aplicaciones
readOnlyUsernameMessage=No puede actualizar su nombre de usuario, ya que es de solo lectura.
authenticateStrong=Se requiere autenticación fuerte para continuar
deleteCredentialMessage=¿Desea eliminar {0}?
confirmOverrideIdpTitle=El enlace con el broker ya existe
federatedIdentityConfirmOverrideMessage=Está intentando vincular su cuenta {0} con la cuenta {1} {2}. Pero su cuenta ya está vinculada con otra cuenta {3} {4}. ¿Puede confirmar si desea reemplazar el enlace existente con la nueva cuenta?
confirmOverrideIdpContinue=Sí, reemplazar el enlace con la cuenta actual
invalidPasswordNotContainsUsernameMessage=Contraseña no válida: no puede contener el nombre de usuario.
emailVerifiedAlreadyMessage=Tu dirección de correo electrónico ya ha sido verificada.
requiredAction.webauthn-register=Registro Webauthn
auth-x509-client-username-form-display-name=Certificado X509
passkey-login-title=Inicio de sesión con Passkey
passkey-available-authenticators=Passkeys disponibles
passkey-doAuthenticate=Inicia sesión con Passkey
passkey-createdAt-label=Creado
passkey-autofill-select=Seleccione su passkey
error-invalid-multivalued-size=El atributo {0} debe tener al menos {1} y como máximo {2} {2,choice,0#valores|1#valor|1<valores}.
organization.confirm-membership.title=Está a punto de unirse a la organización ${kc.org.name}
organization.confirm-membership=Al hacer clic en el enlace de abajo, se convertirá en miembro de la organización {0}:
deleteCredentialTitle=Eliminar {0}
webauthn-registration-init-label-prompt=Por favor, ingrese la etiqueta de su passkey
webauthn-registration-init-label=Passkey (Etiqueta predeterminada)
organization.member.register.title=Cree una cuenta para unirse a la organización ${kc.org.name}
auth-x509-client-username-form-help-text=Inicia sesión con un certificado de cliente X509.
organizationScopeConsentText=Organización
organization.select=Seleccione una organización para continuar:
identityProviderLogoutFailure=Error al cerrar sesión en el proveedor de identidad SAML
passkey-unsupported-browser-text=Este navegador no admite Passkey. Pruebe con otro o contacte a su administrador.
notMemberOfOrganization=El usuario no es miembro de la organización {0}
notMemberOfAnyOrganization=El usuario no es miembro de ninguna organización
emailVerifyInstruction4=Para verificar su dirección de correo electrónico, le enviaremos un correo con instrucciones a la dirección {0}.
emailVerifyResend=Reenviar correo de verificación
emailVerifySend=Enviar correo de verificación
linkIdpActionTitle=Vinculando {0}
linkIdpActionMessage=¿Desea vincular su cuenta con {0}?
