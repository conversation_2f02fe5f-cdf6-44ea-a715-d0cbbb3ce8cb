invalidPasswordMinLengthMessage=Per trumpas slaptažodis: ma<PERSON><PERSON><PERSON><PERSON> ilgis {0}.
invalidPasswordMinLowerCaseCharsMessage=Neteisingas slaptažodis: privaloma įvesti {0} mažąją raidę.
invalidPasswordMinDigitsMessage=Neteisingas slaptažodis: privaloma įvesti {0} skaitmenį.
invalidPasswordMinUpperCaseCharsMessage=Neteisingas slaptažodis: privaloma įvesti {0} didžiąją raidę.
invalidPasswordMinSpecialCharsMessage=Neteisingas slaptažodis: privaloma įvesti {0} specialų simbolį.
invalidPasswordNotUsernameMessage=Neteisingas slaptažodis: slaptažodis negali sutapti su naudotojo vardu.
invalidPasswordRegexPatternMessage=Neteisingas slaptažodis: slaptažodis netenkina regex taisyklės(ių).
invalidPasswordHistoryMessage=Neteisingas slaptažodis: slaptažodis negali sutapti su prieš tai buvusiais {0} slaptažodžiais.

ldapErrorInvalidCustomFilter=Sukonfigūruotas LDAP filtras neprasideda "(" ir nesibaigia ")" simboliais.
ldapErrorMissingClientId=Privaloma nurodyti kliento ID kai srities rolių susiejimas nėra nenaudojamas.
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Grupių paveldėjimo ir UID narystės tipas kartu negali būti naudojami.
ldapErrorCantWriteOnlyForReadOnlyLdap=Negalima nustatyti rašymo rėžimo kuomet LDAP teikėjo rėžimas ne WRITABLE
ldapErrorCantWriteOnlyAndReadOnly=Negalima nustatyti tik rašyti ir tik skaityti kartu

clientRedirectURIsFragmentError=Nurodykite URI fragmentą, kurio negali būti peradresuojamuose URI adresuose
clientRootURLFragmentError=Nurodykite URL fragmentą, kurio negali būti šakniniame URL adrese

pairwiseMalformedClientRedirectURI=Klientas pateikė neteisingą nukreipimo nuorodą.
pairwiseClientRedirectURIsMissingHost=Kliento nukreipimo nuorodos privalo būti nurodytos su serverio vardo komponentu.
pairwiseClientRedirectURIsMultipleHosts=Kuomet nesukonfigūruotas sektoriaus identifikatoriaus URL, kliento nukreipimo nuorodos privalo talpinti ne daugiau kaip vieną skirtingą serverio vardo komponentą.
pairwiseMalformedSectorIdentifierURI=Neteisinga sektoriaus identifikatoriaus URI.
pairwiseFailedToGetRedirectURIs=Nepavyko gauti nukreipimo nuorodų iš sektoriaus identifikatoriaus URI.
pairwiseRedirectURIsMismatch=Kliento nukreipimo nuoroda neatitinka nukreipimo nuorodų iš sektoriaus identifikatoriaus URI.