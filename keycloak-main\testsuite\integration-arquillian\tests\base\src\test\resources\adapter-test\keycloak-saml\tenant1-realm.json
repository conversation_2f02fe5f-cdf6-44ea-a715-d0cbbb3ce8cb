{"id": "tenant1", "realm": "tenant1", "enabled": true, "accessTokenLifespan": 3000, "accessCodeLifespan": 10, "accessCodeLifespanUserAction": 6000, "sslRequired": "external", "registrationAllowed": false, "requiredCredentials": ["password"], "users": [{"username": "<EMAIL>", "enabled": true, "email": "<EMAIL>", "firstName": "Bill", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "password"}], "realmRoles": ["user"]}, {"username": "user-tenant1", "enabled": true, "email": "<EMAIL>", "firstName": "Bill", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "user-tenant1"}], "realmRoles": ["user"]}], "roles": {"realm": [{"name": "user", "description": "User privileges"}]}, "clients": [{"clientId": "multi-tenant", "name": "multi-tenant", "enabled": true, "protocol": "saml", "fullScopeAllowed": true, "frontchannelLogout": true, "baseUrl": "http://localhost:8080/multi-tenant-saml/", "redirectUris": ["http://localhost:8080/multi-tenant-saml/*"], "attributes": {"saml_assertion_consumer_url_post": "http://localhost:8080/multi-tenant-saml/saml?realm=tenant1", "saml_assertion_consumer_url_redirect": "http://localhost:8080/multi-tenant-saml/saml?realm=tenant1", "saml_single_logout_service_url_post": "http://localhost:8080/multi-tenant-saml/saml?realm=tenant1", "saml_single_logout_service_url_redirect": "http://localhost:8080/multi-tenant-saml/saml?realm=tenant1", "saml.server.signature": "true", "saml.client.signature": "true", "saml.signature.algorithm": "RSA_SHA256", "saml.authnstatement": "true", "saml.signing.certificate": "MIICyTCCAbGgAwIBAgIIYEhGf0w2DeQwDQYJKoZIhvcNAQELBQAwEjEQMA4GA1UEAxMHdGVuYW50MTAgFw0yNDA2MjIxMDUyMTRaGA8yMTI0MDUyOTEwNTIxNFowEjEQMA4GA1UEAxMHdGVuYW50MTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKQRUDp50r6J5STnA2vYGHLRkgTC+diY1IRBlrullnf09RMNDmb4QnmTHpPKPuB1NZhy3KLerbaGceDOZljNHk3K61jxz0ECOzst699xcoKV94QEy/UQDhhA6T5D3XIjzrYdDqEffqEvE6iterA1ApgbUdwIsMIQvQscNyB/o+v+EDfQZNgMbJjiZs5gIYSxzHjAfOpq2XKLurN62gwCm/t70DWDaC7M1KQQTtl2ir/veki1nwiPjwtFLptun5PfRDmvoLiUz4zP8EfDjA30O5xa19HcewJV94SCRv+0Wcst8uVpCHMzZHayXdMeECCsopjLJP4Pdea7nzjVTF8BycMCAwEAAaMhMB8wHQYDVR0OBBYEFHX6qYF8QCPeU+AV0XRKE95DxdDDMA0GCSqGSIb3DQEBCwUAA4IBAQBZvVr7vgHt88fDVCMpl3apVNssXUMmBjrySJPPO9O7EaAuqjAaBf5Fpa/PtbuNfD542pMLmycf65W4JCUNuMFy3d9GuU3qK+bgpoXKgppuYb+6deQjbDipyvWeeoYoSmRBs7SsD+QH0JAybZ25/ciPfiyVH2oCbNNSEs2zIDKB07oVU8NDYhmHdSsqPQOlijHHgFFDvR+qpWqu35W8AzQ9KAF1BEHAPexJAH6kRuoarsQiq5cdiZHFco/q3QGKnXArAlXtIhbG5L7KE0cE5UCe3hjYqkP3U5QUx5QejsFFAj6+4ASfQbpM8ud6cr7m95V4s01MyEtNzpHU5jVVjJwK"}}]}