doSave=儲存
doCancel=取消
doLogOutAllSessions=登出所有狀態
doRemove=移除
doAdd=新增
doSignOut=登出
doLogIn=登入
doLink=連結
noAccessMessage=沒有存取權

personalInfoSidebarTitle=個人資訊
accountSecuritySidebarTitle=帳號安全
signingInSidebarTitle=登入狀態
deviceActivitySidebarTitle=裝置狀態
linkedAccountsSidebarTitle=已連結帳號

editAccountHtmlTitle=編輯帳號
personalInfoHtmlTitle=個人資訊
federatedIdentitiesHtmlTitle=聯邦身分
accountLogHtmlTitle=帳號記錄
changePasswordHtmlTitle=變更密碼
deviceActivityHtmlTitle=裝置活動記錄
sessionsHtmlTitle=登入狀態
accountManagementTitle=Keycloak 帳號管理
authenticatorTitle=OTP 驗證器
applicationsHtmlTitle=應用程式
linkedAccountsHtmlTitle=已連結帳號

accountManagementWelcomeMessage=歡迎使用 Keycloak 帳號管理
accountManagementBaseThemeCannotBeUsedDirectly=基礎帳號主題僅包含帳號控制台的翻譯。 \
    若要顯示帳號控制台，您必須將您的主題的父主題設定為其他帳號主題，或提供您自己的 index.ftl 檔案。 \
    請參閱相關文件以獲取更多資訊。
personalInfoIntroMessage=管理您的基本資訊
accountSecurityTitle=帳號安全性
accountSecurityIntroMessage=管理您的密碼和帳號存取權
applicationsIntroMessage=追蹤和管理您的應用程式權限以存取您的帳號
resourceIntroMessage=在團隊成員之間共享您的資源
passwordLastUpdateMessage=您的密碼已更新在
updatePasswordTitle=更新密碼
updatePasswordMessageTitle=確保您使用一個強密碼
updatePasswordMessage=強密碼包含數字、字母和符號的組合。 它很難猜測，不像真實的單詞，並且僅使用於此帳號。
personalSubTitle=您的個人資訊
personalSubMessage=管理您的基本資訊

authenticatorCode=一次性驗證碼
email=電子信箱
firstName=名字
givenName=姓氏
fullName=全名
lastName=姓氏
familyName=姓氏
password=密碼
currentPassword=現有密碼
passwordConfirm=確認密碼
passwordNew=新密碼
username=使用者名稱
address=地址
street=街道
locality=市
region=省、自治區、直轄市
postal_code=郵遞區號
country=國家
emailVerified=已驗證電子信箱
website=網頁
phoneNumber=電話號碼
phoneNumberVerified=已驗證電話號碼
gender=性別
birthday=出生日期
zoneinfo=時區
gssDelegationCredential=GSS 委託憑證

profileScopeConsentText=使用者資訊
emailScopeConsentText=電子信箱
addressScopeConsentText=地址
phoneScopeConsentText=電話號碼
offlineAccessScopeConsentText=離線存取
samlRoleListScopeConsentText=我的角色清單
rolesScopeConsentText=使用者角色清單
organizationScopeConsentText=組織

role_admin=管理員
role_realm-admin=領域管理員
role_create-realm=創立領域
role_view-realm=瀏覽領域
role_view-users=瀏覽使用者清單
role_view-applications=檢視應用清單
role_view-groups=檢視群組清單
role_view-clients=檢視客戶端清單
role_view-events=檢視事件清單
role_view-identity-providers=檢視身分提供者清單
role_view-consent=檢視授權清單
role_manage-realm=管理領域
role_manage-users=管理使用者清單
role_manage-applications=管理應用程式清單
role_manage-identity-providers=管理身分提供者清單
role_manage-clients=管理客戶端清單
role_manage-events=管理事件清單
role_view-profile=檢視個人資料
role_manage-account=管理帳號
role_manage-account-links=管理帳號連結
role_manage-consent=管理授權
role_read-token=讀取 token
role_offline-access=離線存取
role_uma_authorization=取得權限
client_account=帳號
client_account-console=帳號終端
client_security-admin-console=管理員終端
client_admin-cli=Admin CLI
client_realm-management=領域管理
client_broker=Broker


requiredFields=必填的欄位
allFieldsRequired=全部的欄位都必填

backToApplication=&laquo; 返回應用程式
backTo=回到 {0}

date=日期
event=事件
ip=IP
client=客戶端
clients=客戶端
details=細節
started=開始時間
lastAccess=最後存取
expires=過期時間
applications=應用程式

account=帳號
federatedIdentity=聯邦身分
authenticator=驗證器
device-activity=裝置活動
sessions=登入狀態
log=記錄

application=應用程式
availableRoles=可用身分
grantedPermissions=已授權權限
grantedPersonalInfo=已授權個人資訊
additionalGrants=其他授權
action=動作
inResource=在
fullAccess=完整存取
offlineToken=離線 token
revoke=撤銷授權

configureAuthenticators=已設定驗證器
mobile=Mobile
totpStep1=在您的手機中安裝下列程式 (擇一)
totpStep2=開啟應用程式並掃描 QR code：
totpStep3=輸入應用程式提供的一次性密碼並點擊送出來完成設定。
totpStep3DeviceName=輸入裝置名稱，以便管理您的 OTP 驗證器。

totpManualStep2=開啟應用程式並輸入金鑰：
totpManualStep3=如果應用程式能做設定的話，請使用以下這些設定：
totpUnableToScan=無法掃描？
totpScanBarcode=掃描 QR code？

totp.totp=基於時間 (Time-based)
totp.hotp=基於次數 (Counter-based)

totpType=種類
totpAlgorithm=算法
totpDigits=位數
totpInterval=間隔
totpCounter=次數
totpDeviceName=裝置名稱

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

irreversibleAction=這個動作是不可逆的
deletingImplies=刪除您的帳戶意味著：
errasingData=清除您的所有資料
loggingOutImmediately=立即登出您的帳戶
accountUnusable=任何後續使用此帳戶的應用程式都將無法使用

missingUsernameMessage=請提供使用者名稱。
missingFirstNameMessage=請提供名字。
invalidEmailMessage=無效的電子信箱。
missingLastNameMessage=請提供姓氏。
missingEmailMessage=請提供電子信箱。
missingPasswordMessage=請提供密碼。
notMatchPasswordMessage=密碼不相符。
invalidUserMessage=無效的使用者名稱或密碼。
updateReadOnlyAttributesRejectedMessage=無法更新只讀欄位。

missingTotpMessage=請輸入驗證碼。
missingTotpDeviceNameMessage=請提供裝置名稱。
invalidPasswordExistingMessage=無效的密碼：曾使用過的密碼。
invalidPasswordConfirmMessage=密碼不一致。
invalidTotpMessage=無效的驗證碼。

usernameExistsMessage=相同的使用者名稱已存在。
emailExistsMessage=相同的電子信箱已存在。

readOnlyUserMessage=您無法更新您的帳號，因為它是唯讀的。
readOnlyUsernameMessage=您無法更新您的使用者名稱，因為它是唯讀的。
readOnlyPasswordMessage=您無法更新您的密碼，因為它是唯讀的。

successTotpMessage=OTP 驗證器已設定。
successTotpRemovedMessage=OTP 驗證器已移除。

successGrantRevokedMessage=成功撤銷授權。

accountUpdatedMessage=您的帳號資訊已更新。
accountPasswordUpdatedMessage=您的密碼已更新。

missingIdentityProviderMessage=未指定身分提供者。
invalidFederatedIdentityActionMessage=無效或是錯誤的行為。
identityProviderNotFoundMessage=找不到指定身分提供者。
federatedIdentityLinkNotActiveMessage=這個提供者已不再啟用。
federatedIdentityRemovingLastProviderMessage=當你沒有密碼時，你無法移除最後一個聯邦身分。
identityProviderRedirectErrorMessage=無法重新導向至身分提供者。
identityProviderRemovedMessage=身分提供者已成功移除。
identityProviderAlreadyLinkedMessage=身份提供者 {0} 傳回的聯盟身份已連結至另一個使用者。
staleCodeAccountMessage=頁面已過期。請再試一次。
consentDenied=許可被拒。
access-denied-when-idp-auth=當使用 {0} 進行身分驗證時存取被拒。

accountDisabledMessage=帳號已停用，請聯繫系統管理員。

accountTemporarilyDisabledMessage=帳號被暫時停用，請聯繫系統管理員或稍後重試。
invalidPasswordMinLengthMessage=無效的密碼：最短長度為 {0}。
invalidPasswordMaxLengthMessage=無效的密碼：最長長度為 {0}。
invalidPasswordMinLowerCaseCharsMessage=無效的密碼：至少需要 {0} 個小寫字母。
invalidPasswordMinDigitsMessage=無效的密碼：至少需要 {0} 個數字。
invalidPasswordMinUpperCaseCharsMessage=無效的密碼：至少需要 {0} 個大寫字母。
invalidPasswordMinSpecialCharsMessage=無效的密碼：至少需要 {0} 個特殊字元。
invalidPasswordNotUsernameMessage=無效的密碼：不可與使用者名稱相同。
invalidPasswordNotContainsUsernameMessage=無效的密碼：不可包含使用者名稱。
invalidPasswordNotEmailMessage=無效的密碼：不可與電子信箱相同。
invalidPasswordRegexPatternMessage=無效的密碼：不符合 regex 規則。
invalidPasswordHistoryMessage=無效的密碼：不可與前 {0} 個密碼相同。
invalidPasswordBlacklistedMessage=無效的密碼：該密碼已在黑名單中。
invalidPasswordGenericMessage=無效的密碼：新密碼不符合政策。

# Authorization
myResources=我的資源
myResourcesSub=我的資源
doDeny=拒絕
doRevoke=撤回
doApprove=授與
doRemoveSharing=取消共享
doRemoveRequest=取消請求
peopleAccessResource=有權限存取此資源的帳號
resourceManagedPolicies=授予此資源存取權限的權限
resourceNoPermissionsGrantingAccess=沒有授予存取此資源的權限
anyAction=任何動作
description=描述
name=名稱
scopes=範圍
resource=資源
user=使用者
peopleSharingThisResource=共享此資源的使用者
shareWithOthers=與其他人共享
needMyApproval=需要我的批准
requestsWaitingApproval=等待批准的請求
icon=Icon
requestor=請求者
owner=擁有者
resourcesSharedWithMe=與我共享的資源
permissionRequestion=權限請求
permission=權限
shares=共享
notBeingShared=這個資源尚未被共享。
notHaveAnyResource=你沒有任何資源
noResourcesSharedWithYou=沒有任何資源與您共享
havePermissionRequestsWaitingForApproval=您有 {0} 個權限請求等待批准。
clickHereForDetails=點擊這裡查看詳細資訊。
resourceIsNotBeingShared=這個資源尚未被共享

# Applications
applicationName=名稱
applicationType=應用程式型態
applicationInUse=僅限使用中的應用程式
clearAllFilter=清除所有篩選
activeFilters=啟用篩選
filterByName=透過名字篩選 ...
allApps=所有應用程式
internalApps=內部應用程式
thirdpartyApps=第三方應用程式
appResults=結果
clientNotFoundMessage=找不到客戶端。

# Linked account
authorizedProvider=授權的提供者
authorizedProviderMessage=連結到你帳號的提供者
identityProvider=身分提供者
identityProviderMessage=連結到你帳號的身分提供者
socialLogin=社群登入
userDefined=使用者定義
removeAccess=移除授權
removeAccessMessage=如果你想重新使用這個應用程式帳號，你需要重新授權。

#Authenticator
authenticatorStatusMessage=2FA 驗證目前
authenticatorFinishSetUpTitle=你的 2FA 驗證
authenticatorFinishSetUpMessage=每次登入你的 Keycloak 帳號時，你將被要求提供一個兩步驟驗證碼。
authenticatorSubTitle=設定兩步驟驗證
authenticatorSubMessage=為了增強你帳號的安全性，請啟用至少一種可用的兩步驟驗證方法。
authenticatorMobileTitle=手機驗證器
authenticatorMobileMessage=使用手機驗證器來取得 2FA 驗證碼。
authenticatorMobileFinishSetUpMessage=這個驗證器已經綁定到你的手機。
authenticatorActionSetup=設定完畢
authenticatorSMSTitle=SMS 驗證碼
authenticatorSMSMessage=Keycloak 會發送驗證碼到你的手機作為 2FA 驗證。
authenticatorSMSFinishSetUpMessage=簡訊將會發送到
authenticatorDefaultStatus=預設
authenticatorChangePhone=變更手機號碼

#Authenticator - Mobile Authenticator setup
authenticatorMobileSetupTitle=手機驗證設定
smscodeIntroMessage=輸入您的手機號碼，驗證碼將會發送到您的手機。
mobileSetupStep1=在您的手機中安裝一個驗證器。這裡列出的應用程式都有支援。
mobileSetupStep2=開啟應用程式並掃描 QR code：
mobileSetupStep3=輸入應用程式提供的一次性密碼並點擊儲存來完成設定。
scanBarCode=想掃描 QR code？
enterBarCode=輸入一次性驗證碼
doCopy=複製
doFinish=結束

#Authenticator - SMS Code setup
authenticatorSMSCodeSetupTitle=SMS 代碼設定
chooseYourCountry=選擇你的國家
enterYourPhoneNumber=輸入你的手機號碼
sendVerficationCode=送出驗證碼
enterYourVerficationCode=輸入你的驗證碼

#Authenticator - backup Code setup
authenticatorBackupCodesSetupTitle=復原代碼設定
realmName=Realm
doDownload=下載
doPrint=列印
generateNewBackupCodes=產生新的復原代碼
backtoAuthenticatorPage=回到驗證頁面


#Resources
resources=資源
sharedwithMe=與我共享
share=共享
sharedwith=共享給
accessPermissions=存取權限
permissionRequests=權限請求
approve=同意
approveAll=全部同意
people=人員
perPage=每頁
currentPage=目前頁面
sharetheResource=分享資源
group=群組
selectPermission=選取權限
addPeople=新增人員以分享您的資源
addTeam=新增團隊以分享您的資源
myPermissions=我的權限
waitingforApproval=等待批准
anyPermission=任何權限

# Openshift messages
openshift.scope.user_info=使用者資訊
openshift.scope.user_check-access=使用者存取權限資訊
openshift.scope.user_full=完整存取權
openshift.scope.list-projects=專案列表

error-invalid-value=無效的數值。
error-invalid-blank=數值不可為空。
error-empty=數值不可為空。
error-invalid-length=屬性 {0} 的長度必須介於 {1} 和 {2} 之間。
error-invalid-length-too-short=屬性 {0} 的長度最少為 {1}。
error-invalid-length-too-long=屬性 {0} 的長度最多為 {2}。
error-invalid-email=無效的電子信箱。
error-invalid-number=無效的號碼。
error-number-out-of-range=屬性 {0} 的數值必須介於 {1} 和 {2} 之間。
error-number-out-of-range-too-small=屬性 {0} 的數值最少為 {1}。
error-number-out-of-range-too-big=屬性 {0} 的數值最多為 {2}。
error-pattern-no-match=無效的數值。
error-invalid-uri=無效的 URL。
error-invalid-uri-scheme=無效的 URL 協定。
error-invalid-uri-fragment=無效的 URL 片段。
error-user-attribute-required=請設定屬性值 {0}。
error-invalid-date=無效的日期。
error-user-attribute-read-only=屬性 {0} 是唯讀的。
error-username-invalid-character=使用者名稱包含無效字元。
error-person-name-invalid-character=名字包含無效字元。
