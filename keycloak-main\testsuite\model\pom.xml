<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.keycloak</groupId>
        <artifactId>keycloak-testsuite-pom</artifactId>
        <version>999.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <groupId>org.keycloak.testsuite</groupId>
    <artifactId>keycloak-model-test</artifactId>
    <name>Tests for logical storage layer</name>
    <description>Tests for storage layer functionality targetting logical layer, i.e. models</description>
    <packaging>jar</packaging>
    
    <properties>
        <keycloak.connectionsJpa.driver>org.h2.Driver</keycloak.connectionsJpa.driver>
        <keycloak.connectionsJpa.database>keycloak</keycloak.connectionsJpa.database>
        <keycloak.connectionsJpa.user>sa</keycloak.connectionsJpa.user>
        <keycloak.connectionsJpa.password></keycloak.connectionsJpa.password>
        <keycloak.connectionsJpa.url>jdbc:h2:mem:test;DB_CLOSE_DELAY=-1</keycloak.connectionsJpa.url>
        <jdbc.mvn.groupId>com.h2database</jdbc.mvn.groupId>
        <jdbc.mvn.artifactId>h2</jdbc.mvn.artifactId>
        <jdbc.mvn.version>${h2.version}</jdbc.mvn.version>
        <log4j.configuration>file:${project.build.directory}/dependency/log4j.properties</log4j.configuration>
        <jacoco.skip>true</jacoco.skip>
    </properties>
    
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>net.jcip</groupId>
            <artifactId>jcip-annotations</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-reload4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-services</artifactId>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-services</artifactId>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-server-spi-private</artifactId>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-server-spi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.keycloak.testsuite</groupId>
            <artifactId>integration-arquillian-tests-base</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.keycloak</groupId>
                    <artifactId>keycloak-quarkus-server</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>${jdbc.mvn.groupId}</groupId>
            <artifactId>${jdbc.mvn.artifactId}</artifactId>
            <version>${jdbc.mvn.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-model-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-model-infinispan</artifactId>
        </dependency>
        <dependency>
            <groupId>org.keycloak.testsuite</groupId>
            <artifactId>integration-arquillian-testsuite-providers</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.infinispan</groupId>
            <artifactId>infinispan-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.infinispan</groupId>
            <artifactId>infinispan-core</artifactId>
            <type>test-jar</type>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql-jdbc.version}</version>
        </dependency>
        <!-- annotations needed to avoid compile time warnings about enums constants -->
        <dependency>
            <groupId>org.infinispan</groupId>
            <artifactId>infinispan-component-annotations</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.infinispan</groupId>
            <artifactId>infinispan-remote-query-server</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <append>true</append>
                            <includes>
                                <include>org/keycloak/**/*</include>
                            </includes>
                        </configuration>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <!-- See also https://github.com/wildfly/wildfly-core/blob/7e5624cf92ebe4b64a4793a8c0b2a340c0d6d363/core-feature-pack/common/src/main/resources/content/bin/common.sh#L57-L60 -->
                    <argLine>@{argLine} -Xmx1536m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED</argLine>
                    <systemPropertyVariables>
                        <!-- keycloak.model.parameters lists parameter classes from 
                             org.keycloak.model.parameters package and determine enabled providers -->
                        <keycloak.model.parameters>${keycloak.model.parameters}</keycloak.model.parameters>
                        <keycloak.connectionsJpa.default.driver>${keycloak.connectionsJpa.driver}</keycloak.connectionsJpa.default.driver>
                        <keycloak.connectionsJpa.default.database>${keycloak.connectionsJpa.database}</keycloak.connectionsJpa.default.database>
                        <keycloak.connectionsJpa.default.user>${keycloak.connectionsJpa.user}</keycloak.connectionsJpa.default.user>
                        <keycloak.connectionsJpa.default.password>${keycloak.connectionsJpa.password}</keycloak.connectionsJpa.default.password>
                        <keycloak.connectionsJpa.default.url>${keycloak.connectionsJpa.url}</keycloak.connectionsJpa.default.url>
                        <log4j.configuration>file:${project.build.directory}/test-classes/log4j.properties</log4j.configuration> <!-- for the logging to properly work with tests in the 'other' module -->
                        <java.util.logging.manager>org.jboss.logmanager.LogManager</java.util.logging.manager>
                        <org.jboss.logging.provider>log4j</org.jboss.logging.provider>
                        <infinispan.version>${infinispan.version}</infinispan.version>
                        <project.version>${project.version}</project.version>
                    </systemPropertyVariables>
                    <properties>
                        <property>
                            <name>listener</name>
                            <value>org.keycloak.testsuite.model.AfterSuiteListener</value>
                        </property>
                    </properties>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-testsuite-providers-to-model-testsuite</id>
                        <phase>generate-test-resources</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeGroupIds>org.keycloak.testsuite</includeGroupIds>
                            <includeArtifactIds>integration-arquillian-testsuite-providers</includeArtifactIds>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    
    <profiles>
        <profile>
            <id>jpa</id>
            <properties>
                <keycloak.model.parameters>Jpa</keycloak.model.parameters>
            </properties>
        </profile>

        <profile>
            <id>jpa+infinispan</id>
            <properties>
                <keycloak.model.parameters>Infinispan,Jpa</keycloak.model.parameters>
            </properties>
        </profile>

        <profile>
            <id>jpa+infinispan+volatilesessions</id>
            <properties>
                <keycloak.model.parameters>Infinispan,Jpa,VolatileUserSessions</keycloak.model.parameters>
            </properties>
        </profile>

        <profile>
            <id>jpa+infinispan+client-storage</id>
            <properties>
                <keycloak.model.parameters>Jpa,Infinispan,HardcodedClientStorage</keycloak.model.parameters>
            </properties>
        </profile>

        <profile>
            <id>jpa+multi-site-infinispan</id>
            <properties>
                <keycloak.model.parameters>RemoteInfinispan,Jpa</keycloak.model.parameters>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <systemPropertyVariables>
                                <keycloak.profile.feature.multi_site>enabled</keycloak.profile.feature.multi_site>
                            </systemPropertyVariables>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>jpa+clusterless</id>
            <properties>
                <keycloak.model.parameters>RemoteInfinispan,Jpa</keycloak.model.parameters>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <systemPropertyVariables>
                                <keycloak.profile.feature.clusterless>enabled</keycloak.profile.feature.clusterless>
                                <keycloak.profile.feature.persistent_user_sessions>disabled</keycloak.profile.feature.persistent_user_sessions>
                            </systemPropertyVariables>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>jpa-federation+infinispan</id>
            <properties>
                <keycloak.model.parameters>Infinispan,JpaFederation,TestsuiteUserMapStorage</keycloak.model.parameters>
            </properties>
        </profile>

        <profile>
            <id>jpa-federation-backward+infinispan</id>
            <properties>
                <keycloak.model.parameters>Infinispan,JpaFederation,BackwardsCompatibilityUserStorage</keycloak.model.parameters>
            </properties>
        </profile>

        <profile>
            <id>jpa-federation</id>
            <properties>
                <keycloak.model.parameters>JpaFederation,TestsuiteUserMapStorage</keycloak.model.parameters>
            </properties>
        </profile>

        <profile>
            <id>jpa-federation-backward</id>
            <properties>
                <keycloak.model.parameters>JpaFederation,BackwardsCompatibilityUserStorage</keycloak.model.parameters>
            </properties>
        </profile>

        <profile>
            <id>jpa-federation-file-storage</id>
            <properties>
                <keycloak.model.parameters>JpaFederation,TestsuiteUserFileStorage</keycloak.model.parameters>
            </properties>
        </profile>

        <profile>
            <id>jpa-federation-file-storage+infinispan</id>
            <properties>
                <keycloak.model.parameters>JpaFederation,TestsuiteUserFileStorage,Infinispan</keycloak.model.parameters>
            </properties>
        </profile>

        <profile>
            <id>jpa-federation+ldap</id>
            <properties>
                <keycloak.model.parameters>JpaFederation,LdapUserStorage</keycloak.model.parameters>
            </properties>
        </profile>

        <profile>
            <id>jpa-federation+ldap+infinispan</id>
            <properties>
                <keycloak.model.parameters>JpaFederation,LdapUserStorage,Infinispan</keycloak.model.parameters>
            </properties>
        </profile>

        <profile>
            <id>.asyncProfiler</id>
            <activation>
                <property><name>libasyncProfilerPath</name></property>
            </activation>
            <properties>
                <asyncProfiler.event>cpu</asyncProfiler.event>
                <asyncProfiler.file>target/profile.html</asyncProfiler.file>
                <argLine>-agentpath:${libasyncProfilerPath}=start,event=${asyncProfiler.event},file=${asyncProfiler.file}</argLine>
            </properties>
        </profile>

    </profiles>
    
</project>
