emailVerificationSubject=Verificació de l''adreça electrònica
emailVerificationBody=Algú ha creat un compte de {2} amb aquesta adreça de correu electrònic. Si en sabeu res, feu clic a l''enllaç següent per a verificar la vostra adreça de correu electrònic.\n\n{0}\n\nAquest enllaç caducarà en {3}.\n\nSi no heu creat aquest compte, simplement ignoreu aquest missatge.
emailVerificationBodyHtml=<p>Algú ha creat un compte de {2} amb aquesta adreça de correu electrònic. Si en sabeu res, feu clic a l''enllaç següent per a verificar la vostra adreça de correu electrònic.</p><p><a href="{0}">Enllaç a la verificació de l''adreça de correu electrònic</a></p><p> Aquest enllaç caducarà en {3}.</p><p> Si no heu creat aquest compte, simplement ignoreu aquest missatge.</p>
emailUpdateConfirmationSubject=Verifiqueu l''adreça nova
emailUpdateConfirmationBody=Per a actualitzar el vostre compte {2} amb l''adreça de correu electrònic {1}, feu clic a l''enllaç de sota\n\n{0}\n\nAquest enllaç caducarà en {3}.\n\nSi no voleu continuar amb aquesta modificació, simplement ignoreu aquest missatge.
emailUpdateConfirmationBodyHtml=<p>Per a actualitzar el vostre compte {2} amb l''adreça de correu electrònic {1}, feu clic a l''enllaç de sota</p><p><a href="{0}">{0}</a></p><p>Aquest enllaç caducarà en {3}.</p><p>Si no voleu continuar amb aquesta modificació, simplement ignoreu aquest missatge.</p>
emailTestSubject=[KEYCLOAK] - missatge de prova SMTP
emailTestBody=Aquest és un missatge de prova
emailTestBodyHtml=<p>Aquest és un missatge de prova</p>
identityProviderLinkSubject=Enllaç a {0}
identityProviderLinkBody=Algú ha sol·licitat enllaçar el vostre compte «{1}» amb el compte «{0}» de l''usuari {2}. Si heu estat vós, feu clic a l''enllaç de sota per a enllaçar els comptes\n\n{3}\n\nAquest enllaç caducarà en {5}.\n\nSi no voleu enllaçar el compte, simplement ignoreu aquest missatge. Si enllaceu els comptes, podreu identificar-vos a {1} a través de {0}.
identityProviderLinkBodyHtml=<p>Algú ha sol·licitat enllaçar el vostre compte <b>{1}</b> amb el compte <b>{0}</b> de l''usuari {2}. Si heu estat vós, feu clic a l''enllaç de sota per a enllaçar els comptes</p><p><a href="{3}">Enllaç per a confirmar l''enllaçat de comptes</a></p><p>Aquest enllaç caducarà en {5}.</p><p>Si no voleu enllaçar el compte, simplement ignoreu aquest missatge. Si enllaceu els comptes, podreu identificar-vos a {1} a través de {0}.</p>
passwordResetSubject=Restabliu la contrasenya
passwordResetBody=Algú ha demanat canviar les credencials del vostre compte de {2}. Si heu estat vós, feu clic a l''enllaç següent per a restablir-les.\n\n{0}\n\nAquest enllaç i codi caducaran en {3}.\n\nSi no voleu restablir les vostres credencials, simplement ignoreu aquest missatge i no es realitzarà cap canvi.
passwordResetBodyHtml=<p>Algú ha demanat canviar les credencials del vostre compte de {2}. Si heu estat vós, feu clic a l''enllaç següent per a restablir-les.</p><p><a href="{0}">Enllaç per a restablir les credencials</a></p><p>Aquest enllaç caducarà en {3}.</p><p>Si no voleu restablir les vostres credencials, simplement ignoreu aquest missatge i no es realitzarà cap canvi.</p>
executeActionsSubject=Actualitzeu el vostre compte
executeActionsBody=L''administrador ha sol·licitat que actualitzeu el vostre compte de {2} realitzant les accions següents: {3}. Feu clic a l''enllaç següent per a iniciar aquest procés.\n\n{0}\n\nAquest enllaç caducarà en {4}.\n\nSi no esteu al corrent que l''administrador hagi sol·licitat això, simplement ignoreu aquest missatge i no es realitzarà cap canvi.
executeActionsBodyHtml=<p>L''administrador ha sol·licitat que actualitzeu el vostre compte de {2} realitzant les accions següents: {3}. Feu clic a l''enllaç següent per iniciar aquest procés.</p><p><a href="{0}">Enllaç a l''actualització del compte</a></p><p>Aquest enllaç caducarà en {4}.</p><p>Si no esteu al corrent que l''administrador hagi sol·licitat això, simplement ignoreu aquest missatge i no es realitzarà cap canvi.</p>
eventLoginErrorSubject=Fallada en l''inici de sessió
eventLoginErrorBody=S''ha detectat un intent d''accés fallit al vostre compte el {0} des de {1}. Si no en sabeu res, contacteu amb l''administrador.
eventLoginErrorBodyHtml=<p>S''ha detectat un intent d''accés fallit al vostre compte el {0} des de {1}. Si no en sabeu res, contacteu amb l''administrador.</p>
eventRemoveTotpSubject=Eliminació de l''OTP
eventRemoveTotpBody=S''ha eliminat l''OTP del vostre compte el {0} des de {1}. Si no en sabeu res, contacteu amb l''administrador.
eventRemoveTotpBodyHtml=<p>S''ha eliminat l''OTP del vostre compte el {0} des de {1}. Si en sabeu res, contacteu amb l''administrador.</p>
eventUpdatePasswordSubject=Actualització de la contrasenya
eventUpdatePasswordBody=S''ha actualitzat la vostra contrasenya el {0} des de {1}. Si no en sabeu res, contacteu amb l''administrador.
eventUpdatePasswordBodyHtml=<p>S''ha actualitzat la vostra contrasenya el {0} des de {1}. Si no en sabeu res, contacteu amb l''administrador.</p>
eventUpdateTotpSubject=Actualització de l''OTP
eventUpdateTotpBody=S''ha actualitzat l''OTP al vostre compte el {0} des de {1}. Si no en sabeu res, contacteu amb l''administrador.
eventUpdateTotpBodyHtml=<p>S''ha actualitzat l''OTP al vostre compte el {0} des de {1}. Si no en sabeu res, contacteu amb l''administrador.</p>
requiredAction.CONFIGURE_TOTP=Configura l''OTP
requiredAction.TERMS_AND_CONDITIONS=Termes i condicions
requiredAction.UPDATE_PASSWORD=Actualitza la contrasenya
requiredAction.UPDATE_PROFILE=Actualitza el perfil
requiredAction.VERIFY_EMAIL=Verifica el correu electrònic
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Genera codis de recuperació

# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be ''{0,choice,0#minut|1#minuta|2#minuty|2<minut}''
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#segons|1#segon|1<segons}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#minuts|1#minut|1<minuts}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#hores|1#hora|1<hores}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#dies|1#dia|1<dies}
emailVerificationBodyCode=Verifiqueu el vostre correu electrònic introduint el codi següent.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Verifiqueu el vostre correu electrònic introduint el codi següent.</p><p> <b>{0}</b></p>


orgInviteBodyPersonalized=Hola, {5} {6}.\n\nHeu estat convidat a unir-vos a l’organització «{3}». Feu clic a l’enllaç següent per a unir-vos-hi.\n\n{0}\n\nAquest enllaç caducarà el {4}.\n\nSi no us voleu unir a l’organització, podeu ignorar aquest missatge.
orgInviteBodyHtml=<p>Heu estat convidat a unir-vos a l''organització «{3}». Feu clic a l''enllaç següent per a unir-vos-hi.</p><p><a href="{0}">Enllaç per a unir-se a l''organització</a></p><p>Aquest enllaç caducarà el {4}.</p><p>Si no us voleu unir a l''organització, podeu ignorar aquest missatge.</p>
orgInviteBody=Heu estat convidat a unir-vos a l’organització «{3}». Feu clic a l’enllaç següent per a unir-vos-hi.\n\n{0}\n\nAquest enllaç caducarà el {4}.\n\nSi no us voleu unir a l’organització, podeu ignorar aquest missatge.
orgInviteBodyPersonalizedHtml=<p>Hola, {5} {6}</p><p>Heu estat convidat a unir-vos a l’organització «{3}». Feu clic a l’enllaç següent per a unir-vos-hi.</p><p><a href="{0}">Enllaç per a unir-se a l’organització</a></p><p>Aquest enllaç caducarà el {4}.</p><p>Si no us voleu unir a l’organització, podeu ignorar aquest missatge.</p>
orgInviteSubject=Invitació per a unir-vos a l''organització {0}
eventUpdateCredentialSubject=Actualització de la credencial
eventRemoveCredentialSubject=Supressió de la credencial
eventUserDisabledByTemporaryLockoutSubject=L''usuari és inhabilitat per un bloqueig temporal
eventUserDisabledByPermanentLockoutSubject=L''usuari és inhabilitat per un bloqueig permanent
eventUserDisabledByTemporaryLockoutBody=El vostre usuari ha estat inhabilitat temporalment per múltiples intents fallits en {0}. Contacteu amb un administrador si ho necessiteu.
eventUpdateCredentialBody=S''han canviat les vostres credencials {0} el {1} des de {2}. Si no en sabeu res, contacteu amb l''administrador.
eventUserDisabledByTemporaryLockoutHtml=<p>El vostre usuari ha estat inhabilitat temporalment per múltiples intents fallits en {0}. Contacteu amb un administrador si ho necessiteu.</p>
eventUserDisabledByPermanentLockoutHtml=<p>El vostre usuari ha estat inhabilitat permanentment per múltiples intents fallits en {0}. Contacteu amb un administrador.</p>
eventUpdateCredentialBodyHtml=<p>S''han canviat les vostres credencials {0} el {1} des de {2}. Si no en sabeu res, contacteu amb l''administrador.</p>
eventUserDisabledByPermanentLockoutBody=El vostre usuari ha estat inhabilitat permanentment per múltiples intents fallits en {0}. Contacteu amb un administrador.
eventRemoveCredentialBody=S''ha suprimit la credencial {0} del vostre compte el {1} des de {2}. Si no en sabeu res, contacteu amb un administrador.
eventRemoveCredentialBodyHtml=<p>S''ha suprimit la credencial {0} del vostre compte el {1} des de {2}. Si no en sabeu res, contacteu amb un administrador.</p>
