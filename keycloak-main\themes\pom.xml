<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <artifactId>keycloak-parent</artifactId>
        <groupId>org.keycloak</groupId>
        <version>999.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>keycloak-themes</artifactId>
    <name>Keycloak Themes</name>
    <description />
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-admin-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-account-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-themes-vendor</artifactId>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.keycloak</groupId>
                <artifactId>theme-verifier-maven-plugin</artifactId>
                <version>${project.version}</version>
                <executions>
                    <execution>
                        <id>verify-theme</id>
                        <goals>
                            <goal>verify-theme</goal>
                        </goals>
                        <configuration>
                            <validateMessageFormatQuotes>true</validateMessageFormatQuotes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>communityTranslations</id>
            <activation>
                <property>
                    <name>!skipCommunityTranslations</name>
                </property>
            </activation>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources-community</directory>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>
</project>
