invalidPasswordMinLengthMessage=Senha inválida: ta<PERSON><PERSON> {0}.
invalidPasswordMinLowerCaseCharsMessage=Senha inválida: deve conter pelo menos {0} letra(s) minúscula(s).
invalidPasswordMinDigitsMessage=Senha inválida: deve conter pelo menos {0} número(s).
invalidPasswordMinUpperCaseCharsMessage=Senha inválida: deve conter pelo menos {0} letra(s) maiúscula(s).
invalidPasswordMinSpecialCharsMessage=Senha inválida: deve conter pelo menos {0} caractere(s) especial(is).
invalidPasswordNotUsernameMessage=Senha inválida: não pode ser igual ao nome de usuário.
invalidPasswordNotContainsUsernameMessage=Senha inválida: não pode conter o nome de usuário.
invalidPasswordRegexPatternMessage=Senha inválida: não corresponde ao(s) padrão(ões) de expressão regular.
invalidPasswordHistoryMessage=Senha inválida: não pode ser igual a nenhuma da(s) última(s) {0} senha(s).
ldapErrorInvalidCustomFilter=Filtro LDAP não inicia com "(" ou não termina com ")".
ldapErrorMissingClientId=O ID do cliente deve ser fornecido na configuração quando o mapeamento de papéis do domínio não for utilizado.
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Não é possível preservar herança de grupos e usar tipo de associação de UID ao mesmo tempo.
ldapErrorCantWriteOnlyForReadOnlyLdap=Não é possível definir modo de somente escrita quando o provedor LDAP não suporta escrita
ldapErrorCantWriteOnlyAndReadOnly=Não é possível definir somente escrita e somente leitura ao mesmo tempo
clientRedirectURIsFragmentError=URIs de redirecionamento não podem conter fragmentos
clientRootURLFragmentError=URL raiz não pode conter fragmentos
invalidPasswordMaxLengthMessage=Senha inválida: tamanho máximo {0}.
invalidPasswordNotEmailMessage=Senha inválida: não pode ser igual ao email.
invalidPasswordBlacklistedMessage=Senha inválida: esta senha está na lista de exclusão.
invalidPasswordGenericMessage=Senha inválida: a nova senha não cumpre as políticas de senha.
client_broker=Intermediário
error-invalid-blank=Por favor, especifique um valor.
error-empty=Por favor, especifique um valor.
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=Validar Política de Senha é aplicável somente com modo de edição GRAVÁVEL
pairwiseClientRedirectURIsMissingHost=As URIs de redirecionamento do cliente devem conter um componente de host válido.
pairwiseRedirectURIsMismatch=As URIs de redirecionamento do cliente não correspondem às URIs de redirecionamento obtidas a partir da URI de Identificador de Setor.
client_account=Conta
client_account-console=Console da Conta
client_security-admin-console=Console de Administração de Segurança
client_admin-cli=CLI de Administração
client_realm-management=Gerenciamento de Domínio
ldapErrorEditModeMandatory=Modo de Edição é obrigatório
ldapErrorConnectionTimeoutNotNumber=Tempo de Expiração da Conexão deve ser um número
ldapErrorReadTimeoutNotNumber=Tempo de expiração da Leitura deve ser um número
ldapErrorCantEnableStartTlsAndConnectionPooling=Não é possível habilitar StartTLS e pool de conexões ao mesmo tempo.
ldapErrorCantEnableUnsyncedAndImportOff=Não é possível desabilitar a importação de usuários quando o modo do provedor LDAP está como NÃO SINCRONIZADO
ldapErrorMissingGroupsPathGroup=O grupo no caminho especificado não existe – por favor, crie o grupo no caminho indicado primeiro
clientRootURLIllegalSchemeError=URL raiz usa um esquema inválido
clientBaseURLIllegalSchemeError=URL base usa um esquema inválido
backchannelLogoutUrlIllegalSchemeError=URL de logout de backchannel usa um esquema inválido
clientRedirectURIsIllegalSchemeError=Uma URI de redirecionamento usa um esquema inválido
clientBaseURLInvalid=A URL base não é uma URL válida
clientRootURLInvalid=A URL raiz não é uma URL válida
clientRedirectURIsInvalid=Uma URI de redirecionamento não é uma URI válida
backchannelLogoutUrlIsInvalid=URL de logout de backchannel não é uma URL válida
pairwiseMalformedClientRedirectURI=Cliente continha uma URI de redirecionamento inválida.
pairwiseClientRedirectURIsMultipleHosts=Sem uma URI de identificador de setor configurada, as URIs de redirecionamento do cliente não devem conter múltiplos componentes de host.
pairwiseMalformedSectorIdentifierURI=URI de Identificador de Setor Malformada.
pairwiseFailedToGetRedirectURIs=Falha ao obter as URIs de redirecionamento a partir da URI de Identificador de Setor.
duplicatedJwksSettings=As opções "Usar JWKS" e "Usar URL de JWKS" não podem ser ativadas ao mesmo tempo.
error-invalid-value=Valor inválido.
error-invalid-length=O atributo {0} precisa ter um tamanho entre {1} e {2} caracteres.
error-pattern-no-match=Valor inválido.
error-invalid-length-too-short=O atributo {0} deve ter comprimento mínimo de {1}.
error-invalid-length-too-long=O atributo {0} precisa ter um tamanho mínimo de {2} caracteres.
error-invalid-email=Endereço de email inválido.
error-invalid-number=Número inválido.
error-number-out-of-range=O atributo {0} deve ser um número entre {1} e {2}.
error-number-out-of-range-too-small=O atributo {0} precisa ter um valor mínimo de {1}.
error-number-out-of-range-too-big=O atributo {0} precisa ter um valor máximo de {2}.
error-invalid-uri=URL inválida.
error-invalid-uri-scheme=Esquema da URL inválido.
error-invalid-uri-fragment=Fragmento da URL inválido.
error-user-attribute-required=Por favor, informe o atributo {0}.
error-invalid-date=O atributo {0} é uma data inválida.
error-user-attribute-read-only=O atributo {0} is somente leitura.
error-username-invalid-character={0} contém caractere inválido.
error-person-name-invalid-character={0} contém caractere inválido.
error-invalid-multivalued-size=O atributo {0} deve ter no mínimo {1} e no máximo {2} {2,choice,0#values|1#value|1<values}.
