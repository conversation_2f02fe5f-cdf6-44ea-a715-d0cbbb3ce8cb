doLogIn=Connexion
doRegister=Enregistrement
doRegisterSecurityKey=Enregistrement
doCancel=Annuler
doSubmit=Soumettre
doBack=Retour
doYes=Oui
doNo=Non
doContinue=Continuer
doIgnore=Ignorer
doAccept=Accepter
doDecline=Décliner
doForgotPassword=Mot de passe oublié ?
doClickHere=Cliquez ici
doImpersonate=Emprunter l''identité
doTryAgain=Réessayer
doTryAnotherWay=Essayer une autre méthode
doConfirmDelete=Confirmer la suppression
errorDeletingAccount=Erreur lors de la suppression du compte
deletingAccountForbidden=Permissions insuffisantes pour supprimer votre propre compte, contactez un administrateur.
kerberosNotConfigured=Kerberos non configuré
kerberosNotConfiguredTitle=Kerberos non configuré
bypassKerberosDetail=Si vous n''êtes pas connecté via Kerberos ou bien que votre navigateur n''est pas configuré pour la connexion via Kerberos. Veuillez cliquer pour vous connecter via un autre moyen
kerberosNotSetUp=Kerberos n''est pas configuré. Connexion impossible.
registerTitle=S''enregistrer
loginAccountTitle=Connectez-vous à votre compte
loginTitle=Se connecter à {0}
loginTitleHtml={0}
impersonateTitle={0} utilisateur usurpé
impersonateTitleHtml=<strong>{0}</strong> utilisateur usurpé
realmChoice=Domaine
unknownUser=Utilisateur inconnu
loginTotpTitle=Configuration de l''authentification par mobile
loginProfileTitle=Mise à jour du compte
loginIdpReviewProfileTitle=Vérifiez vos informations de profil
loginTimeout=Le temps imparti pour la connexion est écoulé. Le processus de connexion redémarre depuis le début.
oauthGrantTitle=Accorder l''accès à {0}
oauthGrantTitleHtml={0}
oauthGrantInformation=Assurez-vous de faire confiance à {0} en apprenant comment {0} traitera vos données.
oauthGrantReview=Vous pourriez examiner
oauthGrantTos=les conditions générales d''utilisation.
oauthGrantPolicy=la politique de confidentialité.
errorTitle=Nous sommes désolés...
errorTitleHtml=Nous sommes <strong>désolés</strong>...
emailVerifyTitle=Vérification du courriel
emailForgotTitle=Mot de passe oublié ?
updateEmailTitle=Mise à jour du courriel
emailUpdateConfirmationSentTitle=Courriel de confirmation envoyé
emailUpdateConfirmationSent=Un courriel de confirmation a été envoyé à {0}. Vous devez suivre les instructions de ce dernier afin de compléter la mise à jour.
emailUpdatedTitle=Adresse courriel mise à jour
emailUpdated=La mise à jour de votre adresse courriel vers {0} a été complétée avec succès.
updatePasswordTitle=Mise à jour du mot de passe
codeSuccessTitle=Code succès
codeErrorTitle=Code d''erreur \: {0}
displayUnsupported=Type d''affichage demandé non supporté
browserRequired=Navigateur requis pour se connecter
browserContinue=Navigateur requis pour continuer la connexion
browserContinuePrompt=Ouvrir le navigateur et continuer la connexion ? [y/n] :
browserContinueAnswer=y
termsTitle=Termes et Conditions
termsText=<p>Termes et conditions à définir</p>
termsPlainText=Termes et conditions à définir.
termsAcceptanceRequired=Vous devez accepter les termes et conditions.
acceptTerms=J''accepte les termes et conditions
recaptchaFailed=Re-captcha invalide
recaptchaNotConfigured=Re-captcha est requis, mais il n''est pas configuré
consentDenied=Consentement refusé.
noAccount=Nouvel utilisateur ?
username=Nom d''utilisateur
usernameOrEmail=Nom d''utilisateur ou courriel
firstName=Prénom
givenName=Prénom
fullName=Nom complet
lastName=Nom
familyName=Nom de famille
email=Courriel
password=Mot de passe
passwordConfirm=Confirmation du mot de passe
passwordNew=Nouveau mot de passe
passwordNewConfirm=Confirmation du nouveau mot de passe
rememberMe=Se souvenir de moi
authenticatorCode=Code à usage unique
address=Adresse
street=Rue
locality=Ville ou Localité
region=État, Province ou Région
postal_code=Code postal
country=Pays
emailVerified=Courriel vérifié
website=Page web
phoneNumber=Numéro de téléphone
phoneNumberVerified=Numéro de téléphone vérifié
gender=Sexe
birthday=Date de naissance
zoneinfo=Fuseau horaire
gssDelegationCredential=Accréditation de délégation GSS
logoutOtherSessions=Se déconnecter des autres appareils
profileScopeConsentText=Profil utilisateur
emailScopeConsentText=Courriel
addressScopeConsentText=Adresse
phoneScopeConsentText=Numéro de téléphone
offlineAccessScopeConsentText=Accès hors-ligne
samlRoleListScopeConsentText=Mes rôles
rolesScopeConsentText=Rôles utilisateur
restartLoginTooltip=Redémarrer la connexion
loginTotpIntro=Il est nécessaire de configurer un générateur One Time Password pour accéder à ce compte
loginTotpStep1=Installez une des applications suivantes sur votre mobile :
loginTotpStep2=Ouvrez l''application et scannez le code-barres :
loginTotpStep3=Entrez le code à usage unique fourni par l''application et cliquez sur Sauvegarder pour terminer.
loginTotpStep3DeviceName=Renseignez un nom d''appareil pour vous aider à gérer vos appareils OTP.
loginTotpManualStep2=Ouvrez l''application et saisissez la clé :
loginTotpManualStep3=Utilisez la configuration de valeur suivante si l''application permet son édition :
loginTotpUnableToScan=Impossible de scanner ?
loginTotpScanBarcode=Scanner le code-barres ?
loginCredential=Accréditation
loginOtpOneTime=Code à usage unique
loginTotpType=Type
loginTotpAlgorithm=Algorithme
loginTotpDigits=Chiffres
loginTotpInterval=Intervalle
loginTotpCounter=Compteur
loginTotpDeviceName=Nom d''appareil
loginTotp.totp=Basé sur le temps
loginTotp.hotp=Basé sur les compteurs
loginChooseAuthenticator=Sélectionnez une méthode de connexion
oauthGrantRequest=Voulez-vous accorder ces privilèges d''accès ?
inResource=dans
oauth2DeviceVerificationTitle=Connexion de l''appareil
verifyOAuth2DeviceUserCode=Entrez le code à usage unique fourni par votre appareil et cliquez sur Soumettre
oauth2DeviceInvalidUserCodeMessage=Code invalide, veuillez réessayer.
oauth2DeviceExpiredUserCodeMessage=Le code a expiré. Veuillez réessayer de vous connecter depuis votre appareil.
oauth2DeviceVerificationCompleteHeader=Connexion de l''appareil réussie
oauth2DeviceVerificationCompleteMessage=Vous pouvez fermer cette fenêtre de navigateur et retourner sur votre appareil.
oauth2DeviceVerificationFailedHeader=Échec de la connexion au dispositif
oauth2DeviceVerificationFailedMessage=Vous pouvez fermer cette fenêtre de navigateur et retourner sur votre appareil, puis réessayer de vous connecter.
oauth2DeviceConsentDeniedMessage=Consentement refusé pour connecter l''appareil.
oauth2DeviceAuthorizationGrantDisabledMessage=Le client n''est pas autorisé à initier OAuth 2.0 Device Authorization Grant. Le flux est désactivé pour ce client.
emailVerifyInstruction1=Un courriel avec des instructions à suivre a été envoyé à votre adresse {0}.
emailVerifyInstruction2=Vous n''avez pas reçu de code dans le courriel ?
emailVerifyInstruction3=pour renvoyer le courriel.
emailLinkIdpTitle=Association avec {0}
emailLinkIdp1=Un courriel avec des instructions pour associer le compte {1} sur {0} avec votre compte {2} vous a été envoyé.
emailLinkIdp2=Vous n''avez pas reçu de code dans le courriel ?
emailLinkIdp3=pour renvoyer le courriel.
emailLinkIdp4=Si vous avez déjà vérifié votre courriel dans un autre navigateur
emailLinkIdp5=pour continuer.
backToLogin=&laquo; Retour à la connexion
emailInstruction=Entrez votre nom d''utilisateur ou votre courriel ; un courriel va vous être envoyé vous permettant de créer un nouveau mot de passe.
emailInstructionUsername=Entrez votre nom d''utilisateur ; un courriel va vous être envoyé vous permettant de créer un nouveau mot de passe.
copyCodeInstruction=Copiez le code et recopiez-le dans votre application :
pageExpiredTitle=La page a expiré
pageExpiredMsg1=Pour recommencer le processus d''authentification
pageExpiredMsg2=Pour continuer le processus d''authentification
personalInfo=Information personnelle :
role_admin=Administrateur
role_realm-admin=Administrateur du domaine
role_create-realm=Créer un domaine
role_create-client=Créer un client
role_view-realm=Voir un domaine
role_view-users=Voir les utilisateurs
role_view-applications=Voir les applications
role_view-clients=Voir les clients
role_view-events=Voir les événements
role_view-identity-providers=Voir les fournisseurs d''identité
role_manage-realm=Gérer le domaine
role_manage-users=Gérer les utilisateurs
role_manage-applications=Gérer les applications
role_manage-identity-providers=Gérer les fournisseurs d''identité
role_manage-clients=Gérer les clients
role_manage-events=Gérer les événements
role_view-profile=Voir le profil
role_manage-account=Gérer le compte
role_manage-account-links=Gérer les liens de compte
role_read-token=Lire le jeton d''authentification
role_offline-access=Accès hors-ligne
client_account=Compte
client_account-console=Console de gestion du compte
client_security-admin-console=Console d''administration de la sécurité
client_admin-cli=Admin CLI
client_realm-management=Gestion du domaine
client_broker=Broker
requiredFields=Champs requis
invalidUserMessage=Nom d''utilisateur ou mot de passe invalide.
invalidUsernameMessage=Nom d''utilisateur invalide.
invalidUsernameOrEmailMessage=Nom d''utilisateur ou courriel invalide.
invalidPasswordMessage=Mot de passe invalide.
invalidEmailMessage=Courriel invalide.
accountDisabledMessage=Compte désactivé, contactez votre administrateur.
accountTemporarilyDisabledMessage=Nom d''utilisateur ou mot de passe invalide.
accountPermanentlyDisabledMessage=Nom d''utilisateur ou mot de passe invalide.
accountTemporarilyDisabledMessageTotp=Le code d''authentification est invalide.
accountPermanentlyDisabledMessageTotp=Le code d''authentification est invalide.
expiredCodeMessage=Connexion expirée. Veuillez vous reconnecter.
expiredActionMessage=Action expirée. Merci de continuer la connexion.
expiredActionTokenNoSessionMessage=Action expirée.
expiredActionTokenSessionExistsMessage=Action expirée. Merci de recommencer.
missingFirstNameMessage=Veuillez entrer votre prénom.
missingLastNameMessage=Veuillez entrer votre nom.
missingEmailMessage=Veuillez entrer votre courriel.
missingUsernameMessage=Veuillez entrer votre nom d''utilisateur.
missingPasswordMessage=Veuillez entrer votre mot de passe.
missingTotpMessage=Veuillez entrer votre code d''authentification.
missingTotpDeviceNameMessage=Veuillez entrer le nom de votre appareil.
notMatchPasswordMessage=Les mots de passe ne sont pas identiques.
error-invalid-value=Valeur invalide.
error-invalid-blank=Veuillez entrer une valeur.
error-empty=Veuillez entrer une valeur.
error-invalid-length=La longueur doit être entre {1} et {2}.
error-invalid-length-too-short=La longueur minimale est {1}.
error-invalid-length-too-long=La longueur maximale est {2}.
error-invalid-email=Courriel invalide.
error-invalid-number=Nombre invalide.
error-number-out-of-range=Le nombre doit être entre {1} et {2}.
error-number-out-of-range-too-small=Le nombre doit avoir une valeur minimale de {1}.
error-number-out-of-range-too-big=Le nombre doit avoir une valeur maximale de {2}.
error-pattern-no-match=Valeur invalide.
error-invalid-uri=URL invalide.
error-invalid-uri-scheme=Schéma d''URL invalide.
error-invalid-uri-fragment=Fragment d''URL invalide.
error-user-attribute-required=Veuillez renseigner ce champ.
error-invalid-date=Date invalide.
error-user-attribute-read-only=Ce champ est en lecture seule.
error-username-invalid-character=La valeur contient des caractères invalides.
error-person-name-invalid-character=La valeur contient des caractères invalides.
invalidPasswordExistingMessage=Mot de passe existant invalide.
invalidPasswordBlacklistedMessage=Mot de passe invalide : ce mot de passe est blacklisté.
invalidPasswordConfirmMessage=Le mot de passe de confirmation ne correspond pas.
invalidTotpMessage=Le code d''authentification est invalide.
usernameExistsMessage=Le nom d''utilisateur existe déjà.
emailExistsMessage=Le courriel existe déjà.
federatedIdentityExistsMessage=L''utilisateur avec {0} {1} existe déjà. Veuillez accéder à au gestionnaire de compte pour lier le compte.
confirmLinkIdpTitle=Ce compte existe déjà
federatedIdentityConfirmLinkMessage=L''utilisateur {0} {1} existe déjà. Que souhaitez-vous faire ?
federatedIdentityConfirmReauthenticateMessage=Identifiez vous afin de lier votre compte avec {0}
nestedFirstBrokerFlowMessage=L''utilisateur {0} {1} n''est lié à aucun utilisateur connu.
confirmLinkIdpReviewProfile=Vérifiez vos informations de profil
confirmLinkIdpContinue=Souhaitez-vous lier {0} à votre compte existant
configureTotpMessage=Vous devez configurer l''authentification par mobile pour activer votre compte.
updateProfileMessage=Vous devez mettre à jour votre profil pour activer votre compte.
updatePasswordMessage=Vous devez changer votre mot de passe pour activer votre compte.
updateEmailMessage=Vous devez mettre à jour votre adresse de courriel pour activer votre compte.
resetPasswordMessage=Vous devez changer votre mot de passe.
verifyEmailMessage=Vous devez vérifier votre courriel pour activer votre compte.
linkIdpMessage=Vous devez vérifier votre courriel pour lier votre compte avec {0}.
emailSentMessage=Vous devriez recevoir rapidement un courriel avec de plus amples instructions.
emailSendErrorMessage=Erreur lors de l''envoi du courriel, veuillez essayer plus tard.
accountUpdatedMessage=Votre compte a été mis à jour.
accountPasswordUpdatedMessage=Votre mot de passe a été mis à jour.
delegationCompleteHeader=Connexion réussie
delegationCompleteMessage=Vous pouvez fermer cette fenêtre de navigateur et retourner sur votre application console.
delegationFailedHeader=Connexion échouée
delegationFailedMessage=Vous pouvez fermer cette fenêtre de navigateur et retourner sur votre application console, puis réessayer de vous connecter.
noAccessMessage=Aucun accès
invalidPasswordMinLengthMessage=Mot de passe invalide : longueur minimale requise de {0}.
invalidPasswordMaxLengthMessage=Mot de passe invalide : longueur maximale de {0}.
invalidPasswordMinDigitsMessage=Mot de passe invalide : doit contenir au moins {0} chiffre(s).
invalidPasswordMinLowerCaseCharsMessage=Mot de passe invalide : doit contenir au moins {0} lettre(s) en minuscule.
invalidPasswordMinUpperCaseCharsMessage=Mot de passe invalide : doit contenir au moins {0} lettre(s) en majuscule.
invalidPasswordMinSpecialCharsMessage=Mot de passe invalide : doit contenir au moins {0} caractère(s) spéciaux.
invalidPasswordNotUsernameMessage=Mot de passe invalide : ne doit pas être identique au nom d''utilisateur.
invalidPasswordNotEmailMessage=Mot de passe invalide : ne doit pas être identique au courriel.
invalidPasswordRegexPatternMessage=Mot de passe invalide : ne valide pas l''expression rationnelle.
invalidPasswordHistoryMessage=Mot de passe invalide : ne doit pas être égal aux {0} derniers mots de passe.
invalidPasswordGenericMessage=Mot de passe invalide : le nouveau mot de passe ne répond pas à la politique de mot de passe.
failedToProcessResponseMessage=Erreur lors du traitement de la réponse
httpsRequiredMessage=Le protocole HTTPS est requis
realmNotEnabledMessage=Le domaine n''est pas activé
invalidRequestMessage=Requête invalide
successLogout=Vous êtes déconnecté
failedLogout=La déconnexion a échoué
unknownLoginRequesterMessage=Compte inconnu du demandeur
loginRequesterNotEnabledMessage=La connexion du demandeur n''est pas active
bearerOnlyMessage=Les applications Bearer-only ne sont pas autorisées à initier la connexion par navigateur
standardFlowDisabledMessage=Le client n''est pas autorisé à initier une connexion avec le navigateur avec ce response_type. Le flux standard est désactivé pour le client.
implicitFlowDisabledMessage=Le client n''est pas autorisé à initier une connexion avec le navigateur avec ce response_type. Le flux implicite est désactivé pour le client.
invalidRedirectUriMessage=L''URI de redirection est invalide
unsupportedNameIdFormatMessage=NameIDFormat non supporté
invalidRequesterMessage=Demandeur invalide
registrationNotAllowedMessage=L''enregistrement n''est pas autorisé
resetCredentialNotAllowedMessage=La remise à zéro n''est pas autorisée
permissionNotApprovedMessage=La permission n''est pas approuvée.
noRelayStateInResponseMessage=Aucun état de relais dans la réponse du fournisseur d''identité.
insufficientPermissionMessage=Permissions insuffisantes pour lier les identités.
couldNotProceedWithAuthenticationRequestMessage=Impossible de continuer avec la requête d''authentification vers le fournisseur d''identité.
couldNotObtainTokenMessage=Impossible de récupérer le jeton du fournisseur d''identité.
unexpectedErrorRetrievingTokenMessage=Erreur inattendue lors de la récupération du jeton provenant du fournisseur d''identité.
unexpectedErrorHandlingResponseMessage=Erreur inattendue lors du traitement de la réponse provenant du fournisseur d''identité.
identityProviderAuthenticationFailedMessage=L''authentification a échoué. Impossible de s''authentifier avec le fournisseur d''identité.
couldNotSendAuthenticationRequestMessage=Impossible d''envoyer la requête d''authentification vers le fournisseur d''identité.
unexpectedErrorHandlingRequestMessage=Erreur inattendue lors du traitement de la requête vers le fournisseur d''identité.
invalidAccessCodeMessage=Code d''accès invalide.
sessionNotActiveMessage=La session n''est pas active.
invalidCodeMessage=Une erreur est survenue, veuillez vous reconnecter à votre application.
cookieNotFoundMessage=Cookie introuvable. Assurez-vous que les cookies soient activés dans votre navigateur.
identityProviderUnexpectedErrorMessage=Erreur inattendue lors de l''authentification avec le fournisseur d''identité
identityProviderMissingStateMessage=Paramètre d''état manquant dans la réponse du fournisseur d''identité.
identityProviderNotFoundMessage=Impossible de trouver le fournisseur d''identité avec cet identifiant.
identityProviderLinkSuccess=Votre compte a été correctement lié avec {0} compte {1} .
staleCodeMessage=Cette page n''est plus valide, merci de retourner dans votre application et de vous reconnecter
realmSupportsNoCredentialsMessage=Ce domaine ne supporte aucun type d''accréditation.
credentialSetupRequired=Connexion impossible, configuration d''accréditation requise.
identityProviderNotUniqueMessage=Ce domaine autorise plusieurs fournisseurs d''identité. Impossible de déterminer le fournisseur d''identité avec lequel s''authentifier.
emailVerifiedMessage=Votre courriel a été vérifié.
staleEmailVerificationLink=Le lien que vous avez cliqué est périmé et n''est plus valide. Peut-être, vous avez déjà vérifié votre mot de passe.
identityProviderAlreadyLinkedMessage=L''identité fédérée retournée par {0} est déjà liée à un autre utilisateur.
confirmAccountLinking=Confirmez la liaison du compte {0} du fournisseur d''entité {1} avec votre compte.
confirmEmailAddressVerification=Confirmez la validité de l''adresse courriel {0}.
confirmExecutionOfActions=Suivez les instructions suivantes


backToApplication=&laquo; Revenir à l''application
missingParameterMessage=Paramètres manquants \: {0}
clientNotFoundMessage=Client inconnu.
clientDisabledMessage=Client désactivé.
invalidParameterMessage=Paramètre invalide \: {0}
alreadyLoggedIn=Vous êtes déjà connecté.
differentUserAuthenticated=Vous êtes déjà authentifié avec un autre utilisateur ''{0}'' dans cette session. Merci de vous déconnecter.
brokerLinkingSessionExpired=La liaison entre comptes broker a été demandée, mais la session n''est plus valide.
proceedWithAction=» Cliquez ici


requiredAction.CONFIGURE_TOTP=Configurer OTP
requiredAction.TERMS_AND_CONDITIONS=Termes et conditions
requiredAction.UPDATE_PASSWORD=Mettre à jour votre mot de passe
requiredAction.UPDATE_PROFILE=Mettre à jour votre profil
requiredAction.VERIFY_EMAIL=Valider votre adresse email


doX509Login=Vous allez être connecté en tant que  :
clientCertificate=Certificat client X509 :
noCertificate=[Pas de certificat]


pageNotFound=Page non trouvée
internalServerError=Une erreur interne du serveur s''est produite

# Identity provider
identity-provider-redirector=Connexion avec un autre fournisseur d''identité
identity-provider-login-label=Ou se connecter avec
idp-email-verification-display-name=Vérification du courriel
idp-email-verification-help-text=Lier votre compte en validant votre courriel.
idp-username-password-form-display-name=Nom d''utilisateur et mot de passe
idp-username-password-form-help-text=Lier votre compte en vous connectant.
console-username=Nom d''utilisateur :
console-password=Mot de passe :
console-otp=Code à usage unique :
console-new-password=Nouveau mot de passe :
console-confirm-password=Confirmez le mot de passe :
console-update-password=La mise à jour de votre mot de passe est requise.
console-verify-email=Vous devez vérifier votre adresse courriel. Nous avons envoyé un courriel à {0} contenant un code de vérification. Veuillez saisir ce code ci-dessous.
console-email-code=Code courriel :
console-accept-terms=Accepter les conditions ? [o/n] :
console-accept=o

#authenticators
password-display-name=Mot de passe
password-help-text=Connectez-vous en saisissant votre mot de passe.
auth-username-form-display-name=Nom d''utilisateur
auth-username-form-help-text=Commencez la connexion en saisissant votre nom d''utilisateur
auth-username-password-form-display-name=Nom d''utilisateur et mot de passe
auth-username-password-form-help-text=Connectez-vous en saisissant votre nom d''utilisateur et votre mot de passe.

# WebAuthn
webauthn-display-name=Clé de Sécurité
webauthn-help-text=Utilisez votre clé de sécurité pour vous connecter.
webauthn-passwordless-display-name=Clé de Sécurité
webauthn-passwordless-help-text=Utilisez votre clé de sécurité pour vous connecter sans mot de passe.
webauthn-login-title=Connexion avec une Clé de Sécurité
webauthn-registration-title=Enregistrement d''une Clé de Sécurité
webauthn-available-authenticators=Clés de Sécurité disponibles
webauthn-unsupported-browser-text=WebAuthn n''est pas supporté par ce navigateur. Essayez une autre méthode ou contactez votre administrateur.
webauthn-doAuthenticate=Se connecter avec une Clé de Sécurité
webauthn-createdAt-label=Créé le

# WebAuthn Error
webauthn-error-title=Erreur lors de l''utilisation de la Clé de Sécurité
webauthn-error-registration=L''enregistrement de la Clé de Sécurité a échoué.<br /> {0}
webauthn-error-api-get=L''authentification via la Clé de Sécurité a échoué.<br /> {0}
webauthn-error-different-user=Le premier utilisateur authentifié ne correspond pas à celui qui est authentifié par la Clé de Sécurité.
webauthn-error-auth-verification=Le résultat de l''authentification produite par la clé de sécurité est invalide.<br /> {0}
webauthn-error-register-verification=Le résultat de l''enregistrement de la clé de sécurité est invalide.<br /> {0}
webauthn-error-user-not-found=La Clé de Sécurité a authentifié un utilisateur inconnu.
finalDeletionConfirmation=Si vous supprimez votre compte, il ne pourra pas être restauré. Pour conserver votre compte, cliquez sur Annuler.
irreversibleAction=Cette action est irréversible
deleteAccountConfirm=Confirmation de suppression de compte
deletingImplies=Supprimer votre compte implique :
errasingData=Supprimer toutes vos données
loggingOutImmediately=Vous déconnecter immédiatement
accountUnusable=Toute utilisation future de l''application sera impossible avec ce compte
userDeletedSuccessfully=Utilisateur supprimé avec succès
access-denied=Accès refusé
frontchannel-logout.title=Déconnexion
frontchannel-logout.message=Vous êtes déconnecté des applications suivantes
logoutConfirmTitle=Déconnexion
logoutConfirmHeader=Êtes-vous sûr de vouloir vous déconnecter ?
doLogout=Se déconnecter
authenticateStrong=Une authentification forte est nécessaire pour continuer
bluetooth=Bluetooth
identityProviderLogoutFailure=Échec de la déconnexion du fournisseur d''identité SAML
federatedIdentityUnavailableMessage=L''utilisateur {0} authentifié depuis le fournisseur d''identité {1} n''existe pas. Veuillez contacter votre administrateur.
emailVerifiedAlreadyMessage=Votre courriel a déjà été vérifié.
openshift.scope.user_check-access=Information sur les accès de l''utilisateur
openshift.scope.user_full=Accès complet
otp-help-text=Renseignez le code de vérification affiché par votre application d''authentification.
readOnlyUsernameMessage=Vous ne pouvez pas mettre à jour votre nom d''utilisateur, il est en lecture seule.
organization.confirm-membership.title=Vous allez rejoindre l''organisation ${kc.org.name}
reauthenticate=Merci de vous authentifier à nouveau pour continuer
usb=USB
nfc=NFC
totpAppFreeOTPName=FreeOTP
organizationScopeConsentText=Organisation
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator
sessionLimitExceeded=Il y a trop de sessions
error-reset-otp-missing-id=Merci de choisir une configuration OTP.
openshift.scope.user_info=Informations de l''utilisateur
configureBackupCodesMessage=Vous devez définir des codes de secours pour activer votre compte.
insufficientLevelOfAuthentication=Le niveau d''authentification requis n''a pas été atteint.
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Générer des codes de récupération
saml.post-form.js-disabled=JavaScript est désactivé. Il est fortement recommandé de l''activer. Cliquez sur le bouton ci-dessous pour continuer.
otp-display-name=Application d''authentification
recovery-codes-copied=Copiés
recovery-codes-error-invalid=Code de récupération d''authentification invalide
recovery-code-config-warning-message=Assurez-vous d''imprimer, télécharger ou copier ces codes dans un gestionnaire de mot de passe et de les conserver durablement. L''annulation de ce paramètre supprimera ces codes de récupération de votre compte.
recovery-codes-confirmation-message=J''ai enregistré ces codes dans un endroit sûr
confirmOverrideIdpContinue=Oui, remplacer le lien avec le compte actuel
invalidPasswordNotContainsUsernameMessage=Mot de passe invalide : il ne doit pas contenir le nom d''utilisateur.
auth-x509-client-username-form-display-name=Certificat X509
auth-x509-client-username-form-help-text=Se connecter avec un certificat client X509.
auth-recovery-authn-code-form-display-name=Code de récupération de l''authentification
auth-recovery-authn-code-form-help-text=Renseignez un code de récupération d''authentification depuis la liste générée précédemment.
recovery-code-config-header=Codes de récupération d''authentification
recovery-code-config-warning-title=Ces codes de récupération ne seront jamais réaffichés une fois que vous aurez quitté cette page
recovery-codes-print=Imprimer
recovery-codes-download=Télécharger
recovery-codes-copy=Copier
recovery-codes-action-cancel=Paramétrage annulé
recovery-codes-action-complete=Paramétrage terminé
recovery-codes-download-file-header=Conserver ces codes de récupération dans un endroit sûr.
recovery-codes-download-file-date=Ces codes ont été généré le
recovery-codes-label-default=Codes de récupération
organization.confirm-membership=En cliquant sur le lien ci-dessous, vous allez devenir membre de l''organisation {0} :
identityProviderInvalidResponseMessage=Réponse invalide du fournisseur d''identités.
identityProviderInvalidSignatureMessage=Signature invalide pour la réponse du fournisseur d''identités.
openshift.scope.list-projects=Liste des projets
saml.post-form.message=Redirection en cours, patientez.
otp-reset-description=Quelle configuration OTP doit être supprimée ?
internal=Interne
unknown=Inconnu
deleteCredentialTitle=Supprimer {0}
deleteCredentialMessage=Voulez-vous supprimer {0} ?
hidePassword=Cacher le mot de passe
showPassword=Montrer le mot de passe
federatedIdentityUnmatchedEssentialClaimMessage=Le jeton d''identité fourni par le fournisseur d''identité ne correspond pas à l''attribut essentiel configuré. Veuillez contacter votre administrateur.
identityProviderMissingCodeOrErrorMessage=Code manquant ou erreur de paramètre dans la réponse du fournisseur d''identités.
acrNotFulfilled=Exigences liées à l''authentification non remplies
invalidTokenRequiredActions=Des actions nécessaires incluses dans le lien ne sont pas valides
requiredAction.webauthn-register=Enregistrement WebAuthn
requiredAction.webauthn-register-passwordless=Enregistrement WebAuthn sans mot de passe
federatedIdentityConfirmOverrideMessage=Vous essayez de lier votre compte {0} avec le compte {1} {2}. Mais votre compte est déjà lié avec un compte différent {3} {4}. Pouvez-vous confirmer si vous souhaitez remplacer le lien existant avec le nouveau compte ?
emailVerifyResend=Renvoyer le courriel de vérification
emailVerifySend=Envoyer le courriel de vérification
error-invalid-multivalued-size=L''attribut {0} doit avoir au moins {1} et au plus {2} {2,choice,0#valeur|1#valeur|1<valeurs}.
emailVerifyInstruction4=Afin de vérifier votre adresse de courriel, nous allons vous envoyer un courriel avec les instructions à l''adresse {0}.
confirmOverrideIdpTitle=Le lien du fournisseur existe déjà
saml.post-form.title=Redirection de l''authentification
auth-recovery-code-info-message=Renseigner le code de récupération spécifié.
auth-recovery-code-prompt=Code de récupération #{0}
auth-recovery-code-header=Se connecter à l''aide d''un code de récupération de l''authentification
recovery-codes-download-file-description=Les codes de récupération sont des codes à usage unique, permettant de se connecter à votre compte si vous n''avez pas la possibilité d''utiliser votre application d''authentification.
webauthn-registration-init-label-prompt=Merci de renseigner le libellé de votre clé d''accès enregistrée
webauthn-registration-init-label=Clé d''accès (libellé par défaut)
passkey-login-title=Identifiant de la clé d''accès
passkey-available-authenticators=Clés d''accès disponibles
passkey-unsupported-browser-text=Les clés d''accès ne sont pas supportées par ce navigateur. Essayer depuis un autre navigateur ou contacter votre administrateur.
passkey-doAuthenticate=S''enregistrer à l''aide d''une clé d''accès
passkey-createdAt-label=Créée
passkey-autofill-select=Sélectionner votre clé d''accès
access-denied-when-idp-auth=Accès refusé en se connectant avec {0}
organization.member.register.title=Créer un compte pour rejoindre l''organisation ${kc.org.name}
organization.select=Sélectionner une organisation pour continuer :
notMemberOfOrganization=L''utilisateur n''est pas un membre de l''organisation {0}
notMemberOfAnyOrganization=L''utilisateur n''est membre d''aucune organisation
linkIdpActionTitle=Lien {0}
linkIdpActionMessage=Voulez-vous lier votre compte avec {0} ?
saml.artifactResolutionServiceInvalidResponse=Impossible de trouver la dépendance technique.
