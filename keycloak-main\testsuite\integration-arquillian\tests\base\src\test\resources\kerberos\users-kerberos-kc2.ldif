dn: dc=kc2,dc=com
objectclass: dcObject
objectclass: organization
o: Kc2
dc: Kc2

dn: ou=People,dc=kc2,dc=com
objectClass: organizationalUnit
objectClass: top
ou: People

dn: uid=krbtgt,ou=People,dc=kc2,dc=com
objectClass: top
objectClass: person
objectClass: inetOrgPerson
objectClass: krb5principal
objectClass: krb5kdcentry
cn: KDC Service
sn: Service
uid: krbtgt
userPassword: secret
krb5PrincipalName: krbtgt/<EMAIL>
krb5KeyVersionNumber: 0

# Cross-realm trust support! Realm KEYCLOAK.ORG will trust the realm KC2.COM
dn: uid=krbtgt2,ou=People,dc=kc2,dc=com
objectClass: top
objectClass: person
objectClass: inetOrgPerson
objectClass: krb5principal
objectClass: krb5kdcentry
cn: KDC Service
sn: Service
uid: krbtgt2
userPassword: secret
krb5PrincipalName: krbtgt/<EMAIL>
krb5KeyVersionNumber: 0

dn: uid=ldap,ou=People,dc=kc2,dc=com
objectClass: top
objectClass: person
objectClass: inetOrgPerson
objectClass: krb5principal
objectClass: krb5kdcentry
cn: LDAP
sn: Service
uid: ldap
userPassword: randall
krb5PrincipalName: ${ldapSaslPrincipal}
krb5KeyVersionNumber: 0

dn: uid=HTTP,ou=People,dc=kc2,dc=com
objectClass: top
objectClass: person
objectClass: inetOrgPerson
objectClass: krb5principal
objectClass: krb5kdcentry
cn: HTTP
sn: Service
uid: HTTP
userPassword: httppwd
krb5PrincipalName: HTTP/${hostname}@KC2.COM
krb5KeyVersionNumber: 0

dn: uid=hnelson2,ou=People,dc=kc2,dc=com
objectClass: top
objectClass: person
objectClass: inetOrgPerson
objectClass: krb5principal
objectClass: krb5kdcentry
cn: Horatio
sn: Nelson
mail: <EMAIL>
uid: hnelson2
userPassword: secret
krb5PrincipalName: <EMAIL>
krb5KeyVersionNumber: 0

dn: uid=jduke,ou=People,dc=kc2,dc=com
objectClass: top
objectClass: person
objectClass: inetOrgPerson
objectClass: krb5principal
objectClass: krb5kdcentry
cn: Java
sn: Duke
mail: <EMAIL>
uid: jduke
userPassword: theduke
krb5PrincipalName: <EMAIL>
krb5KeyVersionNumber: 0

dn: uid=jduke2,ou=People,dc=kc2,dc=com
objectClass: top
objectClass: person
objectClass: inetOrgPerson
objectClass: krb5principal
objectClass: krb5kdcentry
cn: Java
sn: Duke
mail: <EMAIL>
uid: jduke2
userPassword: theduke2
krb5PrincipalName: <EMAIL>
krb5KeyVersionNumber: 0

dn: uid=gsstestserver,ou=People,dc=kc2,dc=com
objectClass: top
objectClass: person
objectClass: inetOrgPerson
objectClass: krb5principal
objectClass: krb5kdcentry
cn: gsstestserver
sn: Service
uid: gsstestserver
userPassword: gsstestpwd
krb5PrincipalName: gsstestserver/<EMAIL>
krb5KeyVersionNumber: 0
