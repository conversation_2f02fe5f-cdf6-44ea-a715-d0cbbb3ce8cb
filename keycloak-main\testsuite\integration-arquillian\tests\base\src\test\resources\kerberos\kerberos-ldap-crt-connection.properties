#
# Copyright 2017 Red Hat, Inc. and/or its affiliates
# and other contributors as indicated by the <AUTHOR>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Using LDAP from "kc2" domain, but HTTP principal is configured to use KEYCLOAK.ORG domain.
# Realm KC2.COM has the purpose just for the client side login (KerberosUsernamePasswordAuthenticator used by Apache HTTP client in the test)
idm.test.ldap.connection.url=ldap\://localhost\:11389
idm.test.ldap.base.dn=dc\=kc2,dc\=com
idm.test.ldap.roles.dn.suffix=ou\=Roles,dc\=kc2,dc\=com
idm.test.ldap.group.dn.suffix=ou\=Groups,dc\=kc2,dc\=com
idm.test.ldap.user.dn.suffix=ou\=People,dc\=kc2,dc\=com
idm.test.ldap.start.embedded.ldap.server=true
idm.test.ldap.bind.dn=uid\=admin,ou\=system
idm.test.ldap.bind.credential=secret
idm.test.ldap.connection.pooling=true
idm.test.ldap.pagination=true
idm.test.ldap.batch.size.for.sync=3

idm.test.kerberos.allow.kerberos.authentication=true
idm.test.kerberos.realm=KC2.COM
idm.test.kerberos.server.principal=HTTP/<EMAIL>
idm.test.kerberos.debug=true
idm.test.kerberos.use.kerberos.for.password.authentication=true