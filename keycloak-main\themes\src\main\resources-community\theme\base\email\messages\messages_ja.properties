emailVerificationSubject=メールアドレスの検証
emailVerificationBody=このメールアドレスで{2}アカウントが作成されました。以下のリンクをクリックしてメールアドレスの確認を完了してください。\n\n{0}\n\nこのリンクの有効期限は{3}以内です。\n\nもしこのアカウントの作成に心当たりがない場合は、このメールを無視してください。
emailVerificationBodyHtml=<p>このメールアドレスで{2}アカウントが作成されました。以下のリンクをクリックしてメールアドレスの確認を完了してください。</p><p><a href="{0}">メールアドレスの確認</a></p><p>このリンクの有効期限は{3}以内です。</p><p>もしこのアカウントの作成に心当たりがない場合は、このメールを無視してください。</p>
emailTestSubject=[KEYCLOAK] - SMTPテストメッセージ
emailTestBody=これはテストメッセージです
emailTestBodyHtml=<p>これはテストメッセージです</p>
identityProviderLinkSubject=リンク {0}
identityProviderLinkBody=あなたの"{1}"アカウントと{2}ユーザーの"{0}"アカウントのリンクが要求されました。以下のリンクをクリックしてアカウントのリンクを行ってください。\n\n{3}\n\nこのリンクの有効期限は{5}以内です。\n\nもしアカウントのリンクを行わない場合は、このメッセージを無視してください。アカウントのリンクを行うことで、{0}経由で{1}にログインすることができるようになります。
identityProviderLinkBodyHtml=<p>あなたの<b>{1}</b>アカウントと{2}ユーザーの<b>{0}</b>アカウントのリンクが要求されました。以下のリンクをクリックしてアカウントのリンクを行ってください。</p><p><a href="{3}">アカウントリンクの確認</a></p><p>このリンクの有効期限は{5}以内です。</p><p>もしアカウントのリンクを行わない場合は、このメッセージを無視してください。アカウントのリンクを行うことで、{0}経由で{1}にログインすることができるようになります。</p>
passwordResetSubject=パスワードのリセット
passwordResetBody=あなたの{2}アカウントのパスワードの変更が要求されています。以下のリンクをクリックしてパスワードのリセットを行ってください。\n\n{0}\n\nこのリンクは{3}だけ有効です。\n\nもしパスワードのリセットを行わない場合は、このメッセージを無視してください。何も変更されません。
passwordResetBodyHtml=<p>あなたの{2}アカウントのパスワードの変更が要求されています。以下のリンクをクリックしてパスワードのリセットを行ってください。</p><p><a href="{0}">パスワードのリセット</a></p><p>このリンクは{3}だけ有効です。</p><p>もしパスワードのリセットを行わない場合は、このメッセージを無視してください。何も変更されません。</p>
executeActionsSubject=アカウントの更新
executeActionsBody=次のアクションを実行することにより、管理者よりあなたの{2}アカウントの更新が要求されています: {3}。以下のリンクをクリックしてこのプロセスを開始してください。\n\n{0}\n\nこのリンクは{4}だけ有効です。\n\n管理者からのこの変更要求についてご存知ない場合は、このメッセージを無視してください。何も変更されません。
executeActionsBodyHtml=<p>次のアクションを実行することにより、管理者よりあなたの{2}アカウントの更新が要求されています: {3}。以下のリンクをクリックしてこのプロセスを開始してください。</p><p><a href="{0}">アカウントの更新</a></p><p>このリンクは{4}だけ有効です。</p><p>管理者からのこの変更要求についてご存知ない場合は、このメッセージを無視してください。何も変更されません。</p>
eventLoginErrorSubject=ログインエラー
eventLoginErrorBody={0}に{1}からのログイン失敗があなたのアカウントで検出されました。心当たりがない場合は、管理者に連絡してください。
eventLoginErrorBodyHtml=<p>{0}に{1}からのログイン失敗があなたのアカウントで検出されました。心当たりがない場合は管理者に連絡してください。</p>
eventRemoveTotpSubject=OTPの削除
eventRemoveTotpBody={0}に{1}からの操作でOTPが削除されました。心当たりがない場合は、管理者に連絡してください。
eventRemoveTotpBodyHtml=<p>{0}に{1}からの操作でOTPが削除されました。心当たりがない場合は、管理者に連絡してください。</p>
eventUpdatePasswordSubject=パスワードの更新
eventUpdatePasswordBody={0}に{1}からの操作であなたのパスワードが変更されました。心当たりがない場合は、管理者に連絡してください。
eventUpdatePasswordBodyHtml=<p>{0}に{1}からの操作であなたのパスワードが変更されました。心当たりがない場合は、管理者に連絡してください。</p>
eventUpdateTotpSubject=OTPの更新
eventUpdateTotpBody={0}に{1}からの操作でOTPが更新されました。心当たりがない場合は、管理者に連絡してください。
eventUpdateTotpBodyHtml=<p>{0}に{1}からの操作でOTPが更新されました。心当たりがない場合は、管理者に連絡してください。</p>
requiredAction.CONFIGURE_TOTP=OTPの設定
requiredAction.TERMS_AND_CONDITIONS=利用規約
requiredAction.UPDATE_PASSWORD=パスワードの更新
requiredAction.UPDATE_PROFILE=プロファイルの更新
requiredAction.VERIFY_EMAIL=メールアドレスの検証

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds=秒
linkExpirationFormatter.timePeriodUnit.minutes=分
linkExpirationFormatter.timePeriodUnit.hours=時間
linkExpirationFormatter.timePeriodUnit.days=日
emailVerificationBodyCode=次のコードを入力してメールアドレスを確認してください。\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>次のコードを入力してメールアドレスを確認してください。</p><p><b>{0}</b></p>
orgInviteSubject=組織 {0} への招待
orgInviteBody=「{3}」の組織に招待されました。以下のリンクをクリックして加入してください。\n\n{0}\n\nこのリンクの有効期限は{4}以内です。\n\n組織に参加したくない場合は、このメッセージを無視してください。
orgInviteBodyPersonalized=こんにちは、「{5}」 「{6}」。\n\n「{3}」の組織に招待されました。以下のリンクをクリックして参加してください。\n\n{0}\n\nこのリンクの有効期限は{4}以内です。\n\n組織に参加したくない場合は、このメッセージを無視してください。
orgInviteBodyHtml=<p> {3}の組織に招待されました。以下のリンクをクリックして参加してください。</p><p><a href="{0}">の組織への参加リンク</a></p><p> このリンクの有効期限は{4}以内です。</p><p> 組織に参加したくない場合は、このメッセージを無視してください。</p>
emailUpdateConfirmationSubject=新規メールアドレスの検証
emailUpdateConfirmationBody={2}アカウントをメールアドレス{1}で更新するには、以下のリンクをクリックしてください。\n\n{0}\n\nこのリンクの有効期限は{3}以内です。\n\nこの変更を続行したくない場合は、このメッセージを無視してください.
emailUpdateConfirmationBodyHtml=<p>メールアドレス{1}で{2}アカウントを更新するには、以下のリンクをクリックしてください。</p><p><a href="{0}">{0}</a></p><p>このリンクの有効期限は{3}以内です。</p><p> この変更を進めたくない場合は、このメッセージを無視してください。</p>
eventUpdateCredentialSubject=クレデンシャルの更新
eventUpdateCredentialBody={2}から{1}で{0}のクレデンシャルが変更されました。これがあなたでない場合は、管理者に連絡してください。
eventUpdateCredentialBodyHtml=<p>{2}から{1}で{0}のクレデンシャルが変更されました。これがあなたでない場合は、管理者に連絡してください。</p>
eventRemoveCredentialSubject=クレデンシャルの削除
eventRemoveCredentialBody={1}に{2}からの操作でクレデンシャル{0}が削除されました。心当たりがない場合は、管理者に連絡してください。
orgInviteBodyPersonalizedHtml=<p> こんにちは、「{5}」 「{6}」。</p><p> {3}の組織に招待されました。以下のリンクをクリックして参加してください。</p><p><a href="{0}">の組織への参加リンク</a></p><p> このリンクの有効期限は{4}以内です。</p><p> 組織に参加したくない場合は、このメッセージを無視してください。</p>
eventUserDisabledByPermanentLockoutSubject=永続的なロックアウトによって無効にされるユーザー
eventUserDisabledByPermanentLockoutBody={0}で複数回失敗したため、ユーザーは永続的に無効になりました。管理者に連絡してください。
eventRemoveCredentialBodyHtml=<p>{1}に{2}からの操作でクレデンシャル{0}が削除されました。心当たりがない場合は、管理者に連絡してください。</p>
eventUserDisabledByTemporaryLockoutSubject=一時的なロックアウトによって無効なユーザー
eventUserDisabledByTemporaryLockoutBody={0}で複数回失敗したため、ユーザーは一時的に無効になりました。必要に応じて管理者に連絡してください。
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=リカバリーコードの生成
eventUserDisabledByTemporaryLockoutHtml=<p>{0}で複数回失敗したため、ユーザーは一時的に無効になりました。必要に応じて管理者に連絡してください。</p>
eventUserDisabledByPermanentLockoutHtml=<p>{0}で複数回失敗したため、ユーザーは永続的に無効になりました。管理者に連絡してください。</p>
