invalidPasswordMinLengthMessage=無効なパスワード: 最小長は{0}です。
invalidPasswordMinLowerCaseCharsMessage=無効なパスワード: 少なくとも{0}文字の小文字を含む必要があります。
invalidPasswordMinDigitsMessage=無効なパスワード: 少なくとも{0}文字の数字を含む必要があります。
invalidPasswordMinUpperCaseCharsMessage=無効なパスワード: 少なくとも{0}文字の大文字を含む必要があります。
invalidPasswordMinSpecialCharsMessage=無効なパスワード: 少なくとも{0}文字の特殊文字を含む必要があります。
invalidPasswordNotUsernameMessage=無効なパスワード: ユーザー名と同じパスワードは禁止されています。
invalidPasswordRegexPatternMessage=無効なパスワード: 正規表現パターンと一致しません。
invalidPasswordHistoryMessage=無効なパスワード: 直近{0}個のパスワードのいずれかと同じパスワードは禁止されています。
invalidPasswordBlacklistedMessage=無効なパスワード: パスワードがブラックリストに含まれています。
invalidPasswordGenericMessage=無効なパスワード: 新しいパスワードはパスワードポリシーと一致しません。
ldapErrorInvalidCustomFilter=LDAPフィルターのカスタム設定が、「(」から開始または「)」で終了となっていません。
ldapErrorConnectionTimeoutNotNumber=接続タイムアウトは数字でなければなりません
ldapErrorReadTimeoutNotNumber=読み取りタイムアウトは数字でなければなりません
ldapErrorMissingClientId=レルムロールマッピングを使用しない場合は、クライアントIDは設定内で提供される必要があります。
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=グループの継承を維持することと、UIDメンバーシップタイプを使用することは同時にできません。
ldapErrorCantWriteOnlyForReadOnlyLdap=LDAPプロバイダーモードがWRITABLEではない場合は、write onlyを設定することはできません
ldapErrorCantWriteOnlyAndReadOnly=write-onlyとread-onlyを一緒に設定することはできません
ldapErrorCantEnableStartTlsAndConnectionPooling=StartTLSと接続プーリングの両方を有効にできません。
clientRedirectURIsFragmentError=リダイレクトURIにURIフラグメントを含めることはできません。
clientRootURLFragmentError=ルートURLにURLフラグメントを含めることはできません。
pairwiseMalformedClientRedirectURI=クライアントに無効なリダイレクトURIが含まれていました。
pairwiseClientRedirectURIsMissingHost=クライアントのリダイレクトURIには有効なホストコンポーネントが含まれている必要があります。
pairwiseClientRedirectURIsMultipleHosts=設定されたセレクター識別子URIがない場合は、クライアントのリダイレクトURIは複数のホストコンポーネントを含むことはできません。
pairwiseMalformedSectorIdentifierURI=不正なセレクター識別子URIです。
pairwiseFailedToGetRedirectURIs=セクター識別子URIからのリダイレクトURIの取得に失敗しました。
pairwiseRedirectURIsMismatch=クライアントのリダイレクトURIは、セクター識別子URIからフェッチされたリダイレクトURIと一致しません。
invalidPasswordMaxLengthMessage=無効なパスワード: 最大長は{0}です。
invalidPasswordNotEmailMessage=無効なパスワード: メールアドレスと同じパスワードは禁止されています。
ldapErrorEditModeMandatory=編集モードは必須です
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=パスワードポリシーの検証はWRITABLE編集モードでのみ適用されます。
clientRedirectURIsInvalid=リダイレクトURLは有効なURLではありません。
backchannelLogoutUrlIsInvalid=バックチャネルログアウトURLは有効なURLではありません
duplicatedJwksSettings=「JWKSを使用する」スイッチと「JWKS URLを使用する」スイッチを同時にオンにすることはできません。
error-invalid-value=無効な値です。
error-invalid-blank=値を指定してください。
error-empty=値を指定してください。
error-invalid-length=属性{0}の長さは{1}から{2}までの範囲でなければなりません。
error-invalid-length-too-short=属性{0}の最小の長さは{1}である必要があります。
error-invalid-length-too-long=属性{0}の最大長は{2}である必要があります。
error-invalid-email=メールアドレスが無効です。
error-invalid-number=無効な数値です。
error-number-out-of-range=属性{0}は{1}と{2}の間の数値でなければならなりません。
error-number-out-of-range-too-big=属性{0}の最大値は{2}である必要があります。
error-pattern-no-match=無効な値です。
error-invalid-uri=無効なURLです。
error-invalid-uri-scheme=無効なURLスキームです。
error-user-attribute-required=属性{0}を指定してください。
error-invalid-date=属性{0}は無効な日付です。
error-username-invalid-character={0}に無効な文字が含まれています。
error-person-name-invalid-character={0}に無効な文字が含まれています。
error-invalid-multivalued-size=属性{0}は少なくとも{1}、多くても{2} {2,choice,0#values|1#value|1<values}でなければならない。
clientRedirectURIsIllegalSchemeError=リダイレクトURIは不正なスキームを使用しています
ldapErrorMissingGroupsPathGroup=グループパスグループが存在しません - 最初に指定されたパスにグループを作成してください
clientBaseURLIllegalSchemeError=ベースURLは不正なスキームを使用しています。
clientRootURLInvalid=ルートURLは有効なURLではありません。
backchannelLogoutUrlIllegalSchemeError=バックチャネルログアウトURLは不正なスキームを使用しています
error-invalid-uri-fragment=無効なURLフラグメントです。
invalidPasswordNotContainsUsernameMessage=無効なパスワード: ユーザー名を含めることはできません。
ldapErrorCantEnableUnsyncedAndImportOff=LDAPプロバイダーモードがUNSYNCEDの場合、ユーザーのインポートを無効にできません
clientBaseURLInvalid=ベースURLは有効なURLではありません。
clientRootURLIllegalSchemeError=ルートURLは不正なスキームを使用しています
error-user-attribute-read-only={0}属性は読み取り専用です。
error-number-out-of-range-too-small=属性{0}の最小値は{1}である必要があります。
client_account=アカウント
client_account-console=アカウントコンソール
client_security-admin-console=セキュリティー管理コンソール
client_admin-cli=管理CLI
client_realm-management=レルム管理
client_broker=ブローカー
