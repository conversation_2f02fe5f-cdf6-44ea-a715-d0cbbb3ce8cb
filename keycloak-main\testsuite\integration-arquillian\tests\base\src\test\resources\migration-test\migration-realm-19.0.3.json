[{"id": "Migration", "realm": "Migration", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "loginTheme": "rh-sso", "emailTheme": "rh-sso", "accountTheme": "rh-sso.v2", "roles": {"realm": [{"id": "af4f1ab6-338f-4688-a754-a21adacbfb58", "name": "default-roles-migration", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["migration-test-realm-role", "offline_access", "uma_authorization"], "client": {"account": ["view-profile", "manage-account"]}}, "clientRole": false, "containerId": "Migration", "attributes": {}}, {"id": "4cf00611-63ad-4798-8f27-35870c948c17", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "Migration", "attributes": {}}, {"id": "423043d7-ec2b-4975-abd7-ddf80486689f", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "Migration", "attributes": {}}, {"id": "eab5811a-e514-4092-9c11-d902514146e0", "name": "migration-test-realm-role", "composite": false, "clientRole": false, "containerId": "Migration", "attributes": {}}], "client": {"migration-test-client": [{"id": "f64ae467-4f51-4023-87e9-865da81c29cc", "name": "migration-test-client-role", "composite": false, "clientRole": true, "containerId": "0e3543fa-6d38-4a9f-8810-151adab26f7c", "attributes": {}}], "realm-management": [{"id": "253d025a-1e56-4a5f-97a3-5d4e2a00b0a4", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "8efba442-2cf2-4337-93d2-4642ccfae50d", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "69fb62e2-a68f-4583-a8b5-5e0cc497d1e5", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "bf23f119-4b81-4f1b-833f-2d0f01b26284", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["view-identity-providers", "query-clients", "create-client", "manage-events", "manage-users", "view-users", "query-realms", "manage-realm", "manage-clients", "view-events", "impersonation", "view-clients", "query-groups", "view-realm", "manage-authorization", "manage-identity-providers", "query-users", "view-authorization"]}}, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "91ca08c7-2adf-466a-ae24-2278fec0fb71", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "e46d5ce0-fcfc-4410-860a-168f9accbd28", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "459ff65b-72ae-4910-9185-3fbf61aad194", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "c5abd11c-39ac-4b1f-83ce-2a665a3f0908", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "b9d97a25-180a-49f5-b4c6-ae93d31cd563", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "1a1fa059-1bda-43cf-a80f-ef3e242e9bde", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "88d06055-70a2-43c8-a8a2-e2b4410b4aea", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "7d14a29c-242c-4213-9126-375e84f2b3fd", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "36fe9f13-3f71-465c-9139-59191622bcf8", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "ffc7db1a-f81d-434e-94fb-4a391fc18f7f", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "5e84e256-b2fd-43f4-9424-933411d46f6f", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "ca85a3af-3c77-472a-9204-0194a0177a5b", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "30ebf8da-dde2-48f3-843a-519d3b865cb5", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "2ed65f06-f886-4281-9af1-feebb9af165b", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}, {"id": "d2536399-6d26-42b8-afdb-a7ad0f560907", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "attributes": {}}], "migration-saml-client": [], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "17da1ee6-9e13-4497-8364-7a287320a9a1", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "e6071292-ce71-4916-81ee-3956635dce3b", "attributes": {}}], "account": [{"id": "aac16cb7-d1b3-4667-969f-942bc397b7d2", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}, {"id": "dc5ebb15-4d78-4d53-9498-9173217a549c", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}, {"id": "da95329d-53aa-484b-90d6-ff590e4f7b4e", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}, {"id": "9700a00a-bf98-4e68-b9ab-f57d0b9c1769", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}, {"id": "6d284670-2ad0-41a2-80e4-5343d5fff85b", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}, {"id": "1bf1fbea-18f0-495f-b5be-8343b7cc86ea", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}, {"id": "4eebe6a3-b78b-4933-9944-d743df578a37", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "d17942d8-a654-4901-8e62-0ca7341a4c63", "attributes": {}}]}}, "groups": [{"id": "69a9acb5-68eb-405c-9717-3d2fab0d6e6e", "name": "migration-test-group", "path": "/migration-test-group", "attributes": {}, "realmRoles": [], "clientRoles": {}, "subGroups": []}], "defaultRole": {"id": "af4f1ab6-338f-4688-a754-a21adacbfb58", "name": "default-roles-migration", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "Migration"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "users": [{"id": "cf47dd8b-3719-449f-9892-bac9f8ae7ef7", "createdTimestamp": *************, "username": "migration-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}, {"id": "47611b1e-6e38-415f-99b1-8babab008505", "createdTimestamp": *************, "username": "offline-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"id": "b22fe342-e3b6-4e31-a76c-7f7ffdeb83ab", "type": "password", "createdDate": *************, "secretData": "{\"value\":\"kNwotFPNeuwelpT1HWt+E4ONXFK6wjd+h0zbzNBRGwOqacAjeY7vYN9QZQ46DlEKSdn04cEU/3RvX8WPcRegxg==\",\"salt\":\"rEIJDbs+BQqpx31v8mONWA==\"}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\",\"additionalParameters\":{}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "d17942d8-a654-4901-8e62-0ca7341a4c63", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Migration/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/Migration/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "ac9ecafa-e49b-4f88-a97b-3f7d8445e3dd", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Migration/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/Migration/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "3bf98f71-3180-4f73-a659-d7f1dfe8ff47", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "631c208b-3f72-419a-9059-de2f4e74b111", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "e6071292-ce71-4916-81ee-3956635dce3b", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "b14fa52b-4a72-46fd-ab5d-40e4cf76471d", "clientId": "migration-saml-client", "baseUrl": "http://localhost:8080/sales-post", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["http://localhost:8080/sales-post/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": true, "protocol": "saml", "attributes": {"login_theme": "rh-sso", "saml.force.post.binding": "true", "saml.multivalued.roles": "false", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature.keyinfo.ext": "false", "use.refresh.tokens": "true", "extremely_long_attribute": "     00000     00010     00020     00030     00040     00050     00060     00070     00080     00090     00100     00110     00120     00130     00140     00150     00160     00170     00180     00190     00200     00210     00220     00230     00240     00250     00260     00270     00280     00290     00300     00310     00320     00330     00340     00350     00360     00370     00380     00390     00400     00410     00420     00430     00440     00450     00460     00470     00480     00490     00500     00510     00520     00530     00540     00550     00560     00570     00580     00590     00600     00610     00620     00630     00640     00650     00660     00670     00680     00690     00700     00710     00720     00730     00740     00750     00760     00770     00780     00790     00800     00810     00820     00830     00840     00850     00860     00870     00880     00890     00900     00910     00920     00930     00940     00950     00960     00970     00980     00990     01000     01010     01020     01030     01040     01050     01060     01070     01080     01090     01100     01110     01120     01130     01140     01150     01160     01170     01180     01190     01200     01210     01220     01230     01240     01250     01260     01270     01280     01290     01300     01310     01320     01330     01340     01350     01360     01370     01380     01390     01400     01410     01420     01430     01440     01450     01460     01470     01480     01490     01500     01510     01520     01530     01540     01550     01560     01570     01580     01590     01600     01610     01620     01630     01640     01650     01660     01670     01680     01690     01700     01710     01720     01730     01740     01750     01760     01770     01780     01790     01800     01810     01820     01830     01840     01850     01860     01870     01880     01890     01900     01910     01920     01930     01940     01950     01960     01970     01980     01990     02000     02010     02020     02030     02040     02050     02060     02070     02080     02090     02100     02110     02120     02130     02140     02150     02160     02170     02180     02190     02200     02210     02220     02230     02240     02250     02260     02270     02280     02290     02300     02310     02320     02330     02340     02350     02360     02370     02380     02390     02400     02410     02420     02430     02440     02450     02460     02470     02480     02490     02500     02510     02520     02530     02540     02550     02560     02570     02580     02590     02600     02610     02620     02630     02640     02650     02660     02670     02680     02690     02700     02710     02720     02730     02740     02750     02760     02770     02780     02790     02800     02810     02820     02830     02840     02850     02860     02870     02880     02890     02900     02910     02920     02930     02940     02950     02960     02970     02980     02990     03000     03010     03020     03030     03040     03050     03060     03070     03080     03090     03100     03110     03120     03130     03140     03150     03160     03170     03180     03190     03200     03210     03220     03230     03240     03250     03260     03270     03280     03290     03300     03310     03320     03330     03340     03350     03360     03370     03380     03390     03400     03410     03420     03430     03440     03450     03460     03470     03480     03490     03500     03510     03520     03530     03540     03550     03560     03570     03580     03590     03600     03610     03620     03630     03640     03650     03660     03670     03680     03690     03700     03710     03720     03730     03740     03750     03760     03770     03780     03790     03800     03810     03820     03830     03840     03850     03860     03870     03880     03890     03900     03910     03920     03930     03940     03950     03960     03970     03980", "saml.signing.certificate": "MIICuTCCAaECBgGAay77dDANBgkqhkiG9w0BAQsFADAgMR4wHAYDVQQDDBVtaWdyYXRpb24tc2FtbC1jbGllbnQwHhcNMjIwNDI3MTMxOTQyWhcNMzIwNDI3MTMyMTIyWjAgMR4wHAYDVQQDDBVtaWdyYXRpb24tc2FtbC1jbGllbnQwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQChHhg8hfuVngRkyMW7LzBTjL2TKBBV7biXEKRFfoJwTwXPMjRyqvzfbTcb7gsh/B1w1kkmN9Gzigy1YVe3JC01AUGZ9/a6net+q5dxFosBv0frO20tC6/60nIO3kuCmmbYVBJNWFrBXtrioaBWkhUaOoTxkzpveJkE+Yr2ViSfxhJjFD35g7oMuBao3AkYIgDCfra9Y1mL7GHzwGxrpdGDc5ojQG2yJ8IuIyPD79OpJdQ98UoKBs5/odZmF9TRuogFSjgUqMRDw7Z7HiDChBXC9ezRp4MDNFR8hSPZRf7dTqXLH3bpx7BMf3b3g0lveN8nWw/BOrawl7MH14S/peF/AgMBAAEwDQYJKoZIhvcNAQELBQADggEBAJuNkA3YcHIzMx87yaAMnZvwqST7x2++PxoOiPS9+TA7rHg/2JdC0bSmga0Zw4MI9mPGExDCVVmjH3pW3J0K+f5McNQQeUC5LVVx5cEt8m0p8Ci5dyF3binBMVJR6F7oh39N12CRyyfV0wSIhZWzFwDbd58eNHJz/ie/Jae3o2aUsH4O+ifoBykl5BAeMyn2Q/glMejt9xL/QL/gf3HZyX5FnEFlPpPFUAd/ixIglVmVTqH4Ozhnu+GMm446ArpozD9d4yXJ+UyD/FDXuB3AAFOgclAXhLzSGLQHsN/a817L2p28m20PADiv6YtdJ3vDd6Hr9SUrlurmg70gr3RuH+4=", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "false", "client_credentials.use_refresh_token": "false", "saml.signature.algorithm": "RSA_SHA256", "require.pushed.authorization.requests": "false", "saml.client.signature": "true", "saml.signing.private.key": "MIIEowIBAAKCAQEAoR4YPIX7lZ4EZMjFuy8wU4y9kygQVe24lxCkRX6CcE8FzzI0cqr83203G+4LIfwdcNZJJjfRs4oMtWFXtyQtNQFBmff2up3rfquXcRaLAb9H6zttLQuv+tJyDt5Lgppm2FQSTVhawV7a4qGgVpIVGjqE8ZM6b3iZBPmK9lYkn8YSYxQ9+YO6DLgWqNwJGCIAwn62vWNZi+xh88Bsa6XRg3OaI0BtsifCLiMjw+/TqSXUPfFKCgbOf6HWZhfU0bqIBUo4FKjEQ8O2ex4gwoQVwvXs0aeDAzRUfIUj2UX+3U6lyx926cewTH9294NJb3jfJ1sPwTq2sJezB9eEv6XhfwIDAQABAoIBAA14ji81Rpk2WoiwrrOdWO2LZWhR37fHhfj/d7SlxLMuMD6oJHF38Wee4o4vw3eGdFG0YO5UmXBo+PILXwGdgTge0ETW0ia9QGzZXPKkH2A4hYNZ9yFeikqu4MF0fZzAWxv4P4V+/yuOyj9LozPJyADm5qmqFScvzp93W9KH1k0TaOh7diJacjMY4XiDKwV3/+g85+W/rgyfEQ1FjEJWxyQ5EwBrHrn7zlktQZCKDWNVKsHDc32+dub+SZZsrolgDEmsfShjo14PFghS87N626Fcz+DgNv1p14DqLgwA/y7sAyEGckk4u0xLKZ8MCcfAZc8KKgu1Nw4ySiBJDvFHJqUCgYEAuUnAeiWrL5BFgBw5QrJ44jKYs0zOxWi/2y7ofqpFU0d5iG+Zhoxdp5P2w6J+np0rTHeV3qX9+iYpAgKm1ixi4eih33ty1AyuC12nUd21sJtyru0HqrJPAQTb9ZjSP9vXJ0h+UE3gab8KMGTsLo/YR9B93KofHHDh0fvkPAZbtU0CgYEA3pru9kUdH3MTmp5z7+CyGGhJ/K568GT/FWNVxDoZPyOyKD6LKwa+Jsv+siIRRZ1AKWeNp451PEd54IaP2tUn6s2AbpvuElWqL4vFD/Q4j2ze3vkzNHDoFhzUU/ggz2J+hengUxQExzGb9qYijDOk6PAhAlIsLPnTQCoNb7gMG/sCgYBS3nBucPB6Kl1bKcRWVzoHeLeg4YZM9kdwgS9Mj1zIHVx2r+sLKTYt39hqsP9Oc17NKWLnHHhV68FFe+ggO+PRw9i5+h1mR3GD9dQColVDcpK9N5fJOUxqWUEDO+E03F8C28JaqinnQaEN+eVEAeOqYs4X5iJZ9waYgr8WNYXItQKBgFMoCZCR7PksoewmuSSpCZ64hrsP6vkWoXu56fYtxfSiPxFtm8ts72fK2NBujYk13xwYhQBM5VqpUMwhOGgFLK44KnwnX94VhkfQrMLy9pYNk4w0B37Vlr17842SqZ8PKjiYT6Z5WfeXfq27DjjVqgsieRJG5B3BDbtweih71no1AoGBAKddnbLbLY3Znp9ihL85w2WwY2bobABgwee9sTvuQuXkO+19Mm0ER1D3XbtWSIuWgPYzRnUTsgYe/P9qVr1Wrhyf2DC82szbfIa0vez4tN+8oINjsNJhW8gUpPB0TrtoMkCYjw8sJ4BxCgVZaYE50+G+cf+SvNr3Fg5GhfEPAFw5", "id.token.as.detached.signature": "false", "saml.assertion.signature": "false", "saml_single_logout_service_url_post": "http://localhost:8080/sales-post/saml", "saml.encrypt": "false", "saml_assertion_consumer_url_post": "http://localhost:8080/sales-post/saml", "saml.server.signature": "true", "saml_idp_initiated_sso_url_name": "sales-post-very-long-name-greater-than-255-characters-0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901", "exclude.session.state.from.auth.response": "false", "saml.artifact.binding.identifier": "ZDisLXkadz6IlDoL8l343V44KP0=", "saml.artifact.binding": "false", "saml_force_name_id_format": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "true", "display.on.consent.screen": "false", "saml_name_id_format": "username", "saml.onetimeuse.condition": "false", "saml_signature_canonicalization_method": "http://www.w3.org/2001/10/xml-exc-c14n#"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["role_list"], "optionalClientScopes": []}, {"id": "0e3543fa-6d38-4a9f-8810-151adab26f7c", "clientId": "migration-test-client", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"backchannel.logout.session.required": "true", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["acr", "web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "c11d03ac-b4b0-4581-995c-cc9c2f868b17", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "3fcb104b-2354-428b-abbd-9ba9ea5e744b", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/Migration/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/Migration/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "dd1bfe11-ae96-4408-a3ac-33ebb265e876", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "adef1610-70ec-4282-88ef-bcb26b1f5edf", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "57b24be6-e4a9-4a26-8ef8-f04f0caca9a8", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "bb39a0b6-af13-42bb-9919-f332cb052bcd", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "4a915c07-ffd3-476b-9831-d35bf9dcfca9", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "e3027f70-ee80-4368-b16d-496208eb9c5d", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "93395631-9a98-4545-87e0-4f1887613846", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "236c6d56-04e4-49b7-ba9d-b8c54d19460c", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "51faf2f1-3d0d-4633-9cdb-2d3524059a22", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "6a7ce404-535e-4ab2-85c5-f328d6fcaa5b", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "f2d58749-6fb1-43dc-bb74-32bb744a58d5", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "155da808-0318-498a-ad75-1a912b79665b", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "ef75d675-b321-4b37-947c-1d4b2978c27b", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "fc2cd4d1-769c-43aa-b0ee-1cad01e42f05", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "47a9e7bb-e320-4b07-8423-80939e4b87fe", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "2cccf8b0-f67a-4ecf-b15d-a5fe75d73e3f", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "94c91679-3f64-4d4f-9641-4590b950f06b", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "fa7ba608-da25-4a3b-9dfc-b959b983755f", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "4fe835b4-4920-4c54-9c43-1b295fcf72df", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "cbead812-740c-44de-b943-f910e2609556", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "2225ae16-b3e6-4034-b63f-56c4a342e69b", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "115ec7bf-3c90-4770-b76d-1e77d189a65c", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "08a01457-386a-497b-b1f3-4e5c60f1c339", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "1228a28d-a688-46bf-a3cb-4ae2ca8b107d", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}]}, {"id": "69fe9567-58ba-485d-8c53-95ee1126f698", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "87ae4fd7-ee81-4227-b318-d20221793e33", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "e8124dfa-07f9-4944-9a43-c135fea9b3ef", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "4e4c0885-8508-4c6c-815b-0f6384486ba4", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "42440d09-d43b-4ffd-82e1-d646915227dc", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "93956d53-de49-4939-9f06-24c9b1837eb0", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}, {"id": "83c65b82-1422-4057-98cc-548d021ff543", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}]}, {"id": "e2dd44b1-d42e-4357-9481-f9d74fc2eaf6", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "4d6c37f2-d14b-4f4d-9e54-82382e0f2b52", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "97fac94d-e9b5-420e-8af6-cf9b754224a7", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "f84a21a0-6286-43ee-8fbb-2a150e70521b", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "2853551e-ee98-48da-aefe-7ea60ccc56c8", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}, {"id": "6e26ea6e-8d9e-4bb4-a335-df23baea8a89", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "7b7b4a03-9b67-4a0c-a154-e36d8b2e251c", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true"}}]}], "defaultDefaultClientScopes": ["roles", "profile", "role_list", "email", "web-origins", "acr"], "defaultOptionalClientScopes": ["phone", "offline_access", "address", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "e3475a40-1377-4376-94c5-cc0e51858abc", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "5f2a9e9d-1dd9-4181-bf38-c5d91e560d08", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "119720bf-d07a-48c9-88fd-cc6800b90b70", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "6b9af018-656c-4dad-a602-e5239eab39ab", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-sha256-pairwise-sub-mapper", "oidc-full-name-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "oidc-address-mapper", "saml-user-attribute-mapper", "oidc-usermodel-property-mapper"]}}, {"id": "6501d527-b9d2-4c7a-8425-281357563d19", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "93d30870-683a-4e2b-88b3-af5366c43b05", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "f64c4e61-1bd0-470f-965d-2818f86893c2", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "ab149dc9-2675-4070-84ea-5bd4399dfee1", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "oidc-full-name-mapper", "oidc-usermodel-attribute-mapper", "oidc-address-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "saml-user-property-mapper", "oidc-sha256-pairwise-sub-mapper"]}}], "org.keycloak.keys.KeyProvider": [{"id": "b9e22c38-bde6-464b-b121-bcd5dae6f615", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["c8a977cc-63ef-4fec-ac74-0c03bbde0288"], "secret": ["3RkkhRqk4AkTm1opSIe4AsM-X5azpUQ5RDA-3fpAjs5a7Mn7S-r5ON5u6tJIdRcOFfe81Rodq8uE5IBqmxJvsg"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "a5da6470-4334-43ec-be81-9759b1c531a3", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["76340352-dab2-4aa3-bc18-44266977c8fa"], "secret": ["hRLdio4uEBkmzJEfHH5Miw"], "priority": ["100"]}}, {"id": "51cbfea7-ca8b-479d-aa9c-9df7df9ccb26", "name": "rsa-enc-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpAIBAAKCAQEAlr7S8QcT9p5rwbJCm8HktgcUVVSSkCfZGB8Qo390pxoiaSYEVy2ROZBP5q/OMZ1dT4ztYCyOeanfk92kTE/xU66f8gGnx+hi0ZS4T95yXFebo+kfELGCYTkicg3ruO93CJRRnLHtskDBOPBXMNj/bM3TLszogdg8XPQPYC9LbMp4iKAPLk0L6avxQYhjxj90VMLg1AlSUQa/ePUZRmUGSgIGFWoVs72jiMFnLcA1UyqrobkRcL6tgUXd1XZBbv4g98UYw+Q5RXmAr4CmzgNgeLwyWfJPh9VPjCkpuIijG8w5948hEMsQJ/UoSWF4C61V94UVHCGiMwmgba36xUXeHwIDAQABAoIBAQCAsZzIpPBIJWt9S8BN1TR6qoSTbMZLR5mxXCQKUS+30Tn7Zrdh7ccDy85K9WAlP2GAHqIw7xkbiQkU5a30cHNoq7ZlF0C0DBMBNeQ8W35qk2iOgOFCkSjr86kSkgviMJ4Atw8NpWF2Qo4zHAxz6W3256kZVVpSL9cozDusM/RChSy6XCzpYcpr39ifTeoghKtDc4Zx/FySKnkxrNEzptRJEdIIh/F0XYpNRglkEzbySyay6ziLtqBMcVb6tT/QoPUoH/RkZlqivxCNdqyo/f37j6RF+Vw/oiKrRLY5CAMgsiNiqiYR5/j7fnUhnl6P69pjA/G1kxAe5N3BzNrySNtRAoGBAOZub+w5ghD4KIREnoXReoa/A4tj550tlO2YWqGq3wkCMQ9K26sXbzFZiIhF7t74Y5vxhIlhbr+f4jFEb+1cfZ2AGC/88UozLONzw2SF4hiNt/7FTklk2R6JzOKqq77xbteTcRsUYyjPegEQAK671xjTpa1ZDphwUuR+OWUOPTpdAoGBAKd42e2UjrZlBDS0MBg3/4qZBRVWWXsEkss9/6xvKxCnWj4hi8ygNZkSlkgIM1ANQj900xWq7eKS8somzG1uKhCUnXNp4S0XlK6GwPlCEayJHtTslXIDefPfugXkD1S8+gywIxJGicSAPq4sip1pvdTllIKO9KrRonYsjdQ/dUqrAoGASEW9Dt8en0nmE04eViRUhKymZPKps0+XlUmiq3AXUhEA6CX3bdsEwfqt6ufcOsy3GTMP+bK4dJul6wPnAxBuSZbifzKbT0Vrv88QKpgfSMoyOlMR8c0SmBRqUXFsOpAozq5s3CxxyVwBjRHC1QsPNLc1EICNJlyccxPv1nU32PUCgYAO5yo9giLRCFw/dGlzPkh3RPa212BPGjEV9QZzidEDQmE9di7Cnt8mKAlHj7NBRGzIyNSf8P4KU5lXc+xtMIh9wTvhsWJgaODKRyOVCtQXznv35rXF15laomYjcBANMmPuDR6R2cUYv3DUfxI2v9osv+FfXOlY6h7S9VQJtzvw0QKBgQCwHQIo96V5lcIQlDsZd8b18X3HChfO9EIJkSm3q2uxkQWoFPLSDqRjoBOn/5lTHKuKOtU/ahYL0Ae3e4FCvYRfwmruZdZ5kV6PNV3pHJXOCwXvFhiSKUpcDlNwG2gwk/w2WDM3KAzxQpXDSKBvk5xJiSy3nfR0mBll190/VWRdhw=="], "keyUse": ["enc"], "certificate": ["MIICoTCCAYkCBgGBi/E7BjANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAlNaWdyYXRpb24wHhcNMjIwNjIyMTUwMjMxWhcNMzIwNjIyMTUwNDExWjAUMRIwEAYDVQQDDAlNaWdyYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCWvtLxBxP2nmvBskKbweS2BxRVVJKQJ9kYHxCjf3SnGiJpJgRXLZE5kE/mr84xnV1PjO1gLI55qd+T3aRMT/FTrp/yAafH6GLRlLhP3nJcV5uj6R8QsYJhOSJyDeu473cIlFGcse2yQME48Fcw2P9szdMuzOiB2Dxc9A9gL0tsyniIoA8uTQvpq/FBiGPGP3RUwuDUCVJRBr949RlGZQZKAgYVahWzvaOIwWctwDVTKquhuRFwvq2BRd3VdkFu/iD3xRjD5DlFeYCvgKbOA2B4vDJZ8k+H1U+MKSm4iKMbzDn3jyEQyxAn9ShJYXgLrVX3hRUcIaIzCaBtrfrFRd4fAgMBAAEwDQYJKoZIhvcNAQELBQADggEBABuVb4TSURUMZMyCc1kHO9L/sE9brYK561hqZsXyBKK6RHQUHvB10oc3D0V/Fi84LiUYVvqwHHARBc1ZRntqSqOOf6ImAp3sSULWVDSE49RSFHG03VjhRRhKBbsPPAy5LODukiRo5HXSr+bYbIJICeTfXKkDKyhj9prgrJJ1RyMP4dyVFGg1l0dUhmlISy69X6MaMiL025479KYaXEA4KydwEL3oGoGjnT4r8JIKga1DQTDFKWFHKncfVr7Rr8Rw0Ycs8BdTJxiWJFOZ4aX5EyKDxP1mrMMhmlpgkQs6FIoiPmzG+uj63u+QzscHYtmA4WRrq2LkTZKQmLon4tNd7i0="], "priority": ["100"]}}, {"id": "6f87c426-a7ac-4c17-891f-e7f53eb2c7fe", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpAIBAAKCAQEA+uG0ozYC3VW05488M1FHZLwRcxcdG0iTTvQl6vF3tTKP/pFuCeK93In2nsuEyeYkxTn5KVevKMqdQ1YezcUwfp0RBdT+aBEFTaOAnEABKeOmKq242dBlv7j7+TaiJy11ETVkP4eB+00z+S3HiPoffFrvoEEujqBAdnmOkc3pJi76j1ZlQGrOWYIUIO8ei7fRmPONcGQSPlruyVPvou2Jp0EsrylyumqMuI+18ch/PKCLOEYarml1Xw/1m/YPtlG8lKMAnnOOiLyrnySl4pIYUYJgUHZImUVYincKpP3lX6zFskGwntnXVUrNsrsE8gww6z6qwk4IX39Wj3wbSVTFswIDAQABAoIBAQDI860K5iJZQ2NJ7xMbT8lNyM55T1RDHFjR7wHING3cN4nbMIXhaTsGwYNHImFUjwF48LzA/rkQhWp+GydFTuNPJ2QRNdApbyi+TW6guSu1b+ETYl8kKKa3cOZzce1kUCbrB8cqk9HrhqVQu/iNoTtDElvKEyvFllrMCwXFiBduOrD+Lylq4ea09e8dM1x8UanNGvr0X2RtpzNumDrJ5crQCulHgCNVGCGUO0R4fedDWEK+8dOVuvCwUAMHVHgRmuYbtFOhEFiIqccncdb5wzW7vVROV9mAcKLptjEkEBQQOv0NUPlwx6AaExkMVRfGVAoYKdVfMtKvS8NGAIfvlNYBAoGBAP4sHDWlEVI7shkfhjPRUNFdt156AbOzcL5RvdP0Q2yep8e4nyOASqpCixIYHVVYz9QNv29UtdE/096hEOCkNBpyk7t1IRg7dJi+PvbmdP4nLzWnaKOldiVFcBLZQeDgJKWMBhw0D+Gc0NBZNrVW5j+b86xGszu18J8Ynq0bFwKBAoGBAPyvibLKh9DhP/XDKSPsl9YItUrU0h722Up9jqFVdMmfXD5HV1BrtEVhEr/7A5SMTKwXC6fZ3q5lZeCAD+q3yK6JW1dG0Rp8wBJnhRU2kML5ZWpLNFPUxeiJYlMlVXf84OfIS/PCPkkVz8aipdpLOB7+fs6RkZA/pGBbY+vxEEYzAoGAeSWDKJqN3/kuEUVyTN52u+QEU5apmwJrOXtylET3oh0Y8VvxFhW/ANoIuU4fJR/Yp0UKOsFQi/T1QkbY6Ym6f+Sev5m7dXZZQ7hiAIKlvsHkgZlSibWC047+NS2ydcHAFdvgya6S50E8UiJDrjlGM4lcon9sTIiSsPImJ3IjDIECgYEAsdQvyh3ymzQKuUDRsTCOYtjaDYHdJmF+5oeaWzwy5ro6RrIaZsSFp9X7RhS3nlFmnC3TzBruNjmyCt7VVNBmcT7Fg5SZEFT/L3SfGiC+nBi8IKQwfVbeuRoGkpFRpxHWjghOO4xWHUU2ZoqMwiXxpw5f26xijuvbGNz4Y1HRrDUCgYB4jIVzx8b4F2c2EmpDlE6YRzTzrilmFoGjGkm0Ntdh060WknQCJ6VZWcGs9PheKyK5ssMRR/2PBlHJosMIJm4kkwF1R0eSXlpjTUAP2UJiWeurNJkSjKVblJBz0roMk1HHSyeZcHi8qLsfpJo2z+PTxewULHjvk0jGXMFII40djQ=="], "keyUse": ["sig"], "certificate": ["MIICoTCCAYkCBgGBi/E7KzANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAlNaWdyYXRpb24wHhcNMjIwNjIyMTUwMjMxWhcNMzIwNjIyMTUwNDExWjAUMRIwEAYDVQQDDAlNaWdyYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQD64bSjNgLdVbTnjzwzUUdkvBFzFx0bSJNO9CXq8Xe1Mo/+kW4J4r3cifaey4TJ5iTFOfkpV68oyp1DVh7NxTB+nREF1P5oEQVNo4CcQAEp46YqrbjZ0GW/uPv5NqInLXURNWQ/h4H7TTP5LceI+h98Wu+gQS6OoEB2eY6RzekmLvqPVmVAas5ZghQg7x6Lt9GY841wZBI+Wu7JU++i7YmnQSyvKXK6aoy4j7XxyH88oIs4RhquaXVfD/Wb9g+2UbyUowCec46IvKufJKXikhhRgmBQdkiZRViKdwqk/eVfrMWyQbCe2ddVSs2yuwTyDDDrPqrCTghff1aPfBtJVMWzAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAHsNYa+/NP4o0XmP0Er1Q4ca0tErlV6DDIiQMB+M/+urIDMtRblN5sg/IGzzqK/RtXYnKB86q9F6qSaGryeYGz7w3Dg4xcBGUYsPSXPWt8egKniOsTBeQSBZHx6yO2rBJt/uvapZX8kfRYQDaJDh3JIqCp+UlSbTyGpxh9M3nuv6kKi8k/eO8bDw9X97I0XUaVD8H3LIKUa5VKoavJ86e063ouvE+qukL84reWitAssyDbEtxuQTxfeBubAl7QfFDHmmLyt77vcVgNvMHjMyFSxq/hvGOxMq3i5T9p5ARjRuHb9OIZ2iFWc00q5+87TythXeRwJAIZpqcC6YuXv3DAM="], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "b80bc633-8a12-42ee-a4ab-afc8278b793a", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "c23d8bb2-6cf6-4702-831a-5ab024d70642", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "basic-auth-otp", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "8a55bc65-941c-4cb1-afb2-706339b0c088", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "345a1ac6-df83-4389-bc84-b90a37b1959d", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "d8e1409c-1d23-45f5-917c-93e0468b5b2e", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "032910f6-9cdc-4ad8-92c5-672a73a98589", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "03cb8dc5-417b-4291-978f-3278f1da1c66", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "d625013c-c8e5-4d68-9ce6-50c2a69a0d11", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "f00e7092-6be9-4566-961c-0584c9e82f91", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "c27ced69-9683-4bf9-b398-f346b4fb9cf3", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "2138e225-12f0-40e3-9b0f-6f77e8809db1", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "8ace8676-6b71-454a-80dc-ac3d6d088ebc", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "9f230452-63ba-465d-93c8-eb1197527ce8", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "004f23f1-c682-4211-a941-68622db10b86", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "f1706bda-ed12-4570-b422-584a3ae3c82b", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "867b49c6-c316-4bba-8e77-fe7d74880820", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Authentication Options", "userSetupAllowed": false}]}, {"id": "abae4e72-c7b4-4abb-9cf6-8bd156e1a8f5", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "35cc4ae7-7e8e-4160-89ef-da7eddde3f17", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-profile-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "ec49730f-83d7-48df-b4de-370e2d1ffdc5", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "9293a962-7dca-44b6-9c87-096619b86a89", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "cb90fd73-5b01-46ef-879f-7652c519865a", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "b7948479-6d77-4d1c-a4fb-c1dd325d33ab", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "clientOfflineSessionMaxLifespan": "0", "oauth2DevicePollingInterval": "5", "clientSessionIdleTimeout": "0", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "clientOfflineSessionIdleTimeout": "0", "cibaInterval": "5"}, "keycloakVersion": "19.0.3", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}, {"id": "Migration2", "realm": "Migration2", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "fe0dfd49-a60d-4d36-8469-6c396b2b2613", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "Migration2", "attributes": {}}, {"id": "6fd8d647-4472-4f29-bd09-143a2f8b4570", "name": "default-roles-migration2", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["view-profile", "manage-account"]}}, "clientRole": false, "containerId": "Migration2", "attributes": {}}, {"id": "a6931711-21ec-4454-b6e0-f7a2322a6167", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "Migration2", "attributes": {}}], "client": {"realm-management": [{"id": "ee8650e5-297c-49dc-a312-c4b04a3bcd39", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "ccffb6c5-63ff-4e2d-b053-3e508ae5cbcf", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "b878e1c3-a61d-46e5-9ae1-a01a17994408", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "7a3cdc1a-4b84-413e-9ccf-c57fcf3317ab", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "9e48e608-ebef-45bb-8c17-e68145627474", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "8397f0bb-623f-48ab-9611-3e3784ff22dc", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "4945c292-b655-47bf-b853-6a2e864f670d", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "b8c50598-541c-4be5-ae10-c7ab25e857e2", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "de622240-0b70-4871-99cb-24b30c9b7784", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "f29033c0-ab26-4add-9ca3-bf303cad4bd8", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "f0c2f20e-7650-44ba-9ef7-8cae3467e768", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "4b1e22d1-f58a-48dc-84de-9271b948635e", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "49fc51d9-957a-4c1d-9aca-3b85a851f4c6", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "3ab4ee5a-9470-4098-b0c5-c6d71e6f2e27", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "f89758a7-3bb5-4c4a-9242-26f03c783524", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "c6447c99-40f7-4e83-bb32-44456966a025", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "75ba2077-4460-4880-8324-6138698e22dc", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["view-events", "manage-realm", "manage-authorization", "view-users", "view-clients", "view-identity-providers", "query-realms", "query-groups", "query-users", "view-authorization", "manage-identity-providers", "view-realm", "manage-events", "impersonation", "create-client", "query-clients", "manage-users", "manage-clients"]}}, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "62ef0926-17c9-4179-947b-8da243a3ff03", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}, {"id": "********-e752-4d24-b6d8-2ba48280b769", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "********-3010-4d8d-90a6-db9990449b23", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "364a543b-2337-498b-bc1b-ae007fef08ec", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "7eb37807-722f-4091-b8bd-cf126c8a6050", "attributes": {}}], "account": [{"id": "caf46d94-fb53-4e2b-b745-a495d5a9bb7b", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}, {"id": "464b8bb9-55ff-4cbc-8f61-aba1a108e40e", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}, {"id": "56ac9513-0e0c-4081-aefa-5958676a3bc4", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}, {"id": "79b46149-3a45-4ed5-b3af-cdef7af7c2c3", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}, {"id": "dd3013ac-134c-4020-8727-bbaf29fc127f", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}, {"id": "f4542b38-e64a-4dcc-aa82-ebdd158129d3", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}, {"id": "7e55c038-99f1-4a4d-b041-a25661cb80b3", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "attributes": {}}]}}, "groups": [], "defaultRole": {"id": "6fd8d647-4472-4f29-bd09-143a2f8b4570", "name": "default-roles-migration2", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "Migration2"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "99b6b468-88ae-4b21-bc33-197b66e44ddd", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Migration2/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/Migration2/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "96a9ce83-aefa-4ba8-9e19-6b838b552800", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Migration2/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/Migration2/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "4a46da78-f5cf-423a-b3f2-bb07da3f2fbe", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "bab37f84-a2f8-436a-bca6-e966121068db", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "7eb37807-722f-4091-b8bd-cf126c8a6050", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "********-3010-4d8d-90a6-db9990449b23", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "0e80ad6e-0584-4a43-a50e-66c62618cbf0", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/Migration2/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/Migration2/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "c7c9d434-1f13-497e-95e1-84673fd35f32", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "0d154891-3b7c-48f4-92ad-c97df27e20bf", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "d37fc0fd-e2d7-4134-a4d4-bfec108e477e", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "acccd5aa-07d2-4883-8d78-fd55f709bcf1", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "d3b5637b-41cf-4e80-9955-ee66615e06ce", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "56b65c6d-be0c-4b03-b204-5bffe6885375", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "699e8ead-f48e-4138-9361-46f284ef0f48", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}, {"id": "f60da5fe-5595-454d-a40a-fd4eadb5c9a0", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "3728ab78-90ce-4079-a9b5-ecffdb42d329", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "da85fe38-b7f8-4005-b4bc-79c80a577854", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "62991bca-db4f-40ff-b223-4480a697c0b3", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "02006ccb-726f-47d4-878d-5f9223c7e6ce", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "aa118395-ea3a-43a4-a2e3-c921f3261929", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "9345a54b-7aa1-4fab-864e-6be04fc2ccc0", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "fa8c87d1-9b2d-42f6-8295-7e9f32747651", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "348cd00e-7464-4444-b11c-5a07caed9701", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}, {"id": "c600ad07-fc7a-4bb5-9264-264b41d713d5", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "f79c2403-8831-4681-b32d-62507bc17758", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "6bcf684b-8a89-447c-a52a-f2721e2c3954", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "fab89da6-1006-426f-8009-3f011f73f759", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "83a79e37-7142-41b9-a2fe-d168360b502e", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "f39f986f-fb20-47f8-969d-255e74472f19", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "37dc25a0-5929-497b-8ca9-c3ace626eed7", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "d6d4b369-bb9a-4e95-b64c-ffe4c0d8061a", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "cdaa5ca4-0684-45ae-a6e4-a08e5d0da654", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "6a0b3841-d31d-4175-a062-e94534b330cb", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "bfb7b26b-94bb-491e-bd77-492dfdc18ae4", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "6480bbfc-bf69-43d1-aef1-a3d86f8bb2c2", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "ff50f17e-2b22-4752-96d2-7e49ddb5de18", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "3e999ace-5e6f-4ca9-bef0-1ea23a094151", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "4af1af16-08b8-4f3f-adf0-8af7e7c82e3a", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "efbd2e65-9ccb-44aa-943e-3fe2cc065f3c", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "cad4c792-f2bd-451a-92f8-1b182087135d", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "8183099f-1d4c-447e-8e41-1da7b57bfc1e", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}]}, {"id": "1823f7f9-8e23-440e-a25f-800f43203960", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "4d3bef63-2dff-4dd5-97b2-2026252aa589", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["web-origins", "roles", "role_list", "email", "profile", "acr"], "defaultOptionalClientScopes": ["address", "offline_access", "microprofile-jwt", "phone"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [{"alias": "gitlab", "internalId": "6ff5ce97-5018-**********************", "providerId": "gitlab", "enabled": true, "updateProfileFirstLoginMode": "on", "trustEmail": false, "storeToken": false, "addReadTokenRoleOnCreate": false, "authenticateByDefault": false, "linkOnly": false, "config": {"hideOnLoginPage": "true", "clientId": "gitlab-client", "acceptsPromptNoneForwardFromClient": "false", "disableUserInfo": "false", "syncMode": "LEGACY", "filteredByClaim": "false", "clientSecret": "secret", "caseSensitiveOriginalUsername": "false"}}], "identityProviderMappers": [], "components": {"org.keycloak.storage.UserStorageProvider": [{"id": "307bba27-f8da-4c4d-a1b9-3c1bf49f824e", "name": "ldap", "providerId": "ldap", "subComponents": {}, "config": {"pagination": ["false"], "fullSyncPeriod": ["-1"], "startTls": ["false"], "usersDn": ["ou=People,dc=keycloak,dc=org"], "connectionPooling": ["false"], "cachePolicy": ["DEFAULT"], "useKerberosForPasswordAuthentication": ["false"], "importEnabled": ["true"], "enabled": ["false"], "bindCredential": ["anything"], "changedSyncPeriod": ["-1"], "bindDn": ["uid=admin,ou=system"], "usernameLDAPAttribute": ["uid"], "vendor": ["other"], "uuidLDAPAttribute": ["entryUUID"], "connectionUrl": ["ldap://localhost:10389"], "allowKerberosAuthentication": ["false"], "syncRegistrations": ["true"], "authType": ["simple"], "useTruststoreSpi": ["ldapsOnly"], "usePasswordModifyExtendedOp": ["false"], "trustEmail": ["false"], "userObjectClasses": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, organizational<PERSON>erson"], "rdnLDAPAttribute": ["uid"], "editMode": ["WRITABLE"], "validatePasswordPolicy": ["false"]}}], "org.keycloak.userprofile.UserProfileProvider": [{"id": "88cef18c-bcd8-40d2-9e7d-d257298317f2", "providerId": "declarative-user-profile", "subComponents": {}, "config": {"config-piece-0": ["{\"attributes\":[{\"name\":\"username\",\"displayName\":\"${username}\",\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"validations\":{\"length\":{\"min\":3,\"max\":255},\"username-prohibited-characters\":{},\"up-username-not-idn-homograph\":{}}},{\"name\":\"email\",\"displayName\":\"${email}\",\"permissions\":{\"edit\":[\"admin\",\"user\"],\"view\":[\"admin\",\"user\"]},\"validations\":{\"email\":{},\"length\":{\"max\":255},\"pattern\":{\"pattern\":\"[a-zA-Z0-9!#$%&'*+/=?^_`{|}~.-]+@example.nl\",\"error-message\":\"Invalid domain selected\"}},\"annotations\":{\"\":\"\"},\"required\":{\"roles\":[\"user\"]},\"group\":null},{\"name\":\"firstName\",\"displayName\":\"${firstName}\",\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}}},{\"name\":\"lastName\",\"displayName\":\"${lastName}\",\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}}}]}"], "config-pieces-count": ["1"]}}], "org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "55d8aaa7-2307-4e3f-9b49-4a5cf7f0980c", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "oidc-usermodel-attribute-mapper", "oidc-full-name-mapper", "saml-user-property-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-property-mapper", "saml-user-attribute-mapper", "saml-role-list-mapper"]}}, {"id": "cff606fb-0e86-47c9-84f7-62bce4f09837", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "e45930ae-f2bc-41f8-a7fd-e5d7ad7362f1", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "7f27b976-84ba-4cd5-8777-fe340741ccd0", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "b5ca9baa-fcc5-40bd-abff-e5274f94c0e4", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "13113f59-7119-4d6e-88ae-fbd4498b5cea", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "5406436b-03ba-4c14-9821-268e7eadb666", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-full-name-mapper", "oidc-usermodel-property-mapper", "oidc-address-mapper", "oidc-usermodel-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper"]}}, {"id": "d79b1499-24de-4963-9ef4-6c8ce075a923", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}], "org.keycloak.keys.KeyProvider": [{"id": "10e64137-5377-4e5f-a935-908741d18bdc", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["e07b429d-76d2-4f9a-b2e7-220670c87ca3"], "secret": ["SPoR1vrlZ5lO6fOAMI6pJ5coDFJ2U9SAGiN0ruN9leYAL3Ib3El-NeA1VtLTFYS9ObtJkNP4llBHKnT8ZlaJ_Q"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "0ad3f11c-b781-48ce-8ab4-3f3dfdb1f149", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["aba12ef6-6a76-4958-a542-32de274a755b"], "secret": ["pxKMHb3Z6LDkDfAR31mL3w"], "priority": ["100"]}}, {"id": "f2e19e56-94ce-4a7e-82b2-117407051e61", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpQIBAAKCAQEAt4yvJyMjIwmfXsyqgJdebEYhwOu9dAen6Kz3qMKqhJ5mF2QGHZCksang9li9OfNAG4yCl1jRltWjS5pWV4eghEb9aXOfwAr3z4XUVoeL974x5YpEH1CjY+ahX4UL5+p0CIQhRsFkrUumT0+5IdrBE9phncQMJ3m5ZMieTcOIT9nB9KFeCnPpw8RbBDyKE8/2elEg3M/OG1jBJ1fMZu8YV4f3ml4Gak63z31osdcMFFjbxJCBvZw10gktHcfgEdvlClppYZ5MHJPcRaMroA/zKW4y6nCIK3wJrmSBHCyIj3iwKN4uzI4VfkcIlZDGLF/nb8ohg0EQ1EbVbOSpT4sDOQIDAQABAoIBAQCBH584USeuxl4vOpvXyqTKVqv4I98reRdOQCAZwJhko2n+mocS1WaG9ZMkulikI9O9jaNxBTKsga5WyURkXy004Rv0yzpex1h9zRALr19v8YT4jcROo1POO7jakaZGiFEIjKs8M2C36Lgeo18Byfp6FO8HJZvHSYJ6vAdhfWSvhrf9+sgDeT8iHGSB2DbUHq5RCjbi2nfk8RZDkGQcRliav0g69jm/iO62VLhF73ssSliK5VjCvwP9qtxTT5oi3B/ZtCvFDk++OxzjYz0b45/ZZIJ5OZGtmFoPoYsDpJUjQmDv8pl+1GOWsnXkLjibpqooniqWE8RkcgpNQlV449eBAoGBAOJJdFjXquAmJVyJGPyUcobUdyLzr1BojbKFVplwagLSp7zqRuD5YbvOi7psvO+1bfE4NB9ukW+gRZ5FNvE9t3bdwil1dkDMV+fGnWkrSvaY0XdQHLuFcmo2Gn9wE4i6R8XrvB8Kx3HS3bPyrgE8UmWNvDSl5bEiypEVfDFUXOLNAoGBAM+moSYUcETxpYgbprmg0/uqVctgH8PXymrevG/LVCYrxHMLyNmJm+zcMEcyn8S04vt89VnHg5AQMBF0azBhgCUzuWbq5qx+PSMaOtcd6/e9Uf6lrT9wGU45MNlxMn4q+KeBTNTNSvT85BXrwiMwd7saBgAetbaJKNnHUvQe7podAoGAA0CnCXfgHuSteM0HrOm9U67OH5R9GpzGHKDKA96Xx36bpp1/c1BiVT68x2s0tAVZlTQHm8QNFxuopIO+LG2Eq5K4bbwgfQHy6YPzU0WqCC1eJWbjYtaiNGCetOI5haeXzIJ8671N7JbpVOLSGXZygL8uy1rvGEK1wqwaYuMhiqkCgYEAlUz4lxmg82FPpIJFKTjC0jNRxo3RqQ1xw89sa3EliuBzOkjh289+ZhqXN8nlaBirDyl0e13hcWbCOPqcy0d/TMBY8O+6dZG6wY9teRhRfmoIF3DmFTOzbjPetKkYJfEdocptBKCq4WUnVb+ScZy6qbN3VwS9oIYT8inHsjC/W90CgYEAs/h4YQUhVWjWN1i1U/HY2X0EM0E3BRW6VbyL5QE62afy4TGMg6T71SzRZpumLMq5BTm0r8t8rnWaghDXEWwpUkrBrFtJfCkRiw+FcBECr79ECEgHzmBNCPvq8UPi9ewgOYV2F6anQDc+Q1kuo6C4hmBJ0ls51ddmBp4q2AuZsAo="], "keyUse": ["sig"], "certificate": ["MIICozCCAYsCBgGBi/E93DANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApNaWdyYXRpb24yMB4XDTIyMDYyMjE1MDIzMVoXDTMyMDYyMjE1MDQxMVowFTETMBEGA1UEAwwKTWlncmF0aW9uMjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALeMrycjIyMJn17MqoCXXmxGIcDrvXQHp+is96jCqoSeZhdkBh2QpLGp4PZYvTnzQBuMgpdY0ZbVo0uaVleHoIRG/Wlzn8AK98+F1FaHi/e+MeWKRB9Qo2PmoV+FC+fqdAiEIUbBZK1Lpk9PuSHawRPaYZ3EDCd5uWTInk3DiE/ZwfShXgpz6cPEWwQ8ihPP9npRINzPzhtYwSdXzGbvGFeH95peBmpOt899aLHXDBRY28SQgb2cNdIJLR3H4BHb5QpaaWGeTByT3EWjK6AP8yluMupwiCt8Ca5kgRwsiI94sCjeLsyOFX5HCJWQxixf52/KIYNBENRG1WzkqU+LAzkCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEARG0ZsJuL6vAvQNmjmPda1moVpTj5sQ3fe8BrtfDAd+Wfz1K/BZfYZrMuE9r0JSrc2t/yI4DjaRl3wlibfT/WrRLe/duUoVwn15kJ5tBqAheyy2sGwLqA0OaT7ltSaHqZ0xCx8RA9uoE25VHj9Ho22B9WQ2Tj1Y/oB3L03SAyVfFFEXZgq6w8zjRuTp6sf3SEfehGQ0nZz8+LtsSrw2GcQjj8grzVCAO3ckDPRZPW6gBBts8KJdXy5+N+6emunq9imQC2OvZaCzSHZBs73I5fsvW+ffIiagIXHXytsWfelK7LBy0ggzprR2M+qgdnu3Z6FMPeZ1CUzsP1V5iMOYIDwA=="], "priority": ["100"]}}, {"id": "012412ec-78c2-4c8d-8ab7-6dc6e62ef61d", "name": "rsa-enc-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAtqlQVdrbBrs9IVX7brNrki4GnvXb97g4DZx1+9UVzDVgo1DWxO65Xty+8onE/0ERlKh1+30UDCtmAw+mRX34jMYGPi/p6Y1lbH9Xt7Bz4vbIEhMzfTF+udEQVxY17ZyChp0aZLNTpqoZGFlulQbCEK5gqCTuMFdglRHeNdXkrsZOYfGWFalkHqBichh4Br8XQBVXNPvdm5Hde1cnPnYy0j1Stq9sqUR1zhsbS9oagGstDeXVby4RukJctWoY1/r17ZFXXdzEgIIw3AfEaNI723bRM98EcPJ8xyCDetpWFJoGlqbGnIo1ISP9nQUmwFeIgEYO5S+gpb2k4FgHDCuCIwIDAQABAoIBAA1NlXk7QDpkRpZ38aDlVk9GJNtM4qFO/F4ZaApYQYcm2Avs5kdfb4a2Iv39BKjmb0ZZYoZXXK9qNbrBWAW1n/V4spmy2aFV1+4n9BZ/tmDwQi/20AgtI6Ka1ErTZkgQ3vy40mRFHzGDL+KzGLenz1hxqr50OhixNGqG650WH9qOnTqvXiUh5IMrGQOlsD7odAF/xr1Mkjsvh9EW6Wui3+JS9hRNlGYex/eWwdyUGVwZlYEjKr2WGq/+QTqVqN+jZLIGwCG4ufqySDtAVC+PS/wT4Mxuk56p8IWbV9o0ZCldiWHAHmVIXEGTBlrlZk6MZokswDKsBtOCQ9CMKurJu+kCgYEA45xsOT9sP6kGQBY2aQVyz8D3QpIXli/dnAls8lGeZRX9vQ4YPYEAAwvqocES7d9aGP5cjGDDNxKakrCj+lmMAPA0uZXdSTwQLAPubVb2leG8TLbrWBRntO2kxZYFW+ifnGc51kgOGEDpl57agxf5tYAJF3BUxU4ajnQH7YkVoJ0CgYEAzXGoScUFHOTBOW+Hh0bFf8MjbmrTke7NCO8dFDtnCPARb9TpUL+5GYsssjdgrlyT6lj68c4rw79dfiI5u06F5RdZ0Z75i0gUG5xEEFTExUc9EmPG4qCTMP+gBy7/AeiFgQS0iKHJNgjC2YblOYRJK5M8hH5xi2W8+dh+5erjUb8CgYBsss00YceOd2gWvwTpDsMP2HW9Awvkm6NX22B+MOBWIjR1Tv/gj3Sz/8Lj0NL7askaWo9UMvnHmUjceSM2cgFciVqErr9pNBf9DTuWWAuW9KD6efjWrZgBXeRswAJ7LBLdnKaQYQTqLEXiNOjh5ldIiIbO/MRfHbetBFis5eTAOQKBgH4rJubnEFTGR8Q2Mk1psTDdbo0JHhWpSlIKRxl9wbFDffHoF8F3fP2q8zAfwkhSoqRxMOv/afE1PP2Rl1LozmH7DTYnI6neAjtSpJpC9PUS4vbAQAw+ASc+VdETk5hgbFznvteKRD/i8YcVyQChtS4FpkzYOnMZcA0gYA5jSmeRAoGBAKRrjJauGx3UczhWmIp1fCbqfuEiaCyfp0FotuiWJzcpXFZoNsYDvO+uC3DmniTaydwKug7JwWbQ7xECcgPkZzZrCIIz5JR9OwfTsEy89dDTQSmYpS5+0tA9BAaEZXupequYqd7oJV1UEZQxdK1Vm//g52EOXD/ozHx0z8uYAI2p"], "keyUse": ["enc"], "certificate": ["MIICozCCAYsCBgGBi/E+CjANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApNaWdyYXRpb24yMB4XDTIyMDYyMjE1MDIzMloXDTMyMDYyMjE1MDQxMlowFTETMBEGA1UEAwwKTWlncmF0aW9uMjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALapUFXa2wa7PSFV+26za5IuBp712/e4OA2cdfvVFcw1YKNQ1sTuuV7cvvKJxP9BEZSodft9FAwrZgMPpkV9+IzGBj4v6emNZWx/V7ewc+L2yBITM30xfrnREFcWNe2cgoadGmSzU6aqGRhZbpUGwhCuYKgk7jBXYJUR3jXV5K7GTmHxlhWpZB6gYnIYeAa/F0AVVzT73ZuR3XtXJz52MtI9UravbKlEdc4bG0vaGoBrLQ3l1W8uEbpCXLVqGNf69e2RV13cxICCMNwHxGjSO9t20TPfBHDyfMcgg3raVhSaBpamxpyKNSEj/Z0FJsBXiIBGDuUvoKW9pOBYBwwrgiMCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEArX8U0Dg+LpC3d3/aIrP2JbGu+KsF7qJEXZTVxHzra9lFomtAuYoIMucSGqpupnmORFwgKPY+R2r2L0qfbCN57G/tjxqi1X+z/aVJP6bebetsnXCjQtI1xUf2GnaKRpEnbMRnF7R396oVCcXbgF0wTTuMMmer5KfQlNDKRhC/mTtAMfRFHVI8RBOtQYSxART2hT6UuKYNHr8rYJCmDzqnifqA5pQ999FB+QveS+mF1rNIbqh2Fbguo2Okyj3uuRRxYu+NvqoKcMlpcU2+6QhQUW+jtmcUJOFIusHgLs73hsA+H557tlRryA073q54Kw7RgQtPN85CsiepCFEYEZ6aIw=="], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "b6aa24da-9762-468d-a324-cb89efcdff72", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "b6db81c7-bf31-4687-89a4-6544635367d4", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "basic-auth-otp", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "465b76db-252e-4327-a204-279acac353db", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "62faa195-0535-4bca-9691-039c6d8a86cc", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "2a8ed31e-9046-4eb3-81fb-1b91c65ef18d", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "e21467e0-c651-4ef2-a008-5443023e7e0f", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "750ca12b-7174-4ae2-a665-3d52ce06b1b7", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "9e419744-be81-4790-8df9-59c3438a3b07", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "95a36d2b-219b-430a-9699-22863ad0a13f", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "0962478d-3a91-4f5b-b306-5071f98824e1", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "07118edb-9d4a-4d18-a115-891832839dc2", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "11334eda-2260-47b8-a531-87fcdcfe4280", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "7cccdb37-e576-4908-9095-134c2ed6dfce", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "af65c48d-0922-4a34-890a-1f4d7ee4e1c0", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "83a5d2be-bfd7-4889-ab00-9d448370178f", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "85e5bd40-134a-48a4-8c08-b0b387ffd982", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Authentication Options", "userSetupAllowed": false}]}, {"id": "564bf0d7-d41a-4aea-a24e-ae044b85ce26", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "2d9ff511-6ba0-4a6c-adfb-a6da6d1cfe17", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-profile-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "a12333b9-618d-4b85-8171-4fea85ab143f", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "9466437a-9a02-45cc-863d-007cc600eae5", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "1fd8cceb-e9c4-4767-a86f-f4c80abfe92b", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "33657c8e-fa4a-4222-b3b5-a9024b7f85d2", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"userProfileEnabled": "true", "cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "clientOfflineSessionMaxLifespan": "0", "oauth2DevicePollingInterval": "5", "clientSessionIdleTimeout": "0", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "clientOfflineSessionIdleTimeout": "0", "cibaInterval": "5"}, "keycloakVersion": "19.0.3", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}, {"id": "master", "realm": "master", "displayName": "Keycloak", "displayNameHtml": "<div class=\"kc-logo-text\"><span>Keycloak</span></div>", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 60, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 600, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "64c9b38d-9ccc-4756-b38e-62003c7340dd", "name": "create-realm", "description": "${role_create-realm}", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "da1233c8-964c-4020-a71f-c81feb800254", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "8fe23ff9-9bbd-49ff-8123-c23d26a46bac", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "7846b6ab-d865-44ae-8cfb-c37d7bfe118d", "name": "admin", "description": "${role_admin}", "composite": true, "composites": {"realm": ["create-realm"], "client": {"Migration-realm": ["create-client", "view-events", "view-clients", "manage-events", "impersonation", "query-realms", "view-realm", "view-users", "manage-identity-providers", "manage-users", "manage-clients", "view-identity-providers", "query-groups", "manage-authorization", "query-users", "view-authorization", "manage-realm", "query-clients"], "master-realm": ["manage-identity-providers", "view-identity-providers", "query-users", "query-realms", "view-events", "manage-realm", "manage-users", "view-authorization", "view-clients", "view-realm", "manage-events", "impersonation", "view-users", "query-clients", "create-client", "manage-authorization", "manage-clients", "query-groups"], "Migration2-realm": ["manage-users", "query-users", "create-client", "view-users", "query-realms", "query-groups", "manage-events", "manage-realm", "query-clients", "view-clients", "manage-identity-providers", "view-authorization", "manage-clients", "view-identity-providers", "impersonation", "manage-authorization", "view-realm", "view-events"]}}, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "359e663d-42e1-43c1-aa78-c83b4aa928b5", "name": "master-test-realm-role", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "27295a0d-3c99-4b9e-8737-4f8214ed788a", "name": "default-roles-master", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["master-test-realm-role", "offline_access", "uma_authorization"], "client": {"account": ["view-profile", "manage-account"]}}, "clientRole": false, "containerId": "master", "attributes": {}}], "client": {"security-admin-console": [], "master-test-client": [{"id": "********-636d-4d8e-a65e-c26b9eb121b2", "name": "master-test-client-role", "composite": false, "clientRole": true, "containerId": "9a4f9905-8df8-42f7-916c-8d7a4f6fcc33", "attributes": {}}], "admin-cli": [], "Migration-realm": [{"id": "238d1f44-9670-4529-af29-8eca79f0456c", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "83b8e58e-6a7c-4b99-9c1b-556193ee150a", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "3cd1e416-d0c5-46bd-a793-b33e87698664", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "6c7a797c-3fc6-4ad1-b7c7-8cb3293fc385", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "ea8d7388-c06a-4afa-80e7-2fc3b398f682", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "ff8cbfbb-01ef-41cc-9e2f-9152115ab71b", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"Migration-realm": ["query-clients"]}}, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "1db0044c-1fb7-4bda-859a-347837df023f", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "11aecc2c-eaab-47ef-a724-48e59879b2c6", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "f53d60d5-c396-4ac6-a217-bbae25d3f278", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "a1829c40-2f3c-4852-b5c6-ae8cf33370d5", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "173304dc-af4c-484e-8e41-7a43f7df046a", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "8a25a6c7-3ffb-4566-aad2-b945eba639f3", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "b00e67ad-aa61-4a8b-8d02-4b8b2964ca4f", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "dd8c4027-11f2-4ec4-85c9-f29e7a15447c", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"Migration-realm": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "837d2da1-9941-496b-92b9-25c36da8c4ea", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "7960647f-766b-4b22-bc7b-058a21757a7f", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "3b9e55da-cccd-4162-9f13-fb079638278d", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}, {"id": "c8a27f1f-bf86-4b14-89f7-b5d12e288a8e", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "attributes": {}}], "account-console": [], "broker": [{"id": "65b80b8a-b1b4-4a78-b27b-8c39b3697c86", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "1cd64665-7ab2-488a-b20c-d92f14e9eaf3", "attributes": {}}], "master-realm": [{"id": "6e3550c6-f081-49dd-a616-81b3027c1eb1", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "********-4481-4b9b-b564-78038c136f3d", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"master-realm": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "4af5459d-9c89-487f-b216-2586e37d1381", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "a2a735fb-3043-4f6b-b7fc-6fd4944b9464", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "39234834-4176-4913-916b-53d55f4f5919", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "d145d955-0dce-4348-b178-af048f26ea97", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "1fac6ee8-74aa-4dbe-b4f1-71656fd763e0", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "1c8fef67-2f59-4fd5-b991-e8ef8f87dbb4", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "145ca486-f31f-4d44-9b3c-23454544b0e1", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "fb0de938-475f-467a-8b33-6e88ae0da3b5", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "295cf2f2-c09c-4cd6-9773-e82e64b6d803", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "5258ed14-6d32-4f60-bdf6-f78bb3d3f651", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"master-realm": ["query-clients"]}}, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "d1bdefe6-1240-4c3b-a114-39e6ff520c76", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "94c3d7b0-4480-46fa-8f50-5b6a0ffc2f15", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "8d814ea2-f5ab-439d-b1b5-4c52399c2d00", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "775c7e60-0f01-4b3f-b85d-42087672020c", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "d074a2df-2828-4af0-a897-94cbf2a4dc26", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}, {"id": "0337b8b5-4736-40de-903a-76ef731f7d3c", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "e9d87f81-4073-4668-84e6-69d13a88799d", "attributes": {}}], "Migration2-realm": [{"id": "805fee2c-34bf-4a62-b878-3810ded7bb53", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "ab41b343-929e-4f6c-a645-cf76b4d1b7af", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "5169144b-b196-48fd-a644-ce89ca726383", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "d90e90e0-5279-49df-ba08-e06a054a6df3", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "73983867-e593-401c-bb78-6bb401136287", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "b3a99fed-3a4f-4e67-b4c0-35d636e540b5", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "c2ae8276-eed0-47fa-bd8f-80d3761d3f02", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "8660646f-64ea-48eb-9bd2-044bd4f7bd4a", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "f78a37c5-4715-4103-b650-cf123815bbf9", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "1929ab1b-5420-4386-961d-51b88440fe8b", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "55f6f8ee-6f9b-4dbf-93dc-fe5b65eb5c4b", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "2271eb8f-622b-47ff-b69c-9e7394422ebf", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"Migration2-realm": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "0ef9e0d3-ad40-4a9b-8da3-d51b5526335b", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "75a09f00-82b0-45e5-8d74-e665834cb6d8", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "560604a5-b299-4ae9-a6e9-0e9c19da28fa", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "224d4b4c-5390-482e-bd78-46c9b938cafd", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "4cb1dede-0ecb-4bd3-8318-89d21c1bbb02", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}, {"id": "7f4c4b4a-edef-468d-8c10-fc33a9bd1a17", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"Migration2-realm": ["query-clients"]}}, "clientRole": true, "containerId": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "attributes": {}}], "account": [{"id": "ff2519a0-aaef-4d63-b4d4-3db7cc83fc3d", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "2c456fad-bad7-4d97-ac60-69758d2bc191", "attributes": {}}, {"id": "0b7c1002-7083-4f2d-b996-70021612830b", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "2c456fad-bad7-4d97-ac60-69758d2bc191", "attributes": {}}, {"id": "d57921de-6298-477a-ad97-f347e9bee60d", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "2c456fad-bad7-4d97-ac60-69758d2bc191", "attributes": {}}, {"id": "a587a9b5-4764-472a-898b-ae90a9027c80", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "2c456fad-bad7-4d97-ac60-69758d2bc191", "attributes": {}}, {"id": "4970f34a-8aaf-4fd1-a3be-8defe73aacda", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "2c456fad-bad7-4d97-ac60-69758d2bc191", "attributes": {}}, {"id": "c58bad8b-09af-43ed-8c7d-7da179541b28", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "2c456fad-bad7-4d97-ac60-69758d2bc191", "attributes": {}}, {"id": "84033ec8-133e-40a5-94a8-376bd947ed2a", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "2c456fad-bad7-4d97-ac60-69758d2bc191", "attributes": {}}]}}, "groups": [{"id": "5ba589e2-d88a-479d-a0c1-bb35ea6e3fd3", "name": "master-test-group", "path": "/master-test-group", "attributes": {}, "realmRoles": [], "clientRoles": {}, "subGroups": []}], "defaultRole": {"id": "27295a0d-3c99-4b9e-8737-4f8214ed788a", "name": "default-roles-master", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "master"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "users": [{"id": "8c75fca9-b08b-4d6f-a4e9-29dff3d27421", "createdTimestamp": 1589747978814, "username": "admin", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"id": "49082b41-019d-4c64-b127-dac90386423f", "type": "password", "createdDate": *************, "secretData": "{\"value\":\"v5ktaLYXAJgePltpedJm6oZq+CzIQM+XzRs+oktwnUHusJiMMDHzkcNFUx2WQaem595/RGCTw/8tO8tcazV4Ww==\",\"salt\":\"rBWsuTNkZ1aXmkrzqVr2fA==\"}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\"}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access", "admin"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}, {"id": "38b42651-3512-4e99-b25d-f644c7c1e57d", "createdTimestamp": *************, "username": "master-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "325cbe42-fb5f-4493-b4ff-e0bbfd60cb1c", "clientId": "Migration-realm", "name": "Migration Realm", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": [], "optionalClientScopes": []}, {"id": "c9310e94-910d-4ca1-8e57-5a23bb55bc1a", "clientId": "Migration2-realm", "name": "Migration2 Realm", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": [], "optionalClientScopes": []}, {"id": "2c456fad-bad7-4d97-ac60-69758d2bc191", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/master/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/master/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "058f847f-6c23-4478-b97f-e166b88d9607", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/master/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/master/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "461b10c7-308c-48d0-a3a9-5958d4592e83", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "********-ff3b-4424-8111-426cf817b161", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "1cd64665-7ab2-488a-b20c-d92f14e9eaf3", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "e9d87f81-4073-4668-84e6-69d13a88799d", "clientId": "master-realm", "name": "master Realm", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "9a4f9905-8df8-42f7-916c-8d7a4f6fcc33", "clientId": "master-test-client", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"backchannel.logout.session.required": "true", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "e1b6b96e-cad0-40ee-a459-62c047527cba", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/master/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/master/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "bedf9fad-1958-440c-a6e4-bea14ee956d5", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "a2307f21-72c2-4a04-8124-6326d781ab1c", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "42bd993d-816e-447e-8fbc-4a7cd29c527b", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "f83c2255-10f9-4d93-b708-3c08390815cf", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "b7ce78b1-24ef-452e-b18f-ebca0460027f", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "cb94550f-390d-4cc8-841b-7840e14fc290", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "d453cb21-0d78-4eab-b3ec-264b4d3d956e", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "e64b22b7-a0e1-421d-85e6-6c0fe5f8894a", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "44669e61-bbf3-481f-9b1f-14f8a95325a3", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}, {"id": "cf79d887-fde2-4e8f-96a2-69679fcbdc1f", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "6e3c5d2b-377b-42d5-bf3d-a1ad4937fa9a", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "93081491-eb4f-4f80-b3bb-0b46a7033bf6", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "e2795b06-86e4-489e-9877-ed7aa9c731e3", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}]}, {"id": "d524acc0-3adb-4b70-93e6-bd9b97417a5f", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "d63fdbb8-c3df-44ee-9e14-eacc009caaa1", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}, {"id": "446be420-bad4-4cd9-97f7-047dcd679e04", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "7f7faf13-1cde-4ca8-bc79-d305eced49c5", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "8767e7fe-6be5-41eb-a09f-d31f5ac5f5dd", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "dc62ad36-c330-43e9-b088-3de97c29af43", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "d779d24a-f1db-41cc-ac0e-6632eacb3f84", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "1435aab7-5adf-429d-86a7-e05d9386af44", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "667deb0d-efe2-4920-9b65-001263dad7e1", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "839019c0-773d-4f41-9e6e-ee92863f3d9a", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "c15f46e8-f091-4386-84be-d915f4823abe", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "eacb8324-aa5a-4724-b921-59947eed16a5", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "4d12620f-b58e-447a-b044-13d95bfef5c5", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "519fc8ac-f0ba-4048-b8eb-a8b3e5ef49b9", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "a3d61c29-c102-4b25-baf7-0aa56e0f1fce", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "2f5e471b-351c-4f8c-b411-04ead5f4a0b6", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "10d9747d-4e91-41a1-85d5-c88118773509", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "9082e2e5-b8ec-4522-a990-ce8c4beddabe", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "c7d1afe1-3b7e-4d9c-8b5a-94ac3692cd0e", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "30b30244-eb07-4bd3-ae50-1c99db8dca24", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "7309d3b3-2bed-472c-9b14-cbf0ea0d67e3", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "cea268c9-27fc-430b-947e-c6b504923c3e", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "9b0678bd-20ab-4811-a7b0-4629226e4d01", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}]}], "defaultDefaultClientScopes": ["profile", "role_list", "roles", "web-origins", "email", "acr"], "defaultOptionalClientScopes": ["offline_access", "phone", "microprofile-jwt", "address"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "0e41706e-d759-4542-9612-8b33d55e6415", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "c48540d9-6afa-4dde-83e4-7285065b85ac", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "486f6f9f-d60a-4ae6-95ef-bf29677b3863", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "111d8617-32a8-4c3d-93f7-5dabd55a15cb", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "3d2e422b-aa85-4fcf-bbdc-0c6df62b96ac", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-property-mapper", "oidc-address-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "oidc-usermodel-attribute-mapper", "oidc-full-name-mapper"]}}, {"id": "353dc8ba-dac8-45a1-8266-61367664adff", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-property-mapper", "oidc-address-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper"]}}, {"id": "64cc7dba-632b-47b6-8aa3-fdc5951c8946", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "367a4845-72f1-4361-a288-9e6df4dd89b1", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}], "org.keycloak.keys.KeyProvider": [{"id": "1c39497b-d768-419b-bb33-e32de9fd6805", "name": "rsa-enc-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAjH2Dh2NzZZy/a8cVPVtAWHhRdQcHXQa8sbBxPL74vLZaA05HFbuaAkaEz5ig+Z5jxiVBpUChk1CrAPqdrsXaxCsXOSP44hWzlQR+otNit5PunuJgZqliNfOuWrPiOUUNaHhAJTSXzd4VjBfcQTTVq2CO/Y0dimCRxGuYxg219+SOW9LbreOCwDN3tzlHgfpeHQF2r7f17bPAsiP8WKvwvcle+PRIYhBRp6O9S/28GkaXVn3TQxjFWLJTEydxzz7wWTQJ73dHOHX+6ZSxGL+C8Pt9NOnjMegTNCTDaS6hfIRf6W3Jj1e1Sc3TltoQlnGWMEIG8jsETYiJMQeunerkWQIDAQABAoIBABnner0YBUsTH+iLiMsLbE3jwYFGjoEEoP5Sfk6pF8wUA3zgjhLfyIHq5sowZl4gal06uWPzCtfS4/UGQNTc/n11RI7wpw9o2Pf9ICx9tZIL4M/DBetoCAfSqq9mw6uzo8lDgvBZRjRbYrW2IJKVX2B+KV9vCadeLwJ8pGrAVHEkmV649UlEjEVFf0REz1wS8LLUS4HiQKl36PTfKZRF1FBpwM19dAeb7ixJsv0p3QDONRODWX6hUSmAMdWmqwKiZxj54oOvilQ8aLMvY3i7tVJN94P6qMHZRAyHuTL30hv1k9f+uuYbgrP2n664gFjSxU9awyUrd1+MjcVsX02xbVECgYEAyLGdu//sMU6Ot4Arcn6EvTZJKV/8Y6jVo+A5q0yHCxuQn5g0GZ99tIDoEhArhFoY+CVK/MjB8ij6VhU2wiIfEXlbS4aQdzgju1KX3k3EQMmAUbxLNNPWIHGrNS3QdlAsUtNRX3UfNnBOa9NZM2Xdebhj73EwwL5YuvPsBDfp2isCgYEAszS1Vd24fjugau3WQBs5Bd/ADHBntZyPwuTlAsJuxyoZ4FueVtHTqBQCGm/VZFoq9H0zlED+yMnVEXXAijGLHW0AwH64liDjZ3GH02Is32OpZj1XJRPhLre3t932bIM1kOiiwEPyUEDv8p/S6Yika71wC8+1V7+TLYeMm903zYsCgYEAyKOBTQUjLXwbA+r4BKPlsoAGDn4uhbextf6hyNZeH8W0BUmVtJQViRJicESILO4iDlrCKAvQkXdUJiFZOCYCp2aK1eu2m+mdF1YQDCf07sxEI/K5wRuS86aQAj9GLDIkzb0DmgouZS9Lx1Jo2BCbpWepSSUBF4B0lprrNqBrgzcCgYA9ki1/jAdYb7olIwRK0E+eQgsk+vXXAyytpKE+enk/OqrbV/8mCz+ybvVuCeCCcyxxBhdTp6UibZ4kOpXlpTIL8Sih+qgHBNO89dfZ+9A5DG/mbcrkNVlYCM05giypicc8u4E3l+DITBdlAB6iGE2Nqu+1Ty8aL/ssWHcqEi6bEwKBgASdzIv3SlFj+f1Fl0klRWsB3khk33djqdiDQbdblWRio3PZyfCVICUqUvrzgUj5zOSuj38vus9mvP7w0KSUKC+lXbFNSejXwlTUdA4iuc3J0BeLnioiruM0wroAGVrntoCfdr6E8CjcDIAzB7QmGo0B54uvvPcHMVQIVXCJrhvt"], "keyUse": ["enc"], "certificate": ["MIICmzCCAYMCBgGBi/E1qTANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZtYXN0ZXIwHhcNMjIwNjIyMTUwMjI5WhcNMzIwNjIyMTUwNDA5WjARMQ8wDQYDVQQDDAZtYXN0ZXIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCMfYOHY3NlnL9rxxU9W0BYeFF1BwddBryxsHE8vvi8tloDTkcVu5oCRoTPmKD5nmPGJUGlQKGTUKsA+p2uxdrEKxc5I/jiFbOVBH6i02K3k+6e4mBmqWI1865as+I5RQ1oeEAlNJfN3hWMF9xBNNWrYI79jR2KYJHEa5jGDbX35I5b0tut44LAM3e3OUeB+l4dAXavt/Xts8CyI/xYq/C9yV749EhiEFGno71L/bwaRpdWfdNDGMVYslMTJ3HPPvBZNAnvd0c4df7plLEYv4Lw+3006eMx6BM0JMNpLqF8hF/pbcmPV7VJzdOW2hCWcZYwQgbyOwRNiIkxB66d6uRZAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAHS5Z8YUxyPL42HQtF4jxIPWlKIzZyzt0CeI6zQBAfewYeMCyV31Ex+MwOIfuHuo2RbLxv9Tsi6xRIyrWUJmzIwnhYgsWodfZdUeJwhj21YY0aEBWRcpI7bceR5QSgQ6QlV9gUOjytOjnxFpMr7362zKVwoyx35MWopaBoUSMnRkWJAwsb2Oxb2vb7+lJ9IBjPLCTD3xnES5MDybBWpdlnczjYK3QwnjjOk6mu1nzIBulQg8WSVHhAGB7kVXrhEdR5dtre2SN1F0svo/qkN1Iq7+XBI1Byaaw0z7A6fhAAp6eXr9Vl12TXYK6SuR94uSTGM6kDuXRt5p7qMnMbPgqlM="], "priority": ["100"]}}, {"id": "80d32f3b-5504-4c57-b60e-a2c0dc22fb01", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEogIBAAKCAQEAs6s8OEwa0wsXQB5wTV2FYuJRtyri115+lMvyU32/26748n/d82TzKcudZe5pKukNznKzpA1RBd5sPsvA31LFiZxQx6yR7lNtUTbYUFqNfxtaK6MDAysEB3oSR89Bz7l48NOd5GueiZJz3PHxN4A6WRb2sWz7yQ/QNG4t/LgbWRkyJelttL/XeLfqWMw79kh+U6PXovkcJu6TCmM7OiAzK10Dk3gIVmXBV3bqML5AFdV8jx8OIfRBa1Fma1ln7jZAVmGIAmPCd4M+hJVBskZFDM/698x4vnCncv/2dCFmKGWRM8F2JJpLMqcHqhMwmuJkx97m46vACMB/PCgduX9a4wIDAQABAoIBACu7cl0BqUXVXUNuJUevi9gV3m4HMk/Z4FW3jrRj119Rp6bfGNc1qzpxJU66ST74mzO1F6TG2QHksdRXJAs3yn1EXyU7DApGpEUwZFaqs+ZMchZNB16uCb0IKYrxF8PIEXgNClGfIGe8DhepjF1GCO3GeSWWsXKvNq6MYwUwiveUkWPkF8q1NSiiIzjsYEoI+RpDHvZPMkevGpceOEvmKQS8KCb/OZCf9w5vE7LWWxrP2ufDttMu9lkuY9prPmWyi/NvcRW/pZ6nHVyvBDglvd37APFicilxBFgxY2R9CIwvBj59X+STx8Yz07gGzoUi+2GSI3A4q0ehfsdBklW2n4ECgYEA76GqeiXpLxCiOihETBd/SUpRTiupJT0oDXlEQweTmGFHUAXbaNF/drRUkYWwxCjZwE0pekJZgu9+UGJ0IA2iR+23NykYcuOgh0hFIziQRhbpA/MVVkI4HdkVysIKVK/VBV13vUp0BvOJtkiFeopdFFyp7CkM0PbN80IZMRd8R4cCgYEAv/EHTTCm5pRjjmAKOnzOzkbASWEAvcy8/BhMK6VZr2m2fVLzJUyDkYgTKaYDPw1q0z6kt7XqMJ2kZsap00nWtETKmKlEMhONiC+IVlYd5DJbjlBogY5miPCccKwtdG+2gmBfURXrAt3egbhuBfw2OHdQoVXFx0QRxRibvMG1MMUCgYA2LlV/XMcGjzo+8A/RLJbpGVac2B5cfyFpOnwGs9XZZFVdcJWVc5jgXBGq/ZFkKOBjLI8uR4Y5prDmt/wNq0d//+2nGTGf+Hu5WsvXFNj6H//7lqDCaiKKDSVyPMFdOIxvtBx4vjbRw4rlniwf8tzs167L6NQvAz65u746bVb6TQKBgFxubd22OCmB/A9sIIDZ6Z1cZOzxUbIVDk04RjYegnn+pmrjr0Oe/RzwMooz8AxFIdRorlnpuMWApNL5iH6/aC2XkxjBKp4S3G1gtn8dKXfi6EQ4ZJs/QBFiDXQU9i7EPVe7oSsjKvFOdwBxGEq5kkLO8n0/GOdE2GnZ1+WmQuMhAoGAd4Ksf3t8r6Ro39D/qhpI6PBar+jdtg3IcmFKRkRvVKT+vyxf/PA9bjKf+lFye/ZcIRWbpZxEq8dV5/ZWgqmspL3B6nzoqziEIhpJwesmQf2UXCdXE6gNSZdqpkUlMYuauLRr+XYqK43lnVLyE0S7EjYoxy8fixuh7+USISJaRxY="], "keyUse": ["sig"], "certificate": ["MIICmzCCAYMCBgGBi/E2UTANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZtYXN0ZXIwHhcNMjIwNjIyMTUwMjMwWhcNMzIwNjIyMTUwNDEwWjARMQ8wDQYDVQQDDAZtYXN0ZXIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCzqzw4TBrTCxdAHnBNXYVi4lG3KuLXXn6Uy/JTfb/brvjyf93zZPMpy51l7mkq6Q3OcrOkDVEF3mw+y8DfUsWJnFDHrJHuU21RNthQWo1/G1orowMDKwQHehJHz0HPuXjw053ka56JknPc8fE3gDpZFvaxbPvJD9A0bi38uBtZGTIl6W20v9d4t+pYzDv2SH5To9ei+Rwm7pMKYzs6IDMrXQOTeAhWZcFXduowvkAV1XyPHw4h9EFrUWZrWWfuNkBWYYgCY8J3gz6ElUGyRkUMz/r3zHi+cKdy//Z0IWYoZZEzwXYkmksypweqEzCa4mTH3ubjq8AIwH88KB25f1rjAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAIfjRRKyq4ZFAsip66Wn7YI5TOWR7WXHbvDqd0KvIKulAMp//xC1yMr55SG8uXJiYg+flnD1ZElaVh+Ct2EVZ7Kfagl4gEQ4zD7YZX29OBRgE/cMAS6GiGsAfI6MTvCEMry890kfF1d/I+xcgxp4cZCBhoJ5kuqqLfNB51bviKum2zk2jONVskYwQhXD/p9MF35+TjWA5gNgkK9cRad1AjoFZqW0Z+dkeOeju4KC+tdhIga0oLXLBXFBQsFrag8AQDt2uQTcTvYO7emai9mMikfe+1FA2Oai0ESC38N2N+LqwqXJKPKearetrges2mRr9W9+UpG3ArQGMBU7dUkXV1k="], "priority": ["100"]}}, {"id": "584678ef-194f-49c1-b300-cd006a75ccd2", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["de1d53d5-bc73-4849-8bba-95d3cffb81d1"], "secret": ["jIljWI2LOvbe-i2Bp60dVA"], "priority": ["100"]}}, {"id": "2e7e389b-88f7-42c3-b051-1aae4ad8c2e3", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["af696540-89dc-4424-8f68-d9d0fe9e65aa"], "secret": ["iLPc1p6Y_X23TqL8KqqmvLfWCgqD7aKz1avdOjsnKiTFCQ3mGeILRsdpjSD6t5KdSdKAPBClvjd50ZfEdgH9HQ"], "priority": ["100"], "algorithm": ["HS256"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "0b0a29d4-a9d6-4158-8448-9f95c01dceb1", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "069c6bde-ce31-4b13-acf0-f677f93550b1", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "basic-auth-otp", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "6ce6edb4-4ae4-4949-8f2f-87ff059b04ed", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "df41efdb-b7c2-4d51-99dd-f1f1821617a5", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "aa58b640-ad51-481f-b81d-e93893d321bd", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "d3ddbba2-b6cb-412c-9729-4fdf82aa8eea", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "7db4d173-cc2f-4f39-aa71-955a9d39317f", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "0a896187-7565-4483-89b6-12318d2718b3", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "c52b16c5-f019-4fb0-b38e-bf02dd305ecf", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "213a5dbb-7216-4823-9df4-0c8606c98834", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "d9e5f003-4909-4f88-ba9c-c1339c6e9457", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "7b528ff0-aa80-4826-9de8-b482b4ace4a6", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "dc5ed3ea-558d-4a77-9da3-f0d17c005d0d", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "d68192b0-49e3-42b6-ada2-18a7e58e3a94", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "7c0c22c7-5986-4a3b-a73d-de92ea7bc8f9", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "b02d9340-b080-4795-b789-8f53c22429a3", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Authentication Options", "userSetupAllowed": false}]}, {"id": "8628b7bb-f197-45a5-b905-4795a8b5a993", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "3c1a66de-5188-49e9-9a6b-542c6dbcabe3", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-profile-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "86dca791-cee3-425b-9db3-e11f77be24ef", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "148930ff-ac84-4e59-84b8-7f13943957ac", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "847391c3-2e86-4830-b68f-47192a56b874", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "0bd32dd5-c5d4-4c6c-9cbb-c1e42847b7f8", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "clientOfflineSessionMaxLifespan": "0", "oauth2DevicePollingInterval": "600", "clientSessionIdleTimeout": "0", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "clientOfflineSessionIdleTimeout": "0", "cibaInterval": "5"}, "keycloakVersion": "19.0.3", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}]