doSave=Сохранить
doCancel=Отмена
doLogOutAllSessions=Выйти из всех сессий
doRemove=Удалить
doAdd=Добавить
doSignOut=Выход
doLogIn=Вход
accountManagementWelcomeMessage=Добро пожаловать в консоль управления вашей учетной записью
accountSecuritySidebarTitle=Безопасность
editAccountHtmlTitle=Изменение учетной записи
federatedIdentitiesHtmlTitle=Федеративные идентификаторы
accountLogHtmlTitle=Лог учетной записи
changePasswordHtmlTitle=Смена пароля
sessionsHtmlTitle=Сессии
accountManagementTitle=Управление учетной записью
authenticatorTitle=Аутентификатор
applicationsHtmlTitle=Приложения
applicationsIntroMessage=Отслеживайте и управляйте разрешениями приложений на доступ к вашей учетной записи
accountSecurityIntroMessage=Изменение пароля и доступа к учетной записи

# Personal info page
personalInfoSidebarTitle=Личная информация
personalInfoHtmlTitle=Личная информация
personalSubMessage=Управление данными о себе.
personalInfoIntroMessage=Управление данными о себе
applicationName=Имя
applicationType=Тип приложения
client=Клиент
signingInSidebarTitle=Вход

#Authenticator - Mobile Authenticator setup
authenticatorMobileSetupTitle=Настройка мобильного приложения аутентификатора
totpStep1=Установите одно из следующих приложений на ваш мобильный телефон:
totpStep2=Откройте приложение и просканируйте QR-код:
totpStep3=Введите одноразовый код, выданный приложением, и нажмите Подтвердить для завершения настройки.
totpManualStep2=Откройте приложение и введите ключ:
totpManualStep3=Используйте следующие настройки, если приложение позволяет их устанавливать:
totpStep3DeviceName=Укажите имя устройства, которое поможет вам найти его в списке ваших устройств.
totpUnableToScan=Не удается выполнить сканирование?
totpScanBarcode=Сканировать QR-код?
authenticatorCode=Одноразовый код
totpDeviceName=Имя устройства
totpType=Тип
totpAlgorithm=Алгоритм
totpDigits=Количество цифр
totpInterval=Интервал
totpCounter=Счетчик


# Device activity page
deviceActivitySidebarTitle=Активные устройства
deviceActivityHtmlTitle=Активные устройства
device-activity=Активные устройства
email=E-mail
firstName=Имя
givenName=Имя
fullName={0} {1}
lastName=Фамилия
familyName=Фамилия
password=Пароль
passwordConfirm=Подтверждение пароля
passwordNew=Новый пароль
username=Имя пользователя
address=Адрес
street=Улица
locality=Город
region=Регион
postal_code=Почтовый индекс
country=Страна
emailVerified=E-mail подтвержден
gssDelegationCredential=Делегирование учетных данных через GSS
role_admin=Администратор
role_realm-admin=Администратор realm
role_create-realm=Создать realm
role_view-realm=Просмотр realm
role_view-users=Просмотр пользователей
role_view-applications=Просмотр приложений
role_view-clients=Просмотр клиентов
role_view-events=Просмотр событий
role_view-identity-providers=Просмотр провайдеров учетных записей
role_manage-realm=Управление realm
role_manage-users=Управление пользователями
role_manage-applications=Управление приложениями
role_manage-identity-providers=Управление провайдерами учетных записей
role_manage-clients=Управление клиентами
role_manage-events=Управление событиями
role_view-profile=Просмотр профиля
role_manage-account=Управление учетной записью
role_read-token=Чтение токена
role_offline-access=Доступ оффлайн
role_uma_authorization=Получение разрешений
client_account=Учетная запись
client_security-admin-console=Консоль администратора безопасности
client_admin-cli=Командный интерфейс администратора
client_realm-management=Управление Realm
client_broker=Брокер


requiredFields=Обязательные поля
allFieldsRequired=Все поля обязательны
backToApplication=&laquo; Назад в приложение
backTo=Назад в {0}
date=Дата
event=Событие
ip=IP
clients=Клиенты
details=Детали
started=Начата
lastAccess=Последний доступ
expires=Истекает
applications=Приложения
account=Учетная запись
federatedIdentity=Федеративный идентификатор
authenticator=Аутентификатор
sessions=Сессии
log=Журнал
application=Приложение
grantedPermissions=Согласованные разрешения
grantedPersonalInfo=Согласованная персональная информация
additionalGrants=Дополнительные согласования
action=Действие
inResource=в
fullAccess=Полный доступ
offlineToken=Оффлайн токен
revoke=Отозвать согласование


missingUsernameMessage=Введите имя пользователя.
missingFirstNameMessage=Введите имя.
invalidEmailMessage=Введите корректный E-mail.
missingLastNameMessage=Введите фамилию.
missingEmailMessage=Введите E-mail.
missingPasswordMessage=Введите пароль.
notMatchPasswordMessage=Пароли не совпадают.
missingTotpMessage=Введите код аутентификатора.
invalidPasswordExistingMessage=Существующий пароль неверный.
invalidPasswordConfirmMessage=Подтверждение пароля не совпадает.
invalidTotpMessage=Неверный код аутентификатора.
usernameExistsMessage=Имя пользователя уже существует.
emailExistsMessage=E-mail уже существует.
readOnlyUserMessage=Вы не можете обновить информацию вашей учетной записи, т.к. она доступна только для чтения.
readOnlyUsernameMessage=Вы не можете обновить имя пользователя вашей учетной записи, т.к. оно доступно только для чтения.
readOnlyPasswordMessage=Вы не можете обновить пароль вашей учетной записи, т.к. он доступен только для чтения.
successTotpMessage=Аутентификатор в мобильном приложении сконфигурирован.
successTotpRemovedMessage=Аутентификатор в мобильном приложении удален.
successGrantRevokedMessage=Согласование отозвано успешно.
accountUpdatedMessage=Ваша учетная запись обновлена.
accountPasswordUpdatedMessage=Ваш пароль обновлен.
missingIdentityProviderMessage=Провайдер учетных записей не задан.
invalidFederatedIdentityActionMessage=Некорректное или недопустимое действие.
identityProviderNotFoundMessage=Заданный провайдер учетных записей не найден.
federatedIdentityLinkNotActiveMessage=Идентификатор больше не активен.
federatedIdentityRemovingLastProviderMessage=Вы не можете удалить последний федеративный идентификатор, т.к. Вы не имеете пароля.
identityProviderRedirectErrorMessage=Ошибка перенаправления в провайдер учетных записей.
identityProviderRemovedMessage=Провайдер учетных записей успешно удален.
identityProviderAlreadyLinkedMessage=Федеративный идентификатор, возвращенный {0} уже используется другим пользователем.
staleCodeAccountMessage=Страница устарела. Попробуйте еще раз.
consentDenied=В согласовании отказано.
accountDisabledMessage=Учетная запись заблокирована, обратитесь к администратору.
accountTemporarilyDisabledMessage=Учетная запись временно заблокирована, обратитесь к администратору или попробуйте позже.
invalidPasswordMinLengthMessage=Некорректный пароль: длина пароля должна быть не менее {0} символа(ов).
invalidPasswordMinLowerCaseCharsMessage=Некорректный пароль: пароль должен содержать не менее {0} символа(ов) в нижнем регистре.
invalidPasswordMinDigitsMessage=Некорректный пароль: пароль должен содержать не менее {0} цифр(ы).
invalidPasswordMinUpperCaseCharsMessage=Некорректный пароль: пароль должен содержать не менее {0} символа(ов) в верхнем регистре.
invalidPasswordMinSpecialCharsMessage=Некорректный пароль: пароль должен содержать не менее {0} спецсимвола(ов).
invalidPasswordNotUsernameMessage=Некорректный пароль: пароль не должен совпадать с именем пользователя.
invalidPasswordRegexPatternMessage=Некорректный пароль: пароль не удовлетворяет регулярному выражению.
invalidPasswordHistoryMessage=Некорректный пароль: пароль не должен совпадать с последним(и) {0} паролями.
invalidPasswordGenericMessage=Некорректный пароль: новый пароль не соответствует правилам пароля.
doLink=Связь
noAccessMessage=Доступ не разрешен
linkedAccountsSidebarTitle=Связанные аккаунты
linkedAccountsHtmlTitle=Связанные аккаунты
