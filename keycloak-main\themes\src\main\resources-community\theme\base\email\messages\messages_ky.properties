emailVerificationSubject=Электрондук почтаңызды ырастаңыз
emailVerificationBody={2} каттоо эсеби бул электрондук почта дареги менен түзүлгөн. Эгер бул сиз болсоңуз, электрондук почта дарегиңизди ырастаңыз:\n\n{0}\n\nБул шилтеме {3} ичинде мөөнөтү бүтөт.\n\nЭгер бул каттоо эсебин түзгөн эмессиз, анда бул билдирүүгө көңүл бурбаңыз.
emailVerificationBodyHtml=<p>{2} каттоо эсеби бул электрондук почта дареги менен түзүлгөн. Эгер бул сиз болсоңуз, төмөндөгү шилтемени басып, даректи ырастаңыз:</p><p><a href="{0}">Электрондук почтаны ырастоочу шилтеме</a></p><p>Бул шилтеме {3} ичинде мөөнөтү бүтөт.</p><p>Эгер сиз бул каттоо эсебин түзгөн эмессиз, бул билдирүүгө көңүл бурбаңыз.</p>

orgInviteSubject="{0}" уюмуна чакыруу
orgInviteBody=Сиз "{3}" уюмуна кошулуу үчүн чакырылдыңыз. Кошулуу үчүн төмөнкү шилтемени басыңыз:\n\n{0}\n\nБул шилтеме {4} ичинде мөөнөтү бүтөт.\n\nЭгер кошулгуңуз келбесе, бул билдирүүгө көңүл бурбаңыз.
orgInviteBodyHtml=<p>Сиз "{3}" уюмуна кошулуу үчүн чакырылдыңыз. Кошулуу үчүн төмөнкү шилтемени басыңыз:</p><p><a href="{0}">Уюмга кошулуу шилтемеси</a></p><p>Бул шилтеме {4} ичинде мөөнөтү бүтөт.</p><p>Эгер кошулгуңуз келбесе, бул билдирүүгө көңүл бурбаңыз.</p>

orgInviteBodyPersonalized=Салам, "{5}" "{6}".\n\nСиз "{3}" уюмуна кошулуу үчүн чакырылдыңыз. Кошулуу үчүн төмөнкү шилтемени басыңыз:\n\n{0}\n\nБул шилтеме {4} ичинде мөөнөтү бүтөт.\n\nЭгер кошулгуңуз келбесе, бул билдирүүгө көңүл бурбаңыз.
orgInviteBodyPersonalizedHtml=<p>Салам, {5} {6}.</p><p>Сиз "{3}" уюмуна кошулуу үчүн чакырылдыңыз. Кошулуу үчүн төмөнкү шилтемени басыңыз:</p><p><a href="{0}">Уюмга кошулуу шилтемеси</a></p><p>Бул шилтеме {4} ичинде мөөнөтү бүтөт.</p><p>Эгер кошулгуңуз келбесе, бул билдирүүгө көңүл бурбаңыз.</p>

emailUpdateConfirmationSubject=Жаңы электрондук почтаны ырастаңыз
emailUpdateConfirmationBody={2} каттоо эсебиңизди {1} электрондук почта дареги менен жаңыртуу үчүн төмөнкү шилтемени басыңыз:\n\n{0}\n\nБул шилтеме {3} ичинде мөөнөтү бүтөт.\n\nЭгер бул өзгөртүүнү каалабасаңыз, бул билдирүүгө көңүл бурбаңыз.
emailUpdateConfirmationBodyHtml=<p>{2} каттоо эсебиңизди {1} электрондук почта дареги менен жаңыртуу үчүн төмөнкү шилтемени басыңыз:</p><p><a href="{0}">{0}</a></p><p>Бул шилтеме {3} ичинде мөөнөтү бүтөт.</p><p>Эгер бул өзгөртүүнү каалабасаңыз, бул билдирүүгө көңүл бурбаңыз.</p>

emailTestSubject=[KEYCLOAK] - SMTP тест билдирүү
emailTestBody=Бул тесттик билдирүү
emailTestBodyHtml=<p>Бул тесттик билдирүү</p>

identityProviderLinkSubject={0} менен байланыш
identityProviderLinkBody=Бирөө сиздин "{1}" каттоо эсебиңизди "{0}" колдонуучусунун эсеби {2} менен байланыштырууну каалайт. Эгер бул сиз болсоңуз, төмөнкү шилтемени басыңыз:\n\n{3}\n\nБул шилтеме {5} ичинде мөөнөтү бүтөт.\n\nЭгер байланышкыңыз келбесе, бул билдирүүгө көңүл бурбаңыз. Эгер байланыштырылса, сиз {0} аркылуу {1} системасына кире аласыз.
identityProviderLinkBodyHtml=<p>Бирөө сиздин <b>{1}</b> каттоо эсебиңизди <b>{0}</b> колдонуучусу {2} менен байланыштырууну каалайт. Эгер бул сиз болсоңуз, төмөнкү шилтемени басыңыз:</p><p><a href="{3}">Каттоо эсептерди байланыштыруу</a></p><p>Бул шилтеме {5} ичинде мөөнөтү бүтөт.</p><p>Эгер байланыштыруу каалабасаңыз, бул билдирүүгө көңүл бурбаңыз. Эгер байланышса, {0} аркылуу {1} системасына кире аласыз.</p>

passwordResetSubject=Сырсөздү кайра коюу
passwordResetBody=Сиздин {2} каттоо эсебиңиздин сырсөзүн өзгөртүү суранычы келип түштү. Эгер бул сиз болсоңуз, төмөнкү шилтемени басып жаңыртыңыз:\n\n{0}\n\nБул шилтеме {3} ичинде мөөнөтү бүтөт.\n\nЭгер сырсөздү жаңырткыңыз келбесе, бул билдирүүгө көңүл бурбаңыз.
passwordResetBodyHtml=<p>{2} каттоо эсебиңиздин сырсөзүн өзгөртүү суранычы келип түштү. Эгер бул сиз болсоңуз, төмөнкү шилтемени басыңыз:</p><p><a href="{0}">Сырсөздү өзгөртүү шилтемеси</a></p><p>Бул шилтеме {3} ичинде мөөнөтү бүтөт.</p><p>Эгер жаңырткыңыз келбесе, бул билдирүүгө көңүл бурбаңыз.</p>

executeActionsSubject=Каттоо эсебиңизди жаңыртыңыз
executeActionsBody=Администратор сиздин {2} каттоо эсебиңиз үчүн төмөнкү аракеттерди аткарууну суранды: {3}. Төмөнкү шилтемени басып улантыңыз:\n\n{0}\n\nБул шилтеме {4} ичинде мөөнөтү бүтөт.\n\nЭгер муну билбесеңиз, бул билдирүүгө көңүл бурбаңыз.
executeActionsBodyHtml=<p>Администратор сиздин {2} каттоо эсебиңиз үчүн төмөнкү аракеттерди аткарууну суранды: {3}. Улантуу үчүн шилтемени басыңыз:</p><p><a href="{0}">Каттоо эсебин жаңыртуу шилтемеси</a></p><p>Бул шилтеме {4} ичинде мөөнөтү бүтөт.</p><p>Эгер муну билбесеңиз, бул билдирүүгө көңүл бурбаңыз.</p>

eventLoginErrorSubject=Кирүү катасы
eventLoginErrorBody={0} системасына {1} дарегинен кирүү аракетинде ката кетти. Эгер бул сиз болбосоңуз, администраторго кайрылыңыз.
eventLoginErrorBodyHtml=<p>{0} системасына {1} дарегинен кирүү аракетинде ката кетти. Эгер бул сиз болбосоңуз, администраторго кайрылыңыз.</p>

eventRemoveTotpSubject=OTP өчүрүлдү
eventRemoveTotpBody={0} системасынан {1} аркылуу OTP каттоо эсебиңизден өчүрүлдү. Эгер бул сиз болбосоңуз, администраторго кайрылыңыз.
eventRemoveTotpBodyHtml=<p>{0} системасынан {1} аркылуу OTP каттоо эсебиңизден өчүрүлдү. Эгер бул сиз болбосоңуз, администраторго кайрылыңыз.</p>

eventUpdatePasswordSubject=Сырсөз өзгөртүлдү
eventUpdatePasswordBody=Сырсөзүңүз {0} системасында {1} аркылуу өзгөртүлдү. Эгер бул сиз болбосоңуз, администраторго кайрылыңыз.
eventUpdatePasswordBodyHtml=<p>Сырсөзүңүз {0} системасында {1} аркылуу өзгөртүлдү. Эгер бул сиз болбосоңуз, администраторго кайрылыңыз.</p>

eventUpdateTotpSubject=OTP жаңыртылды
eventUpdateTotpBody=OTP {0} системасында {1} аркылуу жаңыртылды. Эгер бул сиз болбосоңуз, администраторго кайрылыңыз.
eventUpdateTotpBodyHtml=<p>OTP {0} системасында {1} аркылуу жаңыртылды. Эгер бул сиз болбосоңуз, администраторго кайрылыңыз.</p>

eventUpdateCredentialSubject=Тастыктоо маалыматы жаңыртылды
eventUpdateCredentialBody=Сиздин {0} тастыктоо маалыматыңыз {1} системасында {2} аркылуу жаңыртылды. Эгер бул сиз болбосоңуз, администраторго кайрылыңыз.
eventUpdateCredentialBodyHtml=<p>Сиздин {0} тастыктоо маалыматыңыз {1} системасында {2} аркылуу жаңыртылды. Эгер бул сиз болбосоңуз, администраторго кайрылыңыз.</p>

eventRemoveCredentialSubject=Тастыктоо маалыматы өчүрүлдү
eventRemoveCredentialBody={0} тастыктоо маалыматыңыз {1} системасында {2} аркылуу өчүрүлдү. Эгер бул сиз болбосоңуз, администраторго кайрылыңыз.
eventRemoveCredentialBodyHtml=<p>{0} тастыктоо маалыматыңыз {1} системасында {2} аркылуу өчүрүлдү. Эгер бул сиз болбосоңуз, администраторго кайрылыңыз.</p>

eventUserDisabledByTemporaryLockoutSubject=Убактылуу бөгөт коюлду
eventUserDisabledByTemporaryLockoutBody=Бир нече жолу кирүү аракетинен улам {0} системасында колдонуучу убактылуу бөгөттөлдү. Зарыл болсо администраторго кайрылыңыз.
eventUserDisabledByTemporaryLockoutHtml=<p>Бир нече жолу кирүү аракетинен улам {0} системасында колдонуучу убактылуу бөгөттөлдү. Зарыл болсо администраторго кайрылыңыз.</p>

eventUserDisabledByPermanentLockoutSubject=Туруктуу бөгөт коюлду
eventUserDisabledByPermanentLockoutBody=Бир нече жолу кирүү аракетинен улам {0} системасында колдонуучу туруктуу бөгөттөлдү. Администраторго кайрылыңыз.
eventUserDisabledByPermanentLockoutHtml=<p>Бир нече жолу кирүү аракетинен улам {0} системасында колдонуучу туруктуу бөгөттөлдү. Администраторго кайрылыңыз.</p>

requiredAction.CONFIGURE_TOTP=OTP жөндөө
requiredAction.TERMS_AND_CONDITIONS=Шарттар менен макулдук
requiredAction.UPDATE_PASSWORD=Сырсөздү жаңыртуу
requiredAction.UPDATE_PROFILE=Профилди жаңыртуу
requiredAction.VERIFY_EMAIL=Электрондук почтаны ырастаңыз
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Калыбына келтирүү коддорун жаратыңыз
# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be '{0,choice,0#minut|1#minuta|2#minuty|2<minut}'
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#секунд|1#секунд|1<секунд}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#мүнөт|1#мүнөт|1<мүнөт}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#саат|1#саат|1<саат}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#күн|1#күн|1<күн}

emailVerificationBodyCode=Электрондук почта дарегиңизди ырастаңыз. Төмөндөгү кодду киргизиңиз:\n\n{0}
emailVerificationBodyCodeHtml=<p>Электрондук почта дарегиңизди ырастаңыз. Төмөндөгү кодду киргизиңиз:</p><p><b>{0}</b></p>