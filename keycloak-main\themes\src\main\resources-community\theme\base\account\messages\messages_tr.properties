doSave=Kaydet
doCancel=İptal
doLogOutAllSessions=Tüm Oturumları Kapat
doRemove=Sil
doAdd=Ekle
doSignOut=Çıkış
doLogIn=Oturum aç
doLink=Bağlantı


editAccountHtmlTitle=Hesabım
personalInfoHtmlTitle=Kişisel bilgi
federatedIdentitiesHtmlTitle=Değiştirilen Kimlikler
accountLogHtmlTitle=Kullanıcı Logları
changePasswordHtmlTitle=Şifre Değiştirme
deviceActivityHtmlTitle=Cihaz Etkinliği
sessionsHtmlTitle=Oturum
accountManagementTitle=Keycloak Kullanıcı Hesabı Yönetimi
authenticatorTitle=Kimlik Doğrulama
applicationsHtmlTitle=Uygulama
linkedAccountsHtmlTitle=Bağlantılı Hesaplar

accountManagementWelcomeMessage=Keycloak Hesap Yönetimine Hoş Geldiniz
personalInfoIntroMessage=Temel bilgilerinizi yönetin
accountSecurityTitle=Hesap Güvenliği
accountSecurityIntroMessage=Şifrenizi ve hesap erişiminizi kontrol edin
applicationsIntroMessage=Hesabınıza erişmek için uygulama izninizi takip edin ve yönetin
resourceIntroMessage=Kaynaklarınızı ekip üyeleri arasında paylaşın
passwordLastUpdateMessage=Şifreniz güncellendi
updatePasswordTitle=Şifre güncelle
updatePasswordMessageTitle=Güçlü bir şifre seçtiğinizden emin olun
updatePasswordMessage=Güçlü bir şifre, sayılar, harfler ve sembollerin karışımından oluşmalıdır. Tahmin etmesi zor ve gerçek bir kelimeye benzemeyen şifre sadece bu hesap için kullanılır.
personalSubTitle=Kişisel Bilgileriniz
personalSubMessage=Bu temel bilgileri yönetin: adınız, soyadınız ve e-posta adresiniz

authenticatorCode=Kimlik Doğrulama Kodu
email=E-Mail
firstName=Ad
givenName=Ad
fullName=Ad Soyad
lastName=Soyad
familyName=Soyad
password=Şifre
currentPassword=Şimdiki Şifre
passwordConfirm=Şifre Doğrulama
passwordNew=Yeni Şifre
username=Kullanıcı Adı
address=Adres
street=Cadde
region=Bölge
postal_code=Posta Kodu
locality=Şehir
country=Ülke
emailVerified=E-Mail Doğrulandı
gssDelegationCredential=GSS Yetki Bilgisi

profileScopeConsentText=Kullanıcı profili
emailScopeConsentText=Email adresi
addressScopeConsentText=Adres
phoneScopeConsentText=Telefon numarası
offlineAccessScopeConsentText=Çevrimdışı Erişim
samlRoleListScopeConsentText=Rollerim
rolesScopeConsentText=Kullanıcı rolleri

role_admin=Admin
role_realm-admin=Realm Admin
role_create-realm=Realm Oluştur
role_view-realm=Realm görüntüle
role_view-users=Kullanıcıları görüntüle
role_view-applications=Uygulamaları görüntüle
role_view-clients=İstemci görüntüle
role_view-events=Olay görüntüle
role_view-identity-providers=Kimlik Sağlayıcılar
role_manage-realm=Realm yönet
role_manage-users=Kullanıcıları yönet
role_manage-applications=Uygulamaları yönet
role_manage-identity-providers=Kimlik Sağlayıcıları Yönet
role_manage-clients=İstemci yönet
role_manage-events=Olay yönet
role_view-profile=Profilleri görüntüle
role_manage-account=Profilleri Yönet
role_manage-account-links=Profil bağlantılarını yönet
role_read-token=Token oku
role_offline-access=Çevirimdışı Yetki
role_uma_authorization=İzinleri Al
client_account=Müşteri Hesabı
client_security-admin-console=Güvenlik Yönetici Konsolu
client_admin-cli=Admin CLI
client_realm-management=Realm-Management
client_broker=Broker

requiredFields=Zorunlu Alanlar
allFieldsRequired=Tüm Alanlar Zorunlu

backToApplication=&laquo; Uygulamaya Dön
backTo=Geri Dön {0}

date=Gün
event=Olay
ip=IP
client=İstemci
clients=İstemciler
details=Detaylar
started=Başlangıç Tarihi
lastAccess=Son Erişim Tarihi
expires=Son Kullanma Tarihi
applications=Uygulama

account=Hesap
federatedIdentity=Federal Kimlik
authenticator=Kimlik Doğrulama
device-activity=Cihaz Etkinliği
sessions=Oturum
log=Log

application=Uygulama
availablePermissions=Kullanılabilir İzinler
availableRoles=Kullanılabilir Roller
grantedPermissions=Verilen İzinler
grantedPersonalInfo=İzin Verilen Kişisel Bilgiler
additionalGrants=Ek İzinler
action=Aksiyon
inResource=Kaynak
fullAccess=Tam Yetki
offlineToken=Çevirimdışı-Token
revoke=İzni İptal et

configureAuthenticators=Çoklu Kimlik Doğrulama
mobile=Mobil
totpStep1=Akıllı Telefonunuza aşağıdaki uygulamalardan birini yükleyin:
totpStep2=Uygulamayı açın ve barkodu okutun.
totpStep3=Uygulama tarafından oluşturulan tek seferlik kodu girin ve Kaydet''i tıklayın.

totpManualStep2=Uygulamayı açın ve aşağıdaki anahtarı girin.
totpManualStep3=Bunları uygulama için özelleştirebilirseniz aşağıdaki yapılandırma değerlerini kullanın:
totpUnableToScan=Barkodu tarayamıyor musunuz?
totpScanBarcode=Barkod Tara?

totp.totp=Zaman bazlı (time-based)
totp.hotp=Sayaç tabanlı (counter-based)

totpType=Tip
totpAlgorithm=Algoritma
totpDigits=Basamak
totpInterval=Aralık
totpCounter=Sayaç

missingUsernameMessage=Lütfen bir kullanıcı adı giriniz.
missingFirstNameMessage=Lütfen bir ad girin.
invalidEmailMessage=Geçersiz e-posta adresi.
missingLastNameMessage=Lütfen bir soyadı giriniz.
missingEmailMessage=Lütfen bir e-mail adresi giriniz.
missingPasswordMessage=Lütfen bir şifre giriniz.
notMatchPasswordMessage=Şifreler aynı değil.

missingTotpMessage=Lütfen tek seferlik kodu girin.
invalidPasswordExistingMessage=Mevcut şifre geçersiz.
invalidPasswordConfirmMessage=Şifre onayı aynı değil.
invalidTotpMessage=Geçersiz tek seferlik kod.

usernameExistsMessage=Kullanıcı adı zaten mevcut.
emailExistsMessage=E-posta adresi zaten mevcut.

readOnlyUserMessage=Yazma korumalı olduğundan kullanıcı hesabınızı değiştiremezsiniz.
readOnlyUsernameMessage=Yazma korumalı olduğundan kullanıcı adınızı değiştiremezsiniz.
readOnlyPasswordMessage=Yazma korumalı olduğundan şifrenizi değiştiremezsiniz.

successTotpMessage=Çoklu kimlik doğrulaması başarıyla yapılandırıldı.
successTotpRemovedMessage=Çoklu kimlik doğrulama başarıyla kaldırıldı.

successGrantRevokedMessage=İzin başarıyla iptal edildi.

accountUpdatedMessage=Kullanıcı hesabınız güncellendi.
accountPasswordUpdatedMessage=Şifreniz güncellendi.

missingIdentityProviderMessage=Kimlik Sağlayıcısı belirtilmemiş.
invalidFederatedIdentityActionMessage=Geçersiz veya eksik eylem.
identityProviderNotFoundMessage=Belirtilen Kimlik Sağlayıcı bulunamadı.
federatedIdentityLinkNotActiveMessage=Bu kimlik artık aktif değil.
federatedIdentityRemovingLastProviderMessage=Şifreniz olmadığı için son girişi kaldıramazsınız.
identityProviderRedirectErrorMessage=Kimlik sağlayıcıya iletilirken hata oluştu.
identityProviderRemovedMessage=Kimlik Sağlayıcısı başarıyla kaldırıldı.
identityProviderAlreadyLinkedMessage=Değiştirilmiş {0} kimliği başka bir kullanıcıya atanmış.
staleCodeAccountMessage=Bu sayfa artık geçerli değil, lütfen tekrar deneyin.
consentDenied=Onay reddedildi.

accountDisabledMessage=Hesabınız kilitlendi, lütfen yöneticiyle iletişime geçin.

accountTemporarilyDisabledMessage=Hesabınız geçici olarak kilitlendi, lütfen yöneticiyle iletişime geçin veya daha sonra tekrar deneyin.
invalidPasswordMinLengthMessage=Geçersiz Şifre: En az {0} karakter uzunluğunda olmalı.
invalidPasswordMinLowerCaseCharsMessage=Geçersiz Şifre \: En az {0} küçük harf içermelidir.
invalidPasswordMinDigitsMessage=Geçersiz Şifre: En az {0} sayı(lar) içermelidir.
invalidPasswordMinUpperCaseCharsMessage=Geçersiz Şifre: En az {0} büyük harf içermelidir.
invalidPasswordMinSpecialCharsMessage=Geçersiz Şifre: En az {0} özel karakter içermelidir.
invalidPasswordNotUsernameMessage=Geçersiz Şifre: Kullanıcı adıyla aynı olamaz.
invalidPasswordRegexPatternMessage=Geçersiz Şifre: Regex Patternine uygun değil.
invalidPasswordHistoryMessage=Geçersiz Şifre: Son {0} şifreden biri olamaz.
invalidPasswordBlacklistedMessage=Geçersiz Şifre: Şifre bloklanmış şifreler listesindedir (kara liste).
invalidPasswordGenericMessage=Geçersiz Şifre: Yeni şifre, şifre kurallarını ihlal ediyor.



# Authorization
myResources=Kaynaklarım
myResourcesSub=Kaynaklarım
doDeny=Reddet
doRevoke=Geri al
doApprove=Onayla
doRemoveSharing=Paylaşımı Kaldır
doRemoveRequest=İsteği Kaldır
peopleAccessResource=Bu kaynağa erişimi olan kişiler
resourceManagedPolicies=Bu kaynağa erişim izni veren izinler
resourceNoPermissionsGrantingAccess=Bu kaynağa erişim izni verilmeyen izin yok
anyAction=Herhangi bir eylem
description=Açıklama
name=İsim
scopes=Kapsam
resource=Kaynak
user=Kullanıcı
peopleSharingThisResource=Bu kaynağı paylaşan kullanıcılar
shareWithOthers=Başkalarıyla paylaş
needMyApproval=Onayım gerekli
requestsWaitingApproval=Talepleriniz onay bekliyor
icon=Icon
requestor=Talep eden
owner=Sahip
resourcesSharedWithMe=Kaynaklar benimle paylaşıldı
permissionRequestion=İzin Talepleri
permission=İzin
shares=Paylaşım(lar)

# Applications
applicationName=İsim
applicationType=Uygulama Tipi
applicationInUse=Yalnızca uygulama içi kullanım
clearAllFilter=Tüm filtreleri temizle
activeFilters=Aktif filtreler
filterByName=İsme Göre Filtrele ...
allApps=Bütün uygulamalar
internalApps=İç uygulamalar
thirdpartyApps=Üçüncü parti uygulamalar
appResults=Sonuçlar

# Linked account
authorizedProvider=Yetkili Tedarikçi
authorizedProviderMessage=Yetkili Sağlayıcılar hesabınızla bağlantılı
identityProvider=Kimlik Sağlayıcısı
identityProviderMessage=Hesabınızı yapılandırdığınız kimlik sağlayıcılarıyla bağlamak için
socialLogin=Sosyal Giriş
userDefined=Kullanıcı tanımlı
removeAccess=Erişimi Kaldır
removeAccessMessage=Bu uygulama hesabını kullanmak istiyorsanız tekrar erişim vermeniz gerekir.

#Authenticator
authenticatorStatusMessage=İki faktörlü kimlik doğrulama aktif
authenticatorFinishSetUpTitle=İki Faktörlü Doğrulama
authenticatorFinishSetUpMessage=Keycloak hesabınızda her oturum açtığınızda, iki faktörlü bir doğrulama kodu girmeniz istenecektir.
authenticatorSubTitle=İki Faktörlü Kimlik Doğrulamayı Ayarlama
authenticatorSubMessage=Hesabınızın güvenliğini artırmak için mevcut iki faktörlü kimlik doğrulama yöntemlerinden en az birini etkinleştirin.
authenticatorMobileTitle=Mobil Kimlik Doğrulayıcı
authenticatorMobileMessage=Doğrulama kodlarını iki faktörlü kimlik doğrulama olarak almak için mobil Doğrulayıcı''yı kullanın.
authenticatorMobileFinishSetUpMessage=Doğrulayıcı, telefonunuza bağlı.
authenticatorActionSetup=Kur
authenticatorSMSTitle=SMS Kodu
authenticatorSMSMessage=Keycloak, doğrulama kodunu telefonunuza iki faktörlü kimlik doğrulaması olarak gönderecektir.
authenticatorSMSFinishSetUpMessage=Kısa mesajlar gönderilir
authenticatorDefaultStatus=Varsayılan
authenticatorChangePhone=Telefon Numarasını Değiştir

#Authenticator - Mobile Authenticator setup
authenticatorMobileSetupTitle=Mobil Kimlik Doğrulama Kurulumu
smscodeIntroMessage=Telefon numaranızı girin ve telefonunuza bir doğrulama kodu gönderilecektir.
mobileSetupStep1=Telefonunuza bir kimlik doğrulama uygulaması yükleyin. Burada listelenen uygulamalar desteklenmektedir.
mobileSetupStep2=Uygulamayı açın ve barkodu tarayın.
mobileSetupStep3=Uygulama tarafından sağlanan tek seferlik kodu girin ve kurulumu tamamlamak için Kaydet''e tıklayın.
scanBarCode=Barkodu taramak ister misiniz?
enterBarCode=Tek seferlik kodu girin
doCopy=Kopyala
doFinish=Bitir

#Authenticator - SMS Code setup
authenticatorSMSCodeSetupTitle=SMS Kodu Kurulumu
chooseYourCountry=Ülkenizi seçin
enterYourPhoneNumber=Telefon numaranızı girin
sendVerficationCode=Doğrulama kodu Gönder
enterYourVerficationCode=Onaylama kodunu girin

#Authenticator - backup Code setup
authenticatorBackupCodesSetupTitle=Yedekleme Kodları Kurulumu
realmName=Realm
doDownload=İndir
doPrint=Yazdır
generateNewBackupCodes=Yeni Yedekleme Kodları Oluştur
backtoAuthenticatorPage=Kimlik Doğrulayıcı Sayfasına Geri Dön

#Resources
resources=Kaynaklar
sharedwithMe=Benimle paylaştı
share=Paylaşım
sharedwith=İle paylaştı
accessPermissions=Erişim İzinleri
permissionRequests=İzin İstekleri
approve=Onayla
approveAll=Tümünü onayla
people=İnsanlar
perPage=Sayfa başına
currentPage=Geçerli sayfa
sharetheResource=Kaynağı paylaş
group=Grup
selectPermission=İzin Seç
addPeople=Kaynağınızı paylaşmak için kullanıcı ekleyin
addTeam=Kaynağınızı paylaşmak için ekip ekleyin
myPermissions=İzinlerim
waitingforApproval=Onay bekleniyor
anyPermission=Herhangi bir izin
