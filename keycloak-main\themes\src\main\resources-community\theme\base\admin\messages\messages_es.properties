invalidPasswordMinLengthMessage=Contraseña incorrecta: longitud mínima {0}.
invalidPasswordMinLowerCaseCharsMessage=Contraseña incorrecta: debe contener al menos {0} letras minúsculas.
invalidPasswordMinDigitsMessage=Contraseña incorrecta: debe contener al menos {0} caracteres numéricos.
invalidPasswordMinUpperCaseCharsMessage=Contraseña incorrecta: debe contener al menos {0} letras mayúsculas.
invalidPasswordMinSpecialCharsMessage=Contraseña incorrecta: debe contener al menos {0} caracteres especiales.
invalidPasswordNotUsernameMessage=Contraseña incorrecta: no puede ser igual al nombre de usuario.
invalidPasswordRegexPatternMessage=Contraseña incorrecta: no cumple la expresión regular.
invalidPasswordHistoryMessage=Contraseña incorrecta: no puede ser igual a ninguna de las últimas {0} contraseñas.
invalidPasswordMaxLengthMessage=Contraseña no válida: longitud máxima {0}.
invalidPasswordNotEmailMessage=Contraseña no válida: no debe ser igual al correo electrónico.
invalidPasswordBlacklistedMessage=Contraseña no válida: la contraseña está en la lista negra.
invalidPasswordGenericMessage=Contraseña no válida: la nueva contraseña no coincide con las políticas de contraseña.
ldapErrorEditModeMandatory=El modo de edición es obligatorio
ldapErrorInvalidCustomFilter=Filtro LDAP configurado personalizado no comienza con "(" o no termina con ")".
ldapErrorConnectionTimeoutNotNumber=El tiempo de espera de la conexión debe ser un número
ldapErrorReadTimeoutNotNumber=El tiempo de espera de lectura debe ser un número
ldapErrorMissingClientId=El ID del cliente debe proporcionarse en la configuración cuando no se utiliza la asignación de roles del realm.
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=No es posible preservar la herencia grupal y usar el tipo de membresía UID juntos.
ldapErrorCantWriteOnlyForReadOnlyLdap=No se puede configurar solo escritura cuando el modo del proveedor LDAP no tiene el modo de edición de escritura
ldapErrorCantWriteOnlyAndReadOnly=No se pueden configurar solo escritura y solo lectura juntos
ldapErrorCantEnableStartTlsAndConnectionPooling=No se puede habilitar StartTLS y agrupación de conexiones a la vez.
ldapErrorCantEnableUnsyncedAndImportOff=No puede deshabilitar los usuarios de importación cuando el modo de proveedor LDAP no está sincronizado
ldapErrorMissingGroupsPathGroup=La ruta de grupos no existe: cree el grupo en la ruta especificada primero
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=Validar la política de contraseña es aplicable solo con el modo de edición de escritura
clientRedirectURIsFragmentError=Las URIs de redirección no deben contener un fragmento URI
clientRootURLFragmentError=La URL de la raíz no debe contener un fragmento de URL
clientRootURLIllegalSchemeError=URL de raíz utiliza un esquema ilegal
clientBaseURLIllegalSchemeError=URL base utiliza un esquema ilegal
backchannelLogoutUrlIllegalSchemeError=URL de inicio de sesión de BackChannel utiliza un esquema ilegal
clientRedirectURIsIllegalSchemeError=Una URI de redirección utiliza un esquema ilegal
clientBaseURLInvalid=La URL base no es una URL válida
clientRootURLInvalid=La URL de la raíz no es una URL válida
clientRedirectURIsInvalid=Una URI de redirección no es una URI válida
backchannelLogoutUrlIsInvalid=La URL de inicio de sesión de BackChannel no es una URL válida


pairwiseMalformedClientRedirectURI=El cliente contenía una URI de redirección inválida.
pairwiseClientRedirectURIsMissingHost=Los URI de redirección del cliente deben contener un componente de host válido.
pairwiseClientRedirectURIsMultipleHosts=Sin una URI de identificador del sector configurado, la URI de redirección del cliente no debe contener múltiples componentes de host.
pairwiseMalformedSectorIdentifierURI=El identificador del sector de la URI está malformado.
pairwiseFailedToGetRedirectURIs=No se pudo obtener la URI de redirección del identificador del sector de la URI.
pairwiseRedirectURIsMismatch=La URI de redirección del cliente no coincide con las URI de redirección obtenidos del identificador del sector de la URI.
duplicatedJwksSettings=El interruptor "Usar JWKS" y el interruptor "Usar URL JWKS" no pueden estar activos al mismo tiempo.
error-invalid-value=Valor no válido.
error-invalid-blank=Especifique el valor.
error-empty=Especifique el valor.
error-invalid-length=El atributo {0} debe tener una longitud entre {1} y {2}.
error-invalid-length-too-short=El atributo {0} debe tener una longitud mínima de {1}.
error-invalid-length-too-long=El atributo {0} debe tener una longitud máxima de {2}.
error-invalid-email=Dirección de correo electrónico no válida.
error-invalid-number=Número invalido.
error-number-out-of-range=El atributo {0} debe ser un número entre {1} y {2}.
error-number-out-of-range-too-small=El atributo {0} debe tener un valor mínimo de {1}.
error-number-out-of-range-too-big=El atributo {0} debe tener un valor máximo de {2}.
error-pattern-no-match=Valor no válido.
error-invalid-uri=URL inválida.
error-invalid-uri-scheme=Esquema de URL no válido.
error-invalid-uri-fragment=Fragmento de URL no válido.
error-user-attribute-required=Especifique el atributo {0}.
error-invalid-date=El atributo {0} es una fecha no válida.
error-user-attribute-read-only=El atributo {0} es de solo lectura.
error-username-invalid-character={0} contiene algún carácter inválido.
error-person-name-invalid-character={0} contiene algún carácter inválido.
error-invalid-multivalued-size=El atributo {0} debe tener al menos {1} y como máximo {2} {2,choice,0#valores|1#valor|1<valores}.
invalidPasswordNotContainsUsernameMessage=Contraseña no válida: no puede contener el nombre de usuario.
client_broker=Intermediario
client_admin-cli=Interfaz de línea de comandos para administración
client_account=Cuenta
client_account-console=Consola de la cuenta
client_security-admin-console=Consola de administración de seguridad
client_realm-management=Administración del realm
