<!--
  ~ Copyright 2016 Red Hat, Inc. and/or its affiliates
  ~ and other contributors as indicated by the <AUTHOR>
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<keycloak-saml-adapter xmlns="urn:keycloak:saml:adapter"
                       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                       xsi:schemaLocation="urn:keycloak:saml:adapter http://www.keycloak.org/schema/keycloak_saml_adapter_1_7.xsd">
    <SP entityID="http://localhost:8280/employee-sig-redir-opt-noidpkey/"
        sslPolicy="EXTERNAL"
        logoutPage="/logout.jsp"
        nameIDPolicyFormat="urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified"
        forceAuthentication="false">
        <Keys>
            <Key signing="true" >
                <KeyStore resource="/WEB-INF/keystore.jks" password="store123">
                    <PrivateKey alias="http://localhost:8080/employee-sig/" password="test123"/>
                    <Certificate alias="http://localhost:8080/employee-sig/"/>
                </KeyStore>
            </Key>
        </Keys>
        <PrincipalNameMapping policy="FROM_NAME_ID"/>
        <RoleIdentifiers>
            <Attribute name="Role"/>
        </RoleIdentifiers>
        <IDP entityID="idp">
            <SingleSignOnService signRequest="true"
                                 validateResponseSignature="true"
                                 requestBinding="REDIRECT"
                                 bindingUrl="http://localhost:8080/auth/realms/demo/protocol/saml"
                    />

            <SingleLogoutService
                    validateRequestSignature="true"
                    validateResponseSignature="true"
                    signRequest="true"
                    signResponse="true"
                    requestBinding="REDIRECT"
                    responseBinding="REDIRECT"
                    redirectBindingUrl="http://localhost:8080/auth/realms/demo/protocol/saml"
                    />
            <HttpClient truststore="classpath:keycloak.truststore" truststorePassword="secret" />
        </IDP>
     </SP>
</keycloak-saml-adapter>