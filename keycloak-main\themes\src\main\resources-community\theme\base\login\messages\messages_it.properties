doLogIn=Accedi
doRegister=Registrati
doRegisterSecurityKey=Registrati
doCancel=Annulla
doSubmit=Invia
doBack=Indietro
doYes=Sì
doNo=No
doContinue=Continua
doIgnore=Ignora
doAccept=Accetta
doDecline=Nega
doForgotPassword=Password dimenticata?
doClickHere=Clicca qui
doImpersonate=Impersona
doTryAgain=Prova ancora
doTryAnotherWay=Prova in un altro modo
kerberosNotConfigured=Kerberos non configurato
kerberosNotConfiguredTitle=Kerberos non configurato
bypassKerberosDetail=Non sei connesso via Kerberos o il tuo browser non supporta l''autenticazione a Kerberos. Fai clic su Continua per accedere in un altro modo
kerberosNotSetUp=Kerberos non è configurato. Non puoi effettuare l''accesso.
registerTitle=Registrati
loginAccountTitle=Accedi
loginTitle=Accedi a {0}
loginTitleHtml={0}
impersonateTitle={0} Impersona utente
impersonateTitleHtml=<strong>{0}</strong> Impersona utente
realmChoice=Realm
unknownUser=Utente sconosciuto
loginTotpTitle=Configura Authenticator sullo smartphone
loginProfileTitle=Aggiorna profilo
loginTimeout=L''autenticazione sta impiegando troppo tempo. Il processo verrà riavviato.
oauthGrantTitle=Autenticazione concessa a {0}
oauthGrantTitleHtml={0}
errorTitle=Siamo spiacenti…
errorTitleHtml=Siamo <strong>spiacenti</strong> ...
emailVerifyTitle=Verifica l''email
emailForgotTitle=Password dimenticata?
updatePasswordTitle=Aggiorna password
codeSuccessTitle=Codice di successo
codeErrorTitle=Codice di errore: {0}
displayUnsupported=Tipo display richiesto non supportato
browserRequired=È richiesto il browser per effettuare l''accesso
browserContinue=È richiesto un browser per proseguire con l''accesso
browserContinuePrompt=Aprire il browser per continuare l''accesso? [s/n]:
browserContinueAnswer=s


termsTitle=Termini e condizioni
termsText=<p>Termini e condizioni da definire</p>
termsPlainText=Termini e condizioni da definire.
recaptchaFailed=Recaptcha non valido
recaptchaNotConfigured=Il Recaptcha è obbligatorio, ma non configurato
consentDenied=Permesso negato.
noAccount=Nuovo utente?
username=Nome utente
usernameOrEmail=Nome utente o email
firstName=Nome
givenName=Nome
fullName=Nome completo
lastName=Cognome
familyName=Cognome
email=Email
password=Password
passwordConfirm=Conferma password
passwordNew=Nuova Password
passwordNewConfirm=Conferma nuova password
rememberMe=Ricordami
authenticatorCode=Codice OTP
address=Indirizzo
locality=Città o Località
street=Via
region=Stato, Provincia o Regione
postal_code=CAP
country=Paese
emailVerified=Email verificata
gssDelegationCredential=Credenziali delega GSS
profileScopeConsentText=Profilo utente
emailScopeConsentText=Indirizzo email
addressScopeConsentText=Indirizzo
phoneScopeConsentText=Numero di telefono
offlineAccessScopeConsentText=Accesso offline
samlRoleListScopeConsentText=I miei ruoli
rolesScopeConsentText=Ruoli utente
restartLoginTooltip=Riavvia la procedura di accesso
loginTotpIntro=Devi impostare un generatore di OTP (password temporanea valida una volta sola) per accedere a questo utente
loginTotpStep1=Installa una delle seguenti applicazioni sul tuo dispositivo mobile:
loginTotpStep2=Apri l''applicazione e scansiona il codice QR:
loginTotpStep3=Scrivi il codice monouso fornito dall''applicazione e premi Invia per completare la configurazione.
loginTotpStep3DeviceName=Fornisci il nome del dispositivo per aiutarti a gestire i dispositivi di autenticazione.
loginTotpManualStep2=Apri l''applicazione e inserisci il codice:
loginTotpManualStep3=Se l''applicazione ti chiede delle impostazioni, usa le seguenti:
loginTotpUnableToScan=Non riesci a scansionare il codice QR?
loginTotpScanBarcode=Vuoi scansionare il codice QR?
loginCredential=Credenziali
loginOtpOneTime=Codice monouso
loginTotpType=Tipo
loginTotpAlgorithm=Algoritmo
loginTotpDigits=Cifre
loginTotpInterval=Intervallo
loginTotpCounter=Contatore
loginTotpDeviceName=Nome del dispositivo di autenticazione
loginTotp.totp=Basato sull''ora
loginTotp.hotp=Basato sul contatore
loginChooseAuthenticator=Seleziona il tuo metodo di autenticazione
oauthGrantRequest=Vuoi assegnare questi privilegi di accesso?
inResource=per
emailVerifyInstruction1=Ti è stata inviata una email con le istruzioni per la verifica del tuo indirizzo {0}.
emailVerifyInstruction2=Non hai ricevuto un codice di verifica nella tua email?
emailVerifyInstruction3=per rinviare la email.
emailLinkIdpTitle=Collega {0}
emailLinkIdp1=Ti è stata inviata una email con le istruzioni per collegare l''utenza {0} {1} con il tuo utente {2}.
emailLinkIdp2=Non hai ricevuto un codice di verifica nella tua email?
emailLinkIdp3=per rinviare la email.
emailLinkIdp4=Se hai già verificato l''indirizzo email in un altro browser
emailLinkIdp5=per continuare.
backToLogin=&laquo; Torna al Login
emailInstruction=Inserisci il tuo nome utente o l''indirizzo email e ti manderemo le istruzioni per creare una nuova password.
copyCodeInstruction=Copia questo codice e incollalo nella tua applicazione:
pageExpiredTitle=La pagina è scaduta
pageExpiredMsg1=Per ripetere la procedura di accesso
pageExpiredMsg2=Per continuare con la procedura di accesso
personalInfo=Informazioni personali:
role_admin=Admin
role_realm-admin=Realm Admin
role_create-realm=Crea realm
role_create-client=Crea client
role_view-realm=Visualizza realm
role_view-users=Visualizza utenti
role_view-applications=Visualizza applicazioni
role_view-clients=Visualizza client
role_view-events=Visualizza eventi
role_view-identity-providers=Visualizza identity provider
role_manage-realm=Gestisci realm
role_manage-users=Gestisci utenti
role_manage-applications=Gestisci applicazioni
role_manage-identity-providers=Gestisci identity provider
role_manage-clients=Gestisci client
role_manage-events=Gestisci eventi
role_view-profile=Visualizza profilo
role_manage-account=Gestisci l''utente
role_manage-account-links=Gestisci i collegamenti per l''utente
role_read-token=Leggi il token
role_offline-access=Accesso offline
client_account=Utente
client_account-console=Console utente
client_security-admin-console=Console di amministrazione di sicurezza
client_admin-cli=Interfaccia amministrativa a linea di comando (CLI)
client_realm-management=Gestione realm
client_broker=Broker
requiredFields=Campi obbligatori
invalidUserMessage=Nome utente o password non validi.
invalidUsernameMessage=Nome utente non valido.
invalidUsernameOrEmailMessage=Nome utente o email non validi.
invalidPasswordMessage=Password non valida.
invalidEmailMessage=Indirizzo email non valido.
accountDisabledMessage=L''utente è stato disabilitato, contatta il tuo amministratore.
accountTemporarilyDisabledMessage=Nome utente o password non validi.
accountPermanentlyDisabledMessage=Nome utente o password non validi.
accountTemporarilyDisabledMessageTotp=Codice di autenticazione non valido.
accountPermanentlyDisabledMessageTotp=Codice di autenticazione non valido.
expiredCodeMessage=L''accesso è scaduto. Riprovare.
expiredActionMessage=Azione scaduta. Si prega di effettuare l''accesso.
expiredActionTokenNoSessionMessage=Azione scaduta.
expiredActionTokenSessionExistsMessage=Azione scaduta. Ricominciare.
missingFirstNameMessage=Inserisci il nome.
missingLastNameMessage=Inserisci il cognome.
missingEmailMessage=Inserisci l''email.
missingUsernameMessage=Inserisci il nome utente.
missingPasswordMessage=Inserisci la password.
missingTotpMessage=Inserisci il codice di autenticazione.
missingTotpDeviceNameMessage=Inserisci il nome del dispositivo di autenticazione.
notMatchPasswordMessage=Le password non coincidono.
invalidPasswordExistingMessage=Password esistente non valida.
invalidPasswordBlacklistedMessage=Password non valida: la password non è consentita.
invalidPasswordConfirmMessage=La password di conferma non coincide.
invalidTotpMessage=Codice di autenticazione non valido.
usernameExistsMessage=Nome utente già esistente.
emailExistsMessage=Email già esistente.
federatedIdentityExistsMessage=L''utente con {0} {1} esiste già. Accedi all''interfaccia di gestione dell''utente per associare l''utenza.
confirmLinkIdpTitle=L''utente esiste già
federatedIdentityConfirmLinkMessage=L''utente con {0} {1} esiste già. Come vuoi procedere?
federatedIdentityConfirmReauthenticateMessage=Autenticati per associare il tuo utente con {0}
confirmLinkIdpReviewProfile=Rivedi profilo
confirmLinkIdpContinue=Aggiungi all''utente esistente
configureTotpMessage=Devi configurare un autenticatore su smartphone per attivare il tuo utente.
updateProfileMessage=Devi aggiornare il profilo per attivare il tuo utente.
updatePasswordMessage=Devi cambiare la password per attivare il tuo utente.
resetPasswordMessage=Devi cambiare la password.
verifyEmailMessage=Devi verificare il tuo indirizzo email per attivare il tuo utente.
linkIdpMessage=Devi verificare il tuo indirizzo email per associare il tuo utente con {0}.
emailSentMessage=Riceverai a breve una email con maggiori istruzioni.
emailSendErrorMessage=Invio email fallito, riprova più tardi.
accountUpdatedMessage=Il tuo utente è stato aggiornato.
accountPasswordUpdatedMessage=La tua password è stata aggiornata.
delegationCompleteHeader=Accesso completato
delegationCompleteMessage=Puoi chiudere questa finestra del browser e tornare alla tua applicazione.
delegationFailedHeader=Accesso fallito
delegationFailedMessage=Puoi chiudere questa finestra del browser e tornare alla tua applicazione per provare ad accedere nuovamente.
noAccessMessage=Nessun accesso
invalidPasswordMinLengthMessage=Password non valida: lunghezza minima {0}.
invalidPasswordMinDigitsMessage=Password non valida: deve contenere almeno {0} numeri.
invalidPasswordMinLowerCaseCharsMessage=Password non valida: deve contenere almeno {0} caratteri minuscoli.
invalidPasswordMinUpperCaseCharsMessage=Password non valida: deve contenere almeno {0} caratteri maiuscoli.
invalidPasswordMinSpecialCharsMessage=Password non valida: deve contenere almeno {0} caratteri speciali.
invalidPasswordNotUsernameMessage=Password non valida: non deve essere uguale al nome utente.
invalidPasswordRegexPatternMessage=Password non valida: non soddisfa una o più espressioni regolari.
invalidPasswordHistoryMessage=Password non valida: non deve essere uguale ad una delle ultime {0} password.
invalidPasswordGenericMessage=Password non valida: la nuova password non rispetta i requisiti previsti.
failedToProcessResponseMessage=Fallimento nell''elaborazione della risposta
httpsRequiredMessage=Richiesto HTTPS
realmNotEnabledMessage=Realm non abilitato
invalidRequestMessage=Richiesta non valida
failedLogout=Disconnessione fallita
unknownLoginRequesterMessage=Richiedente non riconosciuto
loginRequesterNotEnabledMessage=Richiedente non abilitato
bearerOnlyMessage=Alle applicazioni di tipo Bearer-only non è consentito effettuare l''accesso tramite browser
standardFlowDisabledMessage=Al client non è consentito effettuare l''accesso tramite browser con questo response_type. Il flusso standard è stato disabilitato per questo client.
implicitFlowDisabledMessage=Al client non è consentito effettuare l''accesso tramite browser con questo response_type. Il flusso implicito è stato disabilitato per questo client.
invalidRedirectUriMessage=Uri di reindirizzamento non valido
unsupportedNameIdFormatMessage=NameIDFormat non supportato
invalidRequesterMessage=Richiedente non valido
registrationNotAllowedMessage=Registrazione non permessa
resetCredentialNotAllowedMessage=Reimpostazione delle credenziali non permessa
permissionNotApprovedMessage=Permesso non approvato.
noRelayStateInResponseMessage=Nessun relay state in risposta dall''identity provider.
insufficientPermissionMessage=Permessi insufficienti per associare le identità.
couldNotProceedWithAuthenticationRequestMessage=Impossibile procedere con la richiesta di autenticazione all''identity provider.
couldNotObtainTokenMessage=Impossibile ottenere un token dall''identity provider.
unexpectedErrorRetrievingTokenMessage=Errore inaspettato nel recupero del token dall''identity provider.
unexpectedErrorHandlingResponseMessage=Errore inaspettato nella gestione della risposta dall''identity provider.
identityProviderAuthenticationFailedMessage=Autenticazione fallita. Impossibile effettuare l''autenticazione con l''identity provider.
couldNotSendAuthenticationRequestMessage=Impossibile inviare la richiesta di autenticazione all''identity provider.
unexpectedErrorHandlingRequestMessage=Errore inaspettato nella gestione della richiesta di autenticazione all''identity provider.
invalidAccessCodeMessage=Codice di accesso non valido.
sessionNotActiveMessage=Sessione non attiva.
invalidCodeMessage=Si è verificato un errore, effettua di nuovo l''accesso nella tua applicazione.
identityProviderUnexpectedErrorMessage=Errore imprevisto durante l''autenticazione con identity provider
identityProviderNotFoundMessage=Non posso trovare un identity provider con l''identificativo.
identityProviderLinkSuccess=Hai verificato con successo la tua email. Puoi tornare al tuo browser iniziale e continua da lì con l''accesso.
staleCodeMessage=Questa pagina non è più valida, torna alla tua applicazione ed effettua nuovamente l''accesso
realmSupportsNoCredentialsMessage=Il realm non supporta nessun tipo di credenziali.
credentialSetupRequired=Impossibile effettuare l''accesso, è richiesta la configurazione delle credenziali.
identityProviderNotUniqueMessage=Il realm supporta più di un identity provider. Impossibile determinare quale identity provider deve essere utilizzato per autenticarti.
emailVerifiedMessage=Il tuo indirizzo email è stato verificato.
staleEmailVerificationLink=Il collegamento che hai cliccato è scaduto e non è più valido. Probabilmente hai già verificato la tua email.
identityProviderAlreadyLinkedMessage=L''identità federata restituita dall''identity provider {0} è già associata ad un altro utente.
confirmAccountLinking=Conferma il collegamento per l''utenza {0} dell''identity provider {1} con il tuo utente.
confirmEmailAddressVerification=Conferma la validità dell''indirizzo email {0}.
confirmExecutionOfActions=Esegui la/le seguenti azione/i
backToApplication=&laquo; Torna all''applicazione
missingParameterMessage=Parametri mancanti: {0}
clientNotFoundMessage=Client non trovato.
clientDisabledMessage=Client disabilitato.
invalidParameterMessage=Parametro non valido: {0}
alreadyLoggedIn=Sei già connesso.
differentUserAuthenticated=Se già autenticato con l''utente ''{0}'' in questa sessione. Per favore, fai prima il logout.
brokerLinkingSessionExpired=È stato richiesta un''associazione a un utenza broker, ma la sessione corrente non è più valida.
proceedWithAction=&raquo; Clicca qui per continuare
requiredAction.CONFIGURE_TOTP=Configura OTP
requiredAction.TERMS_AND_CONDITIONS=Termini e condizioni
requiredAction.UPDATE_PASSWORD=Aggiornamento password
requiredAction.UPDATE_PROFILE=Aggiornamento profilo
requiredAction.VERIFY_EMAIL=Verifica dell''indirizzo email
doX509Login=Sarai connesso come:
clientCertificate=Certificato client X509:
noCertificate=[Nessun certificato]


pageNotFound=Pagina non trovata
internalServerError=Si è verificato un errore interno del server
console-username=Nome utente:
console-password=Password:
console-otp=One-time password:
console-new-password=Nuova password:
console-confirm-password=Conferma password:
console-update-password=È richiesto l''aggiornamento della tua password.
console-verify-email=Devi verificare il tuo indirizzo email. È stata inviata una email a {0} che contiene un codice di verifica. Per favore inserisci il codice nella casella di testo seguente.
console-email-code=Codice email:
console-accept-terms=Accetti i termini? [s/n]:
console-accept=s

# Openshift messages
openshift.scope.user_info=Informazioni utente
openshift.scope.user_check-access=Informazioni di accesso per l''utente
openshift.scope.user_full=Accesso completo
openshift.scope.list-projects=Elenca i progetti

# SAML authentication
saml.post-form.title=Reindirizzamento per l''autenticazione
saml.post-form.message=Reindirizzamento, attendere per favore.
saml.post-form.js-disabled=JavaScript è disabilitato. È fortemente consigliato abilitarlo. Clicca sul bottone seguente per continuare.

#authenticators
otp-display-name=Applicazione di autenticazione
otp-help-text=Inserire un codice di verifica fornito dall''applicazione di autenticazione.
password-display-name=Password
password-help-text=Accedi inserendo la tua password.
auth-username-form-display-name=Nome utente
auth-username-form-help-text=Per accedere inserisci il tuo nome utente
auth-username-password-form-display-name=Nome utente e password
auth-username-password-form-help-text=Accedi inserendo il tuo nome utente e la password.

# WebAuthn
webauthn-display-name=Passkey
webauthn-help-text=Utilizza la tua Passkey per accedere.
webauthn-passwordless-display-name=Passkey
webauthn-passwordless-help-text=Utilizza la tua Passkey per l''accesso senza password.
webauthn-login-title=Accesso con Passkey
webauthn-registration-title=Registrazione Passkey
webauthn-available-authenticators=Passkey disponibili

# WebAuthn Error
webauthn-error-title=Errore Passkey
webauthn-error-registration=Impossibile registrare la tua Passkey.<br /> {0}
webauthn-error-api-get=L''autenticazione con la Passkey è fallita.<br /> {0}
webauthn-error-different-user=Il primo utente autenticato non è quello autenticato tramite la Passkey.
webauthn-error-auth-verification=Il risultato dell''autenticazione con la Passkey non è valido.<br /> {0}
webauthn-error-register-verification=Il risultato della registrazione della Passkey non è valido.<br /> {0}
webauthn-error-user-not-found=L''utente autenticato la Passkey è sconosciuto.

# Identity provider
identity-provider-redirector=Connettiti con un altro identity provider
identity-provider-login-label=Oppure accedi con
readOnlyUsernameMessage=Non puoi aggiornare il tuo nome utente, poiché è in modalità sola lettura.
reauthenticate=Effettuare nuovamente l''autenticazione per continuare
errorDeletingAccount=Errore durante l''eliminazione dell''utente
authenticateStrong=Per continuare è necessaria un''autenticazione forte
loginIdpReviewProfileTitle=Aggiorna profilo
deletingAccountForbidden=Non hai i permessi sufficienti per eliminare il tuo utente, contatta l''amministratore.
oauthGrantReview=Controlla il
oauthGrantPolicy=politica sulla riservatezza.
totpAppFreeOTPName=FreeOTP
oauth2DeviceVerificationTitle=Dispositivo di accesso
oauth2DeviceExpiredUserCodeMessage=Il codice è scaduto. Torna sul dispositivo e prova a connetterti di nuovo.
oauth2DeviceVerificationCompleteHeader=Autenticato correttamente con dispositivo
oauth2DeviceVerificationCompleteMessage=Puoi chiudere questa finestra e tornare al tuo dispositivo.
oauth2DeviceVerificationFailedHeader=Errore nell''autenticazione con dispositivo
oauth2DeviceVerificationFailedMessage=Puoi chiudere questa finestra e tornare al tuo dispositivo per provare nuovamente.
oauth2DeviceAuthorizationGrantDisabledMessage=Il client non è autorizzato ad effettuare l''autenticazione del dispositivo (OAuth 2.0 Device Authorization Grant). Questo flusso è disabilitato per il client.
identityProviderLogoutFailure=Disconnessione IdP SAML fallita
error-invalid-length-too-long=La lunghezza massima è {2}.
error-invalid-email=Indirizzo email non valido.
error-invalid-number=Numero non valido.
error-number-out-of-range=Il numero deve essere compreso fra {1} e {2}.
error-number-out-of-range-too-small=Il valore minimo è {1}.
error-number-out-of-range-too-big=Il valore massimo è {2}.
error-invalid-uri-fragment=Indirizzo URL non valido.
error-user-attribute-required=Compilare questo campo.
error-reset-otp-missing-id=Si prega di scegliere una configurazione OTP.
federatedIdentityUnmatchedEssentialClaimMessage=Il token emesso dall''identity provider non presenta i criteri richiesti. Si prega di contattare un amministratore.
nestedFirstBrokerFlowMessage=L''utenza {0} {1} non è collegata con alcun utente.
configureBackupCodesMessage=È necessario impostare i codici di ripristino per attivare l''utente.
identityProviderInvalidResponseMessage=Risposta non valida da parte dell''identity provider.
requiredAction.webauthn-register-passwordless=Registrazione Webauthn senza password
auth-x509-client-username-form-display-name=Certificato X509
auth-x509-client-username-form-help-text=Entra utilizzando un certificato X509.
auth-recovery-authn-code-form-display-name=Codice di recupero
auth-recovery-authn-code-form-help-text=Inserisci un codice di recupero da un elenco generato in precedenza.
recovery-codes-error-invalid=Codice di recupero non valido
recovery-code-config-header=Codici di recupero
recovery-code-config-warning-message=Assicurarsi di stamparli, scaricarli o copiarli in un gestore di password per conservarli. Se clicchi annulla i codici di recupero verranno rimossi dal tuo utente.
recovery-codes-print=Stampa
recovery-codes-copy=Copia
recovery-codes-copied=Copiato
recovery-codes-confirmation-message=Ho salvato questi codici in un posto sicuro
recovery-codes-action-complete=Completa la configurazione
recovery-codes-action-cancel=Annulla la configurazione
recovery-codes-download-file-header=Conserva questi codici di recupero in un luogo sicuro.
recovery-codes-download-file-description=I codici di recupero sono dei codici di accesso monouso che consentono di accedere alla propria utenza se non si è in grado di generare un codice OTP.
webauthn-doAuthenticate=Accedi con Passkey
webauthn-registration-init-label=Passkey (nome di default)
passkey-unsupported-browser-text=Le Passkey non sono supportate da questo browser. Prova un altro browser o contatta il tuo amministratore.
passkey-doAuthenticate=Accesso con Passkey
idp-email-verification-display-name=Verifica email
idp-email-verification-help-text=Collega il tuo utente verificando il tuo indirizzo email.
idp-username-password-form-display-name=Nome utente e password
idp-username-password-form-help-text=Collega il tuo utente accedendovi.
irreversibleAction=Questa operazione è irreversibile
deletingImplies=La cancellazione del tuo utente comporta:
finalDeletionConfirmation=Una volta eliminato, non sarà più possibile recuperare il tuo utente. Per mantenerlo clicca su Annulla.
deleteAccountConfirm=Conferma dell''eliminazione dell''utente
userDeletedSuccessfully=Rimozione dell''utente riuscita
access-denied=Accesso negato
frontchannel-logout.title=Disconnessione
logoutConfirmTitle=Disconnessione
error-invalid-multivalued-size=L''attributo {0} deve avere almeno {1} e al massimo {2}{2,choice,0#valori|1#valore|1<valori}.
organization.confirm-membership.title=Ti stai per unire all''organizzazione ${kc.org.name}
organization.member.register.title=Crea un utente per unirti all''organizzazione ${kc.org.name}
organization.select=Seleziona un''organizzazione per procedere:
notMemberOfOrganization=L''utente non è membro dell''organizzazione {0}
doConfirmDelete=Conferma eliminazione
invalidPasswordNotContainsUsernameMessage=Password non valida: non può contenere il nome utente.
error-user-attribute-read-only=Questo campo è di sola lettura.
oauth2DeviceConsentDeniedMessage=È stato negato il consenso per la connessione del dispositivo.
error-invalid-date=Data non valida.
error-invalid-uri-scheme=Schema URL non valido.
error-username-invalid-character=È presente un carattere non valido.
updateEmailMessage=Devi aggiornare l''indirizzo e-mail per attivare l''utente.
frontchannel-logout.message=Ti stai disconnettendo dalle seguenti applicazioni
federatedIdentityUnavailableMessage=L''utente {0} autenticato con l''identity provider {1} non esiste. Si prega di contattare un amministratore.
requiredAction.webauthn-register=Registrazione Webauthn
otp-reset-description=Quale configurazione OTP vuoi rimuovere?
passkey-createdAt-label=Creato
successLogout=Sei stato disconnesso
auth-recovery-code-prompt=Codice di recupero #{0}
passkey-autofill-select=Seleziona la tua passkey
insufficientLevelOfAuthentication=Il livello di autenticazione richiesto non è stato soddisfatto.
auth-recovery-code-header=Accesso con un codice di recupero
auth-recovery-code-info-message=Inserisci il codice di recupero.
webauthn-unsupported-browser-text=Il protocollo WebAuthn non è supportato in questo browser. Provane un''altro o contatta il tuo amministratore.
error-invalid-uri=URL non valido.
confirmOverrideIdpTitle=Il collegamento al broker esiste già
emailVerifiedAlreadyMessage=Il tuo indirizzo email è già stato verificato.
cookieNotFoundMessage=Il cookie non è stato trovato. È possibile che sia scaduto, che sia stato cancellato o che i cookie siano stati disabilitati nel browser. Se i cookie sono disabilitati, attivali. Clicca su Torna all''applicazione per effettuare nuovamente l''accesso.
saml.artifactResolutionServiceInvalidResponse=Impossibile risolvere l''artefatto.
identityProviderInvalidSignatureMessage=Nella risposta dell''identity provider la firma non è valida.
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Genera codici di recupero
recovery-codes-download=Scarica
passkey-login-title=Accesso Passkey
recovery-codes-download-file-date=Questi codici sono stati generati il
loggingOutImmediately=Disconnessione immediata
webauthn-registration-init-label-prompt=Inserisci un nome per la tua Passkey
passkey-available-authenticators=Passkey disponibili
organization.confirm-membership=Cliccando il collegamento sottostante diventerai parte dell''organizzazione {0}:
accountUnusable=Qualsiasi utilizzo successivo dell''applicazione non sarà più possibile con questo utente
oauthGrantInformation=Assicurati di fidarti di {0} capendo come {0} gestirà i vostri dati.
error-person-name-invalid-character=È presente un carattere non valido.
error-pattern-no-match=Valore non valido.
emailVerifyInstruction4=Per verificare la tua email ti stiamo per inviare un messaggio all''indirizzo {0}.
identityProviderMissingCodeOrErrorMessage=Nella risposta dell''identity provider manca il parametro "code" o "error".
invalidTokenRequiredActions=Le azioni richieste nel collegamento non sono valide
confirmOverrideIdpContinue=Sì, sovrascrivi il collegamento con l''utente corrente
invalidPasswordNotEmailMessage=Password non valida: non può essere uguale all''email.
federatedIdentityConfirmOverrideMessage=Stai cercando di collegare l''utente {0} con l''utenza {1} {2}. Tuttavia esiste già un collegamento con l''utenza {3} {4}. Vuoi veramente sostituire il collegamento esistente?
acrNotFulfilled=I requisiti di autenticazione non sono stati soddisfatti
invalidPasswordMaxLengthMessage=Password non valida: la lunghezza massima è {0}.
identityProviderMissingStateMessage=Nella risposta dell''identity provider manca il parametro "state".
recovery-code-config-warning-title=Questi codici di recupero non saranno più visualizzati dopo che avrai lasciato questa pagina
webauthn-createdAt-label=Creato
recovery-codes-label-default=Codici di recupero
errasingData=Rimozione di tutti i tuoi dati
access-denied-when-idp-auth=Accesso negato durante l''autenticazione con {0}
logoutConfirmHeader=Vuoi disconnetterti?
doLogout=Disconnessione
notMemberOfAnyOrganization=L''utente non è membro di alcuna organizzazione
updateEmailTitle=Aggiorna email
emailUpdateConfirmationSentTitle=Email di conferma inviata
emailUpdateConfirmationSent=Un''email di conferma è stata inviata a {0}. Devi seguire le istruzioni contenute per completare il cambio di indirizzo.
emailUpdatedTitle=Email aggiornata
usb=USB
bluetooth=Bluetooth
oauthGrantTos=termini di servizio.
termsAcceptanceRequired=Devi accettare i nostri termini e condizioni.
acceptTerms=Accetto i termini e le condizioni
hidePassword=Nascondi la password
showPassword=Mostra la password
website=Pagina Web
totpAppGoogleName=Google Authenticator
emailVerifySend=Invia l''email di verifica
emailInstructionUsername=Inserisci il tuo nome utente e ti invieremo le istruzioni per creare una nuova password.
sessionLimitExceeded=Ci sono troppe sessioni
error-invalid-value=Valore non valido.
error-invalid-blank=Si prega di inserire un valore.
error-empty=Si prega di inserire un valore.
error-invalid-length=La lunghezza dev''essere compresa tra {1} e {2} caratteri.
error-invalid-length-too-short=La lunghezza minima è {1}.
organizationScopeConsentText=Organizzazione
emailUpdated=L''indirizzo email dell''utente è stato correttamente aggiornato a {0}.
emailVerifyResend=Invia di nuovo l''email di verifica
nfc=NFC
internal=Interno
deleteCredentialMessage=Vuoi eliminare {0}?
unknown=Sconosciuto
deleteCredentialTitle=Elimina {0}
verifyOAuth2DeviceUserCode=Inserisci il codice fornito dal tuo dispositivo e clicca Invia
zoneinfo=Fuso orario
phoneNumber=Numero di telefono
phoneNumberVerified=Numero di telefono verificato
gender=Genere
birthday=Data di nascita
logoutOtherSessions=Scollegati da tutti gli altri dispositivi
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator
oauth2DeviceInvalidUserCodeMessage=Codice non valido, si prega di riprovare.
linkIdpActionMessage=Vuoi collegare il tuo account con {0}?
linkIdpActionTitle=Collegamento {0}
