invalidPasswordMinLengthMessage=Mot de passe invalide : longueur minimale requise de {0}.
invalidPasswordMinLowerCaseCharsMessage=Mot de passe invalide : doit contenir au moins {0} lettre(s) en minuscule.
invalidPasswordMinDigitsMessage=Mot de passe invalide : doit contenir au moins {0} chiffre(s).
invalidPasswordMinUpperCaseCharsMessage=Mot de passe invalide : doit contenir au moins {0} lettre(s) en majuscule.
invalidPasswordMinSpecialCharsMessage=Mot de passe invalide : doit contenir au moins {0} caractères spéciaux.
invalidPasswordNotUsernameMessage=Mot de passe invalide : ne doit pas être identique au nom d''utilisateur.
invalidPasswordRegexPatternMessage=Mot de passe invalide : ne valide pas l''expression rationnelle.
invalidPasswordHistoryMessage=Mot de passe invalide : ne doit pas être égal aux {0} derniers mot de passe.
invalidPasswordMaxLengthMessage=Mot de passe invalide : longueur maximale limitée à {0}.
ldapErrorMissingClientId=L''ID du client doit être renseigné dans le configuration quand l''association des rôles du domaine n''est pas utilisé.
invalidPasswordNotContainsUsernameMessage=Mot de passe invalide : ne doit pas contenir le nom d''utilisateur.
invalidPasswordNotEmailMessage=Mot de passe invalide : ne doit pas être identique à l''adresse de courriel.
invalidPasswordBlacklistedMessage=Mot de passe invalide : mot de passe blacklisté.
invalidPasswordGenericMessage=Mot de passe invalide : le nouveau mot de passe ne respecte pas les politiques de mot de passe.
ldapErrorEditModeMandatory=Le mode d''édition est obligatoire
ldapErrorInvalidCustomFilter=Le filtre LDAP personnalisé ne commence par "(" ou ne finit pas par ")".
ldapErrorConnectionTimeoutNotNumber=La durée de Timeout doit être un nombre
ldapErrorReadTimeoutNotNumber=Le Timeout de lecture doit être un nombre
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=La validation de la politique de mot de passe est applicable uniquement en modification avec droits en écriture
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Impossible de conserver l''héritage des groupes et d''utiliser en même temps l''appartenance par UID.
ldapErrorCantWriteOnlyForReadOnlyLdap=Impossible de définir en écriture seulement lorsque le fournisseur LDAP n''est pas en écriture
ldapErrorCantWriteOnlyAndReadOnly=Impossible de définir en écriture seule et en lecture seule en même temps
ldapErrorCantEnableStartTlsAndConnectionPooling=Impossible d''activer StartTLS et l''interrogation régulière de connexion (pooling).
ldapErrorCantEnableUnsyncedAndImportOff=Impossible de désactiver l''import d''utilisateurs quand le mode du fournisseur LDAP est Non synchronisé
ldapErrorMissingGroupsPathGroup=Le chemin vers les groupes n''existe pas. Merci de d''abord créer le group dans le chemin spécifié
clientRootURLFragmentError=L''URL racine ne doit pas contenir un identificateur de fragment d''URL
clientRedirectURIsFragmentError=L''URI de redirection ne doit pas contenir un identificateur de fragment d''URI
clientRootURLIllegalSchemeError=L''URL racine utilise un schéma non autorisé
clientBaseURLIllegalSchemeError=L''URL de base utilise un schéma non autorisé
clientRedirectURIsIllegalSchemeError=Une URI de redirection utilise un schéma non autorisé
clientBaseURLInvalid=L''URL de base n''est pas une URL valide
clientRootURLInvalid=L''URL racine n''est pas une URL valide
clientRedirectURIsInvalid=Une URI de redirection n''est pas une URI valide
pairwiseMalformedClientRedirectURI=Le client contient une URI de redirection invalide.
pairwiseClientRedirectURIsMissingHost=Les URI de redirection client doivent contenir un composant hôte valide.
duplicatedJwksSettings=Le sélecteur "Utiliser JWKS" et le sélecteur "Utiliser une URL JWKS" ne peuvent pas être actif en même temps.
error-invalid-value=Valeur non valide.
error-invalid-blank=Merci de renseigner une valeur.
error-empty=Merci de renseigner une valeur.
error-invalid-length=L''attribut {0} doit avoir une longueur comprise entre {1} et {2}.
error-invalid-length-too-short=L''attribut {0} doit avoir une longueur minimale de {1}.
error-invalid-length-too-long=L''attribut {0} doit avoir une longueur maximale de {2}.
error-invalid-email=Adresse de courriel non valide.
error-invalid-number=Nombre non valide.
error-number-out-of-range=L''attribut {0} doit être un nombre compris entre {1} et {2}.
error-number-out-of-range-too-small=L''attribut {0} doit valoir au moins {1}.
error-number-out-of-range-too-big=L''attribut {0} doit valoir au plus {2}.
error-pattern-no-match=Valeur non valide.
error-invalid-uri=URL non valide.
error-invalid-uri-scheme=Schéma d''URL non valide.
error-invalid-uri-fragment=Identificateur de fragment URL non valide.
error-user-attribute-required=Merci de renseigner l''attribut {0}.
error-invalid-date=L''attribut {0} est une date non valide.
error-username-invalid-character={0} contient un caractère non valide.
error-user-attribute-read-only=L''attribut {0} est en lecture seule.
error-person-name-invalid-character={0} contient un caractère non valide.
backchannelLogoutUrlIsInvalid=L''URL de déconnexion du canal secondaire n''est pas une URL valide
backchannelLogoutUrlIllegalSchemeError=L''URL de déconnexion du canal secondaire utilise un schéma interdit
pairwiseClientRedirectURIsMultipleHosts=Sans une URL d''identifiant de secteur les URLs de redirection client ne doivent pas contenir plusieurs composants d''hôte.
pairwiseMalformedSectorIdentifierURI=URL d''identifiant de secteur malformée.
pairwiseFailedToGetRedirectURIs=Impossible d''obtenir les URLs de redirection depuis l''URL d''identifiant de secteur.
pairwiseRedirectURIsMismatch=Les URLs de redirection client ne correspondent pas aux URLs de redirection récupérées depuis l''URL d''identifiant de secteur.
error-invalid-multivalued-size=L''attribut {0} doit avoir au moins {1} et au plus {2} {2,choice,0#values|1#value|1<values}.
client_account=Compte
client_account-console=Console de gestion du compte
client_security-admin-console=Console d''administration de la sécurité
client_admin-cli=Administration en lignes de commandes
client_realm-management=Gestion du domaine
client_broker=Broker
