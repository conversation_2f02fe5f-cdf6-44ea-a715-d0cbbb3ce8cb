doLogIn=Кирүү
doRegister=Катталуу
doRegisterSecurityKey=Катталуу
doCancel=Жокко чыгаруу
doSubmit=Жөнөтүү
doBack=Артка
doYes=Ооба
doNo=Жок
doContinue=Улантуу
doIgnore=Капарга албоо
doAccept=Кабыл алуу
doDecline=Баш тартуу
doForgotPassword=Сырсөздү унуттуңузбу?
doClickHere=Бул жерди басыңыз
doImpersonate=Атынан кирүү
doTryAgain=Кайра аракет кылуу
doTryAnotherWay=Башка жол менен аракет кылуу
doConfirmDelete=Өчүрүүнү тастыктоо
errorDeletingAccount=Аккаунтту өчүрүүдө ката кетти
deletingAccountForbidden=Өзүңүздүн аккаунтуңузду өчүрүүгө уруксат жетишсиз, администраторго кайрылыңыз.
kerberosNotConfigured=Kerberos орнотулган эмес
kerberosNotConfiguredTitle=Kerberos орнотулган эмес
bypassKerberosDetail=Сиз Kerberos менен кирген жоксуз же браузериңиз Kerberos үчүн туура жөндөөлбөгөн. Башка жол менен кирүү үчүн “Улантуу” баскычын басыңыз.
kerberosNotSetUp=Kerberos орнотулган эмес. Кире албайсыз.
registerTitle=Катталуу
loginAccountTitle=Аккаунтуңузга кириңиз
loginTitle={0} системасына кириңиз
loginTitleHtml={0}
impersonateTitle={0} атынан колдонуучуга кирүү
impersonateTitleHtml=<strong>{0}</strong> атынан колдонуучуга кирүү
realmChoice=Realm
unknownUser=Белгисиз колдонуучу
loginTotpTitle=Мобилдик аутентификатор орнотуу
loginProfileTitle=Аккаунт маалыматын жаңылоо
loginIdpReviewProfileTitle=Аккаунт маалыматын жаңылоо
loginTimeout=Кирүү аракетиңиздин убактысы өтүп кетти. Кирүү башынан башталат.
reauthenticate=Улантуу үчүн кайра аутентификациялаңыз
authenticateStrong=Улантуу үчүн күчтүү аутентификация талап кылынат
oauthGrantTitle={0} үчүн жеткиликтүүлүк берүү
oauthGrantTitleHtml={0}
oauthGrantInformation={0} сиздин маалыматыңызды кантип пайдалана турганын билип, ага ишенсеңиз болорун текшериңиз.
oauthGrantReview=Сиз текшере аласыз
oauthGrantTos=кызмат көрсөтүү шарттарын.
oauthGrantPolicy=купуялык саясатын.
errorTitle=Кечиресиз...
errorTitleHtml=Биз <strong>кечирим сурайбыз</strong>...
emailVerifyTitle=Электрондук почтаны текшерүү
emailForgotTitle=Сырсөзүңүздү унуттуңузбу?
updateEmailTitle=Электрондук почтаны жаңылоо
emailUpdateConfirmationSentTitle=Тастыктоо кат жөнөтүлдү
emailUpdateConfirmationSent={0} дарегине тастыктоо кат жөнөтүлдү. Электрондук почтаны жаңылоо үчүн көрсөтмөлөрдү аткарыңыз.
emailUpdatedTitle=Электрондук почта жаңыртылды
emailUpdated=Аккаунттун электрондук почтасы {0} дарегине ийгиликтүү жаңыртылды.
updatePasswordTitle=Сырсөздү жаңылоо
codeSuccessTitle=Ийгиликтүү код
codeErrorTitle=Ката коду\: {0}
displayUnsupported=Талап кылынган дисплей түрү колдоого алынбайт
browserRequired=Кирүү үчүн браузер талап кылынат
browserContinue=Кирүүнү аяктоо үчүн браузер талап кылынат
browserContinuePrompt=Браузерди ачып, кирүүнү улантуу керек. Улантабызбы? [о/ж]:
browserContinueAnswer=о

# Transports
usb=USB
nfc=NFC
bluetooth=Bluetooth
internal=Ички
unknown=Белгисиз

termsTitle=Шарттар жана эрежелер
termsText=<p>Шарттар жана эрежелер кийин аныкталат</p>
termsPlainText=Шарттар жана эрежелер кийин аныкталат.
termsAcceptanceRequired=Шарттар жана эрежелерге макул болушуңуз керек.
acceptTerms=Шарттар жана эрежелерге макулмун

deleteCredentialTitle={0} өчүрүү
deleteCredentialMessage={0} өчүрүүнү каалайсызбы?

linkIdpActionTitle={0} туташтыруу
linkIdpActionMessage=Аккаунтуңузду {0} менен туташтыргыңыз келеби?

recaptchaFailed=Жараксыз Recaptcha
recaptchaNotConfigured=Recaptcha талап кылынат, бирок орнотулган эмес
consentDenied=Макулдук берилген жок.

noAccount=Жаңы колдонуучу?
username=Колдонуучу аты
usernameOrEmail=Колдонуучу аты же электрондук почта
firstName=Атыңыз
givenName=Берилген ат
fullName=Толук аты-жөнү
lastName=Фамилия
familyName=Үй-бүлөлүк ат
email=Электрондук почта
password=Сырсөз
passwordConfirm=Сырсөздү тастыктоо
passwordNew=Жаңы сырсөз
passwordNewConfirm=Жаңы сырсөздү тастыктоо
hidePassword=Сырсөздү жашыруу
showPassword=Сырсөздү көрсөтүү
rememberMe=Эсимде сактоо
authenticatorCode=Бир жолку код
address=Дарек
street=Көчө
locality=Шаар же калктуу конуш
region=Област, провинция же регион
postal_code=Индекс же почта коду
country=Өлкө
emailVerified=Электрондук почта тастыкталды
website=Веб-баракча
phoneNumber=Телефон номери
phoneNumberVerified=Телефон номери тастыкталды
gender=Жынысы
birthday=Туулган күнү
zoneinfo=Убакыт алкагы
gssDelegationCredential=GSS ыйгарым укук берүү маалыматы
logoutOtherSessions=Башка түзмөктөрдөн чыгуу

profileScopeConsentText=Колдонуучунун профили
emailScopeConsentText=Электрондук почта дареги
addressScopeConsentText=Дарек
phoneScopeConsentText=Телефон номери
offlineAccessScopeConsentText=Офлайн кирүү
samlRoleListScopeConsentText=Менин ролдорум
rolesScopeConsentText=Колдонуучу ролдору
organizationScopeConsentText=Уюм

restartLoginTooltip=Кирүүнү кайра баштоо

loginTotpIntro=Бул аккаунтка кирүү үчүн Бир жолку сырсөз генераторун орнотушуңуз керек
loginTotpStep1=Мобилдик түзмөгүңүзгө төмөнкү колдонмолордун бирин орнотуңуз:
loginTotpStep2=Колдонмону ачыңыз жана штрих-кодду сканердеңиз:
loginTotpStep3=Колдонмо берген бир жолку кодду киргизип, орнотууну бүтүрүү үчүн Жөнөтүү баскычын басыңыз.
loginTotpStep3DeviceName=OTP түзмөктөрүңүздү башкарууга жардам берүү үчүн түзмөктүн атын киргизиңиз.
loginTotpManualStep2=Колдонмону ачыңыз жана ачкычты киргизиңиз:
loginTotpManualStep3=Эгер колдонмо орнотууларды колдосо, төмөнкү конфигурация маанилерин пайдаланыңыз:
loginTotpUnableToScan=Сканерлөө мүмкүн эмеспи?
loginTotpScanBarcode=Штрих-кодду сканерлөө керекпи?
loginCredential=Маалымат
loginOtpOneTime=Бир жолку код
loginTotpType=Түрү
loginTotpAlgorithm=Алгоритм
loginTotpDigits=Сандардын саны
loginTotpInterval=Интервал
loginTotpCounter=Санак
loginTotpDeviceName=Түзмөктүн аты

loginTotp.totp=Убакытка негизделген
loginTotp.hotp=Санакка негизделген

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

loginChooseAuthenticator=Кирүү ыкмасын тандаңыз

oauthGrantRequest=Бул мүмкүнчүлүктөргө уруксат бересизби?
inResource= ичинде

oauth2DeviceVerificationTitle=Түзмөктөн кирүү
verifyOAuth2DeviceUserCode=Түзмөгүңүз берген кодду киргизип, Жөнөтүү баскычын басыңыз
oauth2DeviceInvalidUserCodeMessage=Жараксыз код, кайра аракет кылыңыз.
oauth2DeviceExpiredUserCodeMessage=Коддун мөөнөтү өттү. Түзмөгүңүзгө кайтып барып, кайра туташтырууга аракет кылыңыз.
oauth2DeviceVerificationCompleteHeader=Түзмөктөн кирүү ийгиликтүү өттү
oauth2DeviceVerificationCompleteMessage=Бул браузер терезесин жаап, түзмөгүңүзгө кайта аласыз.
oauth2DeviceVerificationFailedHeader=Түзмөктөн кирүү ишке ашкан жок
oauth2DeviceVerificationFailedMessage=Бул браузер терезесин жаап, түзмөгүңүзгө кайтып барып кайра туташтырууга аракет кылыңыз.
oauth2DeviceConsentDeniedMessage=Түзмөктү туташтырууга макулдук берилген жок.
oauth2DeviceAuthorizationGrantDisabledMessage=Клиентке OAuth 2.0 Түзмөк Авторизациясын баштоо уруксат берилген эмес. Бул агым клиент үчүн өчүрүлгөн.

emailVerifyInstruction1={0} дарегине электрондук почтаңызды тастыктоо боюнча көрсөтмөлөрдү камтыган кат жөнөтүлдү.
emailVerifyInstruction2=Тастыктоо коду электрондук почтаңызга келбеди беле?
emailVerifyInstruction3=катты кайра жөнөтүү үчүн.
emailVerifyInstruction4={0} дарегине электрондук почтаңызды тастыктоо үчүн көрсөтмөлөрдү камтыган кат жөнөтүлөт.
emailVerifyResend=Тастыктоо катын кайра жөнөтүү
emailVerifySend=Тастыктоо катын жөнөтүү

emailLinkIdpTitle={0} туташтыруу
emailLinkIdp1={0} аккаунтун {1} сиздин {2} аккаунтуңузга туташтыруу боюнча көрсөтмөлөрдү камтыган кат сизге жөнөтүлдү.
emailLinkIdp2=Тастыктоо коду электрондук почтаңызга келбеди беле?
emailLinkIdp3=катты кайра жөнөтүү үчүн.
emailLinkIdp4=Эгер башка браузерде электрондук почтаны тастыктап койгон болсоңуз,
emailLinkIdp5=ушуну улантуу үчүн.

backToLogin=&laquo; Кирүүгө кайтуу

emailInstruction=Колдонуучу атыңызды же электрондук почта дарегиңизди киргизиңиз, биз сизге жаңы сырсөз түзүү боюнча көрсөтмөлөрдү жөнөтөбүз.
emailInstructionUsername=Колдонуучу атыңызды киргизиңиз, биз сизге жаңы сырсөз түзүү боюнча көрсөтмөлөрдү жөнөтөбүз.

copyCodeInstruction=Бул кодду көчүрүп, колдонмоңузга чаптаңыз:

pageExpiredTitle=Баракча мөөнөтү бүткөн
pageExpiredMsg1=Кирүү процессин кайра баштоо үчүн
pageExpiredMsg2=Кирүү процессин улантуу үчүн

personalInfo=Жеке маалымат:
role_admin=Админ
role_realm-admin=Реалмдин админи
role_create-realm=Реалм түзүү
role_create-client=Клиент түзүү
role_view-realm=Реалмды карап чыгуу
role_view-users=Колдонуучуларды карап чыгуу
role_view-applications=Колдонмолорду карап чыгуу
role_view-clients=Клиенттерди карап чыгуу
role_view-events=Окуяларды карап чыгуу
role_view-identity-providers=Аутентификация провайдерлерин карап чыгуу
role_manage-realm=Реалмди башкаруу
role_manage-users=Колдонуучуларды башкаруу
role_manage-applications=Колдонмолорду башкаруу
role_manage-identity-providers=Аутентификация провайдерлерин башкаруу
role_manage-clients=Клиенттерди башкаруу
role_manage-events=Окуяларды башкаруу
role_view-profile=Профилди карап чыгуу
role_manage-account=Аккаунтту башкаруу
role_manage-account-links=Аккаунт шилтемелерин башкаруу
role_read-token=Токенди окуу
role_offline-access=Офлайн кирүү
client_account=Аккаунт
client_account-console=Аккаунт консолу
client_security-admin-console=Коопсуздук админ консолу
client_admin-cli=Админ CLI
client_realm-management=Реалмди башкаруу
client_broker=Брокер

requiredFields=Милдеттүү талаалар

invalidUserMessage=Жараксыз колдонуучу аты же сырсөз.
invalidUsernameMessage=Жараксыз колдонуучу аты.
invalidUsernameOrEmailMessage=Жараксыз колдонуучу аты же электрондук почта.
invalidPasswordMessage=Жараксыз сырсөз.
invalidEmailMessage=Жараксыз электрондук почта дареги.
accountDisabledMessage=Аккаунт өчүрүлгөн, администратор менен байланышыңыз.
# These properties are deliberately the same as "invalidUsernameMessage", so by default, it is not possible to recognize the reason of failed authentication is temporarily disabled account
accountTemporarilyDisabledMessage=Жараксыз колдонуучу аты же сырсөз.
accountPermanentlyDisabledMessage=Жараксыз колдонуучу аты же сырсөз.
# These properties are deliberately the same as "invalidTotpMessage", so by default, it is not possible to recognize the reason of failed authentication is temporarily disabled account
accountTemporarilyDisabledMessageTotp=Жараксыз аутентификатор коду.
accountPermanentlyDisabledMessageTotp=Жараксыз аутентификатор коду.
expiredCodeMessage=Кирүү мөөнөтү бүткөн. Кайра кирип чыгыңыз.
expiredActionMessage=Иш-аракет мөөнөтү бүткөн. Азыр кирүүнү улантыңыз.
expiredActionTokenNoSessionMessage=Иш-аракет мөөнөтү бүткөн.
expiredActionTokenSessionExistsMessage=Иш-аракет мөөнөтү бүткөн. Кайра баштаңыз.
sessionLimitExceeded=Сессиялардын саны ашып кетти
identityProviderLogoutFailure=SAML IdP чыгуу ийгиликсиз болду

missingFirstNameMessage=Атын көрсөтүңүз.
missingLastNameMessage=Фамилияны көрсөтүңүз.
missingEmailMessage=Электрондук почтаңызды көрсөтүңүз.
missingUsernameMessage=Колдонуучу атыңызды көрсөтүңүз.
missingPasswordMessage=Сырсөзүңүздү көрсөтүңүз.
missingTotpMessage=Аутентификатор кодун көрсөтүңүз.
missingTotpDeviceNameMessage=Түзмөктүн атын көрсөтүңүз.
notMatchPasswordMessage=Сырсөздөр дал келбейт.

error-invalid-value=Жараксыз маани.
error-invalid-blank=Маанини көрсөтүңүз.
error-empty=Маанини көрсөтүңүз.
error-invalid-length=Узундугу {1} менен {2} ортосунда болушу керек.
error-invalid-length-too-short=Минималдуу узундук {1}.
error-invalid-length-too-long=Максималдуу узундук {2}.
error-invalid-email=Жараксыз электрондук почта дареги.
error-invalid-number=Жараксыз сан.
error-number-out-of-range=Сан {1} менен {2} ортосунда болушу керек.
error-number-out-of-range-too-small=Сан минималдуу {1} болушу керек.
error-number-out-of-range-too-big=Сан максималдуу {2} болушу керек.
error-pattern-no-match=Жараксыз маани.
error-invalid-uri=Жараксыз URL.
error-invalid-uri-scheme=Жараксыз URL схемасы.
error-invalid-uri-fragment=Жараксыз URL бөлүгү.
error-user-attribute-required=Бул талааны көрсөтүңүз.
error-invalid-date=Жараксыз дата.
error-user-attribute-read-only=Бул талаа окууга гана ачылган.
error-username-invalid-character=Мааниде жараксыз символ бар.
error-person-name-invalid-character=Мааниде жараксыз символ бар.
error-reset-otp-missing-id=OTP конфигурациясын тандаңыз.

invalidPasswordExistingMessage=Жараксыз бар сырсөз.
invalidPasswordBlacklistedMessage=Жараксыз сырсөз: сырсөз кара тизмеде.
invalidPasswordConfirmMessage=Сырсөздү тастыктоо дал келбейт.
invalidTotpMessage=Жараксыз аутентификатор коду.

usernameExistsMessage=Колдонуучу аты мурунтан бар.
emailExistsMessage=Электрондук почта мурунтан бар.

federatedIdentityExistsMessage={0} {1} менен колдонуучу мурунтан бар. Аккаунтту туташтыруу үчүн аккаунт башкарууга кириңиз.
federatedIdentityUnavailableMessage={0} колдонуучу {1} аутентификация провайдери менен жок. Администраторго кайрылыңыз.
federatedIdentityUnmatchedEssentialClaimMessage=Аутентификация провайдери тарабынан берилген ID токен конфигурацияланган негизги талабы менен дал келбейт. Администраторго кайрылыңыз.

confirmLinkIdpTitle=Аккаунт мурунтан бар
confirmOverrideIdpTitle=Брокер шилтемеси мурунтан бар
federatedIdentityConfirmLinkMessage={0} {1} менен колдонуучу мурунтан бар. Кантип улантасыңыз келет?
federatedIdentityConfirmOverrideMessage=Сиз аккаунтуңузду {0} менен {1} аккаунту {2} туташтырыгыңыз келет. Бирок аккаунтуңуз башка {3} аккаунту {4} менен мурунтан туташтырылган. Мурунку шилтемени жаңы аккаунт менен алмаштырууну тастыктайсызбы?
federatedIdentityConfirmReauthenticateMessage=Аккаунтуңузду {0} менен туташтыруу үчүн аутентификациялаңыз
nestedFirstBrokerFlowMessage={0} колдонуучу {1} эч кандай белгилүү колдонуучуга туташкан эмес.
confirmLinkIdpReviewProfile=Профилди карап чыгуу
confirmLinkIdpContinue=Мурунку аккаунтка кошуу
confirmOverrideIdpContinue=Ооба, шилтемени азыркы аккаунт менен алмаштыруу

configureTotpMessage=Аккаунтыңызды активдеш үчүн Мобилдик Аутентификаторду орнотуңуз.
configureBackupCodesMessage=Аккаунтыңызды активдеш үчүн Камсыздоочу коддорду орнотуңуз.
updateProfileMessage=Аккаунтыңызды активдеш үчүн колдонуучу профилиңизди жаңылаңыз.
updatePasswordMessage=Аккаунтыңызды активдеш үчүн сырсөзүңүздү өзгөртүңүз.
updateEmailMessage=Аккаунтыңызды активдеш үчүн электрондук почтаңызды жаңылаңыз.
resetPasswordMessage=Сырсөзүңүздү өзгөртүшүңүз керек.
verifyEmailMessage=Аккаунтыңызды активдеш үчүн электрондук почтаңызды тастыктаңыз.
linkIdpMessage=Аккаунтуңузду {0} менен туташтыруу үчүн электрондук почтаңызды тастыкташыңыз керек.

emailSentMessage=Жакында сизге кошумча көрсөтмөлөрү бар электрондук кат келет.
emailSendErrorMessage=Кат жөнөтүү мүмкүн болбоду, кайрадан аракет кылыңыз.

accountUpdatedMessage=Аккаунтуңуз жаңыланды.
accountPasswordUpdatedMessage=Сырсөзүңүз жаңыланды.

delegationCompleteHeader=Кирүү ийгиликтүү өттү
delegationCompleteMessage=Бул браузерди жаап, консолдук колдонмоңузга кайта аласыз.
delegationFailedHeader=Кирүү ишке ашкан жок
delegationFailedMessage=Бул браузерди жаап, консолдук колдонмоңузга кайтып, кайрадан кирүүгө аракет кылыңыз.

noAccessMessage=Кирүү мүмкүнчүлүгү жок

invalidPasswordMinLengthMessage=Сырсөз жараксыз: минималдуу узундук {0}.
invalidPasswordMaxLengthMessage=Сырсөз жараксыз: максималдуу узундук {0}.
invalidPasswordMinDigitsMessage=Сырсөз жараксыз: жок дегенде {0} сандык белгини камтышы керек.
invalidPasswordMinLowerCaseCharsMessage=Сырсөз жараксыз: жок дегенде {0} кичинекей тамганы камтышы керек.
invalidPasswordMinUpperCaseCharsMessage=Сырсөз жараксыз: жок дегенде {0} чоң тамганы камтышы керек.
invalidPasswordMinSpecialCharsMessage=Сырсөз жараксыз: жок дегенде {0} атайын белгини камтышы керек.
invalidPasswordNotUsernameMessage=Сырсөз жараксыз: колдонуучунун аты менен дал келбеши керек.
invalidPasswordNotContainsUsernameMessage=Сырсөз жараксыз: колдонуучунун атын камтыбайт.
invalidPasswordNotEmailMessage=Сырсөз жараксыз: электрондук почта менен дал келбеши керек.
invalidPasswordRegexPatternMessage=Сырсөз жараксыз: белгиленген үлгүгө туура келбейт.
invalidPasswordHistoryMessage=Сырсөз жараксыз: акыркы {0} сырсөздөрдүн бирине дал келбеши керек.
invalidPasswordGenericMessage=Сырсөз жараксыз: жаңы сырсөз саясатка туура келбейт.

failedToProcessResponseMessage=Жоопту иштеп чыгууда ката кетти
httpsRequiredMessage=HTTPS керек
realmNotEnabledMessage=Realm иштетилген эмес
invalidRequestMessage=Жарамсыз сурануу
successLogout=Сиз чыгарылдыңыз
failedLogout=Чыгуу ишке ашкан жок
unknownLoginRequesterMessage=Белгисиз кирүү суралышы
loginRequesterNotEnabledMessage=Кирүү суралышы иштетилген эмес
bearerOnlyMessage=Bearer-only колдонмолор браузер аркылуу кирүүнү баштай алышпайт
standardFlowDisabledMessage=Клиент берилген response_type менен браузер кирүүсүн баштай албайт. Стандарттык процесс өчүрүлгөн.
implicitFlowDisabledMessage=Клиент берилген response_type менен браузер кирүүсүн баштай албайт. Имплициттик процесс өчүрүлгөн.
invalidRedirectUriMessage=Жарамсыз кайра багыттоо URI
unsupportedNameIdFormatMessage=Колдоого алынбаган NameIDFormat
invalidRequesterMessage=Жарамсыз суралышы
registrationNotAllowedMessage=Тиркелүү мүмкүн эмес
resetCredentialNotAllowedMessage=Креденциалды кайра коюу мүмкүн эмес

permissionNotApprovedMessage=Рухсат берилген эмес.
noRelayStateInResponseMessage=Identity provider жоопунда relay state жок.
insufficientPermissionMessage=Идентификаторлорду туташтыруу үчүн уруксат жетишсиз.
couldNotProceedWithAuthenticationRequestMessage=Identity providerге аутентификация сурамын жибере алган жок.
couldNotObtainTokenMessage=Identity providerден токенди ала алган жок.
unexpectedErrorRetrievingTokenMessage=Identity providerден токен алууда күтүлбөгөн ката.
unexpectedErrorHandlingResponseMessage=Identity providerден жоопту иштетүүдө күтүлбөгөн ката.
identityProviderAuthenticationFailedMessage=Аутентификация өтпөдү. Identity provider менен байланышуу мүмкүн болгон жок.
couldNotSendAuthenticationRequestMessage=Identity providerге аутентификация сурамын жибере алган жок.
unexpectedErrorHandlingRequestMessage=Identity providerге аутентификация сурамын иштетүүдө күтүлбөгөн ката.
invalidAccessCodeMessage=Жарамсыз кирүү коду.
sessionNotActiveMessage=Сессия активдүү эмес.
invalidCodeMessage=Ката кетти, өтүнөмүн колдонмоңуз аркылуу кайра кириңиз.
cookieNotFoundMessage=Кайра кирүү cookie табылган жок. Мүмкүн, мөөнөтү бүткөн; өчүрүлгөн же браузердеги cookie өчүрүлгөн. Эгерде cookie өчүрүлгөн болсо, аны күйгүзүңүз. Кирүү үчүн колдонмоңузга кайтуу баскычын басыңыз.
insufficientLevelOfAuthentication=Суралган аутентификация деңгээли жетишсиз.
identityProviderUnexpectedErrorMessage=Identity provider менен аутентификациялоодо күтүлбөгөн ката
identityProviderMissingStateMessage=Identity provider жоопунда state параметри жок.
identityProviderMissingCodeOrErrorMessage=Identity provider жоопунда код же ката параметри жок.
identityProviderInvalidResponseMessage=Identity provider жооп жараксыз.
identityProviderInvalidSignatureMessage=Identity provider жооптогу кол тамга жараксыз.
identityProviderNotFoundMessage=Белгилеген identity provider табылган жок.
identityProviderLinkSuccess=Электрондук почтаңыз ийгиликтүү тастыкталды. Баштапкы браузериңизге кайтып, андан ары кирүүнү улантыңыз.
staleCodeMessage=Бул барак жараксыз болуп калды, өтүнөмүн колдонмоңузга кайтып, кайра кириңиз
realmSupportsNoCredentialsMessage=Realm эч кандай креденциал түрүн колдобойт.
credentialSetupRequired=Кирүү мүмкүн эмес, креденциал орнотуу зарыл.
identityProviderNotUniqueMessage=Realm бир нече identity providerлерди колдойт. Кайсы identity provider аутентификация үчүн колдонуларын аныктоо мүмкүн болбой жатат.
emailVerifiedMessage=Электрондук почтаңыз тастыкталды.
emailVerifiedAlreadyMessage=Электрондук почтаңыз мурунтан тастыкталган.
staleEmailVerificationLink=Сиз баскан шилтеме эски, жараксыз болуп калды. Балким, сиз почтаңызды мурунтан тастыктагансыз.
identityProviderAlreadyLinkedMessage={0} аркылуу келген федерацияланган идентификация мурунтан башка колдонуучу менен байланышкан.
confirmAccountLinking=Идентификатор провайдер {1} аркылуу аккаунт {0} туташтырууну тастыктаңыз.
confirmEmailAddressVerification=Электрондук почта дареги {0} жарактуулугун тастыктаңыз.
confirmExecutionOfActions=Төмөнкү аракет(тер)ди аткарыңыз

backToApplication=&laquo; Колдонууга кайтуу
missingParameterMessage=Жок параметрлер\: {0}
clientNotFoundMessage=Кардар табылган жок.
clientDisabledMessage=Кардар өчүрүлгөн.
invalidParameterMessage=Туура эмес параметр\: {0}
alreadyLoggedIn=Сиз буга чейин кирип жатасыз.
differentUserAuthenticated=Бул сессияда сиз башка колдонуучу ''{0}'' катары аутентификациядан өттүңүз. Сураныч, биринчи чыгып кетиңиз.
brokerLinkingSessionExpired=Каржылоочу эсепти байланыштыруу суралып, бирок учурдагы сессия жараксыз болду.
proceedWithAction=&raquo; Улантуу үчүн бул жерди чыкылдатыңыз
acrNotFulfilled=Аутентификация талаптары аткарылган жок

requiredAction.CONFIGURE_TOTP=OTP орнотуу
requiredAction.TERMS_AND_CONDITIONS=Шарттар жана эрежелер
requiredAction.UPDATE_PASSWORD=Сырсөздү жаңыртуу
requiredAction.UPDATE_PROFILE=Профилди жаңыртуу
requiredAction.VERIFY_EMAIL=Электрондук почтаны текшерүү
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Калыбына келтирүү коддорун түзүү
requiredAction.webauthn-register-passwordless=Webauthn сырсөзсүз каттоо
requiredAction.webauthn-register=Webauthn каттоо

invalidTokenRequiredActions=Сылтамадагы талап кылынган аракеттер жараксыз

doX509Login=Сиз катары киресиз\:
clientCertificate=X509 кардар сертификаты\:
noCertificate=[Сертификат жок]


pageNotFound=Бет табылган жок
internalServerError=Ички сервердик ката болуп калды

console-username=Колдонуучу аты:
console-password=Сырсөз:
console-otp=Бир жолку сырсөз:
console-new-password=Жаңы сырсөз:
console-confirm-password=Сырсөздү тастыктоо:
console-update-password=Сырсөздү жаңыртуу талап кылынат.
console-verify-email=Сиздин электрондук почтаңызды текшерүү керек. Биз {0} дарегине текшерүү коду бар кат жөнөттүк. Төмөнкү талаага ошол кодду киргизиңиз.
console-email-code=Электрондук почта коду:
console-accept-terms=Шарттарды кабыл алдыңызбы? [о/ж]:
console-accept=о

# Openshift messages
openshift.scope.user_info=Колдонуучу маалыматы
openshift.scope.user_check-access=Колдонуучунун кирүү укугу
openshift.scope.user_full=Толук укук
openshift.scope.list-projects=Проекттерди тизмелөө

# SAML authentication
saml.post-form.title=Аутентификацияга кайра багыттоо
saml.post-form.message=Кайра багытталууда, күтө туруңуз.
saml.post-form.js-disabled=JavaScript өчүрүлгөн. Аны күйгүзүүнү сунуштайбыз. Улантуу үчүн төмөнкү баскычты чыкылдатыңыз.
saml.artifactResolutionServiceInvalidResponse=Артефакт чечилип кеткен жок.

#authenticators
otp-display-name=Аутентификатор колдонмосу
otp-help-text=Аутентификатор колдонмосунан алынган текшерүү кодун киргизиңиз.
otp-reset-description=Кайсы OTP конфигурациясын алып салууну каалайсыз?
password-display-name=Сырсөз
password-help-text=Сырсөзүңүздү киргизип кириңиз.
auth-username-form-display-name=Колдонуучу аты
auth-username-form-help-text=Колдонуучу атыңызды киргизүү менен аутентификацияны баштаңыз
auth-username-password-form-display-name=Колдонуучу аты жана сырсөз
auth-username-password-form-help-text=Колдонуучу атыңызды жана сырсөзүңүздү киргизүү менен кириңиз.
auth-x509-client-username-form-display-name=X509 Сертификат
auth-x509-client-username-form-help-text=X509 кардар сертификаты менен кириңиз.

# Recovery Codes
auth-recovery-authn-code-form-display-name=Калыбына келтирүү аутентификация коду
auth-recovery-authn-code-form-help-text=Мурда түзүлгөн тизмеден калыбына келтирүү аутентификация кодун киргизиңиз.
auth-recovery-code-info-message=Көрсөтүлгөн калыбына келтирүү кодун киргизиңиз.
auth-recovery-code-prompt=Калыбына келтирүү коду #{0}
auth-recovery-code-header=Калыбына келтирүү аутентификация коду менен кириңиз
recovery-codes-error-invalid=Туура эмес калыбына келтирүү коду
recovery-code-config-header=Калыбына келтирүү аутентификация коддору
recovery-code-config-warning-title=Бул калыбына келтирүү коддору баракты таштап кеткенден кийин кайра көрүнбөйт
recovery-code-config-warning-message=Аларды басып чыгаруу, жүктөп алуу же сырсөз менеджерине көчүрүү жана коопсуз жерде сактоо маанилүү. Бул орнотууну жокко чыгаруу аккаунтуңуздан бул коддорду алып салат.
recovery-codes-print=Басып чыгаруу
recovery-codes-download=Жүктөп алуу
recovery-codes-copy=Көчүрүү
recovery-codes-copied=Көчүрүлдү
recovery-codes-confirmation-message=Бул коддорду коопсуз жерде сактадым
recovery-codes-action-complete=Орнотууну бүтүрүү
recovery-codes-action-cancel=Орнотууну жокко чыгаруу
recovery-codes-download-file-header=Бул калыбына келтирүү коддорун коопсуз жерде сактаңыз.
recovery-codes-download-file-description=Калыбына келтирүү коддору бир жолу колдонулуучу сырсөздөр болуп, эгер аутентификаторго кире албасаңыз, аккаунтуңузга кирүүгө мүмкүндүк берет.
recovery-codes-download-file-date=Бул коддор түзүлгөн күнү
recovery-codes-label-default=Калыбына келтирүү коддору

# WebAuthn
webauthn-display-name=Парольсуз ачкыч
webauthn-help-text=Кириүү үчүн Парольсуз ачкычты колдонуңуз.
webauthn-passwordless-display-name=Парольсуз ачкыч
webauthn-passwordless-help-text=Парольсуз кириүү үчүн Парольсуз ачкычты колдонуңуз.
webauthn-login-title=Парольсуз ачкыч менен кирүү
webauthn-registration-title=Парольсуз ачкыч каттоо
webauthn-available-authenticators=Бар болгон Парольсуз ачкычтар
webauthn-unsupported-browser-text=Бул браузер WebAuthn колдобойт. Башка браузерди колдонуп көрүңүз же администратор менен байланышыңыз.
webauthn-doAuthenticate=Парольсуз ачкыч менен кириңиз
webauthn-createdAt-label=Түзүлгөн күнү
webauthn-registration-init-label=Парольсуз ачкыч (Тандалган аталыш)
webauthn-registration-init-label-prompt=Катталган парольсуз ачкычыңыздын аталышын киргизиңиз


# WebAuthn Error
webauthn-error-title=Парольсуз ачкыч катасы
webauthn-error-registration=Парольсуз ачкычты каттоо ийгиликсиз аяктады.<br /> {0}
webauthn-error-api-get=Парольсуз ачкыч менен аутентификациядан өтүү мүмкүн болгон жок.<br /> {0}
webauthn-error-different-user=Биринчи аутентификацияланган колдонуучу парольсуз ачкыч менен аутентификацияланган эмес.
webauthn-error-auth-verification=Парольсуз ачкыч менен аутентификация жыйынтыгы жараксыз.<br /> {0}
webauthn-error-register-verification=Парольсуз ачкыч каттоосу жараксыз.<br /> {0}
webauthn-error-user-not-found=Парольсуз ачкыч менен аутентификацияланган белгисиз колдонуучу.

# Passkey
passkey-login-title=Парольсуз ачкыч менен кирүү
passkey-available-authenticators=Бар болгон Парольсуз ачкычтар
passkey-unsupported-browser-text=Бул браузер Парольсуз ачкычты колдобойт. Башка браузерди колдонуп көрүңүз же администратор менен байланышыңыз.
passkey-doAuthenticate=Парольсуз ачкыч менен кириңиз
passkey-createdAt-label=Түзүлгөн күнү
passkey-autofill-select=Парольсуз ачкычыңызды тандаңыз

# Identity provider
identity-provider-redirector=Башка инсандык провайдер менен байланыштыруу
identity-provider-login-label=Же мында кириңиз
idp-email-verification-display-name=Электрондук почтаны текшерүү
idp-email-verification-help-text=Почтаңызды текшерүү менен аккаунтуңузду байлаңыз.
idp-username-password-form-display-name=Колдонуучу аты жана сырсөз
idp-username-password-form-help-text=Кириңиз, анан аккаунтуңузду байлаңыз.

finalDeletionConfirmation=Эгер аккаунтуңузду өчүрсөңүз, аны калыбына келтирүүгө болбойт. Аккаунтуңузду сактап калуу үчүн Жокко чыгарууну басыңыз.
irreversibleAction=Бул аракет кайтарылгыс
deleteAccountConfirm=Аккаунтту өчүрүүнү ырастоо

deletingImplies=Аккаунтуңузду өчүрүү төмөнкүлөрдү билдирет:
errasingData=Бардык маалыматтар өчүрүлөт
loggingOutImmediately=Сиз дароо чыгып кетесиз
accountUnusable=Андан кийинки колдонуу мүмкүн болбойт
userDeletedSuccessfully=Колдонуучу ийгиликтүү өчүрүлдү

access-denied=Кирүүгө тыюу салынган
access-denied-when-idp-auth={0} аркылуу аутентификация болгондо кирүүгө тыюу салынган

frontchannel-logout.title=Чыгуу
frontchannel-logout.message=Кийинки тиркемелерден чыгып жатасыз
logoutConfirmTitle=Чыгуу
logoutConfirmHeader=Чыгкыңыз келеби?
doLogout=Чыгуу

readOnlyUsernameMessage=Колдонуучу атыңызды жаңырта албайсыз, анткени ал окулчу гана.
error-invalid-multivalued-size=Атрибут {0} төмөнкүдөй болушу керек: к минимуму {1} жана максимум {2} {2,choice,0#маанилер|1#маани|1<маанилер}.

organization.confirm-membership.title=Сиз ${kc.org.name} уюмунун мүчөсү болууга даярсыз
organization.confirm-membership=Төмөнкү шилтемени басуу менен сиз {0} уюмунун мүчөсү болосуз:
organization.member.register.title=${kc.org.name} уюмуна кошулуу үчүн аккаунт түзүңүз
organization.select=Уюмду тандоо үчүн уюмду тандаңыз:
notMemberOfOrganization=Колдонуучу {0} уюмунун мүчөсү эмес
notMemberOfAnyOrganization=Колдонуучу эч кандай уюмдун мүчөсү эмес