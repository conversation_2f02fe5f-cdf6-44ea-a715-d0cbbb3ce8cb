emailVerificationSubject=التحقق من البريد الإلكتروني
emailVerificationBody=قام شخص ما بإنشاء حساب {2} بعنوان البريد الإلكتروني هذا. إذا كان هذا أنت، فانقر على الرابط أدناه للتحقق من عنوان بريدك الإلكتروني\n\n{0}\n\nستنتهي صلاحية هذا الرابط خلال {3}.\n\nإذا لم تكن قد أنشأت هذا الحساب، فقط تجاهل هذه الرسالة.
emailVerificationBodyHtml=<p>قام شخص ما بإنشاء حساب {2} بعنوان البريد الإلكتروني هذا. إذا كان هذا أنت، فانقر على الرابط أدناه للتحقق من عنوان بريدك الإلكتروني</p><p><a href="{0}">رابط التحقق من البريد الإلكتروني</a></p><p>ستنتهي صلاحية هذا الرابط خلال {3}.</p><p>إذا لم تكن قد أنشأت هذا الحساب، فقط تجاهل هذه الرسالة.</p>
emailUpdateConfirmationSubject=التحقق من البريد الإلكتروني الجديد
emailUpdateConfirmationBody=لتحديث حساب {2} الخاص بك بعنوان البريد الإلكتروني {1}، انقر على الرابط أدناه\n\n{0}\n\nستنتهي صلاحية هذا الرابط خلال {3}.\n\nإذا كنت لا تريد القيام بهذا التعديل، فقط تجاهل هذه الرسالة.
emailUpdateConfirmationBodyHtml=<p>لتحديث حساب {2} الخاص بك بعنوان البريد الإلكتروني {1}, انقر على الرابط أدناه</p><p><a href="{0}">{0}</a></p><p>ستنتهي صلاحية هذا الرابط خلال {3}.</p><p>إذا كنت لا تريد القيام بهذا التعديل، فقط تجاهل هذه الرسالة.</p>
emailTestSubject=[KEYCLOAK] - رسالة تجربة
emailTestBody=هذه رسالة تجربة
emailTestBodyHtml=<p>هذه رسالة تجربة</p>
identityProviderLinkSubject=ربط {0}
identityProviderLinkBody=قام شخص ما بطلب ربط الحساب "{1}" بالحساب "{0}" الخاص بالمستخدم {2} . إذا كان هذا أنت، فانقر على الرابط أدناه لإتمام عملية ربط الحسابات\n\n{3}\n\nستنتهي صلاحية هذا الرابط خلال {5}.\n\nإذا كنت لا تريد ربط الحساب، فقط تجاهل هذه الرسالة. إذا قمت بربط الحسابات، فستتمكن من تسجيل الدخول إلى {1} من خلال {0}.
identityProviderLinkBodyHtml=<p>قام شخص ما بطلب ربط الحساب <b>{1}</b> بالحساب <b>{0}</b> الخاص بالمستخدم {2}. إذا كان هذا أنت، فانقر على الرابط أدناه لإتمام عملية ربط الحسابات</p><p><a href="{3}">رابط لتأكيد ربط الحساب</a></p><p>ستنتهي صلاحية هذا الرابط خلال {5}.</p><p>إذا كنت لا تريد ربط الحساب، فقط تجاهل هذه الرسالة. إذا قمت بربط الحسابات، فستتمكن من تسجيل الدخول إلى {1} من خلال {0}.</p>
passwordResetSubject=إعادة تعيين كلمة المرور
passwordResetBody=قام شخص ما بطلب تغيير معلومات الدخول للحساب {2}. إذا كان هذا أنت، فانقر على الرابط أدناه لإعادة تعيين معلومات الدخول.\n\n{0}\n\nستنتهي صلاحية هذا الرابط خلال {3}.\n\nإذا كنت لا تريد إعادة تعيين معلومات الدخول، فقط تجاهل هذه الرسالة.
passwordResetBodyHtml=<p>قام شخص ما بطلب تغيير معلومات الدخول للحساب {2}. إذا كان هذا أنت، فانقر على الرابط أدناه لإعادة تعيين معلومات الدخول.</p><p><a href="{0}">رابط إعادة تعيين معلومات الدخول للحساب</a></p><p>ستنتهي صلاحية هذا الرابط خلال {3}.</p><p>إذا كنت لا تريد إعادة تعيين معلومات الدخول، فقط تجاهل هذه الرسالة.</p>
executeActionsSubject=تحديث بيانات حسابك
executeActionsBody=تلقيت طلب من مسؤول النظام لتحديث بيانات حسابك {2} والقيام بالإجراءات المطلوبة التالية: {3}. انقر على الرابط أدناه للبدء.\n\n{0}\n\nستنتهي صلاحية هذا الرابط خلال {4}.\n\nإذا لم تكن على علم بأن مسؤول النظام قد طلب ذلك، فتجاهل هذه الرسالة ولن يتم تغيير أي شيء.
executeActionsBodyHtml=<p>تلقيت طلب من مسؤول النظام لتحديث بيانات حسابك {2} والقيام بالإجراءات المطلوبة التالية: {3}. انقر على الرابط أدناه للبدء.</p><p><a href="{0}">رابط تحديث بيانات الحساب</a></p><p>ستنتهي صلاحية هذا الرابط خلال {4}.</p><p>إذا لم تكن على علم بأن مسؤول النظام قد طلب ذلك، فتجاهل هذه الرسالة ولن يتم تغيير أي شيء.</p>
eventLoginErrorSubject=خطأ في تسجيل الدخول
eventLoginErrorBody=تم رصد محاولة دخول فاشلة على حسابك في {0} ومن {1}. إذا لم تكن أنت، يرجى التواصل مع مسؤول النظام.
eventLoginErrorBodyHtml=<p>تم رصد محاولة دخول فاشلة على حسابك في {0} ومن {1}. إذا لم تكن أنت، يرجى التواصل مع مسؤول النظام.</p>
eventRemoveTotpSubject=إزالة رمز التحقق
eventRemoveTotpBody=تم إزالة خاصية رمز التحقق من حسابك في {0} ومن {1}. إذا لم تكن أنت، يرجى التواصل مع مسؤول النظام.
eventRemoveTotpBodyHtml=<p>تم إزالة خاصية رمز التحقق من حسابك في {0} ومن {1}. إذا لم تكن أنت، يرجى التواصل مع مسؤول النظام.</p>
eventUpdatePasswordSubject=تحديث كلمة المرور
eventUpdatePasswordBody=تم تغيير كلمة المرور الخاصة بك في {0} ومن {1}. إذا لم تكن أنت، يرجى التواصل مع مسؤول النظام.
eventUpdatePasswordBodyHtml=<p>تم تغيير كلمة المرور الخاصة بك في {0} ومن {1}. إذا لم تكن أنت، يرجى التواصل مع مسؤول النظام.</p>
eventUpdateTotpSubject=تحديث خاصية رمز التحقق
eventUpdateTotpBody=تم تحديث حاصية رمز التحقق لحسابك في {0} ومن {1}. إذا لم تكن أنت، يرجى التواصل مع مسؤول النظام.
eventUpdateTotpBodyHtml=<p>تم تحديث حاصية رمز التحقق لحسابك في {0} ومن {1}. إذا لم تكن أنت، يرجى التواصل مع مسؤول النظام.</p>

requiredAction.CONFIGURE_TOTP=إعداد خاصية رمز التحقق
requiredAction.TERMS_AND_CONDITIONS=الأحكام والشروط
requiredAction.UPDATE_PASSWORD=تحديث كلمة المرور
requiredAction.UPDATE_PROFILE=تحديث الملف التعريفي
requiredAction.VERIFY_EMAIL=التحقق من البريد الإلكتروني
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=توليد رموز مصادقة الاسترداد

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#ثانية|3#ثواني|9<ثانية}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#دقيقة|3#دقائق|9<دقيقة}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#ساعة|3#ساعات|9<ساعة}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#يوم|3#أيام|9<يوم}

emailVerificationBodyCode=يرجى التحقق من عنوان بريدك الإلكتروني عن طريق إدخال الرمز التالي.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>يرجى التحقق من عنوان بريدك الإلكتروني عن طريق إدخال الرمز التالي.</p><p><b>{0}</b></p>

