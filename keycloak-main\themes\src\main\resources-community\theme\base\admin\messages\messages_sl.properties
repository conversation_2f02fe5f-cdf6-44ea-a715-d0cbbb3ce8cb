invalidPasswordMinLengthMessage=Neveljavno geslo: na<PERSON><PERSON><PERSON><PERSON> je {0}.
invalidPasswordMaxLengthMessage=Neveljavno geslo: najve<PERSON>ja dol<PERSON> je {0}.
invalidPasswordMinLowerCaseCharsMessage=Neveljavno geslo: mora vsebovati vsaj {0} malih črk.
invalidPasswordMinDigitsMessage=Neveljavno geslo: mora vsebovati vsaj {0} številk.
invalidPasswordMinUpperCaseCharsMessage=Neveljavno geslo: mora vsebovati vsaj {0} velikih črk.
invalidPasswordMinSpecialCharsMessage=Neveljavno geslo: mora vsebovati vsaj {0} posebnih znakov.
invalidPasswordNotUsernameMessage=Neveljavno geslo: ne sme biti enako uporabniškemu imenu.
invalidPasswordNotContainsUsernameMessage=Neveljavno geslo: ne sme vsebovati upora<PERSON><PERSON>škega imena.
invalidPasswordNotEmailMessage=Neveljavno geslo: ne sme biti enako e-poštnemu naslovu.
invalidPasswordRegexPatternMessage=Neveljavno geslo: ne ustreza vzorcu regex.
invalidPasswordHistoryMessage=Neveljavno geslo: ne sme biti enako kateremu od zadnjih {0} gesel.
invalidPasswordBlacklistedMessage=Neveljavno geslo: geslo je na črni listi.
invalidPasswordGenericMessage=Neveljavno geslo: novo geslo ne ustreza pravilom za gesla.
ldapErrorEditModeMandatory=Način urejanja je obvezen
ldapErrorInvalidCustomFilter=Po meri nastavljen LDAP filter se ne začne z "(" ali se ne konča z ")".
ldapErrorConnectionTimeoutNotNumber=Časovna omejitev povezave mora biti številka
ldapErrorReadTimeoutNotNumber=Časovna omejitev branja mora biti številka
ldapErrorMissingClientId=ID odjemalca mora biti naveden v konfiguraciji, ko se ne uporablja preslikava vlog področja.
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Ni mogoče ohraniti dedovanja skupine in hkrati uporabljati UID tip članstva.
ldapErrorCantWriteOnlyForReadOnlyLdap=Ni mogoče nastaviti samo pisanja, ko način LDAP ponudnika ni ZAPISLJIV
ldapErrorCantWriteOnlyAndReadOnly=Ni mogoče nastaviti samo pisanja in samo branja hkrati
ldapErrorCantEnableStartTlsAndConnectionPooling=Ni mogoče omogočiti StartTLS in združevanja povezav hkrati.
ldapErrorCantEnableUnsyncedAndImportOff=Ni mogoče onemogočiti uvoza uporabnikov, ko je način LDAP ponudnika NESINHRONIZIRANO
ldapErrorMissingGroupsPathGroup=Skupina poti skupin ne obstaja - najprej ustvarite skupino na določeni poti
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=Preverjanje pravil za gesla je na voljo samo v načinu urejanja ZAPISLJIVO
clientRedirectURIsFragmentError=Preusmeritveni URI-ji ne smejo vsebovati fragmenta URI
clientRootURLFragmentError=Korenski URL ne sme vsebovati fragmenta URL
clientRootURLIllegalSchemeError=Korenski URL uporablja nedovoljeno shemo
clientBaseURLIllegalSchemeError=Osnovni URL uporablja nedovoljeno shemo
backchannelLogoutUrlIllegalSchemeError=URL za odjavo preko zadnjega kanala uporablja nedovoljeno shemo
clientRedirectURIsIllegalSchemeError=Preusmeritveni URI uporablja nedovoljeno shemo
clientBaseURLInvalid=Osnovni URL ni veljaven URL
clientRootURLInvalid=Korenski URL ni veljaven URL
clientRedirectURIsInvalid=Preusmeritveni URI ni veljaven URI
backchannelLogoutUrlIsInvalid=URL za odjavo preko zadnjega kanala ni veljaven URL
pairwiseMalformedClientRedirectURI=Odjemalec vsebuje neveljaven preusmeritveni URI.
pairwiseClientRedirectURIsMissingHost=Preusmeritveni URI-ji odjemalca morajo vsebovati veljavno komponento gostitelja.
pairwiseClientRedirectURIsMultipleHosts=Brez konfiguriranega URI-ja identifikatorja sektorja preusmeritveni URI-ji odjemalca ne smejo vsebovati več komponent gostitelja.
pairwiseMalformedSectorIdentifierURI=Napačno oblikovan URI identifikatorja sektorja.
pairwiseFailedToGetRedirectURIs=Ni uspelo pridobiti preusmeritvenih URI-jev iz URI-ja identifikatorja sektorja.
pairwiseRedirectURIsMismatch=Preusmeritveni URI-ji odjemalca se ne ujemajo s preusmeritveni URI-ji, pridobljenimi iz URI-ja identifikatorja sektorja.
duplicatedJwksSettings=Stikali "Uporabi JWKS" in "Uporabi JWKS URL" ne moreta biti vklopljeni hkrati.
error-invalid-value=Neveljavna vrednost.
error-invalid-blank=Prosimo, določite vrednost.
error-empty=Prosimo, določite vrednost.
error-invalid-length=Atribut {0} mora imeti dolžino med {1} in {2}.
error-invalid-length-too-short=Atribut {0} mora imeti najmanjšo dolžino {1}.
error-invalid-length-too-long=Atribut {0} mora imeti dolžino največ {2}.
error-invalid-email=Neveljaven e-poštni naslov.
error-invalid-number=Neveljavna številka.
error-number-out-of-range=Atribut {0} mora biti število med {1} in {2}.
error-number-out-of-range-too-small=Atribut {0} mora imeti vrednost najmanj {1}.
error-number-out-of-range-too-big=Atribut {0} mora imeti vrednost največ {2}.
error-pattern-no-match=Neveljavna vrednost.
error-invalid-uri=Neveljaven URL.
error-invalid-uri-scheme=Neveljavna URL shema.
error-invalid-uri-fragment=Neveljaven URL fragment.
error-user-attribute-required=Prosimo, določite atribut {0}.
error-invalid-date=Atribut {0} je neveljaven datum.
error-user-attribute-read-only=Atribut {0} je samo za branje.
error-username-invalid-character={0} vsebuje neveljaven znak.
error-person-name-invalid-character={0} vsebuje neveljaven znak.
error-invalid-multivalued-size=Atribut {0} mora imeti najmanj {1} in največ {2} {2,choice,0#vrednosti|1#vrednost|101#vrednost|201#vrednost|301#vrednost|401#vrednost|501#vrednost|601#vrednost|701#vrednost|801#vrednost|901#vrednost|1001#vrednost|1101#vrednost|1201#vrednost|1301#vrednost|1401#vrednost|1501#vrednost|1601#vrednost|1701#vrednost|1801#vrednost|1901#vrednost|2001#vrednost|2101#vrednost|2201#vrednost|2301#vrednost|2401#vrednost|2501#vrednost|2601#vrednost|2701#vrednost|2801#vrednost|2901#vrednost|3001#vrednost|1<vrednosti}.
