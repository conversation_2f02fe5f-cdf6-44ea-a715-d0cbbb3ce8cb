#
# Copyright 2016 Red Hat, Inc. and/or its affiliates
# and other contributors as indicated by the <AUTHOR>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

log4j.rootLogger=info, stdout

log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{HH:mm:ss,SSS} %-5p [%c] %m%n

log4j.logger.org.keycloak=info
log4j.logger.org.apache.directory.api=warn
log4j.logger.org.apache.directory.server.core=warn

# Enable to view detailed AS REQ and TGS REQ requests to embedded Kerberos server
#log4j.logger.org.apache.directory.server.kerberos=debug