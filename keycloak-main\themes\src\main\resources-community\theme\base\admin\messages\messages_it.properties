invalidPasswordMinLengthMessage=Password non valida: la lunghezza minima è {0}.
invalidPasswordMinDigitsMessage=Password non valida: deve contenere almeno {0} numeri.
invalidPasswordMinUpperCaseCharsMessage=Password non valida: deve contenere almeno {0} caratteri maiuscoli.
invalidPasswordNotUsernameMessage=Password non valida: non può essere uguale al nome utente.
invalidPasswordNotContainsUsernameMessage=Password non valida: non può contenere il nome utente.
invalidPasswordRegexPatternMessage=Password non valida: non corrisponde al formato richiesto.
invalidPasswordHistoryMessage=Password non valida: deve essere diversa dalle ultime {0} password usate.
invalidPasswordBlacklistedMessage=Password non valida: questo termine è in blacklist.
invalidPasswordGenericMessage=Password non valida: non corrisponde alle regole per le password.
ldapErrorEditModeMandatory=La modalità di modifica è obbligatoria
ldapErrorInvalidCustomFilter=Il filtro LDAP configurato non inizia con "(" o non finisce con ")".
ldapErrorConnectionTimeoutNotNumber=Il timeout della connessione deve essere un numero
ldapErrorReadTimeoutNotNumber=Il timeout della lettura deve essere un numero
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Non è possibile preservare l''ereditarietà del gruppo e contemporaneamente utilizzare l''appartenenza UID.
ldapErrorCantWriteOnlyForReadOnlyLdap=Non è possibile impostare la sola scrittura quando la modalità del provider LDAP non è WRITABLE
ldapErrorCantWriteOnlyAndReadOnly=Non è possibile impostare contemporaneamente sola lettura e sola scrittura
ldapErrorCantEnableStartTlsAndConnectionPooling=Non è possibile abilitare sia StartTLS che il pooling delle connessioni.
ldapErrorCantEnableUnsyncedAndImportOff=Non è possibile disattivare l''importazione degli utenti quando la modalità del provider LDAP è UNSYNCED
clientRootURLIllegalSchemeError=L''URL di root usa un protocollo vietato
clientRedirectURIsIllegalSchemeError=Un URI di redirect usa un protocollo vietato
clientBaseURLInvalid=L''URL di base non è valido
clientRootURLInvalid=L''URL di root non è valido
clientRedirectURIsInvalid=Un URI di redirect non è valido
backchannelLogoutUrlIsInvalid=L''URL di disconnessione non è valido
pairwiseMalformedClientRedirectURI=Il client contiene un URI di redirect non valido.
pairwiseClientRedirectURIsMissingHost=Gli URI di redirect del client devono contenere un host valido.
pairwiseClientRedirectURIsMultipleHosts=Senza un Sector Identifier URI, gli URI di reindirizzamento del client non devono contenere più host.
pairwiseMalformedSectorIdentifierURI=Sector Identifier URI non valido.
pairwiseFailedToGetRedirectURIs=Impossibile ricavare gli URI di redirect dal Sector Identifier URI.
duplicatedJwksSettings=Le opzioni "Usa JWKS" e "Usa URL JWKS" non possono essere attive contemporaneamente.
error-invalid-blank=Si prega di specificare un valore.
error-empty=Si prega di specificare un valore.
error-invalid-length-too-short=Il campo {0} deve avere una lunghezza minima di {1}.
error-invalid-length-too-long=Il campo {0} deve avere una lunghezza massima di {2}.
error-invalid-date=Il campo {0} è una data non valida.
error-user-attribute-read-only=Il campo {0} è di sola lettura.
error-username-invalid-character={0} contiene caratteri non validi.
error-person-name-invalid-character={0} contiene caratteri non validi.
error-invalid-multivalued-size=Il campo {0} deve avere almeno {1} e al massimo {2} {2,choice,0#valori|1#valore|1<valori}.
invalidPasswordMaxLengthMessage=Password non valida: la lunghezza massima è {0}.
clientRootURLFragmentError=L''URL di root non deve contenere un URL fragment
ldapErrorMissingClientId=Se non si usa la mappatura dei ruoli del realm è obbligatorio fornire l''ID del client.
invalidPasswordMinLowerCaseCharsMessage=Password non valida: deve contenere almeno {0} caratteri minuscoli.
error-invalid-email=Indirizzo email non valido.
invalidPasswordNotEmailMessage=Password non valida: non può essere uguale all''email.
invalidPasswordMinSpecialCharsMessage=Password non valida: deve contenere almeno {0} caratteri speciali.
clientRedirectURIsFragmentError=Gli URI di reindirizzamento non devono contenere un URI fragment
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=Il criterio di convalida della password è applicabile solo con la modalità di modifica WRITABLE
clientBaseURLIllegalSchemeError=L''URL di base usa un protocollo vietato
error-invalid-value=Valore non valido.
error-user-attribute-required=Si prega di specificare il campo {0}.
ldapErrorMissingGroupsPathGroup=Il percorso del gruppo non esiste: creare prima il gruppo con il percorso desiderato
backchannelLogoutUrlIllegalSchemeError=L''URL di disconnessione usa un protocollo vietato
error-invalid-number=Numero non valido.
pairwiseRedirectURIsMismatch=Gli URI di redirect del client non corrispondono agli URI di redirect del Sector Identifier URI.
error-invalid-length=Il campo {0} deve avere una lunghezza compresa fra {1} e {2}.
error-number-out-of-range-too-big=Il campo {0} deve avere un valore massimo di {2}.
error-pattern-no-match=Valore non valido.
error-invalid-uri-fragment=URL fragment non valido.
error-invalid-uri-scheme=Protocollo URL non valido.
error-number-out-of-range=Il campo {0} deve essere un numero compreso fra {1} e {2}.
error-number-out-of-range-too-small=Il campo {0} deve avere un valore minimo di {1}.
error-invalid-uri=URL non valido.
