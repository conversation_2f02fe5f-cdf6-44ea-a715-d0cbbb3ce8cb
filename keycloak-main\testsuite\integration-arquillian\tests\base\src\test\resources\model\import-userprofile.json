{"realm": "user-profile", "enabled": true, "accessTokenLifespan": 3000, "accessCodeLifespan": 10, "accessCodeLifespanUserAction": 6000, "sslRequired": "external", "registrationAllowed": false, "requiredCredentials": ["password"], "users": [{"username": "<EMAIL>", "enabled": true, "email": "<EMAIL>", "firstName": "Bill", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "password"}], "realmRoles": ["user"], "applicationRoles": {"account": ["manage-account"]}}], "roles": {"realm": [{"name": "user", "description": "User privileges"}, {"name": "admin", "description": "Administrator privileges"}]}, "scopeMappings": [{"client": "third-party", "roles": ["user"]}, {"client": "customer-portal", "roles": ["user"]}, {"client": "product-portal", "roles": ["user"]}], "applications": [{"name": "customer-portal", "enabled": true, "adminUrl": "http://localhost:8080/customer-portal", "redirectUris": ["http://localhost:8080/customer-portal/*"], "secret": "password"}, {"name": "product-portal", "enabled": true, "adminUrl": "http://localhost:8080/product-portal", "redirectUris": ["http://localhost:8080/product-portal/*"], "secret": "password"}], "oauthClients": [{"name": "third-party", "enabled": true, "redirectUris": ["http://localhost:8080/oauth-client/*", "http://localhost:8080/oauth-client-cdi/*"], "secret": "password"}], "components": {"org.keycloak.userprofile.UserProfileProvider": [{"providerId": "declarative-user-profile", "subComponents": {}, "config": {"kc.user.profile.config": ["{\"attributes\":[{\"name\":\"username\",\"displayName\":\"${username}\",\"validations\":{\"length\":{\"min\":3,\"max\":255},\"username-prohibited-characters\":{}}},{\"name\":\"email\",\"displayName\":\"${email}\",\"validations\":{\"email\":{},\"length\":{\"max\":255}}},{\"name\":\"firstName\",\"displayName\":\"${firstName}\",\"permissions\":{\"view\":[\"user\",\"admin\"],\"edit\":[\"user\",\"admin\"]},\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"selector\":{\"scopes\":[]},\"required\":{}},{\"name\":\"lastName\",\"displayName\":\"${lastName}\",\"permissions\":{\"view\":[\"user\",\"admin\"],\"edit\":[\"user\",\"admin\"]},\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"selector\":{\"scopes\":[]}},{\"selector\":{\"scopes\":[\"microprofile-jwt\"]},\"permissions\":{\"view\":[],\"edit\":[]},\"name\":\"test\"}]}"]}}]}}