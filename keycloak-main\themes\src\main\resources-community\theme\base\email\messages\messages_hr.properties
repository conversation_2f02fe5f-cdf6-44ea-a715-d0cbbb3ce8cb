emailVerificationSubject=Potvrda e-pošte
emailVerificationBody=Netko je stvorio {2} račun s ovom adresom e-pošte. Ako ste to vi, kliknite na donji link kako biste potvrdili svoju adresu e-pošte\n\n{0}\n\nOva poveznica će isteći za {3}.\n\nAko niste stvorili ovaj račun, jednostavno ignorirajte ovu poruku.
emailVerificationBodyHtml=<p>Netko je stvorio {2} račun s ovom adresom e-pošte. Ako ste to vi, kliknite na donji link kako biste potvrdili svoju adresu e-pošte</p><p><a href="{0}">Link za potvrdu e-pošte</a></p><p>Ova poveznica će isteći za {3}.</p><p>Ako niste stvorili ovaj račun, jednostavno ignorirajte ovu poruku.</p>
orgInviteSubject=Poziv za pridruživanje organizaciji {0}
orgInviteBody=Pozvani ste da se pridružite organizaciji "{3}". Kliknite na donji link za pridruživanje.\n\n{0}\n\nOva poveznica će isteći za {4}.\n\nAko se ne želite pridružiti organizaciji, jednostavno ignorirajte ovu poruku.
orgInviteBodyHtml=<p>Pozvani ste da se pridružite organizaciji {3}. Kliknite na donji link za pridruživanje.</p><p><a href="{0}">Link za pridruživanje organizaciji</a></p><p>Ova poveznica će isteći za {4}.</p><p>Ako se ne želite pridružiti organizaciji, jednostavno ignorirajte ovu poruku.</p>
orgInviteBodyPersonalized=Hej, "{5}" "{6}".\n\n Pozvani ste da se pridružite organizaciji "{3}". Kliknite na donji link za pridruživanje.\n\n{0}\n\nOva poveznica će isteći za {4}.\n\nAko se ne želite pridružiti organizaciji, jednostavno ignorirajte ovu poruku.
orgInviteBodyPersonalizedHtml=<p>Hej, {5} {6}.</p><p>Pozvani ste da se pridružite organizaciji {3}. Kliknite na donji link za pridruživanje.</p><p><a href="{0}">Link za pridruživanje organizaciji</a></p><p>Ova poveznica će isteći za {4}.</p><p>Ako se ne želite pridružiti organizaciji, jednostavno ignorirajte ovu poruku.</p>
emailUpdateConfirmationSubject=Potvrda nove e-pošte
emailUpdateConfirmationBody=Za ažuriranje vašeg {2} računa s adresom e-pošte {1}, kliknite na donji link\n\n{0}\n\nOva poveznica će isteći za {3}.\n\nAko ne želite nastaviti s ovom izmjenom, jednostavno ignorirajte ovu poruku.
emailUpdateConfirmationBodyHtml=<p>Za ažuriranje vašeg {2} računa s adresom e-pošte {1}, kliknite na donji link</p><p><a href="{0}">{0}</a></p><p>Ova poveznica će isteći za {3}.</p><p>Ako ne želite nastaviti s ovom izmjenom, jednostavno ignorirajte ovu poruku.</p>
emailTestSubject=[KEYCLOAK] - SMTP testna poruka
emailTestBody=Ovo je testna poruka
emailTestBodyHtml=<p>Ovo je testna poruka</p>
identityProviderLinkSubject=Poveznica {0}
identityProviderLinkBody=Netko želi povezati vaš "{1}" račun s "{0}" računom korisnika {2}. Ako ste to vi, kliknite na donji link za povezivanje računa\n\n{3}\n\nOva poveznica će isteći za {5}.\n\nAko ne želite povezati račun, jednostavno ignorirajte ovu poruku. Ako povežete račune, moći ćete se prijaviti na {1} putem {0}.
identityProviderLinkBodyHtml=<p>Netko želi povezati vaš <b>{1}</b> račun s <b>{0}</b> računom korisnika {2}. Ako ste to vi, kliknite na donji link za povezivanje računa</p><p><a href="{3}">Link za potvrdu povezivanja računa</a></p><p>Ova poveznica će isteći za {5}.</p><p>Ako ne želite povezati račun, jednostavno ignorirajte ovu poruku. Ako povežete račune, moći ćete se prijaviti na {1} putem {0}.</p>
passwordResetSubject=Ponovno postavljanje lozinke
passwordResetBody=Netko je upravo zatražio promjenu podataka za prijavu vašeg {2} računa. Ako ste to vi, kliknite na donji link za resetiranje.\n\n{0}\n\nOva poveznica i kod će isteći za {3}.\n\nAko ne želite resetirati svoje podatke za prijavu, jednostavno ignorirajte ovu poruku i ništa se neće promijeniti.
passwordResetBodyHtml=<p>Netko je upravo zatražio promjenu podataka za prijavu vašeg {2} računa. Ako ste to vi, kliknite na donji link za resetiranje.</p><p><a href="{0}">Link za resetiranje podataka za prijavu</a></p><p>Ova poveznica će isteći za {3}.</p><p>Ako ne želite resetirati svoje podatke za prijavu, jednostavno ignorirajte ovu poruku i ništa se neće promijeniti.</p>
executeActionsSubject=Ažurirajte svoj račun
executeActionsBody=Vaš administrator je upravo zatražio da ažurirate svoj {2} račun izvršavanjem sljedeće akcije(e): {3}. Kliknite na donji link za pokretanje ovog procesa.\n\n{0}\n\nOva poveznica će isteći za {4}.\n\nAko niste svjesni da je vaš administrator to zatražio, jednostavno ignorirajte ovu poruku i ništa se neće promijeniti.
executeActionsBodyHtml=<p>Vaš administrator je upravo zatražio da ažurirate svoj {2} račun izvršavanjem sljedeće akcije(e): {3}. Kliknite na donji link za pokretanje ovog procesa.</p><p><a href="{0}">Link za ažuriranje računa</a></p><p>Ova poveznica će isteći za {4}.</p><p>Ako niste svjesni da je vaš administrator to zatražio, jednostavno ignorirajte ovu poruku i ništa se neće promijeniti.</p>
eventLoginErrorSubject=Greška prilikom prijave
eventLoginErrorBody=Otkriven je neuspješan pokušaj prijave na vaš račun na {0} s {1}. Ako to niste vi, molimo kontaktirajte administratora.
eventLoginErrorBodyHtml=<p>Otkriven je neuspješan pokušaj prijave na vaš račun na {0} s {1}. Ako to niste vi, molimo kontaktirajte administratora.</p>
eventRemoveTotpSubject=Ukloni OTP
eventRemoveTotpBody=OTP je uklonjen s vašeg računa na {0} s {1}. Ako to niste vi, molimo kontaktirajte administratora.
eventRemoveTotpBodyHtml=<p>OTP je uklonjen s vašeg računa na {0} s {1}. Ako to niste vi, molimo kontaktirajte administratora.</p>
eventUpdatePasswordSubject=Ažuriraj lozinku
eventUpdatePasswordBody=Vaša lozinka je promijenjena na {0} s {1}. Ako to niste vi, molimo kontaktirajte administratora.
eventUpdatePasswordBodyHtml=<p>Vaša lozinka je promijenjena na {0} s {1}. Ako to niste vi, molimo kontaktirajte administratora.</p>
eventUpdateTotpSubject=Ažuriraj OTP
eventUpdateTotpBody=OTP je ažuriran za vaš račun na {0} s {1}. Ako to niste vi, molimo kontaktirajte administratora.
eventUpdateTotpBodyHtml=<p>OTP je ažuriran za vaš račun na {0} s {1}. Ako to niste vi, molimo kontaktirajte administratora.</p>
eventUpdateCredentialSubject=Ažuriraj vjerodajnicu
eventUpdateCredentialBody=Vaša {0} vjerodajnica je promijenjena na {1} s {2}. Ako to niste vi, molimo kontaktirajte administratora.
eventUpdateCredentialBodyHtml=<p>Vaša {0} vjerodajnica je promijenjena na {1} s {2}. Ako to niste vi, molimo kontaktirajte administratora.</p>
eventRemoveCredentialSubject=Ukloni vjerodajnicu
eventRemoveCredentialBody=Vjerodajnica {0} je uklonjena s vašeg računa na {1} s {2}. Ako to niste vi, molimo kontaktirajte administratora.
eventRemoveCredentialBodyHtml=<p>Vjerodajnica {0} je uklonjena s vašeg računa na {1} s {2}. Ako to niste vi, molimo kontaktirajte administratora.</p>
eventUserDisabledByTemporaryLockoutSubject=Korisnik onemogućen privremenom blokadom
eventUserDisabledByTemporaryLockoutBody=Vaš korisnik je privremeno onemogućen zbog više neuspješnih pokušaja na {0}. Molimo kontaktirajte administratora ako je potrebno.
eventUserDisabledByTemporaryLockoutHtml=<p>Vaš korisnik je privremeno onemogućen zbog više neuspješnih pokušaja na {0}. Molimo kontaktirajte administratora ako je potrebno.</p>
eventUserDisabledByPermanentLockoutSubject=Korisnik onemogućen trajnom blokadom
eventUserDisabledByPermanentLockoutBody=Vaš korisnik je trajno onemogućen zbog više neuspješnih pokušaja na {0}. Molimo kontaktirajte administratora.
eventUserDisabledByPermanentLockoutHtml=<p>Vaš korisnik je trajno onemogućen zbog više neuspješnih pokušaja na {0}. Molimo kontaktirajte administratora.</p>

requiredAction.CONFIGURE_TOTP=Konfigurirajte OTP
requiredAction.TERMS_AND_CONDITIONS=Uvjete i odredbe
requiredAction.UPDATE_PASSWORD=Ažurirajte lozinku
requiredAction.UPDATE_PROFILE=Ažurirajte profil
requiredAction.VERIFY_EMAIL=Potvrdite e-poštu
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Generirajte kodove za oporavak

# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be '{0,choice,0#minut|1#minuta|2#minuty|2<minut}'
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#sekundi|1#sekunda|2#sekunde|3#sekunde|4#sekunde|4<sekunda}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#minuta|1#minuta|2#minute|3#minute|4#minute|4<minuta}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#sati|1#sata|2#sata|3#sata|4#sata|4<sati}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#dana|0<dana}

emailVerificationBodyCode=Molimo vas da potvrdite svoju adresu e-pošte unosom sljedećeg koda.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Molimo vas da potvrdite svoju adresu e-pošte unosom sljedećeg koda.</p><p><b>{0}</b></p>

