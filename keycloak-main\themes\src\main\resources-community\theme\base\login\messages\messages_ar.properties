doLogIn=تسجيل دخول
doRegister=تسجيل جديد
doRegisterSecurityKey=تسجيل جديد
doCancel=إلغاء
doSubmit=إرسال
doBack=رجوع
doYes=نعم
doNo=لا
doContinue=استمرار
doIgnore=تجاهل
doAccept=موافقة
doDecline=رفض
doForgotPassword=نسيت كلمة المرور؟
doClickHere=انقر هنا
doImpersonate=انتحال شخصية
doTryAgain=المحاولة مرة أخرى
doTryAnotherWay=المجاولة بطريقة أخرى
doConfirmDelete=تأكيد الحذف
errorDeletingAccount=حدث خطأ أثناء حذف الحساب
deletingAccountForbidden=ليس لديك الصلاحية الكافية لحذف حسابك، قم بالتواصل مع مسؤول النظام.
kerberosNotConfigured=لم يتم تهيئة البروتوكول Kerberos
kerberosNotConfiguredTitle=لم يتم تهيئة البروتوكول Kerberos
bypassKerberosDetail=إما أنك لم تقم بتسجيل الدخول بواسطة البروتوكول Kerberos أو أن متصفحك لم يتم إعداده لتسجيل الدخول بواسطة البروتوكول Kerberos. الرجاء النقر على زر استمرار لتسجيل الدخول من خلال وسائل أخرى
kerberosNotSetUp=لم يتم تهيئة البروتوكول Kerberos. لا يمكنك تسجيل الدخول.
registerTitle=تسجيل
loginAccountTitle=تسجيل الدخول إلى حسابك
loginTitle=تسجيل الدخول إلى {0}
loginTitleHtml={0}
impersonateTitle={0} انتحال شخصية المستخدم
impersonateTitleHtml=<strong>{0}</strong> انتحال شخصية المستخدم
realmChoice=المنظومة
unknownUser=مستخدم غير معروف
loginTotpTitle=إعداد تطبيق هاتف مصادق
loginProfileTitle=تحديث معلومات الحساب
loginIdpReviewProfileTitle=تحديث معلومات الحساب
loginTimeout=انتهت مهلة محاولة تسجيل الدخول. سيتم البدء في عملية تسجيل الدخول من جديد.
reauthenticate=يرجى إعادة المصادقة للمتابعة
oauthGrantTitle=منح صلاحية الوصول إلى {0}
oauthGrantTitleHtml={0}
oauthGrantInformation=تأكد من أنك تثق في {0} من خلال معرفة كيف سيتعامل {0} مع بياناتك.
oauthGrantReview=يمكنك مراجعة 
oauthGrantTos=بنود الخدمة.
oauthGrantPolicy=سياسة الخصوصية.
errorTitle=نعتذر...
errorTitleHtml=<strong>نعتذر</strong> ...
emailVerifyTitle=التحقق من البريد الإلكتروني
emailForgotTitle=نسيت كلمة المرور؟
updateEmailTitle=تحديث البريد الإلكتروني
emailUpdateConfirmationSentTitle=تم إرسال رسالة التحقق عبر البريد الإلكتروني
emailUpdateConfirmationSent=تم إرسال رسالة التحقق عبر البريد الإلكتروني إلى {0}. يجب عليك اتباع التعليمات لإكمال تحديث البريد الإلكتروني.
emailUpdatedTitle=تم تحديث البريد الإلكتروني
emailUpdated=تم تحديث البريد الإلكتروني الخاص بالحساب إلى {0}.
updatePasswordTitle=تم تحديث كلمة المرور
codeSuccessTitle=رمز النجاح
codeErrorTitle=رمز الخطأ\: {0}
displayUnsupported=نوع العرض المطلوب غير مدعوم
browserRequired=المتصفح مطلوب لعملية تسجيل الدخول
browserContinue=المتصفح مطلوب لإكمال عملية تسجيل الدخول
browserContinuePrompt=فتح المتصفح لإكمال عملية تسجيل الدخول؟ [ن/ل]:
browserContinueAnswer=ن

# Transports
usb=منفذ USB
nfc=قارئ بطاقة NFC
bluetooth=بلوتوث
internal=داخلي
unknown=غير معروف

termsTitle=الشروط والأحكام
termsText=<p>يجب تحديد الشروط والأحكام</p>
termsPlainText=يجب تحديد الشروط والأحكام.
termsAcceptanceRequired=يجب الموافقة على الشروط والأحكام.
acceptTerms=أوافق على الشروط والأحكام

recaptchaFailed=فشل في اختبار Recaptcha
recaptchaNotConfigured=مطلوب اختبار Recaptcha، ولكن لم يتم تكوينه
consentDenied=تم رفض الموافقة.

noAccount=مستخدم جديد؟
username=اسم المستخدم
usernameOrEmail=اسم المستخدم أو البريد الإلكتروني
firstName=الاسم الأول
givenName=الاسم الأول
fullName=الاسم الكامل
lastName=الاسم الأخير
familyName=اسم العائلة
email=البريد الإلكتروني
password=كلمة المرور
passwordConfirm=تأكيد كلمة المرور
passwordNew=كلمة مرور جديدة
passwordNewConfirm=تأكيد كلمة المرور الجديدة
hidePassword=إخفاء كلمة المرور
showPassword=إظهار كلمة المرور
rememberMe=تذكرني
authenticatorCode=رمز لمرة واحدة
address=العنوان
street=الشارع
locality=المدينة
region=الولاية أو المنطقة
postal_code=الرمز البريدي
country=الدولة
emailVerified=تم التحقق من البريد الإلكتروني
website=الموقع الإلكتروني
phoneNumber=رقم الهاتف
phoneNumberVerified=تم التحقق من رقم الهاتف
gender=الجنس
birthday=تاريخ الميلاد
zoneinfo=التوقيت
gssDelegationCredential=تفويض الاعتماد GSS
logoutOtherSessions=تسجيل الخروج من الأجهزة الأخرى

profileScopeConsentText=ملف تعريفي للمستخدم
emailScopeConsentText=البريد الإلكتروني
addressScopeConsentText=العنوان
phoneScopeConsentText=رقم الهاتف
offlineAccessScopeConsentText=الوصول دون اتصال
samlRoleListScopeConsentText=الأدوار الخاصة بي
rolesScopeConsentText=أدوار المستخدم

restartLoginTooltip=إعادة تسجيل الدخول

loginTotpIntro=تحتاج إلى إعداد مولّد كلمة مرور لمرة واحدة للوصول إلى هذا الحساب
loginTotpStep1=قم بتثبيت إحدى التطبيقات التالية على هاتفك المتنقل:
loginTotpStep2=افتح التطبيق ثم امسح رمز الاستجابة السريعة:
loginTotpStep3=أدخل رمز التحقق ذات الاستخدام الواحد والصادر من التطبيق ثم انقر على زر الحفظ لإتمام الإعداد.
loginTotpStep3DeviceName=ضع اسمًا للجهاز حتى يسهل عليك إدارة أجهزة المصادقة.
loginTotpManualStep2=افتح التطبيق ثم أدخل المفتاح:
loginTotpManualStep3=استخدم قيم التكوين التالية إذا سمح التطبيق بتعيينها:
loginTotpUnableToScan=غير قادر على المسح؟
loginTotpScanBarcode=مسح رمز الاستجابة السريعة؟
loginCredential=بيانات الدخول
loginOtpOneTime=رمز لمرة واحدة
loginTotpType=النوع
loginTotpAlgorithm=الخوارزمية
loginTotpDigits=عدد الخانات
loginTotpInterval=المدة الزمنية
loginTotpCounter=العداد
loginTotpDeviceName=اسم الجهاز

loginTotp.totp=على أساس الوقت
loginTotp.hotp=على أساس العداد

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

loginChooseAuthenticator=حدد طريقة تسجيل الدخول

oauthGrantRequest=هل تود منح صلاحية الوصول هذه؟
inResource=في

oauth2DeviceVerificationTitle=تسجيل الدخول على الأجهزة
verifyOAuth2DeviceUserCode=أدخل الرمز الظاهر على جهازك ثم انقر على زر الإرسال
oauth2DeviceInvalidUserCodeMessage=رمز غير صالح، يرجى المحاولة مرة أخرى.
oauth2DeviceExpiredUserCodeMessage=انتهت صلاحية الرمز. يرجى العودة إلى جهازك ثم محاولة الاتصال مرة أخرى.
oauth2DeviceVerificationCompleteHeader=تم تسجيل الدخول على الجهاز بنجاح
oauth2DeviceVerificationCompleteMessage=يمكنك إغلاق نافذة المتصفح هذه والعودة إلى جهازك.
oauth2DeviceVerificationFailedHeader=فشل في تسجيل الدخول على الجهاز
oauth2DeviceVerificationFailedMessage=يمكنك إغلاق نافذة المتصفح هذه والعودة إلى جهازك ومحاولة الاتصال مرة أخرى.
oauth2DeviceConsentDeniedMessage=تم رفض الموافقة على الاتصال بالجهاز.
oauth2DeviceAuthorizationGrantDisabledMessage=لا يُسمح للعميل ببدء الإجراء OAuth 2.0 Device Authorization Grant، حيث أنه معطل.

emailVerifyInstruction1=تم إرسال بريد إلكتروني يحتوي على إرشادات للتحقق من عنوان بريدك الإلكتروني إلى عنوانك {0}.
emailVerifyInstruction2=ألم تستلم رمز التحقق على بريدك الإلكتروني؟
emailVerifyInstruction3=لإعادة إرسال البريد الإلكتروني.

emailLinkIdpTitle=ربط {0}
emailLinkIdp1=تم إرسال رسالة بريد إلكتروني بها إرشادات لربط الحساب {0} ({1}) بالحساب {2} الخاص بك.
emailLinkIdp2=ألم تستلم رمز التحقق على بريدك الإلكتروني؟
emailLinkIdp3=لإعادة إرسال البريد الإلكتروني.
emailLinkIdp4=إذا كنت قد تحققت بالفعل من البريد الإلكتروني في متصفح مختلف
emailLinkIdp5=للاستمرار.

backToLogin=&raquo; العودة إلى تسجيل الدخول

emailInstruction=أدخل اسم المستخدم أو عنوان البريد الإلكتروني الخاص بك وسنرسل لك تعليمات حول كيفية إنشاء كلمة مرور جديدة.
emailInstructionUsername=أدخل اسم المستخدم الخاص بك وسنرسل لك تعليمات حول كيفية إنشاء كلمة مرور جديدة.

copyCodeInstruction=يرجى نسخ هذا الرمز ولصقه في التطبيق الخاص بك:

pageExpiredTitle=انتهت صلاحية الصفحة
pageExpiredMsg1=لإعادة عملية تسجيل الدخول
pageExpiredMsg2=لمواصلة عملية تسجيل الدخول

personalInfo=البيانات الشخصية:
role_admin=مسؤول
role_realm-admin=مسؤول منظومة
role_create-realm=إنشاء منظومة
role_create-client=إنشاء عميل
role_view-realm=عرض المنظومة
role_view-users=عرض المستخدمين
role_view-applications=عرض التطبيقات
role_view-clients=عرض العملاء
role_view-events=عرض الأحداث
role_view-identity-providers=عرض مزودي الحسابات
role_manage-realm=إدارة المنظومة
role_manage-users=إدارة المستخدمين
role_manage-applications=إدارة التطبيقات
role_manage-identity-providers=إدارة مزودي الحسابات
role_manage-clients=إدارة العملاء
role_manage-events=إدارة الأحداث
role_view-profile=عرض الملف الشخصي
role_manage-account=إدارة الحساب
role_manage-account-links=إدارة ارتباطات الحساب
role_read-token=قراءة الرمز
role_offline-access=الوصول دون اتصال
client_account=الحساب
client_account-console=لوحة التحكم بالحساب
client_security-admin-console=لوحة التحكم بأمان المسؤول
client_admin-cli=واجهة سطر الأوامر للمسؤول
client_realm-management=إدارة المنظومة
client_broker=وسيط

requiredFields=الحقول المطلوبة

invalidUserMessage=اسم المستخدم أو كلمة مرور غير صالحة.
invalidUsernameMessage=اسم المستخدم غير صالح.
invalidUsernameOrEmailMessage=اسم المستخدم أو البريد الإلكتروني غير صالح.
invalidPasswordMessage=كلمة المرور غير صالحة.
invalidEmailMessage=البريد الإلكتروني غير صالح.
accountDisabledMessage=الحساب معطل، تواصل مع مسؤول النظام.
accountTemporarilyDisabledMessage=اسم المستخدم أو كلمة مرور غير صالحة.
accountPermanentlyDisabledMessage=اسم المستخدم أو كلمة مرور غير صالحة.
accountTemporarilyDisabledMessageTotp=رمز التحقق غير صالح.
accountPermanentlyDisabledMessageTotp=رمز التحقق غير صالح.
expiredCodeMessage=نفذ الوقت المسموح للدخول. الرجاء تسجيل الدخول مرة أخرى.
expiredActionMessage=انتهى الإجراء. الرجاء الاستمرار في تسجيل الدخول الآن.
expiredActionTokenNoSessionMessage=انتهى الإجراء.
expiredActionTokenSessionExistsMessage=انتهى الإجراء. يرجى البدء مرة أخرى.
sessionLimitExceeded=هناك جلسات كثيرة جدًا

missingFirstNameMessage=الرجاء تحديد الاسم الأول.
missingLastNameMessage=الرجاء تحديد الاسم الأخير.
missingEmailMessage=الرجاء تحديد البريد الإلكتروني.
missingUsernameMessage=الرجاء تحديد اسم المستخدم.
missingPasswordMessage=الرجاء تحديد كلمة المرور.
missingTotpMessage=الرجاء تحديد رمز التحقق.
missingTotpDeviceNameMessage=الرجاء تحديد اسم الجهاز.
notMatchPasswordMessage=كلمات المرور غير متطابقة.

error-invalid-value=قيمة غير صالحة.
error-invalid-blank=يرجى تحديد قيمة.
error-empty=يرجى تحديد قيمة.
error-invalid-length=الطول يجب أن يكون بين {1} و {2}.
error-invalid-length-too-short=الطول يجب ألا يقل عن {1}.
error-invalid-length-too-long=الطول يجب ألا يزيد عن {2}.
error-invalid-email=بريد إلكتروني غير صالح.
error-invalid-number=رقم غير صالح.
error-number-out-of-range=الرقم يجب أن يكون بين {1} و {2}.
error-number-out-of-range-too-small=الرقم يجب ألا تقل قيمته عن {1}.
error-number-out-of-range-too-big=الرقم يجب ألا تزيد قيمته عن {2}.
error-pattern-no-match=قيمة غير صالحة.
error-invalid-uri=عنوان موقع غير صالح.
error-invalid-uri-scheme=بادئة عنوان موقع غير صالحة.
error-invalid-uri-fragment=ملحق عنوان موقع غير صالح.
error-user-attribute-required=يرجى تحديد هذا الحقل.
error-invalid-date=تاريخ غير صالح.
error-user-attribute-read-only=هذا الحقل للقراءة فقط.
error-username-invalid-character=القيمة تحتوي على حرف غير صالح.
error-person-name-invalid-character=القيمة تحتوي على حرف غير صالح.
error-reset-otp-missing-id=يرجى اختيار إعداد لخاصية رمز التحقق.

invalidPasswordExistingMessage=كلمة المرور الحالية غير صالحة.
invalidPasswordBlacklistedMessage=كلمة المرور غير صالحة: كلمة المرور في القائمة السوداء.
invalidPasswordConfirmMessage=تأكيد كلمة المرور غير متطابق.
invalidTotpMessage=رمز التحقق غير صالح.

usernameExistsMessage=اسم المستخدم مستخدم مسبقًا.
emailExistsMessage=البريد الإلكتروني مستخدم مسبقًا.

federatedIdentityExistsMessage=المستخدم صاحب ({0}: {1}) موجود مسبقًا. الرجاء تسجيل الدخول إلى صفحة إدارة الحساب لربط الحساب.
federatedIdentityUnavailableMessage=المستخدم {0} المصادق عليه بواسطة مزود الحسابات {1} غير موجود. الرجاء التواصل مع مسؤول النظام.
federatedIdentityUnmatchedEssentialClaimMessage=رمز المصادقة للتعريف الذي تم إصداره من مزود الحسابات لا يتطابق مع القيم التي إعدادها مسبقًا. الرجاء التواصل مع مسؤول النظام.

confirmLinkIdpTitle=الحساب موجود مسبقًا
federatedIdentityConfirmLinkMessage=المستخدم صاحب ({0}: {1}) موجود مسبقًا. كيف تريد الاستمرار؟
federatedIdentityConfirmReauthenticateMessage=المصادقة لربط حسابك بـ {0}
nestedFirstBrokerFlowMessage=لم يتم ربط مستخدم {0} ({1}) بأي مستخدم معروف.
confirmLinkIdpReviewProfile=مراجعة الملف الشخصي
confirmLinkIdpContinue=إضافة إلى الحساب الحالي

configureTotpMessage=تحتاج إلى إعداد المصادقة بالهاتف الذكي لتفعيل حسابك.
configureBackupCodesMessage=تحتاج إلى إعداد رموز المصادقة الاحتياطية لتفعيل حسابك.
updateProfileMessage=تحتاج إلى تحديث الملف الشخصي لتفعيل حسابك.
updatePasswordMessage=تحتاج إلى تحديث كلمة المرور لتفعيل حسابك.
updateEmailMessage=تحتاج إلى تحديث البريد الإلكتروني لتفعيل حسابك.
resetPasswordMessage=تحتاج إلى تحديث كلمة المرور الخاصة بك.
verifyEmailMessage=تحتاج إلى التحقق من البريد الإلكتروني لتفعيل حسابك.
linkIdpMessage=تحتاج إلى التحقق من البريد الإلكتروني لربط حسابك بـ {0}.

emailSentMessage=من المفترض أن تتلقى بريدًا إلكترونيًا عما قريب يحتوي على مزيد من الإرشادات.
emailSendErrorMessage=فشل في إرسال البريد الإلكتروني، يرجى المحاولة مرة أخرى في وقت لاحق.

accountUpdatedMessage=تم تحديث الحساب الخاص بك.
accountPasswordUpdatedMessage=تم تحديث كلمة المرور الخاصة بك.

delegationCompleteHeader=تم تسجيل الدخول بنجاح
delegationCompleteMessage=يمكنك إغلاق نافذة المتصفح هذه والعودة إلى تطبيق وحدة التحكم الخاصة بك.
delegationFailedHeader=فشل في تسجيل الدخول
delegationFailedMessage=يمكنك إغلاق نافذة المتصفح هذه والعودة إلى تطبيق وحدة التحكم ومحاولة تسجيل الدخول مرة أخرى.

noAccessMessage=لا وصول

invalidPasswordMinLengthMessage=كلمة المرور غير صالحة: الحد الأدنى للطول {0}.
invalidPasswordMaxLengthMessage=كلمة المرور غير صالحة: الحد الأقصى للطول {0}.
invalidPasswordMinDigitsMessage=كلمة المرور غير صالحة: يجب أن تحتوي على {0} أرقام على الأقل.
invalidPasswordMinLowerCaseCharsMessage=كلمة المرور غير صالحة: يجب أن تحتوي على {0} حروف صغيرة على الأقل.
invalidPasswordMinUpperCaseCharsMessage=كلمة المرور غير صالحة: يجب أن تحتوي على {0} حروف كبيرة على الأقل.
invalidPasswordMinSpecialCharsMessage=كلمة المرور غير صالحة: يجب أن تحتوي على {0} رموز على الأقل.
invalidPasswordNotUsernameMessage=كلمة المرور غير صالحة: يجب ألا تكون مطابقة لاسم المستخدم.
invalidPasswordNotEmailMessage=كلمة المرور غير صالحة: يجب ألا تكون مطابقة للبريد الإلكتروني.
invalidPasswordRegexPatternMessage=كلمة المرور غير صالحة: يجب ألا تكون مطابقة للأنماط المحددة.
invalidPasswordHistoryMessage=كلمة المرور غير صالحة: يجب ألا تكون مطابقة لأي من كلمات المرور الـ {0} الأخيرة.
invalidPasswordGenericMessage=كلمة المرور غير صالحة: كلمة المرور الجديدة لا تتطابق مع سياسات كلمة المرور.

failedToProcessResponseMessage=فشل في معالجة الاستجابة
httpsRequiredMessage=البروتوكول HTTPS مطلوب
realmNotEnabledMessage=المنظومة غير مفعلة
invalidRequestMessage=طلب غير صالح
successLogout=لقد قمت بتسجيل الخروج
failedLogout=فشل في تسجيل الخروج
unknownLoginRequesterMessage=طالب تسجيل الدخول غير معروف
loginRequesterNotEnabledMessage=طالب تسجيل الدخول غير مفعل
bearerOnlyMessage=لا يسمح للتطبيقات Bearer-only ببدء تسجيل الدخول باستخدام المتصفح
standardFlowDisabledMessage=لا يسمح للعميل ببدء تسجيل الدخول باستخدام المتصفح مع قيمة response_type المعطاة. تم تعطيل الإجراء القياسي standard flow لهذا العميل.
implicitFlowDisabledMessage=لا يسمح للعميل ببدء تسجيل الدخول باستخدام المتصفح مع قيمة response_type المعطاة. تم تعطيل الإجراء الضمني implicit flow لهذا العميل.
invalidRedirectUriMessage=رابط إعادة التوجيه غير صالح
unsupportedNameIdFormatMessage=صيغة غير مدعومة للقيمة NameID
invalidRequesterMessage=طالب غير صالح
registrationNotAllowedMessage=التسجيل غير مسموح به
resetCredentialNotAllowedMessage=إعادة تعيين بيانات الدخول غير مسموح به

permissionNotApprovedMessage=لم تتم الموافقة على الإذن.
noRelayStateInResponseMessage=لا تواجد لقيمة Relay State في استجابة مزود الحسابات.
insufficientPermissionMessage=أذونات غير كافية لربط الهويات.
couldNotProceedWithAuthenticationRequestMessage=تعذرت متابعة طلب المصادقة لمزود الحسابات.
couldNotObtainTokenMessage=تعذر الحصول على رمز مصادقة من مزود الحسابات.
unexpectedErrorRetrievingTokenMessage=خطأ غير متوقع عند استرداد رمز المصادقة من مزود الحسابات.
unexpectedErrorHandlingResponseMessage=خطأ غير متوقع عند التعامل مع الاستجابة من مزود الحسابات.
identityProviderAuthenticationFailedMessage=فشلت المصادقة. تعذرت المصادقة مع مزود الحسابات.
couldNotSendAuthenticationRequestMessage=تعذر إرسال طلب المصادقة إلى مزود الحسابات.
unexpectedErrorHandlingRequestMessage=خطأ غير متوقع عند معالجة طلب المصادقة لمزود الحسابات.
invalidAccessCodeMessage=رمز الوصول غير صالح.
sessionNotActiveMessage=الجلسة غير نشطة.
invalidCodeMessage=حدث خطأ، يرجى تسجيل الدخول مرة أخرى من خلال التطبيق الخاص بك.
cookieNotFoundMessage=لم يتم العثور على ملف تعريف الارتباط. يرجى التأكد من تمكين ملفات تعريف الارتباط في متصفحك.
insufficientLevelOfAuthentication=لم يتم استيفاء مستوى المصادقة المطلوب.
identityProviderUnexpectedErrorMessage=خطأ غير متوقع عند المصادقة مع مزود الحسابات
identityProviderMissingStateMessage=المعامل state مفقود في رد مزود الحسابات.
identityProviderMissingCodeOrErrorMessage=المعامل code أو error مفقود في رد مزود الحسابات.
identityProviderInvalidResponseMessage=رد غير صالح من مزود الحسابات.
identityProviderInvalidSignatureMessage=توقيع غير صالح في رد مزود الحسابات.
identityProviderNotFoundMessage=تعذر العثور على مزود الحسابات بالمعرف.
identityProviderLinkSuccess=لقد نجحت في التحقق من بريدك الإلكتروني. يرجى الرجوع إلى متصفحك والمتابعة هناك في عملية تسجيل الدخول.
staleCodeMessage=هذه الصفحة لم تعد صالحة، يرجى الرجوع إلى التطبيق الخاص بك وتسجيل الدخول مرة أخرى
realmSupportsNoCredentialsMessage=لا تدعم المنظومة أي نوع من بيانات الدخول.
credentialSetupRequired=لا يمكن تسجيل الدخول، مطلوب إعداد بيانات الدخول.
identityProviderNotUniqueMessage=تدعم المنظومة عدة مزودي حسابات. تعذر تحديد مزود الحسابات الذي يجب استخدامه للمصادقة معه.
emailVerifiedMessage=تم التحقق من عنوان البريد الإلكتروني الخاص بك.
emailVerifiedAlreadyMessage=تم التحقق من عنوان البريد الإلكتروني الخاص بك مسبقًا.
staleEmailVerificationLink=الرابط الذي نقرت عليه هو رابط قديم ولم يعد صالحًا. ربما تكون قد تحققت مسبقًا من بريدك الإلكتروني.
identityProviderAlreadyLinkedMessage=الهوية الموحدة التي أرجعها {0} مرتبطة مسبقًا بمستخدم آخر.
confirmAccountLinking=تأكيد ربط الحساب {0} من مزود الحسابات {1} مع حسابك.
confirmEmailAddressVerification=تأكد من صحة عنوان البريد الإلكتروني {0}.
confirmExecutionOfActions=قم بتنفيذ الإجراءات التالية

backToApplication=&raquo; العودة إلى التطبيق
missingParameterMessage=عوامل مفقودة\: {0}
clientNotFoundMessage=العميل غير موجود.
clientDisabledMessage=العميل معطل.
invalidParameterMessage=عامل غير صالح\: {0}
alreadyLoggedIn=لقد قمت بتسجيل الدخول مسبقًا.
differentUserAuthenticated=لقد تمت مصادقتك مسبقًا كمستخدم مختلف '' {0} '' في هذه الجلسة. الرجاء تسجيل الخروج أولا.
brokerLinkingSessionExpired=تم طلب ربط حساب الوسيط، ولكن الجلسة الحالية لم تعد صالحة.
proceedWithAction=&laquo; انقر هنا للمتابعة
acrNotFulfilled=لم يتم استيفاء متطلبات المصادقة

requiredAction.CONFIGURE_TOTP=إعداد خاصية رمز التحقق
requiredAction.TERMS_AND_CONDITIONS=الأحكام والشروط
requiredAction.UPDATE_PASSWORD=تحديث كلمة المرور
requiredAction.UPDATE_PROFILE=تحديث الملف التعريفي
requiredAction.VERIFY_EMAIL=التحقق من البريد الإلكتروني
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=توليد رموز مصادقة الاسترداد
requiredAction.webauthn-register-passwordless=المصادقة دون كلمة مرور باستخدام Webauthn

invalidTokenRequiredActions=الإجراءات المطلوبة المدرجة في الرابط غير صالحة

doX509Login=سوف يتم تسجيل دخولك كـ\:
clientCertificate=شهادة العميل X509\:
noCertificate=[بدون شهادة]


pageNotFound=الصفحة غير موجودة
internalServerError=حدث خطأ داخلي في الخادم

console-username=اسم المستخدم:
console-password=كلمة المرور:
console-otp=كلمة مرور لمرة واحدة:
console-new-password=كلمة المرور الجديدة:
console-confirm-password=تأكيد كلمة المرور:
console-update-password=مطلوب تحديث كلمة المرور الخاصة بك.
console-verify-email=تحتاج إلى التحقق من عنوان بريدك الإلكتروني. لقد أرسلنا بريدًا إلكترونيًا إلى {0} يحتوي على رمز تحقق. الرجاء إدخال هذا الرمز في الأسفل.
console-email-code=رمز البريد الالكتروني:
console-accept-terms=قبول الشروط؟ [ن/ل]:
console-accept=ن

# Openshift messages
openshift.scope.user_info=معلومات المستخدم
openshift.scope.user_check-access=معلومات وصول المستخدم
openshift.scope.user_full=الوصول الكامل
openshift.scope.list-projects=قائمة المشاريع

# SAML authentication
saml.post-form.title=إعادة توجيه المصادقة
saml.post-form.message=يتم الآن إعادة التوجيه، الرجاء الانتظار.
saml.post-form.js-disabled=تم تعطيل جافاسكربت. نوصي بشدة لتمكينه. انقر على الزر أدناه للمتابعة. 
saml.artifactResolutionServiceInvalidResponse=غير قادر على إيجاد المعرف artifact.

#authenticators
otp-display-name=تطبيق مصادق
otp-help-text=أدخل رمز التحقق الصادرة من التطبيق المصادق.
otp-reset-description=أي إعداد لخاصية رمز التحقق ترغب بإزالته؟
password-display-name=كلمة المرور
password-help-text=سجل الدخول باستخدام كلمة المرور.
auth-username-form-display-name=اسم المستخدم
auth-username-form-help-text=سجل الدخول باستخدام اسم المستخدم
auth-username-password-form-display-name=اسم المستخدم وكلمة المرور
auth-username-password-form-help-text=سجل الدخول باستخدام اسم المستخدم وكلمة المرور.

# Recovery Codes
auth-recovery-authn-code-form-display-name=رمز مصادقة الاسترداد
auth-recovery-authn-code-form-help-text=أدخل رمز مصادقة الاسترداد من قائمة تم إنشاؤها مسبقًا.
auth-recovery-code-info-message=أدخل رمز مصادقة الاسترداد المحدد.
auth-recovery-code-prompt=رمز مصادقة الاسترداد #{0}
auth-recovery-code-header=تسجيل الدخول باستخدام رمز مصادقة الاسترداد
recovery-codes-error-invalid=رمز مصادقة الاسترداد غير صالح
recovery-code-config-header=رموز مصادقة الاسترداد
recovery-code-config-warning-title=لن تظهر رموز الاسترداد هذه مرة أخرى بعد مغادرة الصفحة
recovery-code-config-warning-message=تأكد من طباعتها أو تنزيلها أو نسخها إلى مدير كلمات المرور واحتفظ بها. سيؤدي إلغاء هذا الإعداد إلى إزالة رموز مصادقة الاسترداد هذه من حسابك.
recovery-codes-print=طباعة
recovery-codes-download=تنزيل
recovery-codes-copy=نسخ
recovery-codes-copied=تم النسخ
recovery-codes-confirmation-message=لقد قمت بحفظ هذه الرموز في مكان ما آمن
recovery-codes-action-complete=إكمال الإعداد
recovery-codes-action-cancel=إلغاء الإعداد
recovery-codes-download-file-header=احتفظ برموز مصادقة الاسترداد هذه في مكان آمن.
recovery-codes-download-file-description=رموز مصادقة الاسترداد هي رموز مرور تستخدم مرة واحدة وتسمح لك بتسجيل الدخول إلى حسابك إذا لم يكن لديك وصول إلى المصدق الخاص بك.
recovery-codes-download-file-date=تم إنشاء هذه الرموز في
recovery-codes-label-default=رموز مصادقة الاسترداد

# WebAuthn
webauthn-display-name=مفتاح أمان
webauthn-help-text=استخدم مفتاح الأمان لتسجيل الدخول.
webauthn-passwordless-display-name=مفتاح أمان
webauthn-passwordless-help-text=استخدم مفتاح الأمان لتسجيل الدخول دون كلمة مرور.
webauthn-login-title=تسجيل الدخول باستخدام مفتاح الأمان
webauthn-registration-title=تسجيل مفتاح أمان
webauthn-available-authenticators=مفاتيح الأمان المتاحة
webauthn-unsupported-browser-text=الخاصية WebAuthn غير مدعومة في هذا المتصفح. جرب متصفح آخر أو تواصل مع مسؤول النظام.
webauthn-doAuthenticate=تسجيل الدخول باستخدام مفتاح الأمان
webauthn-createdAt-label=تم إنشاؤه في

# WebAuthn Error
webauthn-error-title=خطأ في مفتاح الأمان
webauthn-error-registration=فشل في تسجيل مفتاح الأمان الخاص بك.<br /> {0}
webauthn-error-api-get=فشلت المصادقة بواسطة مفتاح الأمان.<br /> {0}
webauthn-error-different-user=أول مستخدم تمت مصادقته ليس هو الشخص الذي تمت مصادقته بواسطة مفتاح الأمان.
webauthn-error-auth-verification=نتيجة مصادقة مفتاح الأمان غير صالحة.<br /> {0}
webauthn-error-register-verification=نتيجة تسجيل مفتاح الأمان غير صالحة.<br /> {0}
webauthn-error-user-not-found=مستخدم غير معروف تمت مصادقته بواسطة مفتاح الأمان.

# Identity provider
identity-provider-redirector=اتصل بمزود حسابات آخر
identity-provider-login-label=أو قم بتسجيل الدخول باستخدام
idp-email-verification-display-name=تأكيد البريد الإلكتروني
idp-email-verification-help-text=ربط حسابك عن طريق التحقق من صحة البريد الإلكتروني الخاص بك.
idp-username-password-form-display-name=اسم المستخدم و كلمة المرور
idp-username-password-form-help-text=ربط حسابك عن طريق تسجيل الدخول.

finalDeletionConfirmation=إذا قمت بحذف حسابك، فلا يمكن استعادته. للاحتفاظ بحسابك، انقر فوق إلغاء.
irreversibleAction=هذا الإجراء لا رجعة فيه
deleteAccountConfirm=تأكيد حذف الحساب

deletingImplies=حذف حسابك يعني ضمناً:
errasingData=محو جميع البيانات الخاصة بك
loggingOutImmediately=تسجيل خروجك على الفور
accountUnusable=أي استخدام لاحق للتطبيق لن يكون ممكناً مع هذا الحساب
userDeletedSuccessfully=تم حذف المستخدم بنجاح

access-denied=تم رفض الوصول
access-denied-when-idp-auth=تم رفض الاتفاقية أثناء المصادقة مع {0}

frontchannel-logout.title=تسجيل الخروج
frontchannel-logout.message=أنت تقوم بتسجيل الخروج من التطبيقات التالية
logoutConfirmTitle=تسجيل الخروج
logoutConfirmHeader=هل تود تسجيل الخروج؟
doLogout=تسجيل الخروج

readOnlyUsernameMessage=لا يمكنك تحديث اسم المستخدم الخاص بك لأنه للقراءة فقط.
