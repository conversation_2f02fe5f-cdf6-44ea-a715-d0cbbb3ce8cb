invalidPasswordMinLengthMessage=无效的密码：最短长度 {0}.
invalidPasswordMinLowerCaseCharsMessage=无效的密码：至少包含 {0} 小写字母
invalidPasswordMinDigitsMessage=无效的密码：至少包含 {0} 个数字
invalidPasswordMinUpperCaseCharsMessage=无效的密码：最短长度 {0} 大写字母
invalidPasswordMinSpecialCharsMessage=无效的密码：最短长度 {0} 特殊字符
invalidPasswordNotUsernameMessage=无效的密码： 不可以与用户名相同
invalidPasswordRegexPatternMessage=无效的密码： 无法与正则表达式匹配
invalidPasswordHistoryMessage=无效的密码：不能与最后使用的 {0} 个密码相同

ldapErrorInvalidCustomFilter=定制的 LDAP过滤器不是以 "(" 开头或以 ")"结尾.
ldapErrorConnectionTimeoutNotNumber=Connection Timeout 必须是个数字
ldapErrorMissingClientId=当域角色映射未启用时，客户端 ID 需要指定。
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=无法在使用UID成员类型的同时维护组继承属性。
ldapErrorCantWriteOnlyForReadOnlyLdap=当LDAP提供方不是可写模式时，无法设置只写
ldapErrorCantWriteOnlyAndReadOnly=无法同时设置只读和只写

clientRedirectURIsFragmentError=重定向URL不应包含URI片段
clientRootURLFragmentError=根URL 不应包含 URL 片段

pairwiseMalformedClientRedirectURI=客户端包含一个无效的重定向URL
pairwiseClientRedirectURIsMissingHost=客户端重定向URL需要有一个有效的主机
pairwiseClientRedirectURIsMultipleHosts=Without a configured Sector Identifier URI, client redirect URIs must not contain multiple host components.
pairwiseMalformedSectorIdentifierURI=Malformed Sector Identifier URI.
pairwiseFailedToGetRedirectURIs=无法从服务器获得重定向URL
pairwiseRedirectURIsMismatch=客户端的重定向URI与服务器端获取的URI配置不匹配。
