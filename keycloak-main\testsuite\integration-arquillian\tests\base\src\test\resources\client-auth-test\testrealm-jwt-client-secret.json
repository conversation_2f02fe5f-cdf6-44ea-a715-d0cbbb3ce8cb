{"id": "test", "realm": "test", "enabled": true, "sslRequired": "external", "requiredCredentials": ["password"], "defaultRoles": ["user"], "users": [{"username": "test-user@localhost", "enabled": true, "email": "test-user@localhost", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "password"}]}], "clients": [{"clientId": "test-app", "enabled": true, "baseUrl": "http://localhost:8180/auth/realms/master/app/auth", "redirectUris": ["http://localhost:8180/auth/realms/master/app/auth/*", "https://localhost:8543/auth/realms/master/app/auth/*"], "adminUrl": "http://localhost:8180/auth/realms/master/app/admin", "clientAuthenticatorType": "client-secret-jwt", "secret": "atleast-14chars-password"}], "roles": {"realm": [{"name": "user", "description": "Have User privileges"}, {"name": "admin", "description": "Have Administrator privileges"}]}, "internationalizationEnabled": true, "supportedLocales": ["en", "de"], "defaultLocale": "en", "eventsListeners": ["jboss-logging", "event-queue"]}