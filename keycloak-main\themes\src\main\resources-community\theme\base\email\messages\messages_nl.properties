emailVerificationSubject=Bevestig e-mailadres
emailVerificationBody=Iemand heeft een {2} account aangemaakt met dit e-mailadres. Als u dit was, klikt u op de onderstaande koppeling om uw e-mailadres te bevestigen \n\n{0}\n\nDeze koppeling zal binnen {3} vervallen.\n\nU kunt dit bericht negeren indien u dit account niet heeft aangemaakt.
emailVerificationBodyHtml=<p>Iemand heeft een {2} account aangemaakt met dit e-mailadres. Als u dit was, klikt u op de onderstaande koppeling om uw e-mailadres te bevestigen</p><p><a href="{0}">Koppeling naar e-mailadres bevestiging</a></p><p>Deze koppeling zal binnen {3} vervallen.</p><p>U kunt dit bericht negeren indien u dit account niet heeft aangemaakt.</p>
orgInviteSubject=Uitnodiging om lid te worden van de {0} organisatie
orgInviteBody=U bent uitgenodigd om lid te worden van de "{3}" organisatie. Klik op onderstaande koppeling om lid te worden.\n\n{0}\n\nDeze koppeling verloopt binnen {4}.\n\nAls u geen lid wilt worden van deze organisatie, dan kunt u dit bericht simpelweg negeren.
orgInviteBodyHtml=<p>U bent uitgenodigd om lid te worden van de {3}  organisatie. Klik op onderstaande koppeling om lid te worden. </p><p><a href="{0}">Koppeling om lid te worden van de {3} organisatie</a></p><p>Deze koppeling verloopt binnen {4}.</p><p>Als u geen lid wilt worden van deze organisatie, dan kunt u dit bericht simpelweg negeren.</p>
orgInviteBodyPersonalized=Hallo, "{5}" "{6}".\n\n U bent uitgenodigd om lid te worden van de "{3}" organisatie. Klik op onderstaande koppeling om lid te worden.\n\n{0}\n\nDeze koppeling verloopt binnen {4}.\n\nIAls u geen lid wilt worden van deze organisatie, dan kunt u dit bericht simpelweg negeren.
orgInviteBodyPersonalizedHtml=<p>Hallo, {5} {6}.</p><p>U bent uitgenodig om lid te worden van de {3} organisatie. Klik op onderstaande koppeling om lid te worden. </p><p><a href="{0}">Koppeling om lid te worden van de {3} organisatie</a></p><p>Deze koppeling verloopt binnen {4}.</p><p>Als u geen lid wilt worden van deze organisatie, dan kunt u dit bericht simpelweg negeren.</p>
emailUpdateConfirmationSubject=Bevestig nieuw e-mailadres
emailUpdateConfirmationBody=Klik op onderstaande koppeling om in uw {2} account het e-mailaddress bij te werken naar {1}\n\n{0}\n\nDeze koppeling verloopt binnen {3}.\n\nAls u deze wijzing toch niet wilt doorvoeren, dan kunt u dit bericht simpelweg negeren.
emailUpdateConfirmationBodyHtml=<p>Klik op onderstaande koppeling om in uw {2} account het e-mailaddress bij te werken naar {1}</p><p><a href="{0}">{0}</a></p><p>Deze koppeling verloopt binnen {3}.</p><p>Als u deze wijzing toch niet wilt doorvoeren, dan kunt u dit bericht simpelweg negeren.</p>
emailTestSubject=[KEYCLOAK] - SMTP testbericht
emailTestBody=Dit is een testbericht
emailTestBodyHtml=<p>Dit is een testbericht</p>
identityProviderLinkSubject=Koppel {0}
identityProviderLinkBody=Iemand wil uw "{1}" account koppelen met "{0}" account van gebruiker {2}. Als u dit was, klik dan op de onderstaande link om de accounts te koppelen\n\n{3}\n\nDeze link zal over {5} vervallen.\n\nAls u de accounts niet wilt koppelen, negeer dan dit bericht. Als u accounts koppelt, dan kunt u bij {1} inloggen via {0}.
identityProviderLinkBodyHtml=<p>Iemand wil uw "{1}" account koppelen met "{0}" account van gebruiker {2}. Als u dit was, klik dan op de onderstaande link om de accounts te koppelen</p><p><a href="{3}">Link om accounts te koppelen</a></p><p>Deze link zal over {5} vervallen.</p><p>Als u de accounts niet wilt koppelen, negeer dan dit bericht. Als u accounts koppelt, dan kunt u bij {1} inloggen via {0}.</p>
passwordResetSubject=Wijzig wachtwoord
passwordResetBody=Iemand verzocht de aanmeldgegevens van uw {2} account te wijzigen. Als u dit was, klik dan op de onderstaande koppeling om ze te wijzigen.\n\n{0}\n\nDe link en de code zullen binnen {3} vervallen.\n\nAls u uw aanmeldgegevens niet wilt wijzigen, negeer dan dit bericht en er zal niets gewijzigd worden.
passwordResetBodyHtml=<p>Iemand verzocht de aanmeldgegevens van uw {2} account te wijzigen. Als u dit was, klik dan op de onderstaande koppeling om ze te wijzigen.</p><p><a href="{0}">Wijzig aanmeldgegevens</a></p><p>De link en de code zullen binnen {3} vervallen.</p><p>Als u uw aanmeldgegevens niet wilt wijzigen, negeer dan dit bericht en er zal niets gewijzigd worden.</p>
executeActionsSubject=Wijzig uw account
executeActionsBody=Uw beheerder heeft u verzocht uw {2} account te wijzigen. Klik op de onderstaande koppeling om dit proces te starten. \n\n{0}\n\nDeze link zal over {4} vervallen. \n\nAls u niet over dit verzoek op de hoogte was, negeer dan dit bericht om uw account ongewijzigd te laten.
executeActionsBodyHtml=<p>Uw beheerder heeft u verzocht uw {2} account te wijzigen. Klik op de onderstaande koppeling om dit proces te starten.</p><p><a href="{0}">Link naar account wijziging</a></p><p>Deze link zal over {4} vervallen.</p><p>Als u niet over dit verzoek op de hoogte was, negeer dan dit bericht om uw account ongewijzigd te laten.</p>
eventLoginErrorSubject=Inlogfout
eventLoginErrorBody=Er is een foutieve inlogpoging gedetecteerd op uw account om {0} vanuit {1}. Als u dit niet was, neem dan contact op met de beheerder.
eventLoginErrorBodyHtml=<p>Er is een foutieve inlogpoging gedetecteerd op uw account om {0} vanuit {1}. Als u dit niet was, neem dan contact op met de beheerder.</p>
eventRemoveTotpSubject=OTP verwijderd
eventRemoveTotpBody=OTP is verwijderd van uw account om {0} vanuit {1}. Als u dit niet deed, neem dan contact op met uw beheerder.
eventRemoveTotpBodyHtml=<p>OTP is verwijderd van uw account om {0} vanuit {1}. Als u dit niet deed, neem dan contact op met uw beheerder.</p>
eventUpdatePasswordSubject=Wachtwoord gewijzigd
eventUpdatePasswordBody=Uw wachtwoord is gewijzigd om {0} vanuit {1}. Als u dit niet deed, neem dan contact op met uw beheerder.
eventUpdatePasswordBodyHtml=<p>Uw wachtwoord is gewijzigd om {0} vanuit {1}. Als u dit niet deed, neem dan contact op met uw beheerder.</p>
eventUpdateTotpSubject=OTP gewijzigd
eventUpdateTotpBody=OTP is gewijzigd voor uw account om {0} vanuit {1}. Als u dit niet deed, neem dan contact op met uw beheerder.
eventUpdateTotpBodyHtml=<p>OTP is gewijzigd voor uw account om {0} door {1}. Als u dit niet was, neem dan contact op met uw beheerder.</p>
eventUpdateCredentialSubject=Aanmeldgegevens bijwerken
eventUpdateCredentialBody=Uw aanmeldgegevens bij {0} zijn gewijzigd op {1} vanuit {2}. Als u dit niet was, neem dan contact op met uw beheerder.
eventUpdateCredentialBodyHtml=<p>Uw aanmeldgegevens bij {0} zijn gewijzigd op {1} vanuit {2}. Als u dit niet was, neem dan contact op met uw beheerder.</p>
eventRemoveCredentialSubject=credential verwijderd
eventRemoveCredentialBody=Credential {0} is verwijderd van uw account op {1} vanuit {2}. Als u dit niet was, neem dan contact op met uw beheerder.
eventRemoveCredentialBodyHtml=<p>Credential {0} is verwijderd van uw account op {1} vanuit {2}. Als u dit niet was, neem dan contact op met uw beheerder.</p>
eventUserDisabledByTemporaryLockoutSubject=Gebruiker tijdelijk geblokkeerd
eventUserDisabledByTemporaryLockoutBody=Uw gebruiker is tijdelijk geblokkeerd door meerdere meerder gefaalde inlogpogingen op {0}. Indien nodig, kunt u een beheerder contacteren.
eventUserDisabledByTemporaryLockoutHtml=<p>Uw gebruiker is tijdelijk geblokkeerd door meerdere meerder gefaalde inlogpogingen op {0}. Indien nodig, kunt u een beheerder contacteren.</p>
eventUserDisabledByPermanentLockoutSubject=Gebruiker permanent geblokkeerd
eventUserDisabledByPermanentLockoutBody=Uw gebruiker is permanent geblokkeerd door meerdere meerder gefaalde inlogpogingen op {0}. Gelieve een beheerder te contacteren.
eventUserDisabledByPermanentLockoutHtml=<p>Uw gebruiker is permanent geblokkeerd door meerdere meerder gefaalde inlogpogingen op {0}. Gelieve een beheerder te contacteren.</p>
requiredAction.CONFIGURE_TOTP=OTP instellen
requiredAction.TERMS_AND_CONDITIONS=Algemene voorwaarden accepteren
requiredAction.UPDATE_PASSWORD=Wachtwoord bijwerken
requiredAction.UPDATE_PROFILE=Profiel bijwerken
requiredAction.VERIFY_EMAIL=E-mail bevestigen
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=AUTHN herstelcodes aanmaken

# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be '{0,choice,0#minut|1#minuta|2#minuty|2<minut}'
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#seconden|1#seconde|1<seconden}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#minuten|1#minuut|1<minuten}
linkExpirationFormatter.timePeriodUnit.hours=uur
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#dagen|1#dag|1<dagen}
emailVerificationBodyCode=Verifieer uw e-mailadres door de volgende code in te voeren:\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Verifieer uw e-mailadres door de volgende code in te voeren: </p><p><b>{0}</b></p>
