doSave=შენახვა
doLink=ბმული
sessionsHtmlTitle=სესიები
federatedIdentitiesHtmlTitle=ფედერაციული იდენტიფიკატორები
accountLogHtmlTitle=ანგარიშის ჟურნალი
email=ელფოსტა
authenticatorCode=ერთჯერადი კოდი
username=მომხმარებლის სახელი
fullName=სრული სახელი
lastName=გვარი
passwordNew=ახალი პაროლი
street=ქუჩა
country=ქვეყანა
emailVerified=ელფოსტა გადამოწმებულია
website=ვებგვერდი
zoneinfo=დროის სარტყელი
profileScopeConsentText=მომხმარებლის პროფილი
phoneScopeConsentText=ტელეფონის ნომერი
samlRoleListScopeConsentText=ჩემი როლები
role_realm-admin=რეალმის ადმინი
role_create-realm=რეალმის შექმნა
role_view-applications=აპლიკაციების ნახვა
role_manage-realm=რეალმის მართვა
role_manage-users=მომხმარებლების მართვა
role_manage-consent=თანხმობების მართვა
role_read-token=კოდის წაკითხვა
client_account-console=ანგარიშის კონსოლი
client_admin-cli=ადმინის CLI
lastAccess=ბოლო წვდომა
federatedIdentity=ფედერაციული იდენტიფიკატორი
device-activity=მოწყობილობის აქტივობა
action=ქმედება
availableRoles=ხელმისაწვდომი როლები
mobile=მობილური
revoke=მინიჭების გაუქმება
totpScanBarcode=დავასკანერო ბარკოდი?
totpDeviceName=მოწყობილობის სახელი
doDeny=აკრძალვა

# Authorization
myResources=ჩემი რესურსები
myResourcesSub=ჩემი რესურსები
anyAction=ნებისმიერი ქმედება
permissionRequestion=წვდომის მოთხოვნა
thirdpartyApps=მესამე პირის აპლიკაციები
userDefined=მომხმარებლის აღწერილი
authenticatorMobileTitle=მობილური ავთენტიკატორი
sharedwith=გაზიარებულია
accessPermissions=წვდომის უფლებები
perPage=გვერდზე
doCancel=გაუქმება
doAdd=დამატება
doRemove=წაშლა
authenticatorTitle=ავთენტიკატორი
applicationsHtmlTitle=აპლიკაციები
passwordConfirm=დადასტურება
addressScopeConsentText=მისამართი
password=პაროლი
address=მისამართი
gender=სქესი
birthday=დაბადების თარიღი
organizationScopeConsentText=ორგანიზაცია
role_admin=ადმინი
client_account=ანგარიში
clients=კლიენტები
details=დეტალები
client_broker=Broker
event=მოვლენა
ip=IP
client=კლიენტი
date=თარიღი
started=გაშვებულია
expires=ვადა
applications=აპლიკაციები
account=ანგარიში
authenticator=ავთენტიკატორი
sessions=სესიები
inResource=სად
log=ჟურნალი
application=აპლიკაცია
totp.totp=დროზე-დაფუძნებული
totp.hotp=მთვლელზე-დაფუძნებული
totpInterval=ინტერვალი
totpType=ტიპი
totpAlgorithm=ალგორითმი
totpDigits=ციფრები
totpCounter=მთვლელი
totpAppFreeOTPName=FreeOTP
doRevoke=გაუქმება
doApprove=დადასტურება
description=აღწერა
name=სახელი
scopes=შუალედები
resource=რესურსი
icon=ხატულა
user=მომხმარებელი
requestor=მომთხოვნი
doFinish=დასრულება
owner=მფლობელი
permission=წვდომა
shares=ზიარ(ებ)-ი

# Applications
applicationName=სახელი
doDownload=გადმოწერა
doPrint=დაბეჭდვა
appResults=შედეგები
share=გაზიარება
group=ჯგუფი
authenticatorDefaultStatus=ნაგულისხმევი
realmName=რეალმი
approve=დადასტურება
people=ხალხი
doCopy=კოპირება
doSignOut=გასვლა


#Resources
resources=რესურსები
doLogIn=შესვლა
personalInfoSidebarTitle=პერსონალური ინფორმაცია
signingInSidebarTitle=შესვლა
accountSecuritySidebarTitle=ანგარიშის უსაფრთხოება
deviceActivitySidebarTitle=მოწყობილობის აქტივობა
linkedAccountsSidebarTitle=მიბმული ანგარიშები
editAccountHtmlTitle=ანგარიშის ჩასწორება
personalInfoHtmlTitle=პერსონალური ინფორმაცია
changePasswordHtmlTitle=პაროლის შეცვლა
deviceActivityHtmlTitle=მოწყობილობის აქტივობა
linkedAccountsHtmlTitle=მიბმული ანგარიშები
accountSecurityTitle=ანგარიშის უსაფრთხოება
updatePasswordTitle=პაროლის განახლება
firstName=სახელი
givenName=დარქმეული სახელი
familyName=მეტსახელი
currentPassword=მიმდინარე პაროლი
phoneNumber=ტელეფონის ნომერი
emailScopeConsentText=ელფოსტის მისამართი
offlineAccessScopeConsentText=ინტერნეტგარეშე წვდომა
rolesScopeConsentText=მომხმარებლის როლები
role_view-realm=რეალმის ნახვა
role_view-users=მომხმარებლების ნახვა
role_view-groups=ჯგუფების ნახვა
role_view-clients=კლიენტების ნახვა
role_view-events=მოვლენების ნახვა
role_view-consent=თანხმობების ნახვა
role_manage-applications=აპლიკაციების მართვა
role_manage-events=მოვლენების მართვა
role_manage-clients=კლიენტების მართვა
role_view-profile=პროფილის ნახვა
role_manage-account=ანგარიშის მართვა
role_offline-access=ინტერნეტგარეშე წვდომა
client_realm-management=რეალმის მართვა
role_uma_authorization=წვდომების მიღება


requiredFields=აუცილებელი ველები
grantedPermissions=მინიჭებული წვდომები
fullAccess=სრული წვდომა
offlineToken=ინტერნეტგარეშე კოდი
additionalGrants=დამატებითი მინიჭებები
configureAuthenticators=მორგებული ავთენტიკატორები
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator
invalidUserMessage=არასწორი მომხმარებელი
consentDenied=თანხმობა უარყოფილია.
doRemoveSharing=გაზიარების წაშლა
doRemoveRequest=მოთხოვნის წაშლა
applicationType=აპლიკაციის ტიპი
activeFilters=აქტიური ფილტრები
internalApps=შიდა აპლიკაციები
allApps=ყველა აპლიკაცია

# Linked account
authorizedProvider=ავტორიზებული პროვაიდერი
identityProvider=იდენტიფიკაციის პროვაიდერი
socialLogin=სოციალური ქსელით შესვლა
removeAccess=წვდომის წაშლა
permissionRequests=წვდომის მოთხოვნები
approveAll=ყველას დადასტურება
currentPage=მიმდინარე გვერდი
authenticatorActionSetup=მორგება
authenticatorSMSTitle=SMS კოდი
doLogOutAllSessions=ყველა სესიის გაგდება
noAccessMessage=წვდომა დაშვებული არაა
accountManagementTitle=Keycloak-ის ანგარიშების მართვა
personalInfoIntroMessage=თქვენი ძირითადი ინფორმაციის მართვა
accountManagementWelcomeMessage=მოგესალმებათ Keybloack-ის ანგარიშების მმართველი
accountSecurityIntroMessage=მართეთ თქვენი პაროლი და ანგარიშთან წვდომა
personalSubTitle=თქვენი პირადი ინფორმაცია
passwordLastUpdateMessage=თქვენი პაროლის განახლების დროა
updatePasswordMessageTitle=დარწმუნდით, რომ აირჩიეთ რთული პაროლი
personalSubMessage=მართეთ თქვენი ძირითადი ინფორმაცია.
locality=ქალაქი ან დაბა
region=შტატი, პროვინცია ან რეგიონი
postal_code=Zip ან საფოსტო კოდი
phoneNumberVerified=ტელეფონის ნომერი გადამოწმებულია
gssDelegationCredential=GSS დელეგაციის ავტორიზაციის დეტალები
role_view-identity-providers=იდენტიფიკატორის მომწოდებლების ნახვა
role_manage-identity-providers=იდენტიფიკატორის მომწოდებლების მართვა
role_manage-account-links=ანგარიშის ბმულების მართვა
allFieldsRequired=ყველა ველი აუცილებელია
backTo={0}-ზე დაბრუნება
backToApplication=&laquo; აპლიკაციაზე დაბრუნება
grantedPersonalInfo=მინიჭებული პირადი ინფორმაცია
totpStep1=დააყენეთ ერთ-ერთი ამ აპლიკაციათაგანი თქვენს მობილურზე:
totpStep3=შეიყვანეთ ერთჯერადი კოდი, რომელიც აპლიკაციამ მოგაწოდათ და დააწკაპუნეთ ღილაკზე ''შენახვა'', რომ მორგება დაასრულოთ.
totpUnableToScan=ვერ დაასკანერეთ?
totpManualStep2=გახსენით აპლიკაცია და შეიყვანეთ კოდი:
irreversibleAction=ეს ქმედება შეუქცევადია
deletingImplies=თქვენი ანგარიშის წაშლა გამოიწვევს:
errasingData=თქვენი ყველა მონაცემის წაშლას
loggingOutImmediately=დაუყოვნებლივ გაგდებას
missingUsernameMessage=მიუთითეთ მომხმარებლის სახელი.
missingFirstNameMessage=მიუთითეთ სახელი.
missingLastNameMessage=მიუთითეთ გვარი.
missingTotpMessage=მიუთითეთ ავთენტიკატორის კოდი.
missingTotpDeviceNameMessage=შეიყვანეთ მოწყობილობის სახელი.
updateReadOnlyAttributesRejectedMessage=მხოლოდ-წაკითხვადი ატრიბუტის განახლება უარყოფილია
usernameExistsMessage=მომხმარებლის სახელი უკვე არსებობს.
emailExistsMessage=ელფოსტა უკვე არსებობს.
invalidPasswordConfirmMessage=პაროლის დადასტურება არ ემთხვევა.
accountUpdatedMessage=თქვენი ანგარიში განახლდა.
identityProviderNotFoundMessage=მითითებული იდენტიფიკატორის მომწოდებელი აღმოჩენილი არაა.
federatedIdentityLinkNotActiveMessage=ეს იდენტიფიკატორი უკვე აქტიური აღარაა.
identityProviderRedirectErrorMessage=იდენტიფიკატორის მომწოდებელზე გადამისამართება ჩავარდა.
identityProviderRemovedMessage=იდენტიფიკატორის მომწოდებლის წაშლა წარმატებულია.
identityProviderAlreadyLinkedMessage=ფედერაციული იდენტიფიკატორი, რომელიც {0}-მა დააბრუნა, უკვე მიბმულია სხვა მომხმარებელზე.
staleCodeAccountMessage=ამ გვერდის ვადა ამოიწურა. კიდევ სცადეთ.
access-denied-when-idp-auth=წვდომა აკრძალულია {0}-ით ავთენტიკაციისას
accountDisabledMessage=ანგარიში გათიშულია. დაუკავშირდით თქვენს ადმინისტრატორს.
accountTemporarilyDisabledMessage=ანგარიში დროებით გათიშულია. დაუკავშირდით თქვენს ადმინისტრატორს ან მოგვიანებით სცადეთ.
invalidPasswordMinLengthMessage=არასწორი პაროლი: მინიმალური სიგრძეა {0}.
invalidPasswordMinDigitsMessage=არასწორი პაროლი: უნდა შეიცავდეს, სულ ცოტა {0} ციფრს.
invalidPasswordMinSpecialCharsMessage=არასწორი პაროლი: უნდა შეიცავდეს, სულ ცოტა {0} სპეციალურ სიმბოლოს.
invalidPasswordRegexPatternMessage=არასწორი პაროლი: არ ემთხვევა რეგგამოსის ნიმუშ(ებ)-ს.
invalidPasswordHistoryMessage=არასწორი პაროლი: არ უნდა უდრიდეს ბოლო {0} პაროლს.
invalidPasswordBlacklistedMessage=არასწორი პაროლი: პაროლი შავ სიაშია.
invalidPasswordGenericMessage=არასწორი პაროლი: ახალი პაროლი არ აკმაყოფილებს პაროლის პოლიტიკებს.
peopleAccessResource=ამ რესურსთან წვდომის მქონე ხალხი
resourceManagedPolicies=ამ რესურსთან უფლებების მინიჭების წვდომები
shareWithOthers=სხვებთან გაზიარება
needMyApproval=სჭირდება ჩემი დადასტურება
requestsWaitingApproval=თქვენი მოთხოვნა დადასტურებას მოელის
resourcesSharedWithMe=ჩემთან გაზიარებული რესურსები
notBeingShared=ეს რესურსი გაზიარებული არაა.
notHaveAnyResource=რესურსები არ გაქვთ
noResourcesSharedWithYou=თქვენთვის რესურსები არ ზიარდება
clickHereForDetails=დააწკაპუნეთ აქ მეტი დეტალის სანახავად.
applicationInUse=მხოლოდ გაშვებული აპი
clearAllFilter=ყველა ფილტრის გასუფთავება
filterByName=გაფილტვრა სახელით...
clientNotFoundMessage=კლიენტი ვერ ვიპოვე.
authorizedProviderMessage=თქვენს ანგარიშზე მიბმული ავტორიზებული მომწოდებლები
identityProviderMessage=თქვენი ანგარიშის მისაბმელად იდენტიფიკატორის მომწოდებლებთან, რომლებიც მოირგეთ
authenticatorFinishSetUpTitle=თქვენი 2FA

#Authenticator
authenticatorStatusMessage=2FA ამჟამად
authenticatorFinishSetUpMessage=რამდენჯერაც შეხვალთ Keycloak-ის ანგარიშში, იმდენჯერ გთხოვთ, 2FA კოდი შეიყვანოთ.
authenticatorSubMessage=თქვენი ანგარიშის უსაფრთხოების გასაზრდელად ჩართეთ, სულ ცოტა, ერთი ხელმისაწვდომი 2FA-ის მეთოდი.
authenticatorMobileFinishSetUpMessage=ავთენტიკატორი მიბმულია თქვენს ტელეფონზე.
authenticatorSMSFinishSetUpMessage=ტექსტური შეტყობინებები გაგზავნილია ნომერზე
authenticatorChangePhone=ტელეფონის ნომრის შეცვლა
smscodeIntroMessage=შეიყვანეთ ტელეფონის ნომერი და გადამოწმების კოდი გამოგეგზავნებათ.
mobileSetupStep1=დააყენეთ ავთენტიკატორი აპი თქვენს ტელეფონზე. მხარდაჭერილია აქ ჩამოთვლილი აპლიკაციები.
mobileSetupStep2=გახსენით აპლიკაცია და დაასკანირეთ ბარკოდი:

#Authenticator - SMS Code setup
authenticatorSMSCodeSetupTitle=SMS კოდის მორგება
chooseYourCountry=აირჩიეთ თქვენი ქვეყანა
mobileSetupStep3=შეიყვანეთ ერთჯერადი კოდი, რომელიც აპლიკაციამ მოგაწოდათ და დააწკაპუნეთ ღილაკზე ''შენახვა'', რომ მორგება დაასრულოთ.
scanBarCode=გნებავთ ბარკოდის დასკანირება?
enterBarCode=შეიყვანეთ ერთჯერადი კოდი
sendVerficationCode=გადამოწმების კოდის გაგზავნა
enterYourVerficationCode=შეიყვანეთ თქვენი გადამოწმების კოდი

#Authenticator - backup Code setup
authenticatorBackupCodesSetupTitle=აღდგენის ავთენტიკაციის კოდების მორგება
sharedwithMe=ჩემთან გაზიარებული
generateNewBackupCodes=ახალი აღდგენის ავთენტიკაციის კოდების გენერაცია
backtoAuthenticatorPage=ავთენტიკაციის გვერდზე დაბრუნება
selectPermission=აირჩიეთ წვდომა
myPermissions=ჩემი წვდომები
anyPermission=ნებისმიერი წვდომა
sharetheResource=რესურსის გაზიარება
waitingforApproval=დადასტურების მოლოდინი
addPeople=დაამატეთ ხალხი, რომლებსაც თქვენს რესურსს გაუზიარებთ
addTeam=დაამატეთ გუნდი, რომელსაც თქვენს რესურსს გაუზიარებთ

# Openshift messages
openshift.scope.user_info=მომხმარებლის ინფორმაცია
openshift.scope.user_full=სრული წვდომა
openshift.scope.list-projects=პროექტების სია
error-invalid-value=არასწორი მნიშვნელობა.
openshift.scope.user_check-access=მომხმარებლის წვდომის ინფორმაცია
error-empty=შეიყვანეთ მნიშვნელობა.
error-invalid-number=არასწორი რიცხვი.
error-pattern-no-match=არასწორი მნიშვნელობა.
error-invalid-uri=არასწორი URL.
error-invalid-date=არასწორი თარიღი.
error-invalid-email=არასწორი ელფოსტის მისამართი.
error-invalid-uri-scheme=არასწორი ბმულის სქემა.
error-invalid-uri-fragment=არასწორი URL-ის ფრაგმენტი.
error-invalid-length=ატრიბუტის {0} სიგრძე {1}-{2} შუალედში უნდა იყოს.
error-invalid-length-too-long=ატრიბუტის {0} მაქსიმალური სიგრძეა {2}.
error-number-out-of-range=ატრიბუტი {0} უნდა იყოს რიცხვი შუალედიდან {1}-{2}.
error-number-out-of-range-too-big=ატრიბუტის {0} მაქსიმალური მნიშვნელობაა {2}.
error-user-attribute-required=მიუთითეთ ატრიბუტი {0}.
error-username-invalid-character=მომხმარებლის სახელი არაწორ სიმბოლოს შეიცავს.
error-person-name-invalid-character=სახელი არასწორ სიმბოლოს შეიცავს.
client_security-admin-console=უსაფრთხოების ადმინის კონსოლი
invalidEmailMessage=არასწორი ელფოსტის მისამართი.
missingEmailMessage=მიუთითეთ ელფოსტა.
missingPasswordMessage=მიუთითეთ პაროლი.
notMatchPasswordMessage=პაროლები არ ემთხვევა.
invalidTotpMessage=არასწორი ავთენტიკატორის კოდი.
invalidPasswordExistingMessage=არასწორი არსებული პაროლი.
successTotpMessage=მობილური ავთენტიკატორი მორგებულია.
successTotpRemovedMessage=მობილური ავთენტიკატორი წაიშალა.
successGrantRevokedMessage=უფლებები წარმატებით გაუქმდა.

#Authenticator - Mobile Authenticator setup
authenticatorMobileSetupTitle=მობილური ავთენტიკატორის მორგება
error-invalid-blank=შეიყვანეთ მნიშვნელობა.
resourceIntroMessage=გაუზიარეთ თქვენი რესურსები გუნდის წევრებს
readOnlyUserMessage=თქვენ არ შეგიძლიათ განაახლოთ თქვენი ანგარიში, რადგან ის მხოლოდ-წაკითხვადია.
accountPasswordUpdatedMessage=თქვენი პაროლი განახლდა.
readOnlyUsernameMessage=თქვენ ვერ განაახლებთ თქვენს მომხმარებლის სახელს, რადგან ის მხოლოდ-წაკითხვადია.
readOnlyPasswordMessage=თქვენ ვერ განაახლებთ თქვენს პაროლს, რადგან თქვენი ანგარიში მხოლოდ-წაკითხვადია.
missingIdentityProviderMessage=იდენტიფიკატორის მომწოდებელი მითითებული არაა.
invalidFederatedIdentityActionMessage=ქმედება არასწორია ან მითითებული არაა.
totpStep2=გახსენით აპლიკაცია და დაასკანირეთ ბარკოდი:
totpManualStep3=გამოიყენეთ შემდეგი კონფიგურაციის მნიშვნელობები, თუ აპლიკაცია საშუალებას გაძლევთ, დააყენოთ ისინი:
accountUnusable=ამ ანგარიშით ამის შემდეგ აპლიკაციას ვერ გამოიყენებთ
federatedIdentityRemovingLastProviderMessage=ბოლო ფედერაციულ იდენტიფიკატორს ვერ წაშლით, რადგან პაროლი არ გაქვთ.
invalidPasswordMaxLengthMessage=არასწორი პაროლი: მაქსიმალური სიგრძეა {0}.
invalidPasswordMinLowerCaseCharsMessage=არასწორი პაროლი: უნდა შეიცავდეს, სულ ცოტა {0} პატარა ასოს.
invalidPasswordMinUpperCaseCharsMessage=არასწორი პაროლი: უნდა შეიცავდეს, სულ ცოტა {0} დიდ ასოს.
invalidPasswordNotUsernameMessage=არასწორი პაროლი: არ უნდა იყოს მომხმარებლის სახელის ტოლი.
invalidPasswordNotContainsUsernameMessage=არასწორი პაროლი: არ შეიძლება, მომხმარებლის სახელს შეიცავდეს.
invalidPasswordNotEmailMessage=არასწორი პაროლი: არ უნდა უდრიდეს ელფოსტას.
resourceNoPermissionsGrantingAccess=ამ რესურსთან წვდომების მინიჭების უფლების გარეშე
havePermissionRequestsWaitingForApproval=გაქვთ {0} წვდომის მოთხოვნა, რომელიც დადასტურებას ელოდება.
peopleSharingThisResource=ხალხი, ვინც ამ რესურსს აზიარებს
resourceIsNotBeingShared=რესურსი გაზიარებული არაა
removeAccessMessage=დაგჭირდებათ, რომ წვდომა თავიდან მიანიჭოთ, თუ გნებავთ გამოიყენოთ ამ აპის ანგარიში.
authenticatorSubTitle=2FA-ის მორგება
authenticatorSMSMessage=Keycloak გააგზავნის გადამოწმების კოდს თქვენს ტელეფონზე 2FA-სთვის.
authenticatorMobileMessage=გამოიყენეთ მობილური ავთენტიკატორი, რომ მიიღოთ გადამოწმების კოდები 2FA-სთვის.
enterYourPhoneNumber=შეიყვანეთ თქვენი ტელეფონის ნომერი
error-invalid-length-too-short=ატრიბუტის {0} მინიმალური სიგრძეა {1}.
error-number-out-of-range-too-small=ატრიბუტის {0} მინიმალური სიგრძე {1} შეიძლება იყოს.
error-user-attribute-read-only=ველი {0} მხოლოდ-წაკთხვადია.
accountManagementBaseThemeCannotBeUsedDirectly=საბაზისო ანგარიშის თემა, მხოლოდ, ანგარიშის კონსოლის თარგმანებს შეიცავს. იმისათვის, რომ ანგარიშის კონსოლი გამოიტანოთ, საჭიროა, ან დააყენოთ თქვენი თემის მშობელი სხვა ანგარიშის თემაზე, ან შეიყვანოთ საკუთარი index.ftl ფაილი. მეტი ინფორმაციისთვის იხილეთ დოკუმენტაცია.
applicationsIntroMessage=ადევნეთ თვალყური და მართეთ თქვენი აპის წვდომები თქვენს ანგარიშთან წვდომისთვის
updatePasswordMessage=რთული პაროლი ციფრების, ასოების და სიმბოლოების ნაკრებს წარმოადგენს. ის ძნელია მისახვედრად, არ ჰგავს ნამდვილ სიტყვას და, მხოლოდ, ამ ანგარიშისთვის გამოიყენება.
totpStep3DeviceName=შეიყვანეთ მოწყობილობის სახელი, რომ თქვენი OTP მოწყობილობების მართვაში დაგეხმაროთ.
federatedIdentityBoundOrganization=ორგანიზაციასთან ასოცირებულ იდენტიფიკატორის მომწოდებლამდე ბმულს ვერ წაშლით.
