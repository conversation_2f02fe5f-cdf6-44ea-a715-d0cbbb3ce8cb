emailVerificationSubject=Overenie e-mailu
emailVerificationBody=Niekto vytvoril účet {2} s touto e-mailovou adresou. Ak ste to vy, kliknite na nižšie uvedený odkaz a overte svoju e-mailovú adresu \n\n{0}\n\nTento odkaz uplynie do {1} minút.\n\nAk ste tento účet nevytvorili, ignorujte túto správu.
emailVerificationBodyHtml=<p>Niekto vytvoril účet {2} s touto e-mailovou adresou. Ak ste to vy, kliknite na nižšie uvedený odkaz na overenie svojej e-mailovej adresy.</p><p><a href="{0}"> Odkaz na overenie e-mailovej adresy </a></p><p>Platnosť odkazu vyprší za {1} minút.</p><p> Ak ste tento účet nevytvorili, ignorujte túto správu.</p>
orgInviteSubject=Pozvánka do organizácie {0}
orgInviteBody=Niekto Vás pozval do organizácie "{3}". Na pridanie sa kliknite na odkaz nižšie.\n\n{0}\n\nTento odkaz vyprší za {4}.\n\nAk sa do organizácie nechcete pridať, ignorujte túto správu.
orgInviteBodyHtml=<p>Niekto Vás pozval do organizácie {3}. Na pridanie sa kliknite na odkaz nižšie. </p><p><a href="{0}">Odkaz na pridanie sa do organizácie</a></p><p>Tento odkaz vyprší za {4}.</p><p>Ak sa do organizácie nechcete pridať, ignorujte túto správu.</p>
orgInviteBodyPersonalized=Dobrý deň, "{5}" "{6}".\n\n Niekto Vás pozval do organizácie "{3}". Na pridanie sa kliknite na odkaz nižšie.\n\n{0}\n\nTento odkaz vyprší za {4}.\n\nAk sa do organizácie nechcete pridať, ignorujte túto správu.
orgInviteBodyPersonalizedHtml=<p>Dobrý deň, {5} {6}.</p><p>Niekto Vás pozval do organizácie {3}. Na pridanie sa kliknite na odkaz nižšie. </p><p><a href="{0}">Odkaz na pridanie sa do organizácie</a></p><p>Tento odkaz vyprší za {4}.</p><p>Ak sa do organizácie nechcete pridať, ignorujte túto správu.</p>
emailUpdateConfirmationSubject=Overenie nového e-mailu
emailUpdateConfirmationBody=Ak chcete aktualizovať svoj {2} účet s e-mailovou adresou {1}, kliknite na odkaz nižšie\n\n{0}\n\nTento odkaz vyprší do {3}.\n\nAk nechcete pokračovať v tejto úprave, jednoducho ignorujte túto správu.
emailUpdateConfirmationBodyHtml=<p>Ak chcete aktualizovať svoj {2} účet s e-mailovou adresou {1}, kliknite na odkaz nižšie</p><p><a href="{0}">{0}</a></p><p>Tento odkaz vyprší do {3}.</p><p>Ak nechcete pokračovať v tejto úprave, jednoducho ignorujte túto správu.</p>
emailTestSubject=[KEYCLOAK] - Testovacia správa SMTP
emailTestBody=Toto je skúšobná správa
emailTestBodyHtml=<p>Toto je skúšobná správa</p>
identityProviderLinkSubject=Odkaz {0}
identityProviderLinkBody=Niekto chce prepojiť Váš účet "{1}" s účtom "{0}"používateľa {2}. Ak ste to vy, kliknutím na odkaz nižšie prepojte účty. \n\n{3}\n\nTento odkaz uplynie do {4} minút.\n\nAk nechcete prepojiť účet, jednoducho ignorujte túto správu , Ak prepájate účty, budete sa môcť prihlásiť do {1} až {0}.
identityProviderLinkBodyHtml=<p>Niekto chce prepojiť Váš účet <b>{1}</b> s účtom <b>{0}</b> používateľa {2}. Ak ste to vy, kliknutím na odkaz nižšie prepojte účty</p><p><a href="{3}">Odkaz na potvrdenie prepojenia účtu </a></p><p> Platnosť tohto odkazu vyprší v rámci {4} minút.</p><p>Ak nechcete prepojiť účet, ignorujte túto správu. Ak prepojujete účty, budete sa môcť prihlásiť do {1} až {0}.</p>
passwordResetSubject=Obnovenie hesla
passwordResetBody=Niekto požiadal, aby ste zmenili svoje poverenia účtu {2}. Ak ste to vy, kliknite na odkaz uvedený nižšie, aby ste ich vynulovali.\n\n{0}\n\nTento odkaz a kód uplynie do {1} minút.\n\nAk nechcete obnoviť svoje poverenia , ignorujte túto správu a nič sa nezmení.
passwordResetBodyHtml=<p>Niekto požiadal, aby ste zmenili svoje poverenia účtu {2}. Ak ste to vy, kliknutím na odkaz nižšie ich resetujte.</p><p><a href="{0}">Odkaz na obnovenie poverení </a></p><p>Platnosť tohto odkazu vyprší v priebehu {1} minút.</p><p>Ak nechcete obnoviť svoje poverenia, ignorujte túto správu a nič sa nezmení.</p>
executeActionsSubject=Aktualizujte svoj účet
executeActionsBody=Váš administrátor práve požiadal o aktualizáciu Vášho účtu {2} vykonaním nasledujúcich akcií: {3}. Kliknutím na odkaz uvedený nižšie spustíte tento proces.\n\n{0}\n\nTento odkaz vyprší za {4}.\n\nAk si nie ste vedomý, že váš administrátor o toto požiadal, ignorujte túto správu a nič bude zmenené.
executeActionsBodyHtml=<p>Váš správca práve požiadal o aktualizáciu Vášho účtu {2} vykonaním nasledujúcich akcií: {3}. Kliknutím na odkaz uvedený nižšie spustíte tento proces.</p><p><a href="{0}"> Odkaz na aktualizáciu účtu </a></p><p> Platnosť tohto odkazu vyprší za {4}.</p><p> Ak si nie ste vedomí, že Váš administrátor o toto požiadal, ignorujte túto správu a nič sa nezmení.</p>
eventLoginErrorSubject=Chyba prihlásenia
eventLoginErrorBody=Bol zistený neúspešný pokus o prihlásenie do Vášho účtu v {0} z {1}. Ak ste to neboli vy, obráťte sa na administrátora.
eventLoginErrorBodyHtml=<p>Bol zistený neúspešný pokus o prihlásenie Vášho účtu na {0} z {1}. Ak ste to neboli vy, kontaktujte administrátora.</p>
eventRemoveTotpSubject=Odstrániť TOTP
eventRemoveTotpBody=OTP bol odstránený z Vášho účtu dňa {0} z {1}. Ak ste to neboli vy, obráťte sa na administrátora.
eventRemoveTotpBodyHtml=<p>OTP bol odstránený z Vášho účtu dňa {0} z {1}. Ak ste to neboli vy, kontaktujte administrátora.</p>
eventUpdatePasswordSubject=Aktualizovať heslo
eventUpdatePasswordBody=Vaše heslo bolo zmenené na {0} z {1}. Ak ste to neboli vy, obráťte sa na administrátora.
eventUpdatePasswordBodyHtml=<p>Vaše heslo bolo zmenené na {0} z {1}. Ak ste to neboli vy, kontaktujte administrátora.</p>
eventUpdateTotpSubject=Aktualizácia TOTP
eventUpdateTotpBody=TOTP bol aktualizovaný pre Váš účet na {0} z {1}. Ak ste to neboli vy, obráťte sa na administrátora.
eventUpdateTotpBodyHtml=<p>TOTP bol aktualizovaný pre Váš účet dňa {0} z {1}. Ak ste to neboli vy, kontaktujte administrátora.</p>
eventUpdateCredentialSubject=Aktualizácia prihlasovacieho údaja
eventUpdateCredentialBody=Váš prihlasovací údaj {0} sa zmenil na {1} z {2}. Pokiaľ ste to neboli vy, kontaktujte administrátora.
eventUpdateCredentialBodyHtml=<p>Váš prihlasovací údaj {0} sa zmenil na {1} z {2}. Pokiaľ ste to neboli vy, kontaktujte administrátora.</p>
eventRemoveCredentialSubject=Odstránenie prihlasovacieho údaja
eventRemoveCredentialBody=Prihlasovací údaj {0} bol odstránený z Vášho účtu na {1} z {2}. Pokiaľ ste to neboli vy, kontaktujte administrátora.
eventRemoveCredentialBodyHtml=<p>Prihlasovací údaj {0} bol odstránený z Vášho účtu na {1} z {2}. Pokiaľ ste to neboli vy, kontaktujte administrátora.</p>
eventUserDisabledByTemporaryLockoutSubject=Používateľ dočasne zablokovaný
eventUserDisabledByTemporaryLockoutBody=Váš používateľ bol dočasne zablokovaný kvôli niekoľkým zlým pokusom na {0}. Kontaktujte prosím administrátora, pokiaľ je to potrebné.
eventUserDisabledByTemporaryLockoutHtml=<p>Váš používateľ bol dočasne zablokovaný kvôli niekoľkým zlým pokusom na {0}. Kontaktujte prosím administrátora, pokiaľ je to potrebné.</p>
eventUserDisabledByPermanentLockoutSubject=Užívateľ trvalo zablokovaný
eventUserDisabledByPermanentLockoutBody=Váš používateľ bol trvalo zablokovaný kvôli niekoľkým zlým pokusom na {0}. Kontaktujte prosím administrátora, pokiaľ je to potrebné.
eventUserDisabledByPermanentLockoutHtml=<p>Váš používateľ bol trvalo zablokovaný kvôli niekoľkým zlým pokusom na {0}. Kontaktujte prosím administrátora, pokiaľ je to potrebné.</p>

requiredAction.CONFIGURE_TOTP=Konfigurácia OTP
requiredAction.TERMS_AND_CONDITIONS=Zmluvné podmienky
requiredAction.UPDATE_PASSWORD=Aktualizovať heslo
requiredAction.UPDATE_PROFILE=Aktualizovať profil
requiredAction.VERIFY_EMAIL=Overiť e-mail
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Generovanie kódov obnovy

# units for link expiration timeout formatting
# for languages which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like described in the Java choice format which is documented here. For Czech, it would be '{0,choice,0#minut|1#minuta|2#minuty|2<minut}'
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/MessageFormat.html
# https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/text/ChoiceFormat.html
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#sekúnd|1#sekunda|2#sekundy|4<sekúnd}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#minút|1#minúta|2#minúty|4<minút}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#hodín|1#hodina|2#hodiny|4<hodín}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#dní|1#deň|2#dni|4<dní}

emailVerificationBodyCode=Prosím, overte svoju e-mailovú adresu zadaním nasledujúceho kódu.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Prosím overte svoju e-mailovú adresu zadaním nasledujúceho kódu.</p><p><b>{0}</b></p>
