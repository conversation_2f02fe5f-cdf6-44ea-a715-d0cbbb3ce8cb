invalidPasswordMinLengthMessage=Жарамсыз сырсөз: минималдуу узундугу {0}.
invalidPasswordMaxLengthMessage=Жарамсыз сырсөз: максималдуу узундугу {0}.
invalidPasswordMinLowerCaseCharsMessage=Жарамсыз сырсөз: жок дегенде {0} кичине тамга болушу керек.
invalidPasswordMinDigitsMessage=Жарамсыз сырсөз: жок дегенде {0} сан болушу керек.
invalidPasswordMinUpperCaseCharsMessage=Жарамсыз сырсөз: жок дегенде {0} чоң тамга болушу керек.
invalidPasswordMinSpecialCharsMessage=Жарамсыз сырсөз: жок дегенде {0} атайын символ болушу керек.
invalidPasswordNotUsernameMessage=Жарамсыз сырсөз: колдонуучу атына барабар болбоошу керек.
invalidPasswordNotContainsUsernameMessage=Жарамсыз сырсөз: колдонуучу аты камтылышы мүмкүн эмес.
invalidPasswordNotEmailMessage=Жарамсыз сырсөз: электрондук почтага барабар болбоошу керек.
invalidPasswordRegexPatternMessage=Жарамсыз сырсөз: көрсөтүлгөн regex үлгүсүнө дал келбейт.
invalidPasswordHistoryMessage=Жарамсыз сырсөз: акыркы {0} сырсөздөрдүн бирине барабар болбоошу керек.
invalidPasswordBlacklistedMessage=Жарамсыз сырсөз: сырсөз кара тизмеде турат.
invalidPasswordGenericMessage=Жарамсыз сырсөз: жаңы сырсөз сырсөз саясатына ылайык келбейт.

ldapErrorEditModeMandatory=Түзөтүү режими сөзсүз талап кылынат
ldapErrorInvalidCustomFilter=Ыңгайлаштырылган LDAP фильтри «(» менен башталып, «)» менен бүтүшү керек.
ldapErrorConnectionTimeoutNotNumber=Байланыш убактысынын чеги сан болушу керек
ldapErrorReadTimeoutNotNumber=Окуу убактысынын чеги сан болушу керек
ldapErrorMissingClientId=Realm Roles Mapping колдонулбаганда конфигурацияда кардар ID көрсөтүлүшү керек.
ldapErrorCantPreserveGroupInheritanceWithUIDMembershipType=Топ мурастоону сактап калуу жана UID мүчөлүк түрүн бир эле учурда колдонуу мүмкүн эмес.
ldapErrorCantWriteOnlyForReadOnlyLdap=LDAP камсыздоочу жазууга мүмкүн эмес болгондо, жазууга гана уруксат берүү мүмкүн эмес
ldapErrorCantWriteOnlyAndReadOnly=Жазууга гана жана окууга гана бир эле учурда коюу мүмкүн эмес
ldapErrorCantEnableStartTlsAndConnectionPooling=StartTLS жана байланыш пулун бир эле учурда иштетүү мүмкүн эмес.
ldapErrorCantEnableUnsyncedAndImportOff=LDAP камсыздоочу UNSYNCED режиминде болгондо колдонуучуларды импорттоону өчүрүү мүмкүн эмес
ldapErrorMissingGroupsPathGroup=Көрсөтүлгөн жолдо топ жок – алгач бул топту көрсөтүлгөн жолдо түзүңүз
ldapErrorValidatePasswordPolicyAvailableForWritableOnly=Сырсөз саясатын текшерүү мүмкүнчүлүгү WRITABLE түзөтүү режиминде гана колдонулат

clientRedirectURIsFragmentError=Багыттоо URI''си URI фрагментин камтыбашы керек
clientRootURLFragmentError=Тамыр URL''и URL фрагментин камтыбашы керек
clientRootURLIllegalSchemeError=Тамыр URL мыйзамсыз схема менен колдонулган
clientBaseURLIllegalSchemeError=Базалык URL мыйзамсыз схема менен колдонулган
backchannelLogoutUrlIllegalSchemeError=Backchannel чыгуу URL мыйзамсыз схема менен колдонулган
clientRedirectURIsIllegalSchemeError=Багыттоо URI мыйзамсыз схема менен колдонулган
clientBaseURLInvalid=Базалык URL жарактуу URL эмес
clientRootURLInvalid=Тамыр URL жарактуу URL эмес
clientRedirectURIsInvalid=Багыттоо URI жарактуу URI эмес
backchannelLogoutUrlIsInvalid=Backchannel чыгуу URL жарактуу URL эмес


pairwiseMalformedClientRedirectURI=Клиентте жараксыз багыттоо URI бар.
pairwiseClientRedirectURIsMissingHost=Клиент багыттоо URI''леринде жарактуу хост компоненти болушу керек.
pairwiseClientRedirectURIsMultipleHosts=Сектор идентификаторунун URI''си конфигурацияланбаса, клиент багыттоо URI''леринде бир нече хост компоненти болбошу керек.
pairwiseMalformedSectorIdentifierURI=Сектор идентификаторунун URI''си туура эмес түзүлгөн.
pairwiseFailedToGetRedirectURIs=Сектор идентификаторунун URI''синен багыттоо URI''лерин алуу ишке ашкан жок.
pairwiseRedirectURIsMismatch=Клиенттин багыттоо URI''лери Сектор идентификаторунун URI''синен алынган URI''лер менен дал келбейт.

duplicatedJwksSettings="JWKS колдонуу" жана "JWKS URL колдонуу" которгучтары бир эле учурда күйгүзүлбөшү керек.

error-invalid-value=Туура эмес маани.
error-invalid-blank=Маанини көрсөтүңүз.
error-empty=Маанини көрсөтүңүз.
error-invalid-length={0} атрибутунун узундугу {1} менен {2} ортосунда болушу керек.
error-invalid-length-too-short={0} атрибутунун минималдуу узундугу {1} болушу керек.
error-invalid-length-too-long={0} атрибутунун максималдуу узундугу {2} болушу керек.
error-invalid-email=Туура эмес электрондук почта дареги.
error-invalid-number=Туура эмес сан.
error-number-out-of-range={0} атрибуту {1} менен {2} ортосундагы сан болушу керек.
error-number-out-of-range-too-small={0} атрибутунун минималдуу мааниси {1} болушу керек.
error-number-out-of-range-too-big={0} атрибутунун максималдуу мааниси {2} болушу керек.
error-pattern-no-match=Туура эмес маани.
error-invalid-uri=Туура эмес URL.
error-invalid-uri-scheme=Туура эмес URL схемасы.
error-invalid-uri-fragment=Туура эмес URL фрагменти.
error-user-attribute-required={0} атрибутун көрсөтүңүз.
error-invalid-date={0} атрибуту туура эмес дата.
error-user-attribute-read-only={0} атрибуту жазууга болбойт.
error-username-invalid-character={0} жараксыз символ камтыйт.
error-person-name-invalid-character={0} жараксыз символ камтыйт.
error-invalid-multivalued-size={0} атрибуту жок дегенде {1} жана эң көп дегенде {2} {2,choice,0#маани|1#маани|1<маани} камтышы керек.

client_account=Аккаунт
client_account-console=Аккаунт консолу
client_security-admin-console=Коопсуздук админ консолу
client_admin-cli=Админ CLI
client_realm-management=Realm башкаруу
client_broker=Брокер