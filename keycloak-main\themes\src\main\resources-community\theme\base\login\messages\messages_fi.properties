doLogIn=Kirjaudu
doRegister=Rekisteröidy
doRegisterSecurityKey=Rekisteröidy
doCancel=Peruuta
doSubmit=Lähetä
doBack=Takaisin
doYes=Kyllä
doNo=Ei
doContinue=Jatka
doIgnore=Sivuuta
doAccept=Hyväksy
doDecline=En hyväksy
doForgotPassword=Unohditko salasanan?
doClickHere=Klikkaa tästä
doImpersonate=Edusta
doTryAgain=Yritä uudelleen
doTryAnotherWay=Yritä toista tapaa
doConfirmDelete=Vahvista poisto
errorDeletingAccount=Tilin poistossa tapahtui virhe
deletingAccountForbidden=Sinulla ei ole riittäviä oikeuksia poistaakseesi omaa tiliä, ota yhteyttä järjestelmänvalvojaan.
kerberosNotConfigured=Kerberosta ei ole konfiguroitu
kerberosNotConfiguredTitle=Kerberosta ei ole konfiguroitu
bypassKerberosDetail=Joko et ole kirjautunut Kerberoksen kautta tai selaintasi ei ole asetettu käyttämään Kerberosta kirjautumiseen.  Klikkaa jatkaaksesi kirjautumista jollain toisella tavalla
kerberosNotSetUp=Kerberosta ei ole asennettu.  Et voi kirjautua sisään.
registerTitle=Rekisteröidy
loginAccountTitle=Kirjaudu sisään
loginTitle=Kirjaudu {0}
loginTitleHtml={0}
impersonateTitle={0} Edusta käyttäjää
impersonateTitleHtml=<strong>{0}</strong> Edusta käyttäjää
realmChoice=Realm
unknownUser=Tuntematon käyttäjä
loginTotpTitle=Mobiili-todentajan asetukset
loginProfileTitle=Päivitä käyttäjätilin tiedot
loginIdpReviewProfileTitle=Päivitä käyttäjätilin tiedot
loginTimeout=Kirjautumisyritys kesti liian kauan. Kirjautuminen aloitetaan alusta.
oauthGrantTitle=Myönnä pääsy {0}
oauthGrantTitleHtml={0}
errorTitle=Pahoittelut...
errorTitleHtml=Olemme <strong>pahoillamme</strong> ...
emailVerifyTitle=Sähköpostiosoitteen varmistus
emailForgotTitle=Unohditko salasanasi?
updatePasswordTitle=Päivitä salasana
codeSuccessTitle=Success-koodi
codeErrorTitle=Virhekoodi: {0}
displayUnsupported=Pyydetty näyttötyyppi ei ole tuettu
browserRequired=Selain vaatii sisäänkirjautumista
browserContinue=Selain vaatii sisäänkirjautumisen viimeistelyä
browserContinuePrompt=Avaa selain ja jatka kirjautumista? [k/e]:
browserContinueAnswer=k


termsTitle=Käyttöehdot
termsText=<p>Käyttöehdot ja niiden määrittely</p>
termsPlainText=Käyttöehdot ja niiden määrittely.

recaptchaFailed=Virheellinen Recaptcha
recaptchaNotConfigured=Recaptcha vaaditaan, mutta sitä ei ole konfiguroitu
consentDenied=Suostumus kielletty.

noAccount=Uusi käyttäjä?
username=Käyttäjätunnus
usernameOrEmail=Käyttäjätunnus tai sähköpostiosoite
firstName=Etunimi
givenName=Sukunimi
fullName=Koko nimi
lastName=Sukunimi
familyName=Sukunimi
email=Sähköposti
password=Salasana
passwordConfirm=Salasana uudelleen
passwordNew=Uusi salasana
passwordNewConfirm=Uusi salasana uudelleen
rememberMe=Muista minut
authenticatorCode=Kertakäyttökoodi
address=Osoite
street=Katu
locality=Kaupunki
region=Osavaltio, Provinssi, tai Alue
postal_code=Postinumero
country=Maa
emailVerified=Sähköposti vahvistettu
website=Verkkosivu
phoneNumber=Puhelinnumero
phoneNumberVerified=Puhelinnumero varmennettu
gender=Sukupuoli
birthday=Syntymäpäivä
zoneinfo=Aikavyöhyke
gssDelegationCredential=GSS Delegation Credential
logoutOtherSessions=Kirjaudu ulos muilta laitteilta

profileScopeConsentText=Käyttäjän profiili
emailScopeConsentText=Sähköpostiosoite
addressScopeConsentText=Osoite
phoneScopeConsentText=Puhelinnumero
offlineAccessScopeConsentText=Offline-käyttö
samlRoleListScopeConsentText=Omat roolit
rolesScopeConsentText=Käyttäjäroolit

restartLoginTooltip=Aloita kirjautuminen alusta

loginTotpIntro=Sinun täytyy asentaa OTP (One Time Password) luontityökalu päästäksesi tälle tilille
loginTotpStep1=Asenna yksi seuraavista sovelluksista puhelimeesi:
loginTotpStep2=Avaa sovellus ja skannaa viivakoodi
loginTotpStep3=Liitä sovelluksesta saatu kertaluontoinen koodi ja paina Lähetä viimeistelläksesi asennuksen
loginTotpStep3DeviceName=Anna laitteelle nimi, jotta voit hallinnoida OTP-laitteitasi.
loginTotpManualStep2=Avaa sovellus ja liitä avain
loginTotpManualStep3=Käytä seuraavia konfiguraatioarvoja jos sovellus antaa asettaa ne:
loginTotpUnableToScan=Ongelmia skannauksessa?
loginTotpScanBarcode=Skannaa viivakoodi?
loginCredential=Kirjautumistieto
loginTotpOneTime=Kertaluontoinen koodi
loginTotpType=Tyyppi
loginTotpAlgorithm=Algoritmi
loginTotpDigits=Numerot
loginTotpInterval=Intervalli
loginTotpCounter=Laskuri
loginTotpDeviceName=Laitteen nimi

loginTotp.totp=Aikapohjainen
loginTotp.hotp=Laskuripohjainen

loginChooseAuthenticator=Valitse kirjautumistapa

oauthGrantRequest=Myönnätkö nämä käyttöoikeudet?
inResource=in

oauth2DeviceVerificationTitle=Laitekirjautuminen
verifyOAuth2DeviceUserCode=Liitä laitteeltasi saamasi kertaluontoinen koodi ja paina Lähetä
oauth2DeviceInvalidUserCodeMessage=Virheellinen koodi. yritä uudelleen.
oauth2DeviceExpiredUserCodeMessage=Koodi on vanhentunut. Ole hyvä ja mene takaisin laitteellesi ja yritä yhdistämistä uudellleen.
oauth2DeviceVerificationCompleteHeader=Laitekirjautuminen onnistui
oauth2DeviceVerificationCompleteMessage=Voit sulkea tämän ikkunan ja mennä takaisin laitteellesi.
oauth2DeviceVerificationFailedHeader=Laitekirjautuminen epäonnistui
oauth2DeviceVerificationFailedMessage=Voit sulkea tämän ikkunan, mennä takaisin laitteellesi ja yrittää kirjautumista uudelleen.
oauth2DeviceConsentDeniedMessage=Laitteen kirjautumisen suostumus evätty.
oauth2DeviceAuthorizationGrantDisabledMessage=Client is not allowed to initiate OAuth 2.0 Device Authorization Grant. The flow is disabled for the client.

emailVerifyInstruction1=Sähköpostin vahvistamisohjeet sisältävä viesti on lähetetty sähköpostiisi.
emailVerifyInstruction2=Etkö ole saanut vahvistuskoodia sähköpostiisi?
emailVerifyInstruction3=saadaksesi uuden sähköpostiviestin.

emailLinkIdpTitle=Linkitä {0}
emailLinkIdp1=Sinulle on lähetetty ohjeet tunnuksen linkittämiseen palvelun {0} kanssa.
emailLinkIdp2=Etkö saanut vahvistuskoodia sähköpostiisi?
emailLinkIdp3=saadaksesi uuden sähköpostiviestin.
emailLinkIdp4=Jos olet jo vahvistanut sähköpostisi toisella selaimella,
emailLinkIdp5=jatkaaksesi.

backToLogin=&laquo; Takaisin kirjautumiseen

emailInstruction=Syötä käyttäjätunnuksesi tai sähköpostiosoitteesi niin lähetämme sinulle ohjeet salasanan palauttamista varten.
emailInstructionUsername=Syötä käyttäjänimesi niin lähetämme sinulle ohjeet uuden salasanan luomiseksi.

copyCodeInstruction=Ole hyvä ja kopioi tämä koodi ja liitä se sovellukseesi:

pageExpiredTitle=Sivu on vanhentunut
pageExpiredMsg1=Aloita kirjautuminen alusta
pageExpiredMsg2=Jatka kirjautumista

personalInfo=Henkilökohtaiset tiedot:
role_admin=Järjestelmänvalvoja
role_realm-admin=Realm Järjestelmänvalvoja
role_create-realm=Luo realm
role_create-client=Luo asiakas
role_view-realm=Näytä realm
role_view-users=Näytä käyttäjät
role_view-applications=Näytä sovellukset
role_view-clients=Näytä asiakkaat
role_view-events=Näytä tapahtumat
role_view-identity-providers=Näytä henkilöllisyyden tarjoajat
role_manage-realm=Hallinnoi realmia
role_manage-users=Hallinnoi käyttäjiä
role_manage-applications=Hallinnoi sovelluksia
role_manage-identity-providers=Hallinnoi henkilöllisyyden tarjoajia
role_manage-clients=Hallinnoi asiakkaita
role_manage-events=Hallinnoi tapahtumia
role_view-profile=Näytä profiili
role_manage-account=Hallitse tiliä
role_manage-account-links=Hallitse tilin linkkejä
role_read-token=Lue token
role_offline-access=Offline-pääsy
client_account=Tili
client_account-console=Tilin konsoli
client_security-admin-console=Turvallisuus-hallintapaneeli
client_admin-cli=Admin CLI
client_realm-management=Realm Hallinta
client_broker=Broker

requiredFields=Vaaditut kentät

invalidUserMessage=Virheellinen käyttäjätunnus tai salasana.
invalidUsernameMessage=Väärä salasana.
invalidUsernameOrEmailMessage=Väärä salasana tai sähköposti.
invalidPasswordMessage=Väärä salasana.
invalidEmailMessage=Virheellinen sähköpostiosoite.
accountDisabledMessage=Tili on poistettu käytöstä, ota yhteyttä järjestelmänvalvojaan.
accountTemporarilyDisabledMessage=Virheellinen käyttäjätunnus tai salasana.
accountPermanentlyDisabledMessage=Virheellinen käyttäjätunnus tai salasana.
accountTemporarilyDisabledMessageTotp=Väärä todentaja-koodi.
accountPermanentlyDisabledMessageTotp=Väärä todentaja-koodi.
expiredCodeMessage=Kirjautuminen kesti liian kauan. Ole hyvä ja kirjaudu uudestaan.
expiredActionMessage=Toiminto kesti liian kauan. Ole hyvä ja jatka kirjautumiseen.
expiredActionTokenNoSessionMessage=Toiminto vanhentui.
expiredActionTokenSessionExistsMessage=Toiminto vanhentui. Aloita alusta.

missingFirstNameMessage=Anna etunimi.
missingLastNameMessage=Anna sukunimi.
missingEmailMessage=Anna sähköpostiosoite.
missingUsernameMessage=Anna käyttäjätunnus.
missingPasswordMessage=Anna salasana.
missingTotpMessage=Ole hyvä ja määrittele todentajan koodi.
missingTotpDeviceNameMessage=Ole hyvä ja määrittele laitteen nimi.
notMatchPasswordMessage=Salasanat eivät täsmää.

error-invalid-value=Väärä arvo.
error-invalid-blank=Ole hyvä ja määritä arvo.
error-empty=Ole hyvä ja määritä arvo. 
error-invalid-length=Ominaisuudella {0} täytyy olla pituus väliltä {1} ja {2}.
error-invalid-length-too-short=Ominaisuudella {0} täytyy olla minimipituus {1}.
error-invalid-length-too-long=Ominaisuudella {0} täytyy olla maksimipituus {2}.
error-invalid-email=Väärä sähköpostiosoite.
error-invalid-number=Väärä numero.
error-number-out-of-range=Ominaisuuden {0} täytyy olla numero väliltä {1} ja {2}.
error-number-out-of-range-too-small=Ominaisuudella {0} täytyy olla minimiarvona {1}.
error-number-out-of-range-too-big=Ominaisuudella {0} täytyy olla maksimiarvona {2}.
error-pattern-no-match=Väärä arvo.
error-invalid-uri=Väärä URL.
error-invalid-uri-scheme=Väärä URL:n malli.
error-invalid-uri-fragment=Väärä URL:n osa.
error-user-attribute-required=Ole hyvä ja määritä ominaisuus {0}.
error-invalid-date=Väärä päivämäärä.
error-user-attribute-read-only=Kenttä {0} on "vain luku"-tilassa.
error-username-invalid-character=Käyttäjänimi sisältää vääriä merkkejä.
error-person-name-invalid-character=Nimi sisältää vääriä merkkejä.

invalidPasswordExistingMessage=Vanha salasana on virheellinen.
invalidPasswordBlacklistedMessage=Väärä salasana: salasana on lisätty mustalle listalle.
invalidPasswordConfirmMessage=Salasanan vahvistus ei täsmää.
invalidTotpMessage=Väärä todentaja-koodi.

usernameExistsMessage=Käyttäjänimi on varattu.
emailExistsMessage=Sähköpostiosoite on jo käytössä.

federatedIdentityExistsMessage=Käyttäjä {0} {1} on jo olemassa. Kirjaudu tilihallintaan linkittääksesi tilin.
federatedIdentityUnavailableMessage=Käyttäjä {0} joka on tunnistettu henkilöllisyyden tarjoajalla {1} ei ole olemassa. Ota yhteyttä järjestelmänvalvojaan.

confirmLinkIdpTitle=Käyttäjätunnus on jo olemassa
federatedIdentityConfirmLinkMessage=Käyttäjätunnus, jolla {0} on {1} on jo olemassa. Kuinka haluat jatkaa?
federatedIdentityConfirmReauthenticateMessage=Tunnistaudu linkittääksesi {0}-tilin.
nestedFirstBrokerFlowMessage={0} käyttäjä {1} ei ole linkitetty tunnettuun käyttäjään.
confirmLinkIdpReviewProfile=Tarkastele profiilia
confirmLinkIdpContinue=Lisää olemassa olevaan tiliin

configureTotpMessage=Sinun täytyy asentaa mobiili-todentaja aktivoidaksesi tilin.
updateProfileMessage=Sinun tulee päivittää profiilisi aktivoidaksesi tilisi.
updatePasswordMessage=Sinun tulee vaihtaa salasanasi aktivoidaksesi tilisi.
resetPasswordMessage=Sinun tulee päivittää salasanasi.
verifyEmailMessage=Sinun tulee vahvistaa sähköpostiosoitteesi aktivoidaksesi tilisi.
linkIdpMessage=Sinun tulee vahvistaa sähköpostiosoitteesi linkittääksesi tilin palvelun {0} kanssa.

emailSentMessage=Sinun pitäisi saada sähköpostiisi lisäohjeita hetken kuluttua.
emailSendErrorMessage=Sähköpostin lähettäminen epäonnistui. Yritä hetken kuluttua uudelleen.

accountUpdatedMessage=Käyttäjätiedot päivitetty.
accountPasswordUpdatedMessage=Salasana vaihdettu.

delegationCompleteHeader=Kirjautuminen onnistui
delegationCompleteMessage=Voit sulkea tämän ikkunan ja siirtyä takaisin konsolisovellukseen.
delegationFailedHeader=Kirjautuminen epäonnistui
delegationFailedMessage=Voit sulkea tämän selaimen, siirtyä takaisin konsolisovellukseen ja yrittää kirjautumista uudelleen.

noAccessMessage=Ei pääsyä

invalidPasswordMinLengthMessage=Virheellinen salasana: vähimmäispituus {0}.
invalidPasswordMaxLengthMessage=Virheellinen salasana: maksimipituus {0}.
invalidPasswordMinDigitsMessage=Virheellinen salasana: salasanassa tulee olla vähintään {0} numeroa.
invalidPasswordMinLowerCaseCharsMessage=Virheellinen salasana: salasanassa tulee olla vähintään {0} pientä kirjainta.
invalidPasswordMinUpperCaseCharsMessage=Virheellinen salasana: salasanassa tulee olla vähintään {0} isoa kirjainta.
invalidPasswordMinSpecialCharsMessage=Virheellinen salasana: salasanassa tulee olla vähintään {0} erikoismerkkiä.
invalidPasswordNotUsernameMessage=Virheellinen salasana: salasana ei saa olla sama kuin käyttäjätunnus.
invalidPasswordNotEmailMessage=Virheellinen salasana: ei voi olla sama kuin sähköposti.
invalidPasswordRegexPatternMessage=Virheellinen salasana: ei vastaa "regex pattern(s)".
invalidPasswordHistoryMessage=Virheellinen salasana: salasana ei saa olla sama kuin {0} edellistä salasanaasi.
invalidPasswordGenericMessage=Virheellinen salasana: uusi salasana ei täytä salasanavaatimuksia.

failedToProcessResponseMessage=Vastauksen käsittely epäonnistui
httpsRequiredMessage=HTTPS vaaditaan
realmNotEnabledMessage=Realm ei otettu käyttöön
invalidRequestMessage=Virheellinen pyyntö
failedLogout=Uloskirjautuminen epäonnistui
unknownLoginRequesterMessage=Tuntematon kirjautumispyynnön tekijä
loginRequesterNotEnabledMessage=kirjautumispyynnön tekijää ei ole otettu käyttöön
bearerOnlyMessage="Bearer-only" sovellusten ei ole sallittua aloittaa selainkirjautumista
standardFlowDisabledMessage=Asiakas ei saa aloittaa selainkirjautumista annetulla vastaustyypillä ("response_type"). "Standard flow" on poistettu käytöstä asiakkaalla.
implicitFlowDisabledMessage=Asiakas ei saa aloittaa selainkirjautumista annetulla vastaustyypillä ("response_type"). "Implicit flow" on poistettu käytöstä asiakkaalla.
invalidRedirectUriMessage=Virheellinen uudelleenohjaus-uri
unsupportedNameIdFormatMessage=Ei-tuettu "NameIDFormat"
invalidRequesterMessage=Virheellinen pyynnön tekijä
registrationNotAllowedMessage=Rekisteröinti ei ole sallittu
resetCredentialNotAllowedMessage=Kirjautumistietojen nollaus ei ole sallittu

permissionNotApprovedMessage=Lupaa ei myönnetty
noRelayStateInResponseMessage="relay state" puuttuu henkilöllisyyden tarjoajan vastauksesta.
insufficientPermissionMessage=Riittämättömät oikeudet henkilöllisyyksien linkittämiseksi.
couldNotProceedWithAuthenticationRequestMessage=Tunnistuspyyntöä henkilöllisyyden tarjoajalle ei voitu jatkaa.
couldNotObtainTokenMessage=Ei voitu saada tokenia henkilöllisyyden tarjoajalta.
unexpectedErrorRetrievingTokenMessage=Odottamaton virhe hankkiessa tokenia henkilöllisyyden tarjoajalta.
unexpectedErrorHandlingResponseMessage=Odottamaton virhe käsiteltäessä vastausta henkilöllisyyden tarjoajalta.
identityProviderAuthenticationFailedMessage=Tunnistautuminen epäonnistui. Ei voitu tunnistautua henkilöllisyyden tarjoajan kautta.
identityProviderDifferentUserMessage=Tunnistautunut {0}, vaikka pitäisi olla tunnistautunut {1}
couldNotSendAuthenticationRequestMessage=Ei voitu lähettää tunnistautumispyyntö henkilöllisyyden tarjoajalle.
unexpectedErrorHandlingRequestMessage=Odottamaton virhe käsiteltäessä tunnistautumispyyntö henkilöllisyyden tarjoajalle.
invalidAccessCodeMessage=Virheellinen pääsykoodi
sessionNotActiveMessage=Istunto ei ole aktiivinen.
invalidCodeMessage=Tapahtui virhe, ole hyvä ja kirjaudu uudelleen sovelluksesi kautta.
identityProviderUnexpectedErrorMessage=Tunnistautumisen yhteydessä tapahtui virhe tunnistetietojen tarjoajan kanssa.
identityProviderNotFoundMessage=Tunnisteella ei löytynyt henkilöllisyyden tarjoajaa.
identityProviderLinkSuccess=Sähköpostin vahvistus onnistui Ole hyvä ja palaa alkuperäiseen selainikkunaan jatkaaksesi kirjautumista.
staleCodeMessage=Tämä sivu ei ole enää voimassa, ole hyvä ja palaa sovellukseesi ja kirjaudu uudelleen
realmSupportsNoCredentialsMessage=Realm ei tue mitään kirjautumistiedon tyyppiä.
credentialSetupRequired=Ei voida kirjautua, kirjautumistietojen asetukset vaaditaan.
identityProviderNotUniqueMessage=Realm tukee useita henkilöllisyyden tarjoajia. Ei voitu määrittää, mitä henkilöllisyyden tarjoajaa pitäisi käyttää tunnistautumiseen.
emailVerifiedMessage=Sähköpostisi on vahvistettu.
staleEmailVerificationLink=Klikkaamasi linkki on vanhentunut eikä enää toimi. Oletko jo vahvistanut sähköpostisi?
identityProviderAlreadyLinkedMessage=Yhdistetty henkilöllisyys, minkä {0} palautti, on jo linkitetty toiseen käyttäjään.
confirmAccountLinking=Vahvista tilin {0} linkitys, henkilöllisyyden tarjoajalta {1}, tiliisi.
confirmEmailAddressVerification=Vahvista sähköpostiosoitteen {0} voimassaolo.
confirmExecutionOfActions=Suorita seuraavat toiminnot

backToApplication=&laquo; Takaisin sovellukseen
missingParameterMessage=Puuttuva parametri\: {0}
clientNotFoundMessage=Asiakasta ei löytynyt.
clientDisabledMessage=Asiakas ei ole käytössä.
invalidParameterMessage=Epäkelpo parametri\: {0}
alreadyLoggedIn=Olet jo kirjautunut sisään
differentUserAuthenticated=Olet kirjautunut sisään tilillä ''{0}''. Ole hyvä ja kirjaudu ulos ensin.
brokerLinkingSessionExpired=Pyysit tilin yhdistämistä mutta sessio on vanhentunut.
proceedWithAction=&raquo; Klikkaa tästä jatkaaksesi

requiredAction.CONFIGURE_TOTP=Konfiguroi OTP
requiredAction.TERMS_AND_CONDITIONS=Käyttöehdot
requiredAction.UPDATE_PASSWORD=Päivitä salasana
requiredAction.UPDATE_PROFILE=Päivitä profiili
requiredAction.VERIFY_EMAIL=Vahvista sähköposti

doX509Login=Kirjaudut sisään nimellä\:
clientCertificate=X509 asiakas-varmenne\:
noCertificate=[No Certificate]


pageNotFound=Sivua ei löytynyt
internalServerError=Tapahtui sisäinen virhe palvelimella.

console-username=Käyttäjänimi:
console-password=Salasana:
console-otp=One Time Password:
console-new-password=Uusi salasana:
console-confirm-password=Vahvista salana:
console-update-password=Salasanan päivitys vaaditaan.
console-verify-email=Sinun täytyy vahvistaa sähköpostiosoitteesi.  Sähköposti, mikä sisältää vahvistuskoodin, on lähetetty osoitteeseen {0}.  Ole hyvä ja kirjoita tämä koodi alapuolella olevaan kenttään.
console-email-code=Sähköposti-koodi:
console-accept-terms=Hyväksy käyttöehdot? [k/e]:
console-accept=k

# Openshift messages
openshift.scope.user_info=Käyttäjän tiedot
openshift.scope.user_check-access=Käyttäjän käyttöoikeustiedot
openshift.scope.user_full=Täysi käyttöoikeus
openshift.scope.list-projects=Listaa projektit
# SAML authentication
saml.post-form.title=Tunnistautumisen uudelleenohjaus
saml.post-form.message=Uudelleenohjataan, odota hetki..
saml.post-form.js-disabled=JavaScript on pois käytöstä. Suosittelemme vahvasti sen käyttöönottoa. Klikkaa alla olevaa nappia jatkaaksesi. 
saml.artifactResolutionServiceInvalidResponse=Unable to resolve artifact.

#authenticators
otp-display-name=Todentajasovellus
otp-help-text=Syötä todentajasovelluksen tarjoama vahvistuskoodi.
password-display-name=Salasana
password-help-text=Kirjaudu sisään syöttämällä salasanasi.
auth-username-form-display-name=Käyttäjänimi
auth-username-form-help-text=Aloita kirjautuminen syöttämällä käyttäjänimesi
auth-username-password-form-display-name=käyttäjänimi ja salasana
auth-username-password-form-help-text=Kirjaudu sisään syöttämällä käyttäjänimi ja salasana.

# WebAuthn
webauthn-display-name=Turva-avain
webauthn-help-text=Käytä Turva-avaintasi kirjatuaksesi sisään.
webauthn-passwordless-display-name=Turva-avain
webauthn-passwordless-help-text=Käytä Turva-avaintasi kirjatuaksesi sisään ilman salasanaa.
webauthn-login-title=Turva-avain kirjautuminen
webauthn-registration-title=Turva-avain rekisteröinti
webauthn-available-authenticators=Saatavilla olevat todentajat
webauthn-unsupported-browser-text="WebAuthn" ei ole tuettu tällä selaimella. Kokeile jotain toista tai ota yhteyttä järjestelmänvalvojaan.
webauthn-doAuthenticate=Kirjaudu sisään Turva-avaimella

# WebAuthn Error
webauthn-error-title=Turva-avain virhe
webauthn-error-registration=Turva-avaimen rekisteröinti epäonnistui.<br /> {0}
webauthn-error-api-get=Tunnistautuminen Turva-avaimella epäonnistui.<br /> {0}
webauthn-error-different-user=Ensiksi tunnistautunut käyttäjä ei ole sama kuin Turva-avaimella tunnistaunut.
webauthn-error-auth-verification=Turva-avain -tunnistautumisen tulos on virheellinen.<br /> {0}
webauthn-error-register-verification=Turva-avaimen rekisteröinnin tulos on virheellinen.<br /> {0}
webauthn-error-user-not-found=Tuntematon käyttäjä tunnistautui Turva-avaimella.

# Identity provider
identity-provider-redirector=Yhdistä käyttämällä toista henkilöllisyyden tarjoajaa
identity-provider-login-label=Tai kirjaudu jollain näistä:

finalDeletionConfirmation=Jos poistat tilisi, sitä ei voida enää palauttaa. Säilyttääksesi tilisi, paina Peruuta.
irreversibleAction=Tätä toimintoa ei voi peruuttaa
deleteAccountConfirm=Tilin poistamisen vahvistus

deletingImplies=Tilin poisto tarkoittaa sitä, että:
errasingData=Kaikki tietosi poistetaan
loggingOutImmediately=Sinut kirjataan ulos välittömästi
accountUnusable=Tämän sovelluksen käyttö ei myöhemmin enää ole mahdollista tällä käyttäjätilillä
userDeletedSuccessfully=Käyttäjä poistettu onnistuneesti

access-denied=Pääsy evätty

readOnlyUsernameMessage=Et voi päivittää käyttäjänimeäsi, koska se on "vain-luku"-tilassa.
