doLogIn=Anmelden
doRegister=Registrieren
doRegisterSecurityKey=Registrieren
doCancel=Abbrechen
doSubmit=Absenden
doBack=Zurück
doYes=Ja
doNo=Nein
doContinue=Weiter
doIgnore=Ignorieren
doAccept=Annehmen
doDecline=Ablehnen
doForgotPassword=Passwort vergessen?
doClickHere=Hier klicken
doImpersonate=Identitätswechsel
doTryAgain=Erneut versuchen
doTryAnotherWay=Einen anderen Weg versuchen
doConfirmDelete=Löschung bestätigen
errorDeletingAccount=Beim Löschen des Kontos ist ein Fehler aufgetreten
deletingAccountForbidden=Sie haben nicht genügend Berechtigungen, um Ihr eigenes Konto zu löschen, wenden Sie sich an einen Administrator.
kerberosNotConfigured=Kerberos ist nicht konfiguriert
kerberosNotConfiguredTitle=Kerberos nicht konfiguriert
bypassKerberosDetail=Sie sind entweder nicht mit Kerberos angemeldet, oder Ihr Browser ist nicht für eine Anmeldung mit Kerberos konfiguriert. Bitte klicken Sie auf Weiter, damit Sie sich auf eine andere Art anmelden können
kerberosNotSetUp=Kerberos ist nicht eingerichtet. Sie können sich nicht anmelden.
registerTitle=Registrierung
loginAccountTitle=Bei Ihrem Konto anmelden
loginTitle=Anmeldung bei {0}
loginTitleHtml={0}
impersonateTitle={0} Identitätswechsel
impersonateTitleHtml=<strong>{0}</strong> Identitätswechsel
realmChoice=Realm
unknownUser=Unbekannter Benutzer
loginTotpTitle=Mehrfachauthentifizierung konfigurieren
loginProfileTitle=Benutzerkonto Informationen aktualisieren
loginIdpReviewProfileTitle=Benutzerkonto Informationen aktualisieren
loginTimeout=Ihr Anmeldeversuch wurde wegen Zeitüberschreitung abgebrochen. Die Anmeldung beginnt von vorne.
reauthenticate=Zum Fortsetzen bitte erneut anmelden
authenticateStrong=Um fortzufahren wird ein stärkeres Authentifizierungsverfahren benötigt
oauthGrantTitle=Zugang zu {0} gewähren
oauthGrantTitleHtml={0}
oauthGrantInformation=Stellen Sie sicher, dass Sie {0} vertrauen können und informieren Sie sich darüber, wie {0} mit Ihren Daten umgeht.
oauthGrantReview=Prüfen Sie gerne die
oauthGrantTos=Nutzungsbedingungen.
oauthGrantPolicy=Datenschutzrichtlinie.
errorTitle=Es ist ein Fehler aufgetreten.
errorTitleHtml=Es ist ein Fehler aufgetreten.
emailVerifyTitle=E-Mail verifizieren
emailForgotTitle=Passwort vergessen?
updateEmailTitle=E-Mail aktualisieren
emailUpdateConfirmationSentTitle=E-Mail Sendebestätigung
emailUpdateConfirmationSent=Eine Bestätigungs-E-Mail wurde an {0} versendet. Bitte folgen Sie den Anweisungen, um die Aktualisierung der E-Mail abzuschließen.
emailUpdatedTitle=E-Mail aktualisiert
emailUpdated=Die E-Mail wurde erfolgreich zu {0} aktualisiert.
updatePasswordTitle=Passwort aktualisieren
codeSuccessTitle=Erfolgreicher Code
codeErrorTitle=Fehlercode: {0}
displayUnsupported=Angeforderter Anzeigetyp wird nicht unterstützt
browserRequired=Browser für die Anmeldung erforderlich
browserContinue=Browser erforderlich, um die Anmeldung abzuschließen
browserContinuePrompt=Browser öffnen und Anmeldung fortsetzen? [y/n]:
browserContinueAnswer=y
termsTitle=Bedingungen und Konditionen
termsText=<p>Zu definierende Bedingungen und Konditionen</p>
termsPlainText=Zu definierende Bedingungen und Konditionen.
termsAcceptanceRequired=Sie müssen unseren Bedingungen und Konditionen zustimmen.
acceptTerms=Ich stimme den Bedingungen und Konditionen zu
deleteCredentialTitle=Lösche {0}
deleteCredentialMessage=Wollen Sie {0} löschen?
recaptchaFailed=Ungültiges Recaptcha
recaptchaNotConfigured=Recaptcha-Eingabe ist erforderlich, jedoch noch nicht konfiguriert.
consentDenied=Zustimmung verweigert.
noAccount=Neuer Benutzer?
username=Benutzername
usernameOrEmail=Benutzername oder E-Mail
firstName=Vorname
givenName=Vorname
fullName=Voller Name
lastName=Nachname
familyName=Nachname
email=E-Mail
password=Passwort
passwordConfirm=Passwort bestätigen
passwordNew=Neues Passwort
passwordNewConfirm=Neues Passwort bestätigen
hidePassword=Passwort ausblenden
showPassword=Passwort einblenden
rememberMe=Angemeldet bleiben
authenticatorCode=One-time Code
address=Adresse
street=Straße
locality=Stadt oder Ortschaft
region=Staat, Provinz, Region
postal_code=PLZ
country=Land
emailVerified=E-Mail verifiziert
website=Website
phoneNumber=Telefonnummer
phoneNumberVerified=Telefonnummer verifiziert
gender=Geschlecht
birthday=Geburtsdatum
zoneinfo=Zeitzone
gssDelegationCredential=GSS delegierte Berechtigung
logoutOtherSessions=Von anderen Geräten abmelden
profileScopeConsentText=Nutzerkonto
emailScopeConsentText=E-Mail Adresse
addressScopeConsentText=Adresse
phoneScopeConsentText=Telefonnummer
offlineAccessScopeConsentText=Offline Zugriff
samlRoleListScopeConsentText=Meine Rollen
rolesScopeConsentText=Nutzerrollen
restartLoginTooltip=Login neu starten
loginTotpIntro=Sie müssen einen One Time Passwort-Generator einrichten, um auf dieses Konto zugreifen zu können.
loginTotpStep1=Installieren Sie eine der folgenden Applikationen auf Ihrem Smartphone:
loginTotpStep2=Öffnen Sie die Applikation und scannen Sie den QR-Code:
loginTotpStep3=Geben Sie den von der Applikation generierten One-time Code ein und klicken Sie auf Absenden.
loginTotpStep3DeviceName=Geben Sie einen Gerätenamen an, um die Verwaltung Ihrer OTP-Geräte zu erleichtern.
loginTotpManualStep2=Öffnen Sie die Applikation und geben Sie den folgenden Schlüssel ein:
loginTotpManualStep3=Verwenden Sie die folgenden Konfigurationswerte, falls Sie diese für die Applikation anpassen können:
loginTotpUnableToScan=Sie können den QR-Code nicht scannen?
loginTotpScanBarcode=QR-Code scannen?
loginCredential=Zugangsdaten
loginOtpOneTime=One-time code
loginTotpType=Typ
loginTotpAlgorithm=Algorithmus
loginTotpDigits=Ziffern
loginTotpInterval=Intervall
loginTotpCounter=Zähler
loginTotpDeviceName=Gerätename
loginTotp.totp=zeitbasiert (time-based)
loginTotp.hotp=zählerbasiert (counter-based)
loginChooseAuthenticator=Login Methode auswählen
oauthGrantRequest=Wollen Sie diese Zugriffsrechte gewähren?
inResource=in
oauth2DeviceVerificationTitle=Geräte-Login
verifyOAuth2DeviceUserCode=Geben Sie den Code von Ihrem Gerät ein und klicken Sie auf Senden
oauth2DeviceInvalidUserCodeMessage=Ungültiger Code, bitte probieren Sie es erneut.
oauth2DeviceExpiredUserCodeMessage=Der Code ist abgelaufen. Bitte versuchen Sie, Ihr Gerät erneut zu verbinden.
oauth2DeviceVerificationCompleteHeader=Geräte-Login erfolgreich
oauth2DeviceVerificationCompleteMessage=Sie können dieses Browser-Fenster schließen und auf Ihrem Gerät weiterarbeiten.
oauth2DeviceVerificationFailedHeader=Geräte-Login fehlgeschlagen
oauth2DeviceVerificationFailedMessage=Sie können dieses Browser-Fenster schließen und versuchen, Ihr Gerät erneut zu verbinden.
oauth2DeviceConsentDeniedMessage=Zustimmung für Verbindung mit dem Gerät verweigert.
oauth2DeviceAuthorizationGrantDisabledMessage=Der Client ist nicht berechtigt, den OAuth 2.0 Device Authorization Grant auszuführen. Der Flow wurde für diesen Client deaktiviert.
emailVerifyInstruction1=Eine E-Mail mit Anweisungen, um Ihre E-Mail-Adresse zu überprüfen, wurde an Ihre Adresse geschickt {0}.
emailVerifyInstruction2=Sie haben keinen Verifizierungs-Code in Ihrer E-Mail erhalten?
emailVerifyInstruction3=um eine neue E-Mail versenden zu lassen.
emailLinkIdpTitle={0} verknüpfen
emailLinkIdp1=Eine E-Mail mit weiteren Anweisungen um {0} Konto {1} mit Ihrem {2} Konto zu verknüpfen wurde an Sie versendet.
emailLinkIdp2=Sie haben keinen Code in Ihrer E-Mail erhalten?
emailLinkIdp3=um eine neue E-Mail versenden zu lassen.
emailLinkIdp4=Wenn Sie die E-Mail bereits in einem anderen Browser verifiziert haben
emailLinkIdp5=um fortzufahren.
backToLogin=&laquo; Zurück zur Anmeldung
emailInstruction=Geben Sie Ihren Benutzernamen oder Ihre E-Mail Adresse ein und klicken Sie auf Absenden. Danach werden wir Ihnen eine E-Mail mit weiteren Instruktionen zusenden.
emailInstructionUsername=Geben Sie Ihren Benutzernamen ein und klicken Sie auf Absenden. Danach werden wir Ihnen eine E-Mail mit weiteren Instruktionen zusenden.
copyCodeInstruction=Bitte kopieren Sie den folgenden Code und fügen ihn in die Applikation ein:
pageExpiredTitle=Diese Seite ist nicht mehr gültig.
pageExpiredMsg1=Um den Anmeldevorgang neu zu starten
pageExpiredMsg2=Um den Anmeldevorgang fortzusetzen
personalInfo=Persönliche Informationen:
role_admin=Admin
role_realm-admin=Realm Admin
role_create-realm=Realm erstellen
role_create-client=Client erstellen
role_view-realm=Realm ansehen
role_view-users=Benutzer ansehen
role_view-applications=Applikationen ansehen
role_view-clients=Clients ansehen
role_view-events=Events ansehen
role_view-identity-providers=Identity Provider ansehen
role_manage-realm=Realm verwalten
role_manage-users=Benutzer verwalten
role_manage-applications=Applikationen verwalten
role_manage-identity-providers=Identity Provider verwalten
role_manage-clients=Clients verwalten
role_manage-events=Events verwalten
role_view-profile=Profile ansehen
role_manage-account=Profile verwalten
role_manage-account-links=Profil-Links verwalten
role_read-token=Token lesen
role_offline-access=Offline-Zugriff
client_account=Clientkonto
client_account-console=Accountkonsole
client_security-admin-console=Security Adminkonsole
client_admin-cli=Admin CLI
client_realm-management=Realm-Management
client_broker=Broker
requiredFields=Benötigte Felder
invalidUserMessage=Ungültiger Benutzername oder Passwort.
invalidUsernameMessage=Ungültiger Benutzername.
invalidUsernameOrEmailMessage=Ungültiger Benutzername oder E-Mail.
invalidPasswordMessage=Ungültiges Passwort.
invalidEmailMessage=Ungültige E-Mail-Adresse.
accountDisabledMessage=Ihr Benutzerkonto ist gesperrt, bitte kontaktieren Sie den Admin.
accountTemporarilyDisabledMessage=Ungültiger Benutzername oder Passwort.
accountPermanentlyDisabledMessage=Ungültiger Benutzername oder Passwort.
accountTemporarilyDisabledMessageTotp=Ungültiger One-time Code.
accountPermanentlyDisabledMessageTotp=Ungültiger One-time Code.
expiredCodeMessage=Zeitüberschreitung bei der Anmeldung. Bitte melden Sie sich erneut an.
expiredActionMessage=Die Aktion ist nicht mehr gültig. Bitte fahren Sie nun mit der Anmeldung fort.
expiredActionTokenNoSessionMessage=Die Aktion ist nicht mehr gültig.
expiredActionTokenSessionExistsMessage=Die Aktion ist nicht mehr gültig. Bitte fangen Sie noch einmal an.
sessionLimitExceeded=Es existieren zu viele aktive Sessions
missingFirstNameMessage=Bitte geben Sie einen Vornamen ein.
missingLastNameMessage=Bitte geben Sie einen Nachnamen ein.
missingEmailMessage=Bitte geben Sie eine E-Mail-Adresse ein.
missingUsernameMessage=Bitte geben Sie einen Benutzernamen ein.
missingPasswordMessage=Bitte geben Sie ein Passwort ein.
missingTotpMessage=Bitte geben Sie den One-time Code ein.
missingTotpDeviceNameMessage=Bitte geben Sie einen Gerätenamen ein.
notMatchPasswordMessage=Passwörter sind nicht identisch.
error-invalid-value=Ungültiger Wert.
error-invalid-blank=Bitte geben Sie einen Wert an.
error-empty=Bitte geben Sie einen Wert an.
error-invalid-length=Länge muss zwischen {1} und {2} Zeichen liegen.
error-invalid-length-too-short=Minimale Länge ist {1}.
error-invalid-length-too-long=Maximale Länge ist {2}.
error-invalid-email=Ungültige E-Mail-Adresse.
error-invalid-number=Ungültige Nummer.
error-number-out-of-range=Nummer muss zwischen {1} und {2} liegen.
error-number-out-of-range-too-small=Nummer muss einen minimalen Wert von {1} haben.
error-number-out-of-range-too-big=Nummer muss einen maximalen Wert von {2} haben.
error-pattern-no-match=Ungültiger Wert.
error-invalid-uri=Ungültige URL.
error-invalid-uri-scheme=Ungültiges URL-Schema.
error-invalid-uri-fragment=Ungültiger URL-Bestandteil.
error-user-attribute-required=Bitte füllen Sie dieses Feld aus.
error-invalid-date=Ungültiges Datum.
error-user-attribute-read-only=Dieses Feld darf nicht editiert werden.
error-username-invalid-character=Wert enthält ungültiges Zeichen.
error-person-name-invalid-character=Wert enthält ungültiges Zeichen.
error-reset-otp-missing-id=Bitte wählen Sie eine OTP-Konfiguration aus.
invalidPasswordExistingMessage=Das aktuelle Passwort ist ungültig.
invalidPasswordBlacklistedMessage=Ungültiges Passwort: Das Passwort steht auf der Blockliste (schwarzen Liste).
invalidPasswordConfirmMessage=Die Passwortbestätigung ist nicht identisch.
invalidTotpMessage=Ungültiger One-time Code.
usernameExistsMessage=Benutzername existiert bereits.
emailExistsMessage=E-Mail existiert bereits.
federatedIdentityExistsMessage=Ein Benutzer mit {0} {1} existiert bereits. Bitte melden Sie sich an der Benutzerkontoverwaltung an um den Benutzer zu verknüpfen.
federatedIdentityUnavailableMessage=Der mit dem Identitätsanbieter {1} authentifizierte Benutzer {0} ist nicht vorhanden. Bitte wenden Sie sich an Ihren Administrator.
federatedIdentityUnmatchedEssentialClaimMessage=Dem vom Identitätsanbieter ausgestellten ID-Token fehlen die passenden Claims. Bitte kontaktieren Sie Ihren Administrator.
confirmLinkIdpTitle=Das Benutzerkonto existiert bereits.
federatedIdentityConfirmLinkMessage=Ein Benutzer mit {0} {1} existiert bereits. Wie möchten Sie fortfahren?
federatedIdentityConfirmReauthenticateMessage=Anmelden um das Benutzerkonto mit {0} zu verknüpfen
nestedFirstBrokerFlowMessage=Der {0} Benutzer {1} ist mit keinem bekannten Benutzer verknüpfen.
confirmLinkIdpReviewProfile=Benutzerkonto überprüfen
confirmLinkIdpContinue=Zu einem bestehenden Benutzerkonto hinzufügen
configureTotpMessage=Sie müssen eine Mehrfachauthentifizierung einrichten, um das Benutzerkonto zu aktivieren.
configureBackupCodesMessage=Sie müssen Backup Codes einrichten, um das Benutzerkonto zu aktivieren.
updateProfileMessage=Sie müssen Ihr Benutzerkonto aktualisieren, um das Benutzerkonto zu aktivieren.
updatePasswordMessage=Sie müssen Ihr Passwort ändern, um das Benutzerkonto zu aktivieren.
updateEmailMessage=Sie müssen Ihre E-Mail-Adresse aktualisieren, um das Benutzerkonto zu aktivieren..
resetPasswordMessage=Sie müssen Ihr Passwort ändern.
verifyEmailMessage=Sie müssen Ihre E-Mail-Adresse verifizieren, um das Benutzerkonto zu aktivieren.
linkIdpMessage=Sie müssen Ihre E-Mail-Adresse verifizieren, um Ihr Benutzerkonto mit {0} zu verknüpfen.
emailSentMessage=Sie sollten in Kürze eine E-Mail mit weiteren Instruktionen erhalten.
emailSendErrorMessage=Die E-Mail konnte nicht versendet werden. Bitte versuchen Sie es später noch einmal.
accountUpdatedMessage=Ihr Benutzerkonto wurde aktualisiert.
accountPasswordUpdatedMessage=Ihr Passwort wurde aktualisiert.
delegationCompleteHeader=Login Erfolgreich
delegationCompleteMessage=Sie können dieses Browserfenster schließen und zu Ihrer Konsolenanwendung zurückkehren.
delegationFailedHeader=Login Fehlgeschlagen
delegationFailedMessage=Sie können dieses Browserfenster schließen und zu Ihrer Konsolenanwendung zurückkehren und versuchen, sich erneut anzumelden.
noAccessMessage=Kein Zugriff
invalidPasswordMinLengthMessage=Ungültiges Passwort: Es muss mindestens {0} Zeichen lang sein.
invalidPasswordMaxLengthMessage=Ungültiges Passwort: Es darf höchstens {0} Zeichen lang sein.
invalidPasswordMinDigitsMessage=Ungültiges Passwort: Es muss mindestens {0} Zahl(en) beinhalten.
invalidPasswordMinLowerCaseCharsMessage=Ungültiges Passwort: Es muss mindestens {0} Kleinbuchstaben beinhalten.
invalidPasswordMinUpperCaseCharsMessage=Ungültiges Passwort: Es muss mindestens {0} Großbuchstaben beinhalten.
invalidPasswordMinSpecialCharsMessage=Ungültiges Passwort: Es muss mindestens {0} Sonderzeichen beinhalten.
invalidPasswordNotUsernameMessage=Ungültiges Passwort: Es darf nicht gleich sein wie der Benutzername.
invalidPasswordNotEmailMessage=Ungültiges Passwort: darf nicht identisch mit der E-Mail-Adresse sein.
invalidPasswordRegexPatternMessage=Ungültiges Passwort: Es entspricht nicht dem Regex-Muster.
invalidPasswordHistoryMessage=Ungültiges Passwort: Es darf nicht einem der letzten {0} Passwörter entsprechen.
invalidPasswordGenericMessage=Ungültiges Passwort: Es verletzt die Passwort-Richtlinien.
failedToProcessResponseMessage=Konnte Antwort nicht verarbeiten.
httpsRequiredMessage=HTTPS erforderlich.
realmNotEnabledMessage=Realm nicht aktiviert.
invalidRequestMessage=Ungültiger Request.
successLogout=Sie sind abgemeldet.
failedLogout=Logout fehlgeschlagen.
unknownLoginRequesterMessage=Ungültiger Login Requester
loginRequesterNotEnabledMessage=Login Requester nicht aktiviert
bearerOnlyMessage=Bearer-only Clients können sich nicht via Browser anmelden.
standardFlowDisabledMessage=Client darf sich mit diesem response_type nicht via Browser anmelden. Standard Flow ist für diesen Client deaktiviert.
implicitFlowDisabledMessage=Client darf sich mit diesem response_type nicht via Browser anmelden. Implicit Flow ist für diesen Client deaktiviert.
invalidRedirectUriMessage=Ungültige Redirect URI
unsupportedNameIdFormatMessage=Nicht unterstütztes NameIDFormat.
invalidRequesterMessage=Ungültiger Requester
registrationNotAllowedMessage=Registrierung nicht erlaubt.
resetCredentialNotAllowedMessage=Zurücksetzen der Zugangsdaten nicht erlaubt
permissionNotApprovedMessage=Berechtigung nicht bestätigt.
noRelayStateInResponseMessage=Kein Relay State in der Antwort von Identity Provider.
insufficientPermissionMessage=Nicht genügend Rechte, um die Identität zu verknüpfen.
couldNotProceedWithAuthenticationRequestMessage=Konnte die Authentifizierungsanfrage nicht weiter verarbeiten.
couldNotObtainTokenMessage=Konnte kein Token vom Identity Provider erhalten.
unexpectedErrorRetrievingTokenMessage=Unerwarteter Fehler während dem Empfang des Tokens vom Identity Provider.
unexpectedErrorHandlingResponseMessage=Unerwarteter Fehler während der Bearbeitung der Antwort vom Identity Provider.
identityProviderAuthenticationFailedMessage=Authentifizierung fehlgeschlagen. Authentifizierung mit dem Identity Provider nicht möglich.
couldNotSendAuthenticationRequestMessage=Konnte Authentifizierungsanfrage nicht an den Identity Provider senden.
unexpectedErrorHandlingRequestMessage=Unerwarteter Fehler während der Bearbeitung der Anfrage an den Identity Provider.
invalidAccessCodeMessage=Ungültiger Access-Code.
sessionNotActiveMessage=Session nicht aktiv.
invalidCodeMessage=Ungültiger Code, bitte melden Sie sich erneut über die Applikation an.
cookieNotFoundMessage=Cookie konnte nicht gefunden werden. Bitte stellen Sie sicher, dass Cookies in Ihrem Browser aktiviert sind.
insufficientLevelOfAuthentication=Die angefragte Stärke des Authentifizierungsverfahrens wurde nicht erfüllt.
identityProviderUnexpectedErrorMessage=Unerwarteter Fehler während der Authentifizierung mit dem Identity Provider.
identityProviderMissingStateMessage=Fehlender state-Parameter in der Antwort vom Identitätsanbieter.
identityProviderMissingCodeOrErrorMessage=Fehlender code- oder error-Parameter in der Antwort vom Identitätsanbieter.
identityProviderInvalidResponseMessage=Ungültige Antwort vom Identity Provider.
identityProviderInvalidSignatureMessage=Ungültige Signatur in der Antwort vom Identity Provider.
identityProviderNotFoundMessage=Konnte keinen Identity Provider zu der Identität finden.
identityProviderLinkSuccess=Sie haben Ihre E-Mail-Adresse erfolgreich verifiziert. Bitte kehren Sie zu Ihrem ursprünglichen Browser zurück und fahren Sie dort mit der Anmeldung fort.
staleCodeMessage=Diese Seite ist nicht mehr gültig, bitte kehren Sie zu Ihrer Applikation zurück und melden Sie sich erneut an.
realmSupportsNoCredentialsMessage=Realm unterstützt keine Credential Typen.
credentialSetupRequired=Anmeldung nicht möglich, Einrichtung der Zugangsdaten erforderlich.
identityProviderNotUniqueMessage=Der Realm unterstützt mehrere Identity Provider. Es konnte kein eindeutiger Identity Provider zum Authentifizieren gewählt werden.
emailVerifiedMessage=Ihre E-Mail-Adresse wurde erfolgreich verifiziert.
emailVerifiedAlreadyMessage=Ihre E-Mail-Adresse wurde bereits verifiziert.
staleEmailVerificationLink=Der von Ihnen angeklickte Link ist nicht mehr gültig. Eventuell haben Sie Ihre E-Mail-Adresse bereits verifiziert.
identityProviderAlreadyLinkedMessage=Die föderierte Identität von {0} ist bereits einem anderen Benutzer zugewiesen.
confirmAccountLinking=Bestätigen Sie den Account {0} des Identity Provider {1} mit Ihrem Account zu verknüpfen.
confirmEmailAddressVerification=Bestätigen Sie, dass die E-Mail-Adresse {0} gültig ist.
confirmExecutionOfActions=Führen Sie die folgende(n) Aktion(en) aus
backToApplication=&laquo; Zurück zur Applikation
missingParameterMessage=Fehlender Parameter: {0}
clientNotFoundMessage=Client nicht gefunden.
clientDisabledMessage=Client deaktiviert.
invalidParameterMessage=Ungültiger Parameter: {0}
alreadyLoggedIn=Sie sind bereits angemeldet.
differentUserAuthenticated=Sie sind in dieser Session bereits mit einem anderen Benutzer ''{0}'' angemeldet. Bitte melden Sie sich zuerst ab.
brokerLinkingSessionExpired=Broker Account Linking angefordert; Ihre Session ist allerdings nicht mehr gültig.
proceedWithAction=» Klicken Sie hier um fortzufahren
acrNotFulfilled=Anforderungen an die Authentifizierung nicht erfüllt
requiredAction.CONFIGURE_TOTP=Mehrfachauthentifizierung konfigurieren
requiredAction.TERMS_AND_CONDITIONS=Bedingungen und Konditionen
requiredAction.UPDATE_PASSWORD=Passwort aktualisieren
requiredAction.UPDATE_PROFILE=Profil aktualisieren
requiredAction.VERIFY_EMAIL=E-Mail-Adresse verifizieren
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Wiederherstellungscodes einrichten
requiredAction.webauthn-register-passwordless=Kennwortlose Authentifizierung einrichten
invalidTokenRequiredActions=Die im Link angegebenen erforderlichen Aktionen sind nicht bekannt
doX509Login=Sie werden angemeldet als:
clientCertificate=X509 Client Zertifikat:
noCertificate=[Kein Zertifikat]


pageNotFound=Seite nicht gefunden
internalServerError=Es ist ein interner Server-Fehler aufgetreten
console-username=Benutzername:
console-password=Passwort:
console-otp=One Time Passwort:
console-new-password=Neues Passwort:
console-confirm-password=Passwort bestätigen:
console-update-password=Eine Aktualisierung Ihres Passworts ist erforderlich.
console-verify-email=Sie müssen Ihre E-Mail-Adresse verifizieren. Wir haben eine E-Mail an {0} gesendet, die einen Verifizierungscode enthält. Bitte geben Sie diesen Code in das untenstehende Eingabefeld ein.
console-email-code=E-Mail Code:
console-accept-terms=Nutzungsbedingungen akzeptieren? [y/n]:
console-accept=y

# Openshift messages
openshift.scope.user_info=Nutzerinformation
openshift.scope.user_check-access=Benutzerzugriffsinformationen
openshift.scope.user_full=Voller Zugriff
openshift.scope.list-projects=Projekte auflisten

# SAML authentication
saml.post-form.title=Authentifizierungsumleitung
saml.post-form.message=Sie werden weitergeleitet, bitte warten.
saml.post-form.js-disabled=JavaScript ist deaktiviert. Wir empfehlen dringend, es zu aktivieren. Klicken Sie auf die Schaltfläche unten, um fortzufahren.
saml.artifactResolutionServiceInvalidResponse=Artefakt konnte nicht aufgelöst werden.

#authenticators
otp-display-name=Authenticator-Anwendung
otp-help-text=Eingabe eines Verifizierungscodes aus der Authenticator-Anwendung.
otp-reset-description=Welche OTP-Konfiguration soll entfernt werden?
password-display-name=Passwort
password-help-text=Melden Sie sich an, indem Sie Ihr Passwort eingeben.
auth-username-form-display-name=Benutzername
auth-username-form-help-text=Anmelden durch Eingabe des Benutzernamens
auth-username-password-form-display-name=Benutzername und Passwort
auth-username-password-form-help-text=Anmelden, indem Sie Ihren Benutzernamen und Ihr Passwort eingeben.

# Recovery Codes
auth-recovery-authn-code-form-display-name=Wiederherstellungscode
auth-recovery-authn-code-form-help-text=Geben Sie einen Wiederherstellungscode aus einer zuvor erstellten Liste ein.
auth-recovery-code-info-message=Geben Sie den angegebenen Wiederherstellungscode ein.
auth-recovery-code-prompt=Wiederherstellungscode #{0}
auth-recovery-code-header=Anmeldung mit einem Wiederherstellungscode
recovery-codes-error-invalid=Ungültiger Wiederherstellungscode
recovery-code-config-header=Wiederherstellungscode
recovery-code-config-warning-title=Diese Wiederherstellungscodes werden nach Verlassen dieser Seite nicht mehr angezeigt
recovery-code-config-warning-message=Drucken Sie sie aus, laden Sie sie herunter oder kopieren Sie sie in einen Passwort-Manager und speichern Sie sie. Wenn Sie diese Einrichtung abbrechen, werden die Wiederherstellungscodes von Ihrem Konto entfernt.
recovery-codes-print=Drucken
recovery-codes-download=Herunterladen
recovery-codes-copy=Kopieren
recovery-codes-copied=Kopiert
recovery-codes-confirmation-message=Ich habe diese Codes an einem sicheren Ort gespeichert
recovery-codes-action-complete=Fertigstellen
recovery-codes-action-cancel=Abbrechen
recovery-codes-download-file-header=Bewahren Sie diese Wiederherstellungscodes an einem sicheren Ort auf.
recovery-codes-download-file-description=Wiederherstellungscodes sind einmalig verwendbare Passwörter, mit denen Sie sich bei Ihrem Konto anmelden können, wenn Sie keinen Zugriff auf Ihren Authentifikator haben.
recovery-codes-download-file-date=Diese Codes wurden generiert am
recovery-codes-label-default=Wiederherstellungscodes

# WebAuthn
webauthn-display-name=Passkey
webauthn-help-text=Verwenden Sie Ihren Passkey zur Anmeldung.
webauthn-passwordless-display-name=Passkey
webauthn-passwordless-help-text=Verwenden Sie Ihren Passkey zur kennwortlosen Anmeldung.
webauthn-login-title=Anmeldung mit Passkey
webauthn-registration-title=Passkey registrieren
webauthn-available-authenticators=Verfügbare Passkeys
webauthn-unsupported-browser-text=WebAuthn wird von diesem Browser nicht unterstützt. Versuchen Sie es mit einem anderen oder wenden Sie sich an Ihren Administrator.
webauthn-doAuthenticate=Anmelden mit Passkey
webauthn-createdAt-label=Erstellt

# WebAuthn Error
webauthn-error-title=Passkey Fehler
webauthn-error-registration=Fehler beim Registrieren Ihres Passkeys.<br /> {0}
webauthn-error-api-get=Fehler beim Authentifizieren mit dem Passkey.<br /> {0}
webauthn-error-different-user=Der erste authentifizierte Benutzer ist nicht derjenige, der durch den Passkey authentifiziert wurde.
webauthn-error-auth-verification=Das Ergebnis der Passkey-Authentifizierung ist ungültig.<br /> {0}
webauthn-error-register-verification=Das Ergebnis der Passkey-Registrierung ist ungültig.<br /> {0}
webauthn-error-user-not-found=Unbekannter Benutzer, der mit dem Passkey authentifiziert wurde.

# Identity provider
identity-provider-redirector=Mit einem anderen Indentitätsprovider verbinden
identity-provider-login-label=Oder anmelden mit
idp-email-verification-display-name=E-Mail Verifizierung
idp-email-verification-help-text=Verknüpfen Sie Ihr Benutzerkonto durch die Bestätigung ihre E-Mail-Adresse.
idp-username-password-form-display-name=Passwort Verifizierung
idp-username-password-form-help-text=Verknüpfen Sie Ihr Benutzerkonto indem sie sich anmelden.
finalDeletionConfirmation=Wenn Sie Ihr Konto löschen, kann es nicht wiederhergestellt werden. Um Ihr Konto zu behalten, klicken Sie auf Abbrechen.
irreversibleAction=Diese Aktion ist unwiderruflich
deleteAccountConfirm=Löschung des Kontos bestätigen
deletingImplies=Die Löschung Ihres Kontos bedeutet:
errasingData=Löschen aller Ihrer Daten
loggingOutImmediately=Sofortige Abmeldung
accountUnusable=Eine spätere Nutzung der Anwendung ist mit diesem Konto nicht mehr möglich
userDeletedSuccessfully=Nutzer erfolgreich gelöscht
access-denied=Zugriff verweigert
access-denied-when-idp-auth=Zugriff verweigert wenn über {0} authentifiziert
frontchannel-logout.title=Abmelden
frontchannel-logout.message=Sie melden sich von folgenden Anwendungen ab
logoutConfirmTitle=Abmelden
logoutConfirmHeader=Wollen Sie sich abmelden?
doLogout=Abmelden
readOnlyUsernameMessage=Sie können Ihren Benutzernamen nicht ändern, da er schreibgeschützt ist.
error-invalid-multivalued-size=Attribut {0} muss mindestens {1} und darf höchstens {2} {2,choice,0#Werte|1#Wert|1<Werte} haben.
webauthn-registration-init-label=Passkey (Standard Bezeichnung)
nfc=NFC
identityProviderLogoutFailure=Fehler bei Logout mit SAML Identitätsanbieter
webauthn-registration-init-label-prompt=Bitte geben Sie die Bezeichnung Ihres registrierten Passkeys ein
internal=Intern
unknown=Unbekannt

# Transports
usb=USB
bluetooth=Bluetooth
organizationScopeConsentText=Organisation
totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator
confirmOverrideIdpTitle=Es besteht bereits eine Verküpfung mit einem Identitätsanbieter
federatedIdentityConfirmOverrideMessage=Sie versuchen, Ihr Benutzerkonto {0} mit dem {1} Benutzerkonto {2} zu verknüpfen. Ihr Benutzerkonto ist jedoch bereits mit einem anderen {3} Benutzerkonto {4} verknüpft. Können Sie bestätigen, ob Sie die bestehende Verknüpfung durch das neue Benutzerkonto ersetzen möchten?
confirmOverrideIdpContinue=Ja, die Verknüpfung mit dem neuen Benutzerkonto ersetzen.
invalidPasswordNotContainsUsernameMessage=Ungültiges Passwort: darf nicht den Benutzernamen enthalten.
organization.confirm-membership=Durch Anklicken des unten stehenden Links werden Sie Mitglied der {0} Organization:
auth-x509-client-username-form-help-text=Melden Sie sich mit einem X509 Client-Zertifikat an.
organization.member.register.title=Erstellen Sie ein Konto, um der Organization ${kc.org.name} beizutreten
auth-x509-client-username-form-display-name=X509 Zertifikat
passkey-unsupported-browser-text=Passkey wird von diesem Browser nicht unterstützt. Probieren Sie einen anderen oder kontaktieren Sie Ihren Administrator.
organization.confirm-membership.title=Sie sind dabei, der Organization ${kc.org} beizutreten

# Passkey
passkey-login-title=Anmeldung mit Passkey
passkey-available-authenticators=Verfügbare Passkeys
passkey-doAuthenticate=Anmelden mit Passkey
passkey-createdAt-label=Erstellt
passkey-autofill-select=Wählen Sie Ihren Passkey
requiredAction.webauthn-register=Passkey einrichten
organization.select=Wählen Sie eine Organisation, um fortzufahren:
notMemberOfAnyOrganization=Benutzer ist kein Mitglied einer Organisation
notMemberOfOrganization=Benutzer ist kein Mitglied der Organisation {0}
emailVerifyInstruction4=Um Ihre E-Mail-Adresse zu verifizieren werden wir eine E-Mail mit weiteren Hinweisen an {0} senden.
emailVerifyResend=Verifizierungs-E-Mail erneut senden
emailVerifySend=Verifizierungs-E-Mail senden
linkIdpActionMessage=Möchten Sie Ihren Account mit {0} verlinken?
