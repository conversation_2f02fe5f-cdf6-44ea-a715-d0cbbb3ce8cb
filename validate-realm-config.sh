#!/bin/bash

# Keycloak Realm Configuration Validator
# This script validates your realm configuration before deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REALM_FILE="${1:-realm-config.json}"
VALIDATION_ERRORS=0
VALIDATION_WARNINGS=0

echo -e "${BLUE}=== Keycloak Realm Configuration Validator ===${NC}"
echo ""

# Function to print colored output
print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
    ((VALIDATION_WARNINGS++))
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
    ((VALIDATION_ERRORS++))
}

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Check if file exists
if [ ! -f "$REALM_FILE" ]; then
    print_error "Configuration file '$REALM_FILE' not found!"
    exit 1
fi

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    print_error "jq is required but not installed. Please install jq first."
    exit 1
fi

print_info "Validating: $REALM_FILE"
echo ""

# 1. JSON Syntax Validation
echo "=== JSON Syntax Validation ==="
if jq empty "$REALM_FILE" 2>/dev/null; then
    print_success "Valid JSON syntax"
else
    print_error "Invalid JSON syntax"
    exit 1
fi
echo ""

# 2. Basic Realm Configuration
echo "=== Basic Realm Configuration ==="

# Check realm name
REALM_NAME=$(jq -r '.realm' "$REALM_FILE")
if [ "$REALM_NAME" = "null" ] || [ -z "$REALM_NAME" ]; then
    print_error "Realm name is missing or empty"
else
    print_success "Realm name: $REALM_NAME"
fi

# Check if realm is enabled
ENABLED=$(jq -r '.enabled' "$REALM_FILE")
if [ "$ENABLED" = "true" ]; then
    print_success "Realm is enabled"
elif [ "$ENABLED" = "false" ]; then
    print_warning "Realm is disabled"
else
    print_error "Realm enabled status is missing or invalid"
fi

# Check SSL requirement
SSL_REQUIRED=$(jq -r '.sslRequired' "$REALM_FILE")
case "$SSL_REQUIRED" in
    "external")
        print_success "SSL required for external requests (recommended)"
        ;;
    "all")
        print_success "SSL required for all requests (high security)"
        ;;
    "none")
        print_warning "SSL not required (only for development)"
        ;;
    *)
        print_error "Invalid or missing SSL requirement"
        ;;
esac
echo ""

# 3. Security Configuration
echo "=== Security Configuration ==="

# Check token lifespans
ACCESS_TOKEN_LIFESPAN=$(jq -r '.accessTokenLifespan' "$REALM_FILE")
if [ "$ACCESS_TOKEN_LIFESPAN" != "null" ]; then
    if [ "$ACCESS_TOKEN_LIFESPAN" -le 300 ]; then
        print_success "Access token lifespan: ${ACCESS_TOKEN_LIFESPAN}s (secure)"
    elif [ "$ACCESS_TOKEN_LIFESPAN" -le 900 ]; then
        print_warning "Access token lifespan: ${ACCESS_TOKEN_LIFESPAN}s (consider shorter)"
    else
        print_warning "Access token lifespan: ${ACCESS_TOKEN_LIFESPAN}s (too long, security risk)"
    fi
else
    print_warning "Access token lifespan not specified"
fi

# Check brute force protection
BRUTE_FORCE=$(jq -r '.bruteForceProtected' "$REALM_FILE")
if [ "$BRUTE_FORCE" = "true" ]; then
    print_success "Brute force protection enabled"
else
    print_warning "Brute force protection disabled"
fi

# Check signature algorithm
SIG_ALGORITHM=$(jq -r '.defaultSignatureAlgorithm' "$REALM_FILE")
if [ "$SIG_ALGORITHM" = "RS256" ]; then
    print_success "Using RS256 signature algorithm (recommended)"
elif [ "$SIG_ALGORITHM" != "null" ]; then
    print_warning "Using $SIG_ALGORITHM signature algorithm"
else
    print_warning "Signature algorithm not specified"
fi
echo ""

# 4. Client Configuration Validation
echo "=== Client Configuration ==="

CLIENTS_COUNT=$(jq '.clients | length' "$REALM_FILE" 2>/dev/null || echo "0")
print_info "Number of clients configured: $CLIENTS_COUNT"

if [ "$CLIENTS_COUNT" -gt 0 ]; then
    # Validate each client
    for i in $(seq 0 $((CLIENTS_COUNT - 1))); do
        CLIENT_ID=$(jq -r ".clients[$i].clientId" "$REALM_FILE")
        PROTOCOL=$(jq -r ".clients[$i].protocol" "$REALM_FILE")
        
        echo "  Client: $CLIENT_ID ($PROTOCOL)"
        
        # Check for required fields
        if [ "$(jq -r ".clients[$i].enabled" "$REALM_FILE")" = "true" ]; then
            print_success "    Client is enabled"
        else
            print_warning "    Client is disabled"
        fi
        
        # Check redirect URIs
        REDIRECT_URIS=$(jq -r ".clients[$i].redirectUris[]?" "$REALM_FILE" 2>/dev/null || echo "")
        if [ -n "$REDIRECT_URIS" ]; then
            print_success "    Redirect URIs configured"
            # Check for wildcards in production
            if echo "$REDIRECT_URIS" | grep -q '\*'; then
                print_warning "    Contains wildcard redirect URIs (avoid in production)"
            fi
        else
            print_warning "    No redirect URIs configured"
        fi
        
        # Protocol-specific validation
        if [ "$PROTOCOL" = "openid-connect" ]; then
            # Check for client secret if not public
            PUBLIC_CLIENT=$(jq -r ".clients[$i].publicClient" "$REALM_FILE")
            if [ "$PUBLIC_CLIENT" = "false" ]; then
                SECRET=$(jq -r ".clients[$i].secret" "$REALM_FILE")
                if [ "$SECRET" != "null" ] && [ -n "$SECRET" ]; then
                    if [ ${#SECRET} -ge 32 ]; then
                        print_success "    Client secret configured (adequate length)"
                    else
                        print_warning "    Client secret too short (use 32+ characters)"
                    fi
                else
                    print_error "    Missing client secret for confidential client"
                fi
            fi
            
            # Check flow settings
            STANDARD_FLOW=$(jq -r ".clients[$i].standardFlowEnabled" "$REALM_FILE")
            IMPLICIT_FLOW=$(jq -r ".clients[$i].implicitFlowEnabled" "$REALM_FILE")
            DIRECT_ACCESS=$(jq -r ".clients[$i].directAccessGrantsEnabled" "$REALM_FILE")
            
            if [ "$STANDARD_FLOW" = "true" ]; then
                print_success "    Standard flow enabled (authorization code)"
            fi
            
            if [ "$IMPLICIT_FLOW" = "true" ]; then
                print_warning "    Implicit flow enabled (deprecated, use PKCE)"
            fi
            
            if [ "$DIRECT_ACCESS" = "true" ]; then
                print_warning "    Direct access grants enabled (security risk)"
            fi
        fi
    done
fi
echo ""

# 5. Identity Provider Configuration
echo "=== Identity Provider Configuration ==="

IDP_COUNT=$(jq '.identityProviders | length' "$REALM_FILE" 2>/dev/null || echo "0")
print_info "Number of identity providers configured: $IDP_COUNT"

if [ "$IDP_COUNT" -gt 0 ]; then
    for i in $(seq 0 $((IDP_COUNT - 1))); do
        ALIAS=$(jq -r ".identityProviders[$i].alias" "$REALM_FILE")
        PROVIDER_ID=$(jq -r ".identityProviders[$i].providerId" "$REALM_FILE")
        ENABLED=$(jq -r ".identityProviders[$i].enabled" "$REALM_FILE")
        
        echo "  Identity Provider: $ALIAS ($PROVIDER_ID)"
        
        if [ "$ENABLED" = "true" ]; then
            print_success "    Provider is enabled"
        else
            print_warning "    Provider is disabled"
        fi
        
        # Check for required configuration
        CLIENT_ID=$(jq -r ".identityProviders[$i].config.clientId" "$REALM_FILE")
        if [ "$CLIENT_ID" != "null" ] && [ -n "$CLIENT_ID" ]; then
            if [[ "$CLIENT_ID" =~ ^your-.*|.*placeholder.*|.*example.* ]]; then
                print_error "    Client ID contains placeholder value: $CLIENT_ID"
            else
                print_success "    Client ID configured"
            fi
        fi
        
        CLIENT_SECRET=$(jq -r ".identityProviders[$i].config.clientSecret" "$REALM_FILE")
        if [ "$CLIENT_SECRET" != "null" ] && [ -n "$CLIENT_SECRET" ]; then
            if [[ "$CLIENT_SECRET" =~ ^your-.*|.*placeholder.*|.*secret$ ]]; then
                print_error "    Client secret contains placeholder value"
            else
                print_success "    Client secret configured"
            fi
        fi
    done
fi
echo ""

# 6. SMTP Configuration
echo "=== SMTP Configuration ==="

SMTP_HOST=$(jq -r '.smtpServer.host' "$REALM_FILE")
if [ "$SMTP_HOST" != "null" ] && [ -n "$SMTP_HOST" ]; then
    if [[ "$SMTP_HOST" =~ localhost|example\.com ]]; then
        print_warning "SMTP host appears to be placeholder: $SMTP_HOST"
    else
        print_success "SMTP host configured: $SMTP_HOST"
    fi
    
    SMTP_FROM=$(jq -r '.smtpServer.from' "$REALM_FILE")
    if [ "$SMTP_FROM" != "null" ] && [ -n "$SMTP_FROM" ]; then
        print_success "SMTP from address configured"
    else
        print_warning "SMTP from address not configured"
    fi
else
    print_warning "SMTP server not configured (required for email verification)"
fi
echo ""

# 7. User Configuration
echo "=== User Configuration ==="

USERS_COUNT=$(jq '.users | length' "$REALM_FILE" 2>/dev/null || echo "0")
if [ "$USERS_COUNT" -gt 0 ]; then
    print_warning "Found $USERS_COUNT users in configuration (remove in production)"
    
    # Check for default passwords
    DEFAULT_PASSWORDS=$(jq -r '.users[].credentials[]? | select(.value == "password" or .value == "admin" or .value == "test123") | .value' "$REALM_FILE" 2>/dev/null || echo "")
    if [ -n "$DEFAULT_PASSWORDS" ]; then
        print_error "Found users with default passwords (security risk)"
    fi
else
    print_success "No users in configuration (good for production)"
fi
echo ""

# 8. Check for Placeholder Values
echo "=== Placeholder Value Check ==="

PLACEHOLDERS=$(jq -r '.. | strings | select(test("your-.*|change-this|placeholder|example\\.com|localhost"))' "$REALM_FILE" 2>/dev/null || true)

if [ -n "$PLACEHOLDERS" ]; then
    print_error "Found placeholder values that need to be replaced:"
    echo "$PLACEHOLDERS" | while read -r placeholder; do
        echo "    - $placeholder"
    done
else
    print_success "No placeholder values found"
fi
echo ""

# 9. Summary
echo "=== Validation Summary ==="
if [ $VALIDATION_ERRORS -eq 0 ] && [ $VALIDATION_WARNINGS -eq 0 ]; then
    print_success "Configuration validation passed with no issues!"
elif [ $VALIDATION_ERRORS -eq 0 ]; then
    print_warning "Configuration validation passed with $VALIDATION_WARNINGS warning(s)"
    echo "Consider addressing the warnings before deployment."
else
    print_error "Configuration validation failed with $VALIDATION_ERRORS error(s) and $VALIDATION_WARNINGS warning(s)"
    echo "Please fix the errors before deployment."
    exit 1
fi

echo ""
print_info "Validation completed for: $REALM_FILE"

if [ $VALIDATION_ERRORS -eq 0 ]; then
    echo ""
    print_info "Ready for deployment! Use: ./deploy-realm.sh"
fi
