emailVerificationSubject=E-Mail verifizieren
emailVerificationBody=Je<PERSON> hat ein {2} Konto mit dieser E-Mail-Adresse erstellt. Falls Sie das waren, dann klicken Sie auf den Link, um die E-Mail-Adresse zu verifizieren.\n\n{0}\n\nDer Link ist {3} gültig.\n\nFalls Sie dieses Konto nicht erstellt haben, dann können sie diese Nachricht ignorieren.
emailVerificationBodyHtml=<p>Jemand hat ein {2} Konto mit dieser E-Mail-Adresse erstellt. Falls Sie das waren, klicken Sie auf den Link, um die E-Mail-Adresse zu verifizieren.</p><p><a href="{0}">Link zur Bestätigung der E-Mail-Adresse</a></p><p>Der Link ist {3} gültig.</p><p>Falls Sie dieses Konto nicht erstellt haben, dann können sie diese Nachricht ignorieren.</p>
emailUpdateConfirmationSubject=Neue E-Mail verifizieren
emailUpdateConfirmationBody=Um Ihren {2} Account mit der E-Mail-Adresse {1} zu verifizieren, klicken Sie den folgenden Link\n\n{0}\n\nDer Link ist {3} gültig.\n\nSollten Sie die neue E-Mail-Adresse nicht verwenden wollen, ignorieren Sie diese Nachricht.
emailUpdateConfirmationBodyHtml=<p>Um Ihren {2} Account mit der E-Mail-Adresse {1} zu verifizieren, klicken Sie den folgenden Link</p><p><a href="{0}">{0}</a></p><p>Der Link ist {3} gültig.</p><p>Sollten Sie die neue E-Mail-Adresse nicht verwenden wollen, ignorieren Sie diese Nachricht.</p>
identityProviderLinkSubject=Link {0}
identityProviderLinkBody=Es wurde beantragt Ihren Account "{1}" mit dem Account "{0}" von Benutzer {2} zu verlinken. Sollten Sie dies beantragt haben, klicken Sie auf den unten stehenden Link.\n\n{3}\n\nDer Link ist {5} gültig.\n\nSollten Sie Ihren Account nicht verlinken wollen, ignorieren Sie diese Nachricht. Wenn Sie die Accounts verlinken wird ein Login auf {1} über {0} ermöglicht.
identityProviderLinkBodyHtml=<p>Es wurde beantragt Ihren Account <b>{1}</b> mit dem Account <b>{0}</b> von Benutzer {2} zu verlinken. Sollten Sie dies beantragt haben, klicken Sie auf den unten stehenden Link.</p><p><a href="{3}">Link zur Bestätigung der Kontoverknüpfung</a></p><p>Der Link ist {5} gültig.</p><p>Sollten Sie Ihren Account nicht verlinken wollen, ignorieren Sie diese Nachricht. Wenn Sie die Accounts verlinken wird ein Login auf {1} über {0} ermöglicht.</p>
passwordResetSubject=Passwort zurücksetzen
passwordResetBody=Es wurde eine Änderung der Zugangsdaten für Ihren Account {2} angefordert. Wenn Sie diese Änderung beantragt haben, klicken Sie auf den unten stehenden Link.\n\n{0}\n\nDer Link ist {3} gültig.\n\nSollten Sie keine Änderung vollziehen wollen können Sie diese Nachricht ignorieren und an Ihrem Account wird nichts geändert.
passwordResetBodyHtml=<p>Es wurde eine Änderung der Zugangsdaten für Ihren Account {2} angefordert. Wenn Sie diese Änderung beantragt haben, klicken Sie auf den unten stehenden Link.</p><p><a href="{0}">Link zum Zurücksetzen von Zugangsdaten</a></p><p>Der Link ist {3} gültig.</p><p>Sollten Sie keine Änderung vollziehen wollen können Sie diese Nachricht ignorieren und an Ihrem Account wird nichts geändert.</p>
executeActionsSubject=Aktualisieren Sie Ihr Konto
executeActionsBody=Ihr Administrator hat Sie aufgefordert Ihren Account {2} zu aktualisieren und folgende Aktion(en) durchzuführen: {3}. Klicken Sie auf den unten stehenden Link um den Prozess zu starten.\n\n{0}\n\nDer Link ist {4} gültig.\n\nSollten Sie sich dieser Aufforderung nicht bewusst sein, ignorieren Sie diese Nachricht und Ihr Account bleibt unverändert.
executeActionsBodyHtml=<p>Ihr Administrator hat Sie aufgefordert Ihren Account {2} zu aktualisieren und folgende Aktion(en) durchzuführen: {3}. Klicken Sie auf den unten stehenden Link um den Prozess zu starten.</p><p><a href="{0}">Link zum Account-Update</a></p><p>Der Link ist {4} gültig.</p><p>Sollten Sie sich dieser Aufforderung nicht bewusst sein, ignorieren Sie diese Nachricht und Ihr Account bleibt unverändert.</p>
eventLoginErrorSubject=Fehlgeschlagene Anmeldung
eventLoginErrorBody=Jemand hat um {0} von {1} versucht, sich mit Ihrem Konto anzumelden. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.
eventLoginErrorBodyHtml=<p>Jemand hat um {0} von {1} versucht, sich mit Ihrem Konto anzumelden. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.</p>
eventRemoveTotpSubject=OTP Entfernt
eventRemoveTotpBody=OTP wurde von Ihrem Konto am {0} von {1} entfernt. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.
eventRemoveTotpBodyHtml=<p>OTP wurde von Ihrem Konto am {0} von {1} entfernt. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.</p>
eventUpdatePasswordSubject=Passwort Aktualisiert
eventUpdatePasswordBody=Ihr Passwort wurde am {0} von {1} geändert. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.
eventUpdatePasswordBodyHtml=<p>Ihr Passwort wurde am {0} von {1} geändert. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.</p>
eventUpdateTotpSubject=OTP Aktualisiert
eventUpdateTotpBody=OTP wurde am {0} von {1} geändert. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.
eventUpdateTotpBodyHtml=<p>OTP wurde am {0} von {1} geändert. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.</p>
requiredAction.CONFIGURE_TOTP=Mehrfachauthentifizierung konfigurieren
requiredAction.TERMS_AND_CONDITIONS=Bedingungen und Konditionen
requiredAction.UPDATE_PASSWORD=Passwort aktualisieren
requiredAction.UPDATE_PROFILE=Profil aktualisieren
requiredAction.VERIFY_EMAIL=E-Mail-Adresse verifizieren

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#Sekunden|1#Sekunde|1<Sekunden}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#Minuten|1#Minute|1<Minuten}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#Stunden|1#Stunde|1<Stunden}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#Tage|1#Tag|1<Tage}
emailVerificationBodyCode=Bitte verifizieren Sie Ihre E-Mail-Adresse, indem Sie den folgenden Code eingeben.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Bitte verifizieren Sie Ihre E-Mail-Adresse, indem Sie den folgenden Code eingeben.</p><p><b>{0}</b></p>
emailTestBody=Dies ist eine Testnachricht
emailTestSubject=[KEYCLOAK] - SMTP Testnachricht
emailTestBodyHtml=<p>Dies ist eine Testnachricht</p>
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Wiederherstellungscodes generieren
orgInviteBodyPersonalizedHtml=<p>Hi, {5} {6}.</p><p>Sie wurden eingeladen, der {3} Organization beizutreten. Klicken Sie auf den Link unten, um sich anzuschließen. </p><p><a href="{0}">Link zur Organization</a></p><p> Dieser Link wird innerhalb von {4} ablaufen.</p><p> Wenn Sie der Organization nicht beitreten möchten, ignorieren Sie diese Nachricht.</p>
orgInviteSubject=Einladung der Organization {0} beizutreten
orgInviteBody=Sie wurden eingeladen, der "{3}" Organization beizutreten. Klicken Sie auf den Link unten, um ihr beizutreten.\n\n{0}\n\nDieser Link läuft innerhalb von {4} ab.\n\nWenn Sie nicht der Organization beitreten wollen, ignorieren Sie diese Nachricht.
orgInviteBodyHtml=<p>Sie wurden eingeladen, der {3} Organization beizutreten. Klicken Sie auf den Link unten, um ihr beizutreten. </p><p><a href="{0}">Link zur Organization</a></p><p> Dieser Link wird innerhalb von {4} ablaufen.</p><p> Wenn Sie der Organization nicht beitreten möchten, ignorieren Sie diese Nachricht.</p>
orgInviteBodyPersonalized=Hi, "{5}" "{6}".\n\nSie wurden eingeladen, der "{3}" Organization beizutreten. Klicken Sie auf den Link unten, um sich anzuschließen.\n\n{0}\n\nDieser Link läuft innerhalb von {4} ab.\n\nWenn Sie nicht der Organization beitreten wollen, ignorieren Sie diese Nachricht.
eventRemoveCredentialBodyHtml=<p> Zugangsdaten {0} wurde auf {1} von {2} von Ihrem Konto entfernt. Wenn Sie das nicht waren, wenden Sie sich bitte an einen Administrator.</p>
eventUpdateCredentialSubject=Zugangsdaten aktualisieren
eventRemoveCredentialSubject=Zugangsdaten entfernen
eventUpdateCredentialBody=Ihre {0} Zugangsdaten wurden auf {1} von {2} geändert. Wenn Sie das nicht waren, wenden Sie sich bitte an einen Administrator.
eventUpdateCredentialBodyHtml=<p>Ihre {0} Zugangsdaten wurden auf {1} von {2} geändert. Wenn Sie das nicht waren, wenden Sie sich bitte an einen Administrator.</p>
eventRemoveCredentialBody=Zugangsdaten {0} wurde auf {1} von {2} von Ihrem Konto entfernt. Wenn Sie das nicht waren, wenden Sie sich bitte an einen Administrator.
eventUserDisabledByTemporaryLockoutSubject=Benutzer durch vorübergehende Sperre deaktiviert
eventUserDisabledByPermanentLockoutSubject=Benutzer durch permanente Sperre deaktiviert
eventUserDisabledByPermanentLockoutHtml=<p>Ihr Benutzer wurde aufgrund mehrerer fehlgeschlagener Versuche auf {0} dauerhaft deaktiviert. Bitte kontaktieren Sie einen Administrator.</p>
eventUserDisabledByTemporaryLockoutBody=Ihr Benutzer wurde vorübergehend wegen mehrerer fehlgeschlagener Versuche auf {0} deaktiviert. Bitte wenden Sie sich bei Bedarf an einen Administrator.
eventUserDisabledByTemporaryLockoutHtml=<p> Ihr Benutzer wurde vorübergehend wegen mehrfacher fehlgeschlagener Versuche auf {0} deaktiviert. Bitte wenden Sie sich bei Bedarf an einen Administrator.</p>
eventUserDisabledByPermanentLockoutBody=Ihr Benutzer wurde aufgrund mehrfacher fehlgeschlagener Versuche auf {0} dauerhaft deaktiviert. Bitte kontaktieren Sie einen Administrator.
