doSave=Save
doCancel=Cancel
doLogOutAllSessions=Log out all sessions
doRemove=Remove
doAdd=Add
doSignOut=Sign out
doLogIn=Log In
doLink=Link
noAccessMessage=Access not allowed

personalInfoSidebarTitle=Personal info
accountSecuritySidebarTitle=Account security
signingInSidebarTitle=Signing in
deviceActivitySidebarTitle=Device activity
linkedAccountsSidebarTitle=Linked accounts

editAccountHtmlTitle=Edit Account
personalInfoHtmlTitle=Personal Info
federatedIdentitiesHtmlTitle=Federated Identities
accountLogHtmlTitle=Account Log
changePasswordHtmlTitle=Change Password
deviceActivityHtmlTitle=Device Activity
sessionsHtmlTitle=Sessions
accountManagementTitle=Keycloak Account Management
authenticatorTitle=Authenticator
applicationsHtmlTitle=Applications
linkedAccountsHtmlTitle=Linked accounts

accountManagementWelcomeMessage=Welcome to Keycloak Account Management
accountManagementBaseThemeCannotBeUsedDirectly=The base account theme only contains translations for account console. \
    To display account console, you either need to set the parent of your theme to another account theme, or supply your own index.ftl file. \
    Please see the documentation for further information.
personalInfoIntroMessage=Manage your basic information
accountSecurityTitle=Account Security
accountSecurityIntroMessage=Control your password and account access
applicationsIntroMessage=Track and manage your app permission to access your account
resourceIntroMessage=Share your resources among team members
passwordLastUpdateMessage=Your password was updated at
updatePasswordTitle=Update Password
updatePasswordMessageTitle=Make sure you choose a strong password
updatePasswordMessage=A strong password contains a mix of numbers, letters, and symbols. It is hard to guess, does not resemble a real word, and is only used for this account.
personalSubTitle=Your Personal Info
personalSubMessage=Manage your basic information.

authenticatorCode=One-time code
email=Email
firstName=First name
givenName=Given name
fullName=Full name
lastName=Last name
familyName=Family name
password=Password
currentPassword=Current Password
passwordConfirm=Confirmation
passwordNew=New Password
username=Username
address=Address
street=Street
locality=City or Locality
region=State, Province, or Region
postal_code=Zip or Postal code
country=Country
emailVerified=Email verified
website=Web page
phoneNumber=Phone number
phoneNumberVerified=Phone number verified
gender=Gender
birthday=Birthdate
zoneinfo=Time zone
gssDelegationCredential=GSS Delegation Credential

profileScopeConsentText=User profile
emailScopeConsentText=Email address
addressScopeConsentText=Address
phoneScopeConsentText=Phone number
offlineAccessScopeConsentText=Offline Access
samlRoleListScopeConsentText=My Roles
rolesScopeConsentText=User roles
organizationScopeConsentText=Organization

role_admin=Admin
role_realm-admin=Realm Admin
role_create-realm=Create realm
role_view-realm=View realm
role_view-users=View users
role_view-applications=View applications
role_view-groups=View groups
role_view-clients=View clients
role_view-events=View events
role_view-identity-providers=View identity providers
role_view-consent=View consents
role_manage-realm=Manage realm
role_manage-users=Manage users
role_manage-applications=Manage applications
role_manage-identity-providers=Manage identity providers
role_manage-clients=Manage clients
role_manage-events=Manage events
role_view-profile=View profile
role_manage-account=Manage account
role_manage-account-links=Manage account links
role_manage-consent=Manage consents
role_read-token=Read token
role_offline-access=Offline access
role_uma_authorization=Obtain permissions
client_account=Account
client_account-console=Account Console
client_security-admin-console=Security Admin Console
client_admin-cli=Admin CLI
client_realm-management=Realm Management
client_broker=Broker


requiredFields=Required fields
allFieldsRequired=All fields required

backToApplication=&laquo; Back to application
backTo=Back to {0}

date=Date
event=Event
ip=IP
client=Client
clients=Clients
details=Details
started=Started
lastAccess=Last Access
expires=Expires
applications=Applications

account=Account
federatedIdentity=Federated Identity
authenticator=Authenticator
device-activity=Device activity
sessions=Sessions
log=Log

application=Application
availableRoles=Available Roles
grantedPermissions=Granted Permissions
grantedPersonalInfo=Granted Personal Info
additionalGrants=Additional Grants
action=Action
inResource=in
fullAccess=Full Access
offlineToken=Offline Token
revoke=Revoke Grant

configureAuthenticators=Configured Authenticators
mobile=Mobile
totpStep1=Install one of the following applications on your mobile:
totpStep2=Open the application and scan the barcode:
totpStep3=Enter the one-time code provided by the application and click Save to finish the setup.
totpStep3DeviceName=Provide a Device Name to help you manage your OTP devices.

totpManualStep2=Open the application and enter the key:
totpManualStep3=Use the following configuration values if the application allows setting them:
totpUnableToScan=Unable to scan?
totpScanBarcode=Scan barcode?

totp.totp=Time-based
totp.hotp=Counter-based

totpType=Type
totpAlgorithm=Algorithm
totpDigits=Digits
totpInterval=Interval
totpCounter=Counter
totpDeviceName=Device Name

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

irreversibleAction=This action is irreversible
deletingImplies=Deleting your account implies:
errasingData=Erasing all your data
loggingOutImmediately=Logging you out immediately
accountUnusable=Any subsequent use of the application will not be possible with this account

missingUsernameMessage=Please specify username.
missingFirstNameMessage=Please specify first name.
invalidEmailMessage=Invalid email address.
missingLastNameMessage=Please specify last name.
missingEmailMessage=Please specify email.
missingPasswordMessage=Please specify password.
notMatchPasswordMessage=Passwords don''t match.
invalidUserMessage=Invalid user
updateReadOnlyAttributesRejectedMessage=Update of read-only attribute rejected

missingTotpMessage=Please specify authenticator code.
missingTotpDeviceNameMessage=Please specify device name.
invalidPasswordExistingMessage=Invalid existing password.
invalidPasswordConfirmMessage=Password confirmation doesn''t match.
invalidTotpMessage=Invalid authenticator code.

usernameExistsMessage=Username already exists.
emailExistsMessage=Email already exists.

readOnlyUserMessage=You can''t update your account as it is read-only.
readOnlyUsernameMessage=You can''t update your username as it is read-only.
readOnlyPasswordMessage=You can''t update your password as your account is read-only.

successTotpMessage=Mobile authenticator configured.
successTotpRemovedMessage=Mobile authenticator removed.

successGrantRevokedMessage=Grant revoked successfully.

accountUpdatedMessage=Your account has been updated.
accountPasswordUpdatedMessage=Your password has been updated.

missingIdentityProviderMessage=Identity provider not specified.
invalidFederatedIdentityActionMessage=Invalid or missing action.
identityProviderNotFoundMessage=Specified identity provider not found.
federatedIdentityLinkNotActiveMessage=This identity is not active anymore.
federatedIdentityRemovingLastProviderMessage=You can not remove last federated identity as you do not have a password.
federatedIdentityBoundOrganization=You cannot remove the link to an identity provider associated with an organization.
identityProviderRedirectErrorMessage=Failed to redirect to identity provider.
identityProviderRemovedMessage=Identity provider removed successfully.
identityProviderAlreadyLinkedMessage=Federated identity returned by {0} is already linked to another user.
staleCodeAccountMessage=The page expired. Please try one more time.
consentDenied=Consent denied.
access-denied-when-idp-auth=Access denied when authenticating with {0}

accountDisabledMessage=Account is disabled, contact your administrator.

accountTemporarilyDisabledMessage=Account is temporarily disabled, contact your administrator or try again later.
invalidPasswordMinLengthMessage=Invalid password: minimum length {0}.
invalidPasswordMaxLengthMessage=Invalid password: maximum length {0}.
invalidPasswordMinLowerCaseCharsMessage=Invalid password: must contain at least {0} lower case characters.
invalidPasswordMinDigitsMessage=Invalid password: must contain at least {0} numerical digits.
invalidPasswordMinUpperCaseCharsMessage=Invalid password: must contain at least {0} upper case characters.
invalidPasswordMinSpecialCharsMessage=Invalid password: must contain at least {0} special characters.
invalidPasswordNotUsernameMessage=Invalid password: must not be equal to the username.
invalidPasswordNotContainsUsernameMessage=Invalid password: Can not contain the username.
invalidPasswordNotEmailMessage=Invalid password: must not be equal to the email.
invalidPasswordRegexPatternMessage=Invalid password: fails to match regex pattern(s).
invalidPasswordHistoryMessage=Invalid password: must not be equal to any of last {0} passwords.
invalidPasswordBlacklistedMessage=Invalid password: password is blacklisted.
invalidPasswordGenericMessage=Invalid password: new password doesn''t match password policies.

# Authorization
myResources=My Resources
myResourcesSub=My resources
doDeny=Deny
doRevoke=Revoke
doApprove=Approve
doRemoveSharing=Remove Sharing
doRemoveRequest=Remove Request
peopleAccessResource=People with access to this resource
resourceManagedPolicies=Permissions granting access to this resource
resourceNoPermissionsGrantingAccess=No permissions granting access to this resource
anyAction=Any action
description=Description
name=Name
scopes=Scopes
resource=Resource
user=User
peopleSharingThisResource=People sharing this resource
shareWithOthers=Share with others
needMyApproval=Need my approval
requestsWaitingApproval=Your requests waiting approval
icon=Icon
requestor=Requestor
owner=Owner
resourcesSharedWithMe=Resources shared with me
permissionRequestion=Permission Requestion
permission=Permission
shares=share(s)
notBeingShared=This resource is not being shared.
notHaveAnyResource=You don’t have any resources
noResourcesSharedWithYou=There are no resources shared with you
havePermissionRequestsWaitingForApproval=You have {0} permission request(s) waiting for approval.
clickHereForDetails=Click here for details.
resourceIsNotBeingShared=The resource is not being shared

# Applications
applicationName=Name
applicationType=Application Type
applicationInUse=In-use app only
clearAllFilter=Clear all filters
activeFilters=Active filters
filterByName=Filter By Name ...
allApps=All applications
internalApps=Internal applications
thirdpartyApps=Third-Party applications
appResults=Results
clientNotFoundMessage=Client not found.

# Linked account
authorizedProvider=Authorized Provider
authorizedProviderMessage=Authorized Providers linked with your account
identityProvider=Identity Provider
identityProviderMessage=To link your account with identity providers you have configured
socialLogin=Social Login
userDefined=User Defined
removeAccess=Remove Access
removeAccessMessage=You will need to grant access again, if you want to use this app account.

#Authenticator
authenticatorStatusMessage=Two-factor authentication is currently
authenticatorFinishSetUpTitle=Your Two-Factor Authentication
authenticatorFinishSetUpMessage=Each time you sign in to your Keycloak account, you will be asked to provide a two-factor authentication code.
authenticatorSubTitle=Set Up Two-Factor Authentication
authenticatorSubMessage=To enhance the security of your account, enable at least one of the available two-factor authentication methods.
authenticatorMobileTitle=Mobile Authenticator
authenticatorMobileMessage=Use mobile Authenticator to get Verification codes as the two-factor authentication.
authenticatorMobileFinishSetUpMessage=The authenticator has been bound to your phone.
authenticatorActionSetup=Set up
authenticatorSMSTitle=SMS Code
authenticatorSMSMessage=Keycloak will send the Verification code to your phone as the two-factor authentication.
authenticatorSMSFinishSetUpMessage=Text messages are sent to
authenticatorDefaultStatus=Default
authenticatorChangePhone=Change Phone Number

#Authenticator - Mobile Authenticator setup
authenticatorMobileSetupTitle=Mobile Authenticator Setup
smscodeIntroMessage=Enter your phone number and a verification code will be sent to your phone.
mobileSetupStep1=Install an authenticator application on your phone. The applications listed here are supported.
mobileSetupStep2=Open the application and scan the barcode:
mobileSetupStep3=Enter the one-time code provided by the application and click Save to finish the setup.
scanBarCode=Want to scan the barcode?
enterBarCode=Enter the one-time code
doCopy=Copy
doFinish=Finish

#Authenticator - SMS Code setup
authenticatorSMSCodeSetupTitle=SMS Code Setup
chooseYourCountry=Choose your country
enterYourPhoneNumber=Enter your phone number
sendVerficationCode=Send Verification Code
enterYourVerficationCode=Enter your verification code

#Authenticator - backup Code setup
authenticatorBackupCodesSetupTitle=Recovery Authentication Codes Setup
realmName=Realm
doDownload=Download
doPrint=Print
generateNewBackupCodes=Generate New Recovery Authentication Codes
backtoAuthenticatorPage=Back to Authenticator Page


#Resources
resources=Resources
sharedwithMe=Shared with Me
share=Share
sharedwith=Shared with
accessPermissions=Access Permissions
permissionRequests=Permission Requests
approve=Approve
approveAll=Approve all
people=people
perPage=per page
currentPage=Current Page
sharetheResource=Share the resource
group=Group
selectPermission=Select Permission
addPeople=Add people to share your resource with
addTeam=Add team to share your resource with
myPermissions=My Permissions
waitingforApproval=Waiting for approval
anyPermission=Any Permission

# Openshift messages
openshift.scope.user_info=User information
openshift.scope.user_check-access=User access information
openshift.scope.user_full=Full Access
openshift.scope.list-projects=List projects

error-invalid-value=Invalid value.
error-invalid-blank=Please specify value.
error-empty=Please specify value.
error-invalid-length=Attribute {0} must have a length between {1} and {2}.
error-invalid-length-too-short=Attribute {0} must have minimal length of {1}.
error-invalid-length-too-long=Attribute {0} must have maximal length of {2}.
error-invalid-email=Invalid email address.
error-invalid-number=Invalid number.
error-number-out-of-range=Attribute {0} must be a number between {1} and {2}.
error-number-out-of-range-too-small=Attribute {0} must have minimal value of {1}.
error-number-out-of-range-too-big=Attribute {0} must have maximal value of {2}.
error-pattern-no-match=Invalid value.
error-invalid-uri=Invalid URL.
error-invalid-uri-scheme=Invalid URL scheme.
error-invalid-uri-fragment=Invalid URL fragment.
error-user-attribute-required=Please specify attribute {0}.
error-invalid-date=Invalid date.
error-user-attribute-read-only=The field {0} is read only.
error-username-invalid-character=Username contains invalid character.
error-person-name-invalid-character=Name contains invalid character.
