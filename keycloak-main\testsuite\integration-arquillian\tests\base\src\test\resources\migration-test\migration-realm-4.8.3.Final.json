[{"id": "Migration", "realm": "Migration", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "4dc7308b-6340-4caa-b01c-64766b8f4fe8", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "Migration", "attributes": {}}, {"id": "2c213283-5f48-4c2c-b3b1-665e099bed20", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "Migration", "attributes": {}}, {"id": "211119dc-3fab-4327-8998-065a19fd9635", "name": "migration-test-realm-role", "composite": false, "clientRole": false, "containerId": "Migration", "attributes": {}}], "client": {"migration-test-client": [{"id": "b17299eb-d1e6-4ca6-b830-e7cf74d32197", "name": "migration-test-client-role", "composite": false, "clientRole": true, "containerId": "a46d507a-0641-4cd1-b6ea-2f9c020d19c0", "attributes": {}}], "realm-management": [{"id": "a10713d2-eed2-4f64-abb8-3c42df4024c0", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["view-events", "view-users", "manage-users", "view-clients", "manage-identity-providers", "manage-realm", "manage-authorization", "view-identity-providers", "query-groups", "manage-events", "view-realm", "manage-clients", "query-realms", "view-authorization", "impersonation", "query-users", "query-clients", "create-client"]}}, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "8e32fddf-3922-4b83-b476-ae083307e17b", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "5980836e-80dd-4bae-900b-344c87fdbffc", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "c832695c-df43-48db-ba28-e944fd6a2a43", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "09570d18-c23c-4c06-995c-d8bc945fc899", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "42e0f40d-4a79-4307-a4b9-2f4f68bb9084", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "6b2949c1-451d-4c9e-b46c-03fb126d1a01", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "b8c3958a-7ad9-4b42-82af-b771c80c1238", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "4a9cfbbf-13db-4a9a-9a31-012b41cfe5b6", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "a27cb638-3c3c-4b51-92d9-01b43c499ac4", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "b970adc0-682b-4e8e-9332-ba694a31ec14", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "79391b5a-5724-4f79-b423-707bc0075b3b", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "610db80f-d40e-4a61-b350-745694871fd2", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "1806bb01-e151-497f-8b3b-6ee6e3a9dda9", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "0a7f5e79-ce34-49da-aff6-8f48e14e2a09", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "92a636c7-1ae4-4548-beda-6b070c25297a", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "b85cd128-a6b5-4b2c-a766-b63f6ef90f4b", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "5c8ed411-4fe1-43f2-aaf0-c803c6abde40", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}, {"id": "85abd540-dd5b-4c1c-952d-d279f05fab19", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "attributes": {}}], "security-admin-console": [], "authz-servlet": [], "admin-cli": [], "client-with-template": [], "broker": [{"id": "68e1df20-23a4-4007-812c-************", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "08b72946-628a-48a2-994f-6ccbbf2f5f8e", "attributes": {}}], "account": [{"id": "fc469075-ceec-4def-b10b-45a9c7d10130", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "932ba09f-f0ae-4f25-9847-393a2d9952ef", "attributes": {}}, {"id": "d93a8d25-93b1-4a2e-819c-989db80973ed", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "932ba09f-f0ae-4f25-9847-393a2d9952ef", "attributes": {}}, {"id": "30ca3f88-e311-4ffc-b469-b637e8a2521b", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "932ba09f-f0ae-4f25-9847-393a2d9952ef", "attributes": {}}]}}, "groups": [{"id": "cddb1ffe-2d8d-445f-8c0b-88a2a7e723cd", "name": "migration-test-group", "path": "/migration-test-group", "attributes": {}, "realmRoles": [], "clientRoles": {}, "subGroups": []}], "defaultRoles": ["offline_access", "uma_authorization", "migration-test-realm-role"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "users": [{"id": "089110e3-0b38-4ae3-b019-dce1f1b34512", "createdTimestamp": *************, "username": "migration-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"type": "totp", "hashedSaltedValue": "dSdmuHLQhkm54oIm0A0S", "hashIterations": 0, "counter": 0, "algorithm": "HmacSHA1", "digits": 8, "period": 40, "createdDate": *************, "config": {}}, {"type": "password", "hashedSaltedValue": "kNwotFPNeuwelpT1HWt+E4ONXFK6wjd+h0zbzNBRGwOqacAjeY7vYN9QZQ46DlEKSdn04cEU/3RvX8WPcRegxg==", "salt": "rEIJDbs+BQqpx31v8mONWA==", "hashIterations": 27500, "counter": 0, "algorithm": "pbkdf2-sha256", "digits": 0, "period": 0, "createdDate": *************, "config": {}}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}, {"id": "189110e3-0b38-4ae3-b019-dce1f1b34512", "createdTimestamp": *************, "username": "offline-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"type": "password", "hashedSaltedValue": "kNwotFPNeuwelpT1HWt+E4ONXFK6wjd+h0zbzNBRGwOqacAjeY7vYN9QZQ46DlEKSdn04cEU/3RvX8WPcRegxg==", "salt": "rEIJDbs+BQqpx31v8mONWA==", "hashIterations": 27500, "counter": 0, "algorithm": "pbkdf2-sha256", "digits": 0, "period": 0, "createdDate": *************, "config": {}}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clients": [{"id": "932ba09f-f0ae-4f25-9847-393a2d9952ef", "clientId": "account", "name": "${client_account}", "baseUrl": "/auth/realms/Migration/account", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "b3cb0c5c-f485-4881-9eac-8c0808c75b0c", "defaultRoles": ["manage-account", "view-profile"], "redirectUris": ["/auth/realms/Migration/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "275ca12b-698c-4405-af2a-5362195ed71a", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "40dc13c9-e01e-47e8-8213-dcf20d6b82c2", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "a416a8da-293d-4459-8b6a-f5dbd92f207c", "clientId": "authz-servlet", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "d8e5acec-0bac-4a25-993d-8c7c74bf7e56", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access"], "authorizationServicesEnabled": true}, {"id": "08b72946-628a-48a2-994f-6ccbbf2f5f8e", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "631dfd08-37e1-4734-a13b-6bd494deffac", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "41870b95-a778-4674-a6d6-f6c22a900e79", "clientId": "client-with-template", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "0884f2d3-debd-4825-a376-5bb7361915c0", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "a46d507a-0641-4cd1-b6ea-2f9c020d19c0", "clientId": "migration-test-client", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "secret", "defaultRoles": ["migration-test-client-role"], "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"clientId": "migration-saml-client", "enabled": true, "fullScopeAllowed": true, "protocol": "saml", "baseUrl": "http://localhost:8080/sales-post", "redirectUris": ["http://localhost:8080/sales-post/*"], "attributes": {"saml_assertion_consumer_url_post": "http://localhost:8080/sales-post/saml", "saml_single_logout_service_url_post": "http://localhost:8080/sales-post/saml", "saml.authnstatement": "true", "extremely_long_attribute": "     00000     00010     00020     00030     00040     00050     00060     00070     00080     00090     00100     00110     00120     00130     00140     00150     00160     00170     00180     00190     00200     00210     00220     00230     00240     00250     00260     00270     00280     00290     00300     00310     00320     00330     00340     00350     00360     00370     00380     00390     00400     00410     00420     00430     00440     00450     00460     00470     00480     00490     00500     00510     00520     00530     00540     00550     00560     00570     00580     00590     00600     00610     00620     00630     00640     00650     00660     00670     00680     00690     00700     00710     00720     00730     00740     00750     00760     00770     00780     00790     00800     00810     00820     00830     00840     00850     00860     00870     00880     00890     00900     00910     00920     00930     00940     00950     00960     00970     00980     00990     01000     01010     01020     01030     01040     01050     01060     01070     01080     01090     01100     01110     01120     01130     01140     01150     01160     01170     01180     01190     01200     01210     01220     01230     01240     01250     01260     01270     01280     01290     01300     01310     01320     01330     01340     01350     01360     01370     01380     01390     01400     01410     01420     01430     01440     01450     01460     01470     01480     01490     01500     01510     01520     01530     01540     01550     01560     01570     01580     01590     01600     01610     01620     01630     01640     01650     01660     01670     01680     01690     01700     01710     01720     01730     01740     01750     01760     01770     01780     01790     01800     01810     01820     01830     01840     01850     01860     01870     01880     01890     01900     01910     01920     01930     01940     01950     01960     01970     01980     01990     02000     02010     02020     02030     02040     02050     02060     02070     02080     02090     02100     02110     02120     02130     02140     02150     02160     02170     02180     02190     02200     02210     02220     02230     02240     02250     02260     02270     02280     02290     02300     02310     02320     02330     02340     02350     02360     02370     02380     02390     02400     02410     02420     02430     02440     02450     02460     02470     02480     02490     02500     02510     02520     02530     02540     02550     02560     02570     02580     02590     02600     02610     02620     02630     02640     02650     02660     02670     02680     02690     02700     02710     02720     02730     02740     02750     02760     02770     02780     02790     02800     02810     02820     02830     02840     02850     02860     02870     02880     02890     02900     02910     02920     02930     02940     02950     02960     02970     02980     02990     03000     03010     03020     03030     03040     03050     03060     03070     03080     03090     03100     03110     03120     03130     03140     03150     03160     03170     03180     03190     03200     03210     03220     03230     03240     03250     03260     03270     03280     03290     03300     03310     03320     03330     03340     03350     03360     03370     03380     03390     03400     03410     03420     03430     03440     03450     03460     03470     03480     03490     03500     03510     03520     03530     03540     03550     03560     03570     03580     03590     03600     03610     03620     03630     03640     03650     03660     03670     03680     03690     03700     03710     03720     03730     03740     03750     03760     03770     03780     03790     03800     03810     03820     03830     03840     03850     03860     03870     03880     03890     03900     03910     03920     03930     03940     03950     03960     03970     03980", "saml_idp_initiated_sso_url_name": "sales-post"}}, {"id": "99a28a93-e2e3-4b1d-a377-8a30f6b4930a", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "ba927bb7-7c8b-41ba-9dc2-52ae43e17262", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "b22903fc-1f96-47da-a25d-7617dc4a21f8", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "baseUrl": "/auth/admin/Migration/console/index.html", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "65d49e9c-1140-4c21-979c-447e7b144b5b", "redirectUris": ["/auth/admin/Migration/console/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "99ee4d36-624f-4026-8836-01992b23f1f8", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}], "clientScopes": [{"id": "02d75f89-5c35-4a54-9e41-e0586130e74f", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "8c4adecc-82a0-468f-b577-c66cc299a608", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "f79f9798-eb6b-4a92-9fc1-d73a7e65b648", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "b8a50ce1-a06e-43db-8675-0708822613b5", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}, {"id": "6f0d1f1c-32b7-4e41-8f6e-d083a9f3758f", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "9844dc78-ffb0-4e92-a551-2391259499db", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "2178734c-d32d-44d1-aec4-d0cb2f077a7d", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "a0a91dfe-e477-46a0-8072-572447198323", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "bc82e25a-390b-43ee-bf07-964f1c790abe", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "c95185cc-74ee-45ef-b56e-17f9d8f60a22", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "61749d3b-b365-4765-a85c-44ceac266e40", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "c73b6722-bfb9-4ed6-b071-d32b22af2ace", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "b935262d-f366-46f5-8fe8-a89c08633ff7", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "128fc148-86d7-4281-8179-30d10239a093", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "20ebb046-2a8b-42cc-9b5a-4cafe79543b6", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "24c86c7b-783d-46a8-afbc-64594df7106e", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "b1ece861-bcc6-4b33-9e1e-e351926c70eb", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "2333fe31-16b7-4dcf-999e-35ead26125cf", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "cff58417-5989-4da4-ab69-ca405555d434", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "a72f5106-773f-4f93-8a57-b178f28d7212", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "ccf0f93c-a089-42ed-bb2a-9e26b5758970", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "69dbbfa3-ad81-4715-83d9-931cb088e250", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "529af06a-a4e5-435b-985b-eb6a71f7d14a", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "12c4bd80-0dac-45a1-91bd-55e274f1953e", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}]}, {"id": "df6994e3-5e10-45d3-bbe7-3f078fc1c404", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "c18585d3-8c65-4b38-9f38-0b95ac226c16", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "5e3366e9-ac51-444e-9a92-53f8949e7251", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "30a6e8c7-bb7e-47c0-a5f4-38ff30923bde", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "98a63b81-68ed-41f8-a9b7-843191909bb9", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "b74e0480-eccb-4efd-bc40-e576a2eebaf8", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "3180b876-6f45-45e3-98e5-24b3f2c56d8e", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "0b8e4522-e636-4243-89b0-b2f1296b74e9", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins"], "defaultOptionalClientScopes": ["offline_access", "address", "phone"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "xXSSProtection": "1; mode=block", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "264398b4-154e-4b10-81e2-9b332c779b87", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-role-list-mapper", "oidc-usermodel-property-mapper", "saml-user-property-mapper", "saml-user-attribute-mapper", "oidc-usermodel-attribute-mapper"]}}, {"id": "c7f72620-0ac3-4420-a9f5-9832f214e14a", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "984b3f9d-c6b1-4dcb-9807-91e754d341a9", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "ac6f79f3-6b2d-4f87-8550-65cb6d414cdf", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-property-mapper", "oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-property-mapper", "oidc-usermodel-attribute-mapper", "oidc-address-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper"]}}, {"id": "96196266-c599-4bd5-84e0-256b4cf001c4", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "005c948c-ebb9-471e-b1bf-d499b6696676", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "41febfb9-3422-434a-89d5-b7632f8bbd72", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "010400eb-a150-4d62-b694-68b9eb9e4610", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}], "org.keycloak.keys.KeyProvider": [{"id": "bf8f6a1f-5aa2-47a5-8e4c-3bc474cb1fd5", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["a5509f08-e10f-456f-8469-8136484feed9"], "secret": ["GS0M_iS6RPQ2rSkjtLl_GaffS7r3Ch9z2atwcgwYC21ttTjsdMu_Eev3qtlYhDeMqkpsWBdDaZ9DSKSNtIvIEg"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "68390aa2-802f-47cb-aaa0-1b55e88d0a4a", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAsBEk0wt3yt6lcZOCsP4ai3U8eeW8qM086yU0fFvUC4JwXu5A4IW9GXYwiP8NVIcOhGakFZprWD5cLD3yn+ViEFNq1YzLIlc7Iv+PUhsaE13IEyme5EalNTFj1OBF8akmo1NgVFwfB/R7rXAz87ngbech49gXr31aOjdLH0bUDGahyBHJTsDAqS7ZRiGVFN+YQENeAaeiAMhffhc8w5fUemuX/Qt1RvcJR05KqGhT6fRxPc0+U5ZsepbcQbuvA1DVoM6ZYYu7bfF/nFzmmiEzXatLijTQOypiapfH45YEljs7djw0N8PFmRc8tQ4JLUE8ZmmR/U6i6YGcKy0S2nIZSQIDAQABAoIBABnmm700Pa4pbLs1kQ9KgksrtvbTtho6/xA0i/ETbTE4QiGk+QROiE/7lA5DPwnFSHxvwQmwisQTgGm9vziIfRGbw+agbK2jmX5/i+zvMbnRnQI8qDQ6tEkL9/yz9XYkhRuXw18FokcAT0VncS0UlhB05QZonqthTGmuAq4aNE2SeRelXktAukXmGm9OzVTF9PXievOcA0DP11GI+req3uSAoO8t7Eec5W4bhsGm2u/h4W1kt2+4mnFEXfNVd5pz9GAmQRrIOWfBmuqiaRoRRvGovN5cX1pB7p/s2ZXaXPV2hXKGEf+jV9SAxLPcVxxNJ3d+uasVrnfWOftgEdgnhFECgYEA13v2b/ufbyr10pI7m0wkZCOwSSYGVskYbXeawmlbsJB+9JmcZekfrm3xH9nDusUOfAd4nhlapAC1Db86+WsGlA8NfJaa+ZNCITNb7atwasmdjsTbf2cN+u3rrLJ9kFikrvmtviVT8fe5F0p90CiGwStAI8lgj7zGn+ZEJ2QhSuUCgYEA0Svir5RsjmqJa6yCn3zcZvcfa3jn7dxlCQKuZDCcP4u54iOM65XIxqe8rtNGBsNR435yBl3Cv75XZ8GwPcnnPlqIjBphC8dvAyqzfznhHDI9abx5RFTwmDCBQyLCFNAR1oQOrE0wq6v5IdCbPjuAM/991sZPDy2Pyb+3oY75WpUCgYEAgP2czPYkP4jI9I9DDTisRCG1AmS3wLXKYKULSDuHfo9Q4B0gKUU/CMyZXcaeyyvw+sACLSniP3CI8Ty7EdlGa9UC7sS86o+DM5qp+gD5X4NnRLKE7qQDE2XfAVcroP/fPTsLDVsNADiZXN7jp3rLB9FbKNEAc6q+f7N4stfWsh0CgYBAGRWvPmmBvu37m/KvUwDrYKE1Oui/wEEJ4NN2CltnMbc1sDU34D+VeMNeQ4n6+pG2iAIDJ2pNfdOlDPqoBJ4jQnUDkfXZOTUwoD4/J4pvLIobQ9FZejn8OSjzXMxiulwtA2zHqiPXB60hip54FJ210X5LeZuyiAC/D0wDax/IfQKBgGj/ouvDVi5Q1Bws/RWTHKns448dvlQIWy+9Kj3xTSQKy/5+jE/0rwQnJTSGUTA1YtN77aszGLHzicb+wGz962eLGSXw/g0TcOnNhfFtQaNoodMQXz6CvRUUv3MYmV6jdz3HDH4k6GUeYNtW7XF+4/E6XnAJ6JYvswgHbNBiWT5H"], "certificate": ["MIICoTCCAYkCBgFpCtVMejANBgkqhkiG9w0BAQsFADAUMRIwEAYDVQQDDAlNaWdyYXRpb24wHhcNMTkwMjIwMTIxNDA0WhcNMjkwMjIwMTIxNTQ0WjAUMRIwEAYDVQQDDAlNaWdyYXRpb24wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCwESTTC3fK3qVxk4Kw/hqLdTx55byozTzrJTR8W9QLgnBe7kDghb0ZdjCI/w1Uhw6EZqQVmmtYPlwsPfKf5WIQU2rVjMsiVzsi/49SGxoTXcgTKZ7kRqU1MWPU4EXxqSajU2BUXB8H9HutcDPzueBt5yHj2BevfVo6N0sfRtQMZqHIEclOwMCpLtlGIZUU35hAQ14Bp6IAyF9+FzzDl9R6a5f9C3VG9wlHTkqoaFPp9HE9zT5Tlmx6ltxBu68DUNWgzplhi7tt8X+cXOaaITNdq0uKNNA7KmJql8fjlgSWOzt2PDQ3w8WZFzy1DgktQTxmaZH9TqLpgZwrLRLachlJAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAHjfTLIBH6ts4Lxpjj6EhDoFsIRoqjOOIEkIPKYcrNfHsELJ54bxoNY10v1WwU0j6sJDaOIApKjP48vZcO6kG9EBX+NYoT74tqKLPd7Ahy39S5sExRAOGEDBOApGPde/krYTlFp+H6ZNZdo0ZFgPuJTDgkcqCE11w8H0eWD2jUcndBm/YmQf323t3XnwQCmdi9+AGuLevRNsL04tUMW/jtwWYMzfsBoDJeQMxZwodq4ub6/M6J5/HMmP7OJbjKcyumkHH82Bey0qcihfkY0WY7eVkG7iwl6PTVZvGmjRHz+fh5w4noexV6L0W8YSeZfZ9wjDjuYmGEKduVpJ8BsIeqA="], "priority": ["100"]}}, {"id": "724e2008-5b0f-42e6-a13c-a91acc6b93b3", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["2f8c23fb-0872-44c8-bded-af0fe11dc38a"], "secret": ["oyEEoitUedtWtXh2hqTLuQ"], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "7629e5cc-570a-4d69-8ff9-0ccdd991342a", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "e97de18d-745b-4fdc-80a1-414bae363002", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "4313858e-affd-4050-bcf7-8df4a04ce2e3", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "82a5f8f4-839f-4d7c-9bf5-cb27eb345a70", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-x509", "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "10731d78-d59a-4405-9bed-ec7b6351e306", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "OPTIONAL", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "b644e6fd-0087-4289-b486-c4d9e29cc128", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "319519f0-d1db-4c91-bcad-5fd75c1a4e5c", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "60fd7436-6ce1-4c68-aed4-916629fff598", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "31da167c-1101-4960-a812-0471ba373fb0", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth-otp", "requirement": "DISABLED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "57886926-43cf-44bd-9c8c-2ac1ec5e57e6", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "eb0f088d-3c9a-4815-8cc5-069051d93b49", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "6356a433-bfe8-44cd-af85-3f9699b0d040", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "OPTIONAL", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "169a8cf7-73d5-48e9-8bf7-91f15339f115", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "9b909619-f84a-4e3c-9f39-8b96a540db4f", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "b258bd9a-1f96-4c45-9ab4-55a5f98b319a", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"_browser_header.xXSSProtection": "1; mode=block", "_browser_header.xFrameOptions": "SAMEORIGIN", "_browser_header.strictTransportSecurity": "max-age=31536000; includeSubDomains", "permanentLockout": "false", "quickLoginCheckMilliSeconds": "1000", "_browser_header.xRobotsTag": "none", "maxFailureWaitSeconds": "900", "minimumQuickLoginWaitSeconds": "60", "failureFactor": "30", "actionTokenGeneratedByUserLifespan": "300", "maxDeltaTimeSeconds": "43200", "_browser_header.xContentTypeOptions": "nosniff", "offlineSessionMaxLifespan": "5184000", "actionTokenGeneratedByAdminLifespan": "43200", "_browser_header.contentSecurityPolicyReportOnly": "", "bruteForceProtected": "false", "_browser_header.contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "waitIncrementSeconds": "60", "offlineSessionMaxLifespanEnabled": "false", "custom_attribute": "custom_value"}, "keycloakVersion": "4.8.3.Final", "userManagedAccessAllowed": false}, {"id": "Migration2", "realm": "Migration2", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "f79a54f4-039d-4283-8547-43ef439ca5e8", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "Migration2", "attributes": {}}, {"id": "618baa2c-12cd-470d-8581-74f005a09e97", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "Migration2", "attributes": {}}, {"id": "a495da40-f44c-4e28-8f82-75bb5677e597", "name": "default-roles-migration2", "description": "${role_default-roles}", "scopeParamRequired": true, "composite": false}], "client": {"realm-management": [{"id": "6bfe361d-318b-4eca-b683-34c955a7f28b", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "9829d879-cfdb-4532-91d6-e526501abbc7", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "230bc8be-72c1-448d-a4d2-5d449eb10b80", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "787e89ea-5c14-4828-a351-7942b8bf6db0", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "b5ca9c4f-231f-45a7-ae4b-461435e4d030", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "c51f9f46-1918-41ef-8bd0-d1e53846ada9", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "c731700d-407c-4144-af26-da63d48e5780", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "d0bf0ce6-6c49-4872-9b85-cd054e5d4501", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "56b5aaa4-20db-4178-8aa7-b921a2d06871", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "f8b3c048-7547-4fd7-aecf-fc9637939d14", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "b5fb7e53-474d-4ed7-92af-14928d634167", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "14aebf12-45ad-41ac-a473-548c05c0e7fb", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "ca78242c-30a3-42ce-a314-8a2ada0b472c", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "f8abc67c-600b-4326-8035-bcc17cb2ef07", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "22007ca1-7df8-4cb2-af0b-96af7e1f26e7", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["view-events", "view-authorization", "create-client", "query-users", "view-identity-providers", "query-clients", "manage-events", "view-clients", "view-realm", "manage-realm", "view-users", "impersonation", "query-realms", "manage-users", "manage-identity-providers", "manage-clients", "query-groups", "manage-authorization"]}}, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "0366bea9-43a8-4b0d-8b68-47c8a0eac489", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "09da1581-9de5-4818-992c-f23d13843162", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "4da30d67-342b-4dda-a637-1761f9470662", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}, {"id": "22df3021-8d3f-4dc6-943f-74ee52c20c5a", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "a12cb803-94f4-4972-98bf-fcae00219953", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "broker": [{"id": "e2470e7a-6c0b-47a7-a171-7421c22cba24", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "0980e126-19be-4a59-b176-b209bcd8e79d", "attributes": {}}], "account": [{"id": "312f44a8-3d5f-4d6c-8a3d-48755708cbd8", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "16cb27cf-6fa6-49bf-aaad-1f3793494666", "attributes": {}}, {"id": "c04e1626-9a44-46d1-b4d1-b47d3900bd89", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "16cb27cf-6fa6-49bf-aaad-1f3793494666", "attributes": {}}, {"id": "7cba8a70-e5ef-42c0-b766-77bd85d9b40a", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "16cb27cf-6fa6-49bf-aaad-1f3793494666", "attributes": {}}]}}, "groups": [], "defaultRoles": ["uma_authorization", "offline_access"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clients": [{"id": "16cb27cf-6fa6-49bf-aaad-1f3793494666", "clientId": "account", "name": "${client_account}", "baseUrl": "/auth/realms/Migration2/account", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "47b13acb-2a45-47d6-a470-24c8cfcb11e1", "defaultRoles": ["manage-account", "view-profile"], "redirectUris": ["/auth/realms/Migration2/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "5d11b989-cb45-45cc-87a2-816c5fb1d88b", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "d8eff81a-64c0-4051-b08e-a3b8e7f5deb5", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "0980e126-19be-4a59-b176-b209bcd8e79d", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "168ae077-598f-4873-963e-8f8a12257064", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "a12cb803-94f4-4972-98bf-fcae00219953", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "f41cb50b-7e82-4a81-a7b2-8cd61a0513b7", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "b986ab7a-a474-4866-b3e3-64c705d91a0c", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "baseUrl": "/auth/admin/Migration2/console/index.html", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "0cbd9282-86d4-4980-8bd1-f9c90e5256f1", "redirectUris": ["/auth/admin/Migration2/console/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "********-0383-4e46-bafa-3784aefd2a7c", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}], "clientScopes": [{"id": "f9d93d84-19d2-4e9d-905a-1e1048c4359f", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "66eeaed4-6da0-434d-809f-138c8ec39edd", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "72d504b9-fb9c-48cf-a218-89fd93b25c2c", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "b583722c-3dfa-4499-845c-436a72d7d245", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}, {"id": "cbb2a173-0a9d-4bd0-9959-875ea6fc7f25", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "8e3ce44e-e922-4d52-98c4-8a07e8667096", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "eef0f8ef-a504-4304-b1bf-fe51d87e8b8b", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "10600960-c1d2-48bf-b437-f27f0fac4fcd", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}, {"id": "31b6829b-27c2-47f2-a794-8a8c02a95551", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}]}, {"id": "4d157645-72e9-4598-adc7-f620828aa6de", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "35e05df8-9f8d-4062-aee9-35e9f18d6b6f", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "8e6f54d6-c2d1-4069-9a4c-cfdd170e5d00", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "ef56eac3-7012-4432-8648-19f3b4fd1703", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "cae2476d-4469-4b6b-ad0c-6d5ac0a056f4", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "67005649-2a50-4662-ac67-f3586bf33663", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "1e08b346-2709-4786-afc9-67960b176b96", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "2edfdbf8-efd4-4307-a540-4a987385f8e7", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "88b9ea0a-3c31-4551-add2-998cfe8befe1", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "8704b505-3822-428c-9158-dc22ee89c238", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "b8cab226-b105-4094-98ef-52b3436efe9c", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "5c2d8346-9f6a-4862-aea4-8693e64f883d", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "10cfa1c7-e90e-45ed-9e78-b60f3542ba41", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "6cff6464-a1e2-47a5-9397-5d24e2fdfbdb", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "74bd7e3b-6a59-4826-8192-c77958d2432a", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}]}, {"id": "3de5a6f9-e0f5-446d-9bb2-f8358d12b3c4", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "78eb395a-fb19-4633-b7c0-96843cfb9a3e", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "6bee2974-78d9-46c5-bf75-1c01ddc34bde", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "23cc51e0-6a39-459c-a39f-d69216619e97", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "2b65dbc2-43ff-45ff-950e-0c6b67137992", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "91e35e13-9101-4023-abd8-d533e7117151", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "6ca2e8d7-4140-47c2-b125-07092de4b7c3", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "299c1acc-9028-41ef-b4a6-86f0a0d73fc1", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins"], "defaultOptionalClientScopes": ["offline_access", "address", "phone"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "xXSSProtection": "1; mode=block", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "76c0467b-2e26-4bb5-8d26-f7f257a71262", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "oidc-full-name-mapper", "oidc-usermodel-attribute-mapper", "oidc-address-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-property-mapper"]}}, {"id": "56dd5d03-6197-484c-b7fc-d5d6f04b5954", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "61ec13d9-16eb-49b3-ae94-8982059c917a", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "993dc81f-3ecc-4464-9c32-b5d5d38efda1", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "oidc-usermodel-attribute-mapper", "saml-role-list-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper", "saml-user-property-mapper", "oidc-address-mapper"]}}, {"id": "96a9ac5c-5713-4df9-820a-ae37d4f33828", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "41065ea2-9086-4d21-bcc1-008d37d9048a", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "d6f27205-7a4d-414f-9fef-30eb42f0d612", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "3e7550bc-39e0-4abe-88fd-e2de504cd064", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}], "org.keycloak.keys.KeyProvider": [{"id": "3eaf2c28-4a3e-4b58-a34e-2ac847e0aad1", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpQIBAAKCAQEAv/MHA79HlIu9DBO0uYfGS+CleDwsbwNVst76c2/Mvx06vuLZj+GYbRtLBxwVzfJyFJat6UTBNDn5KUl+CuigB9rv4/EOtUeqonhNknFd3OjCYVZzX3//t6if1OOpcMK42IjQRpjutJJPrTaJI5VC/yXg13iv6gPCTKM0BG6nZqtlt36IyVQEhwC5CTXJ2+Ln/CcWAGbn0zpwhO4VdtUMu5DLE37turFPAv6I+Xx0y8mxK7hkx5PBpwWp4Btz53/LvoQIpwKqgrPqnUnFpI3qpn9Khpw8e1JG5O+PGqENVE6azLyuvtxhfOPwjn7NTztF/yW1KnW0tNIH7l1icmf/mwIDAQABAoIBAFb8CAq/rO15IdOOPpDxIYCTLgk9o5kvNq/XckHLwUo+Mbf9T0BvqEaRXr+7D3qDywTff7I9MRy8xNfb8R16a8MDiUCPpe2qq33y7JxXeYS5ihTYO3Jkq/qLlNrBzLBH3cJqxY+okEwOHcNN+nnAk1rSzIB4tadjyrI2J0V266+PZWNLzGG/KHRiwGekjD7KZ9b4sM8rehEkUG/ESXZU902UnqfoFHGo9Bci0seo6PfdKGBdiz5LhUWqTMVd+tHrlO31K5BarNew+o/HvBnSaKcx20mJ5Uh5iMo2w3c8cstE2+FnVWr5qTgm50MRGCIEeGQ0wJ3l6Vm0m+Nq56/pYHkCgYEA6MJqNQQU77yxxiXNXRhRfi5A16SisArrzI5ayqr8MsjufLd2NrPYhjIg4cwVu2HAqYp4cQu2BgImGDrJmT05uF6pGNzdRwgmrYw2qyp2AqQspTowpCN64/L207rnLQm6AoTXzt25SR0q8PGowrfeySgfdJoqN6IyKmlXUuE4Sv0CgYEA0x12LqPmefKhUB3rbglop0Kmd6oKY8o3rlCcm7SG2qrJ2g5unYx+YcYXgFl6IXUVtR3QxLbRJKYpTsauOyxpjCr9L/WskBmxPcjCqg+iXMJin2hj2O/WEL17EzFRVa/kZVXfoGi3sMqJ08f3cze4HODXVgeu9Ym7xWsbDJYU9HcCgYEAoG6ArhImLCgvkJg1uNkuMn7HCtvbluOwK8pjIhZAxvBgEW6S+Jl1wOmEaJ42zHVQSou7AJBF4lZQ05qRoiHlSwNUImL50h/4KN7gP5KAzMlETI6TQE+EQOIS51WISi7OGzZdWaTSNWb5ViH7ZFtAH/6bz88DEl2lnj4FhobeOjECgYEAmnIeSKa9cQ+Q9fS9jMWzW1IGSNsMOM7yh0MQH1Ulpwl1HGKciAQFXXA3Nz2+fIWUuOi0F4WLYd1xsODnDAFEwjec/J4lfVJeeU3LI/DGHkhTMD2FdmqXV63XQUY+M4q3c28sZ8TURbEWvem33QGxXucagiSi2ZzG0vm1UzLk56cCgYEAn0R0cQA6MvNFqqO8ZHRkh5AmAAVUnNNEWlgmNqWoH894kGmNucQKhFWLYQGl91AnTdvwiqQqhrxOYMQl+W/rMXSU2jQUFzmZjP7dqJNTJvfRxv2xurRHWQkfHBQzhj46MN5ue/pbewAIoS+qonqV6LP13tPLMhzYNqScf/DXmEc="], "certificate": ["MIICozCCAYsCBgFpCtXb2DANBgkqhkiG9w0BAQsFADAVMRMwEQYDVQQDDApNaWdyYXRpb24yMB4XDTE5MDIyMDEyMTQ0MVoXDTI5MDIyMDEyMTYyMVowFTETMBEGA1UEAwwKTWlncmF0aW9uMjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAL/zBwO/R5SLvQwTtLmHxkvgpXg8LG8DVbLe+nNvzL8dOr7i2Y/hmG0bSwccFc3ychSWrelEwTQ5+SlJfgrooAfa7+PxDrVHqqJ4TZJxXdzowmFWc19//7eon9TjqXDCuNiI0EaY7rSST602iSOVQv8l4Nd4r+oDwkyjNARup2arZbd+iMlUBIcAuQk1ydvi5/wnFgBm59M6cITuFXbVDLuQyxN+7bqxTwL+iPl8dMvJsSu4ZMeTwacFqeAbc+d/y76ECKcCqoKz6p1JxaSN6qZ/SoacPHtSRuTvjxqhDVROmsy8rr7cYXzj8I5+zU87Rf8ltSp1tLTSB+5dYnJn/5sCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAWhqPylJI3LnUQTQ4FQ/LwkZCavoku4sAa5/ccgLNR62Iz2umOy53fbrV30gdbUjz2cMeOY5sHUuuDKMWZvtl+z6OFINF5fv5k1ub0b9npg/CX2I2PjRqDWSEMZSOGbDidW9Ad6Is1dhZMNd00JdzII+poTFER/mirfHy5IK5m7JIMpsSVrRNj+1EWkKZFt0KtqcnVkcI6xeyCfv5cD5qxAWfJ6y9Bb8BM1f6i3Mly3rqKFfLtVRXkigWxY+sN++avHp2tAuK7opjHv9DId4a+VgtCAul7FCKGfF4nQ5z3X5SxbMgBFDw/s/arzDOQjxLGIGmzjvRG7P4sSg/6/tUSA=="], "priority": ["100"]}}, {"id": "713f5684-d80b-44d5-a68b-47278ec81249", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["162b2a67-4fde-4077-99bb-c931c6f6a3c1"], "secret": ["_CES6wRuRYqTqIiu7iNn0g"], "priority": ["100"]}}, {"id": "857a5fbf-b772-45b1-9982-6918a82ccc7e", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["eaddf301-a136-4604-9828-6a55a5e042eb"], "secret": ["yGKvAgU0JxKnkghdhAxRj5lBvbFadiWGkOFKTST33LQuEHz90Zbn0JZP2HIsiRuAKUPHUlZErNYhKBFBVbWb_w"], "priority": ["100"], "algorithm": ["HS256"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "9eacaf64-f8dc-4f0b-afc5-4854f7986b8b", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "ebb78b05-a4e5-4a03-8dc7-4ea72e618033", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "f7c21eb4-a2e3-414d-8764-9439bce0031c", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "3b586b1e-5074-4028-9495-249c5382ce7d", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-x509", "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "995449e2-72a7-4b4b-88b4-9d180ad3b4aa", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "OPTIONAL", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "1d8be4ca-f7cd-4aa0-a81d-0b0dd215acb4", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "fbfb29ba-9b54-4496-8107-4e0c037f58e8", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "e9701c42-574c-4ed8-9e4f-2fc5e50a2367", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "e8d38021-192a-4938-9725-d7a0a894f54e", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth-otp", "requirement": "DISABLED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "c34c0b45-d524-44c8-9eb0-54260c0498f6", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "b71ff027-8901-40a6-a53b-50c818e97e48", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "5a813394-16c4-45e4-a693-39edc855c345", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "OPTIONAL", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "2e2115a3-0df4-4773-a319-8fc87a47212d", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "ffa6a3e9-8a61-494c-bda5-7d99cba5c2a1", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "8377ae36-d0db-4b9f-af0f-24d685b9d981", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"_browser_header.xXSSProtection": "1; mode=block", "_browser_header.xFrameOptions": "SAMEORIGIN", "_browser_header.strictTransportSecurity": "max-age=31536000; includeSubDomains", "permanentLockout": "false", "quickLoginCheckMilliSeconds": "1000", "_browser_header.xRobotsTag": "none", "maxFailureWaitSeconds": "900", "minimumQuickLoginWaitSeconds": "60", "failureFactor": "30", "actionTokenGeneratedByUserLifespan": "300", "maxDeltaTimeSeconds": "43200", "_browser_header.xContentTypeOptions": "nosniff", "offlineSessionMaxLifespan": "5184000", "actionTokenGeneratedByAdminLifespan": "43200", "_browser_header.contentSecurityPolicyReportOnly": "", "bruteForceProtected": "false", "_browser_header.contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "waitIncrementSeconds": "60", "offlineSessionMaxLifespanEnabled": "false"}, "keycloakVersion": "4.8.3.Final", "userManagedAccessAllowed": false}, {"id": "master", "realm": "master", "displayName": "Keycloak", "displayNameHtml": "<div class=\"kc-logo-text\"><span>Keycloak</span></div>", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 60, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "c17ba844-ca9e-4a26-bd78-f18f16855325", "name": "admin", "description": "${role_admin}", "composite": true, "composites": {"realm": ["create-realm"], "client": {"Migration-realm": ["query-clients", "query-groups", "view-users", "manage-identity-providers", "view-realm", "create-client", "query-users", "manage-events", "impersonation", "manage-clients", "view-identity-providers", "view-events", "view-authorization", "query-realms", "manage-realm", "manage-authorization", "manage-users", "view-clients"], "master-realm": ["manage-realm", "create-client", "manage-events", "query-users", "query-groups", "view-authorization", "view-realm", "view-clients", "query-realms", "manage-users", "impersonation", "manage-identity-providers", "view-identity-providers", "manage-authorization", "query-clients", "view-events", "view-users", "manage-clients"], "Migration2-realm": ["query-realms", "view-realm", "query-clients", "manage-authorization", "view-users", "manage-clients", "manage-realm", "create-client", "view-events", "manage-users", "query-groups", "view-clients", "view-authorization", "view-identity-providers", "query-users", "manage-events", "impersonation", "manage-identity-providers"]}}, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "c7796d7c-1cc2-4d02-8c7d-cfcbc53ccc66", "name": "create-realm", "description": "${role_create-realm}", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "a73eef4c-8283-4c30-a5b9-de6b79b91db7", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "2e9a2991-29e3-4915-9c7c-a691663664ef", "name": "master-test-realm-role", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "e2ee97af-a684-417c-8035-ebe01d4d3f67", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}], "client": {"security-admin-console": [], "master-test-client": [{"id": "eabded89-9521-40a4-9518-82f5f96c3638", "name": "master-test-client-role", "composite": false, "clientRole": true, "containerId": "d90e9606-d303-4033-b246-3f925295207a", "attributes": {}}], "admin-cli": [], "Migration-realm": [{"id": "ae87300a-5af7-4450-b12d-1efacd4a990f", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "4e4e984c-d272-49d1-90ec-852af5b89226", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "fd5bdb21-b9cb-4b4a-a848-3b9d8420af84", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "b039e96b-0a2f-4ecd-b495-d6f36e6a953e", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "d91c7c61-6acf-4ca3-929a-a8ec014bc464", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "376983fb-7a53-4c7c-8944-646ab6f31fe4", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "7dcd155f-6b1b-428b-8b90-68da4e284f0c", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "2251a0fd-f8a7-4232-a98a-73232d1f3bbe", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "888307ef-3dde-44df-a12e-7f8241c45b66", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "fb80b34e-0e2a-43b6-9e81-f6263adb5f89", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"Migration-realm": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "3a332e3a-6cd9-42cf-a90c-aa54afdf6c15", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "6a0f8723-add7-43ea-a769-765fe13122ec", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "883a647f-a5f1-48a0-9646-71281e5f28b4", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "1a4add46-9f8e-4ac2-8939-6be7669bde39", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "9d9557f1-848c-4e40-8771-10f9c4573294", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"Migration-realm": ["query-clients"]}}, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "7a320a3b-8210-4768-84e8-46bdcad35370", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "d06adca3-6d5e-4e02-ab55-239d3d3362f7", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}, {"id": "7549e0d2-f3c2-4dda-8402-569d59a27dc5", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "attributes": {}}], "broker": [{"id": "c217575b-95a4-4423-93a8-946f7ab4ccb6", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "b80e487e-99a3-4212-b4f4-6614a49a37eb", "attributes": {}}], "master-realm": [{"id": "dafa7911-f259-4efd-8e9f-badbf5052f3b", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "6986b2da-a0b6-40ab-b715-a3607d98d4dc", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "92af2cb7-d4a7-4195-b652-dba612813cb9", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "a2af47ce-6169-4356-b72f-979ed3c714d0", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "21de0808-9df1-4358-98eb-a95eeac34b07", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"master-realm": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "9cb9fa62-4a86-4501-9fec-d12649055130", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "a6203651-ab22-4c71-a592-1d1eccba1872", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "a3fb2b73-4fc2-4b78-8865-93a0fe05c6f5", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "f6b63a7b-4974-4a46-b604-cc15d8ac38f6", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "ece380ce-33b9-4855-8844-3bf1a8f6c6ac", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "b80e8389-856b-4be0-bcf8-bb31fdffcc0a", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "0d6ae815-1bce-460e-a016-95ea82e76e63", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "eaf5a79b-5acd-4d45-869a-e526792e050d", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "d037dc77-c538-426e-b94a-78fbeefed8f9", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "f65ad233-10e0-4bcc-a9d0-1516929a7b6e", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "d39380f3-6526-448a-86f1-68eedd991350", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "d51f6b7c-3d50-4208-ae6d-e83c25217328", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}, {"id": "23f9cbcc-072e-453d-a221-ad7ad9cfa450", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"master-realm": ["query-clients"]}}, "clientRole": true, "containerId": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "attributes": {}}], "Migration2-realm": [{"id": "43ba5a74-b186-4b1f-b743-1dd82f215b2c", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "26a5f512-06aa-485f-8991-84461b6a5ea1", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "b589ed0f-08c5-45b0-9b22-b48f3ef20f79", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "9147d4fe-89c1-4861-b9cd-a96724221e0f", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "04a27bf8-6fef-406f-a6e3-21be1c2f28f7", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "6c3389b7-763a-4fa5-81b9-58e4b341d376", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "904efb53-5c67-445d-9f27-fd47227a9038", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "bf99651d-bcf4-4731-898e-cc73d8e9735a", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "6ac10c2e-3bdb-4026-9b71-3eaac37a1816", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "abdfd3a4-d3a9-4636-83dc-4781d353c97d", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"Migration2-realm": ["query-clients"]}}, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "aee8a20c-e3cf-4c8a-9562-ca051ed2415a", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "800f0b98-1211-4a3f-9ffc-316200c04b42", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"Migration2-realm": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "b8a6d7bd-af51-44f7-99be-4beb4abfcd76", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "948443a4-0456-4971-adb1-a98d133b34b8", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "ed74f2ed-b8bc-4339-adaf-a46a79841732", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "b01fa62b-4026-4997-80ee-52a355e271d4", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "6e46af49-d150-4922-b0ea-1fc1088de6a8", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}, {"id": "199e3eb1-f9d8-4e87-8de8-9bb00d4d6c69", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "attributes": {}}], "account": [{"id": "2fbf813c-5318-4121-b1ed-a7f09ff8eec4", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "306fc5a9-5977-4946-848c-066a988801e7", "attributes": {}}, {"id": "3436368f-a10b-4708-8992-cf0e5c0f4550", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "306fc5a9-5977-4946-848c-066a988801e7", "attributes": {}}, {"id": "d9253df6-0a4f-4972-b6b6-698ba3564499", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "306fc5a9-5977-4946-848c-066a988801e7", "attributes": {}}]}}, "groups": [{"id": "8f6cb0b7-1af9-431b-83e6-fc2d144b74b8", "name": "master-test-group", "path": "/master-test-group", "attributes": {}, "realmRoles": [], "clientRoles": {}, "subGroups": []}], "defaultRoles": ["offline_access", "uma_authorization", "master-test-realm-role"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "users": [{"id": "e14fa9cb-d424-4e4b-9808-a7ebf937a9bf", "createdTimestamp": *************, "username": "admin", "enabled": true, "totp": false, "emailVerified": false, "credentials": [{"type": "password", "hashedSaltedValue": "JOrh81WYkyhsdJSYiQZEpbtV4FQYEMmqRxncHiBZKunm8g0zNqQOezqEF20IJZWvxvudT6wAcmTzWsv4Qd1tvg==", "salt": "no71Rq8NWrZUpRY8cPxo2Q==", "hashIterations": 27500, "counter": 0, "algorithm": "pbkdf2-sha256", "digits": 0, "period": 0, "createdDate": *************, "config": {}}], "disableableCredentialTypes": ["password"], "requiredActions": [], "realmRoles": ["admin", "offline_access", "uma_authorization"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}, {"id": "ac3e6b1a-2e8f-41cb-b3d7-ae3ce5ab4ee9", "createdTimestamp": *************, "username": "master-test-user", "enabled": true, "totp": false, "emailVerified": false, "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["offline_access", "uma_authorization"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clients": [{"id": "32fe06e0-df1e-4538-b00f-d9a5f90dd796", "clientId": "Migration-realm", "name": "Migration Realm", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "6d38417d-9219-42a5-aa34-e70111c1c6ff", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "a663c142-4534-46ca-8fd8-32f4b2b57ecb", "clientId": "Migration2-realm", "name": "Migration2 Realm", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "1c6efd75-610c-49b2-89cb-739d91428f93", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "306fc5a9-5977-4946-848c-066a988801e7", "clientId": "account", "name": "${client_account}", "baseUrl": "/auth/realms/master/account", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "afd01c15-ca9f-4f39-bde9-d9baf7468540", "defaultRoles": ["view-profile", "manage-account"], "redirectUris": ["/auth/realms/master/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "98898a27-448c-4245-9ce2-cd8234d9ec49", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "2d98cce2-bc13-4b66-9b0d-e504d24dcdd2", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "b80e487e-99a3-4212-b4f4-6614a49a37eb", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "06cf4e0f-efaf-4b7e-9f87-3bb6b2ac58cf", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "07c5f77a-3fa5-4a11-b7da-5fa95bad5dd6", "clientId": "master-realm", "name": "master Realm", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "03cb055b-8eb3-4391-a5f8-56b3f66142e8", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "d90e9606-d303-4033-b246-3f925295207a", "clientId": "master-test-client", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "21e5b3ab-d978-4552-bbb5-dead19284b5d", "defaultRoles": ["master-test-client-role"], "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}, {"id": "e5665872-410a-40b7-953d-771a153202f4", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "baseUrl": "/auth/admin/master/console/index.html", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "a3005404-45b2-4203-9c4a-5e77692f34a8", "redirectUris": ["/auth/admin/master/console/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "e6a36298-adc0-44f4-b2a9-d2ae80a3768c", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access"]}], "clientScopes": [{"id": "0bb8404a-8ef7-4020-af65-e436a702f4dc", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "ae0fabcf-4c4c-467b-8598-eb74d918b8df", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "c22213d3-08c8-4ebb-8206-b16d58214a19", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "6d56708b-2bf1-48e8-8700-fffc3cd0b00a", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}, {"id": "96122304-fc29-4004-a3c0-f2423e4de989", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "325df4ea-16f8-4952-bde8-8742dea8526f", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "5f96defc-c1f8-467d-bc63-7e3d02c58e31", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "ab28f890-6af1-47ea-83de-5b8c6b992309", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}, {"id": "b0d9fc38-66df-4d28-8244-6ee6e85a7f03", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}]}, {"id": "0e3d1a86-9dc6-4419-a5c7-a0df594c3fe4", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "ff44e729-b576-4085-9a69-63af963551a5", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "8bb9069e-6e28-4d3c-a119-bf8cc0c42629", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "7b73e899-bd7e-4e0b-87d7-1f7509841303", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "01fe1290-4fdb-4c09-8240-a3f401bd4b22", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "f3c7edd0-5f20-4032-8f3f-8b28284ace9b", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "2de0ddd3-7441-44de-a87b-868752faa208", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "2a9be98d-f85f-4b0d-b58e-3543f198a8c8", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "0160fd5f-dcaf-480b-9aef-22344e8d6dc9", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "969aca0b-73b0-4409-8d71-381e4cde53cd", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "14132fdf-dafc-47eb-9a46-3959aec966d5", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "601e3363-b8d3-4631-b14f-7bdacd52bd9a", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "cfafe487-2cdd-46ef-b613-31a3d7a1a551", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "1b4a75b4-4e43-408b-a3f2-9b64d8304ec4", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "869c7de1-4e14-46f2-bab1-2d6e55453ab5", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}]}, {"id": "e0ffb279-ffa0-49b5-92f8-5856db65d8b8", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "5f62b02f-6f5f-4bd1-8dfd-14626fd2d690", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "042a7fc1-3e84-4c4b-90cd-e1de2e5829da", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "3d2f1986-93ec-4526-8a84-8f2544e372c5", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "08775f0f-df69-406a-a961-64f412a63def", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "8d6a6f5b-9d37-4334-98c0-8228b4f6f48d", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}]}, {"id": "2849ec12-758c-4d01-9324-b1b05feec488", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "24a72853-7ae2-4031-beaa-265a546a59b4", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins"], "defaultOptionalClientScopes": ["offline_access", "address", "phone"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "xXSSProtection": "1; mode=block", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "218b4da8-e311-4c2a-a2c7-71bd3d19f304", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "188cb41d-13c9-46cd-b2ac-7f9f9876bc4a", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "0e2953ba-84e2-47f1-9219-d5bcbbde774c", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "oidc-address-mapper", "oidc-full-name-mapper", "saml-user-attribute-mapper", "oidc-usermodel-property-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-role-list-mapper"]}}, {"id": "a6d6f5d7-1284-4c3c-ae08-90c928256391", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "3365f134-be00-478d-8f25-a64ff7800c7d", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-property-mapper", "saml-user-attribute-mapper", "oidc-full-name-mapper", "saml-role-list-mapper", "oidc-address-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper"]}}, {"id": "1f93b706-81d9-4460-b4d1-710a06d013b4", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "aea919cd-bf4e-4cd0-933e-5cf67990f780", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "a980fe0f-2711-4359-bb24-cf31f0967bd7", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}], "org.keycloak.keys.KeyProvider": [{"id": "f1ac0163-1029-4ef8-a485-1546315213fc", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEogIBAAKCAQEAw0OdkIZj1Hm13khGX5tCwc/lsIaORbMUzEfQrD9aJXtWrx7fTQWshA8oRpndD06KcfwY3GLHuKDnwCUknMsGGa+vO3zXb35w3qULzvJ4nLv3cYIEwywDJ/XBCjdziogcXOgrgmMbFRPv1YwDUjBxprgnQC/xcvxIkVWuXaCrrMiO1S3bDpKd75dNTd9MzJuMv+Nr6PZvwEWD/Zuv3tX1rVYB7QE3n4cLGJXf2w+I9bppLiyXsBEhuFVqoVgwj33nFiO86XPYK8++7L3iFSyg19OV4ylOu/tR3zcui9suYHv0/I1ltID3Gm7b9RI6hDf3ajXTgqecH3tpXyiY6ylLcQIDAQABAoIBABYvWnmPK0x6v5UPtR8rrrwCeOBDiWqV3CkRh73qPL3Bc6nV4h0IJf+jETpkRN92Vvh4YgK4HmT1sx77mVtPHdRRR6GEn52FSiGhsuPCXL3CBW0aDpx2Ja2mmFYm0rPMnxxXLIpV5D/S0g77J21xH5iHl74NNGuSClSo3XS5yoeI1rTAcHdBUrNstDoo+ICTen9sqy/DHz7PLdCVrZM0SbNqhah9nHZ56m5x9fkTL9HwKP+T9QLCxA2j3mCeqGdzNrdQPZ7lSr/zEIMFqkChmvg7WvovC0BzepDtjHJM2Q5QxlL+sI+EdgwfXpGH4izBDirp/U8JPjwG3wFvp5j/e/kCgYEA/EFkt8oLR5m03i8GC9/u9qF9DlfXCcKqpiF3cv7hfhXGJ/sfZ0jkdLuiH9L3t9zD62iOv/2HZR20v2pFmcfQikKg4goJTxI51i9XBuFN44xAFYkfGa/DWpkj5k8jcDWmHXQfD5KNiIDEC5bokJ7+QkFaQJe3z1w3NH2s3sWJ87MCgYEAximlnWYpEP94sMId2uXMdHaUwjgiY6e0+YKhlcDhHS42FJBVk2mZr5YUZcfUZkWXBI3RZMWXvHPZQlYvo9dMm+5u0Czysi1CAQKAUrNKXYQEtd08j2sEFacGg5Vx1ZM4swdC9ovyRy4BouKmrsFtUOfw834i+9ImVBaLcCMKgksCgYA05MlnuFfxJYvFYvrTlpsW3/nG/ig7BbSerTnMxSEnPQ2V6JRHJgOxbn6qSaRjy0jRndUA8Ppo+3ehA3mgq0NRGnN9sjkNeXbjMH4BFVEtu4TcfyokSrSd1Kqwaju2FanFV0oEC+jdjJKPE4+kc5JA9k8kaCvEsUNqIEgVVjp8/QKBgF5kpEFP0qwIJYN9VB9ba4LmoYHKn4b88hYQhcwB7rmps5UkE79+g/jB8sFw6No2t/MxYtcv0FGylYEl8NeIzTNR5nM/lfQN3u0ANgQu53OYWW4QDrL6dR2jTFUNsQXddRrCkMFEdIo3fgwLDv8boGjzszEpmpx9xEg13QHzjyP1AoGAPfsTwHy3Og5GNUQeV47zPmU5WOOwJYXqTAQBFGwjL3Z3rlaoVjS8ULiJPSekRf/AGv/9ytP6/zUtqzVkoxlbOooCQQY4yYha3vMujZFTq3xpm4CLDY/wp7Sh26rslCf+EHVfrK2W0XkifGkwilsshahPRlPOsAyVCJmviTAsrrY="], "certificate": ["MIICmzCCAYMCBgFpCpepBTANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZtYXN0ZXIwHhcNMTkwMjIwMTEwNjQ1WhcNMjkwMjIwMTEwODI1WjARMQ8wDQYDVQQDDAZtYXN0ZXIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDDQ52QhmPUebXeSEZfm0LBz+Wwho5FsxTMR9CsP1ole1avHt9NBayEDyhGmd0PTopx/BjcYse4oOfAJSScywYZr687fNdvfnDepQvO8nicu/dxggTDLAMn9cEKN3OKiBxc6CuCYxsVE+/VjANSMHGmuCdAL/Fy/EiRVa5doKusyI7VLdsOkp3vl01N30zMm4y/42vo9m/ARYP9m6/e1fWtVgHtATefhwsYld/bD4j1umkuLJewESG4VWqhWDCPfecWI7zpc9grz77sveIVLKDX05XjKU67+1HfNy6L2y5ge/T8jWW0gPcabtv1EjqEN/dqNdOCp5wfe2lfKJjrKUtxAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAE25eRmqrJDg23bNqakE+cXk1SH1RaIxpyqk3uKyjOJM1U2vb1kjkEUZqmpI8ztfJmmKYmyR5BvbJrh5ZCBc33SYB/+ECQ//9AOQDiShWcO9Bz+TiqOwV176d+Lb3SIjLzW15PpgGoCUkuu3osr9IRoLDgaZrO4A1TQFWmvCvEvyW2Fz4uLOzgWJGGMHv3DWOa0WKoVUL4XJPl000jEOBCx7aTOTizUOGRYdAbg3ycGIUKztKNtyn9WdmO5bHOJdipk6ZeCq0hJQh5Z3wyoh7uZ9rt2HN3kvklCKDL1FzFG5qNCJPPieXDOXjiY/ycFfPeFTWfNZUOxeCLcIy5O9rQs="], "priority": ["100"]}}, {"id": "25208972-4726-41c0-9f2f-d05dddbb23ee", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["f9c3b758-4175-45e4-986b-42d00c66e93c"], "secret": ["GS3q6A_G0-CSs5FUv6gnNFLKZtFmOF6hZ0jlSpBHgQqRKAQxvXGYdHEpztgJ93Y247cq0UyeVy6ehw33iYFH1A"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "587b1997-a42f-415a-8f1b-e19f7f7119ac", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["e197f445-f18a-4fee-b26e-d0e7f6482bf8"], "secret": ["opj9umR6GqhiyBkpXC0oqg"], "priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "97a6e315-bd8c-408a-bb8e-4706472f429e", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "ab7d7890-0631-4856-8c82-192bb955ca52", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "2f846448-0e88-4773-bd94-a3aa7f40f529", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "90ac1d4f-6117-4a39-85f4-bdcf1c33472a", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-x509", "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "b3982b5b-0833-4810-ba44-490c91a9b483", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "OPTIONAL", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "4ab6c730-d160-4adc-b723-a55a3d39c5e1", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "********-af92-4f4c-80b6-4ce8ffd86637", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "3320f512-1306-44e2-8c12-a24b68ee0439", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "24d7c045-eea9-4061-8339-c99d8ac9884c", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth-otp", "requirement": "DISABLED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "e2184fb6-e056-43cf-a087-4a69f347e775", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "6b10b3d6-6e5b-49da-a43c-831a7adc3d7d", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "9f622b86-ff16-4a8e-8834-6d35d6a1386b", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "OPTIONAL", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "dcd96b0f-2359-46b9-94c8-dcf1960214a9", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "75969765-96f1-497e-805a-d54fb4700156", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "0fc144c6-2251-4921-832f-67fc60969900", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"_browser_header.xXSSProtection": "1; mode=block", "_browser_header.xFrameOptions": "SAMEORIGIN", "_browser_header.strictTransportSecurity": "max-age=31536000; includeSubDomains", "permanentLockout": "false", "quickLoginCheckMilliSeconds": "1000", "displayName": "Keycloak", "_browser_header.xRobotsTag": "none", "maxFailureWaitSeconds": "900", "minimumQuickLoginWaitSeconds": "60", "displayNameHtml": "<div class=\"kc-logo-text\"><span>Keycloak</span></div>", "failureFactor": "30", "maxDeltaTimeSeconds": "43200", "_browser_header.xContentTypeOptions": "nosniff", "offlineSessionMaxLifespan": "5184000", "_browser_header.contentSecurityPolicyReportOnly": "", "bruteForceProtected": "false", "_browser_header.contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "waitIncrementSeconds": "60", "offlineSessionMaxLifespanEnabled": "false"}, "keycloakVersion": "4.8.3.Final", "userManagedAccessAllowed": false}]