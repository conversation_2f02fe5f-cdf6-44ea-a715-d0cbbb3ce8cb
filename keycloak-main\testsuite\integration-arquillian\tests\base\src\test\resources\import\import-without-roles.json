{"id": "import-without-roles", "realm": "import-without-roles", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "offlineSessionIdleTimeout": 2592000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "defaultRoles": ["uma_authorization", "offline_access"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "clients": [{"id": "938ba3e1-eabc-4bf2-bf87-43a5824b2824", "clientId": "account", "name": "${client_account}", "baseUrl": "/auth/realms/import-without-roles/account", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "**********", "defaultRoles": ["view-profile", "manage-account"], "redirectUris": ["/auth/realms/import-without-roles/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "d5c720ba-dcbd-4730-a15a-2d2fba1512c9", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "d45c591d-ef75-4816-bc42-487ba44498cc", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "********-1f27-4222-87a0-f78dbc49829d", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "8e341cf4-c7dc-40b2-b14e-634a4aacea44", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "cc510e62-52ea-4035-81d0-eb7dd2fb3722", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "3b7ce41b-66e1-4e6b-9ba8-4eafc4fb8b79", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "aceec783-0b6f-4eae-a4b9-4d97217d8c9b", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "0437942b-0e5f-4b08-98e2-2c3a27ad1808", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "52fa0e17-9634-45d5-9e6b-cd6f9803e7e7", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "c9301fee-8e33-47c3-856a-1fdb532c828f", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "be22824c-00b7-4803-9cde-f155b7410aa9", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "149859e9-066a-426e-85bd-5476c0a67c79", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "e668c6e0-72e3-4394-a6fe-19047a9e29c7", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "e156734b-7039-4bf9-acdc-9c2f92b3981e", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "bd70aec6-2696-4936-91f5-18389a7c925c", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "0e8e108b-fa89-411b-a08c-b794e552825e", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "7e45f4b1-8323-4b5a-9009-54ec1ba8bc91", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "ce0da762-de49-4914-b217-cf289ebfd44a", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "9250e31c-fadb-4e7a-9d1b-341626fbe6a3", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "5579d355-06dc-4e9c-b1ab-d968e44005ac", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "********-fed9-407c-97ca-aec699ab8389", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "85fdc6bf-47fb-4eec-b277-aa1794b9a5f9", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "e7ddaa35-1627-49e8-987a-53772fbeacbe", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "618c7891-57a0-45dc-80b5-363f6919c550", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "4919911a-f927-4c0d-8cc9-2944abc2dd4a", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "24754ffa-4a34-44b2-8df3-87dc1e4d7698", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "ee2ab491-0b0c-4ac2-89eb-0a3f8e75b61d", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}, {"id": "b97fc203-0297-4eac-a1f7-35a7b6722f86", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "baseUrl": "/auth/admin/import-without-roles/console/index.html", "surrogateAuthRequired": false, "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/auth/admin/import-without-roles/console/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "abd17ea8-4fb5-4e66-9594-3c22ce38f515", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${givenName}", "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "26e30623-33ea-435f-9876-fe8469edc01f", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": true, "consentText": "${fullName}", "config": {"id.token.claim": "true", "access.token.claim": "true"}}, {"id": "8a6e66f6-9901-40b3-9469-65396597a6d2", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${familyName}", "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "5a50bd32-00c0-4d8b-b64c-8bc0c0a7b1a1", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${username}", "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "0cead636-2f98-4f6b-b261-22a258d795e0", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": true, "consentText": "${email}", "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "8698187e-6b0a-4c03-814f-4668de90948d", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}, {"id": "a7c95686-3b2c-4bda-a07b-cda77dfbad6b", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "consentText": "${locale}", "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "useTemplateConfig": false, "useTemplateScope": false, "useTemplateMappers": false}], "clientTemplates": [], "browserSecurityHeaders": {"xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "5b942906-8a41-4ccd-9ad9-ea3ae24e6d56", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "211d788e-3df1-4c01-9ee2-1085945544f2", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "16c3becf-16d8-4fa1-af72-9ca8f9c2ce31", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {}}, {"id": "769242e8-bb32-4704-af9f-290944a8420a", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "93ddca2c-e4df-4a00-a0f4-76c6838d56f4", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "95fd31fb-f513-4bea-bca0-61d7d030b1b2", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "bdd962d7-b76b-4f52-9aef-d19f08d7137b", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-role-list-mapper", "saml-user-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "oidc-full-name-mapper", "oidc-usermodel-property-mapper", "saml-user-property-mapper", "oidc-address-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "f96fa5be-8af3-425e-9a40-a14a6b56bdb3", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-full-name-mapper", "saml-user-attribute-mapper", "saml-role-list-mapper", "oidc-usermodel-attribute-mapper", "oidc-address-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-property-mapper", "saml-user-property-mapper"], "consent-required-for-all-mappers": ["true"]}}], "org.keycloak.keys.KeyProvider": [{"id": "950cd5f7-9e73-434b-926a-463c5d7e8985", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "66a95a1b-788b-4942-805f-5c9552755c76", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "41640c93-40e7-4dc2-beae-2887f8bbebc2", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "bed29d4f-aba7-4992-a600-18c0a28c1fc3", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "e8b8f564-6d56-4171-ba36-a8922c6eae49", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "83dcb1e4-2c7c-4494-825d-f2677cbc114c", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "b0a80dc3-d473-468e-b6e8-f1d306c21360", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "a1882f46-54be-4738-847a-32e849d53240", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "OPTIONAL", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "c727a208-587c-4f27-8f48-ba2a0d4effdd", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "6a6ac775-4000-4ccf-9271-6cb599297d4b", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "2a84808d-e0c7-4759-aee8-cf9229542429", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "817f990a-1c46-464c-9051-5e0ae39d63db", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "266fca50-7b69-4cd4-80eb-a569e87ff8a2", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "b516cb39-8f6d-4d08-ac82-236377be6500", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "OPTIONAL", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "9b9ae730-11e0-451f-b693-e32f09415e42", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "b6d38dcd-7b53-4991-b4eb-c866ce3c5e70", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "8408f503-b929-422f-b52b-277cebda44ba", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"_browser_header.xFrameOptions": "SAMEORIGIN", "_browser_header.strictTransportSecurity": "max-age=31536000; includeSubDomains", "permanentLockout": "false", "quickLoginCheckMilliSeconds": "1000", "_browser_header.xRobotsTag": "none", "maxFailureWaitSeconds": "900", "minimumQuickLoginWaitSeconds": "60", "failureFactor": "30", "actionTokenGeneratedByUserLifespan": "300", "maxDeltaTimeSeconds": "43200", "_browser_header.xContentTypeOptions": "nosniff", "actionTokenGeneratedByAdminLifespan": "43200", "bruteForceProtected": "false", "_browser_header.contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "waitIncrementSeconds": "60"}, "keycloakVersion": "4.0.0.Beta2-SNAPSHOT", "userManagedAccessAllowed": false}