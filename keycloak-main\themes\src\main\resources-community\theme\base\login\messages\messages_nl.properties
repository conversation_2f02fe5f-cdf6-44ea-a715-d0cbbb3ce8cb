doLogIn=Inloggen
doRegister=Registreren
doRegisterSecurityKey=Registreren
doCancel=Annuleren
doSubmit=Verzenden
doBack=Vorige
doYes=Ja
doNo=Nee
doContinue=Doorgaan
doIgnore=Negeren
doAccept=Accepteren
doDecline=Afwijzen
doForgotPassword=Wachtwoord vergeten?
doClickHere=Klik hier
doImpersonate=Identiteit overnemen
doTryAgain=Opnieuw proberen
doTryAnotherWay=Probeer op een ander manier
doConfirmDelete=Verwijderen bevestigen
errorDeletingAccount=Onverwachte fout bij het verwijderen van account
deletingAccountForbidden=U heeft onvoldoende rechten om dit account te verwijderen, neem contact op met de beheerder.
kerberosNotConfigured=Kerberos is niet geconfigureerd
kerberosNotConfiguredTitle=Kerberos is niet geconfigureerd
bypassKerberosDetail=U bent niet ingelogd via Kerberos of uw browser kan niet met Kerberos inloggen. Klik op ''doorgaan'' om via een andere manier in te loggen
kerberosNotSetUp=Kerberos is onjuist geconfigureerd. U kunt niet inloggen.
registerTitle=Registreren
loginAccountTitle=Inloggen bij uw account
loginTitle=Log in bij {0}
loginTitleHtml={0}
impersonateTitle={0} Identiteit overnemen
impersonateTitleHtml=<strong>{0}</strong> Identiteit overnemen
realmChoice=Realm
unknownUser=Onbekende gebruiker
loginTotpTitle=Mobiele authenticator instellen
loginProfileTitle=Accountinformatie bijwerken
loginIdpReviewProfileTitle=Accountinformatie bijwerken
loginTimeout=Time-out bij inlogpoging. Het inlogproces begint opnieuw.
reauthenticate=Log opnieuw in om verder te gaan
authenticateStrong=Sterke authenticatie vereist om verder te gaan
oauthGrantTitle=Toegang verlenen tot {0}
oauthGrantTitleHtml={0}
oauthGrantInformation=Zorg dat u {0} vertrouwt en dat u weet hoe {0} omgaat met uw gegevens.
oauthGrantReview=U kunt dit document nalezen: 
oauthGrantTos=algemene voorwaarden.
oauthGrantPolicy=privacybeleid.
errorTitle=Er is een fout opgetreden...
errorTitleHtml=Er is een fout opgetreden...
emailVerifyTitle=E-mailadres-verificatie
emailForgotTitle=Bent u uw wachtwoord vergeten?
updateEmailTitle=E-mail bijwerken
emailUpdateConfirmationSentTitle=Bevestigingsmail verstuurd
emailUpdateConfirmationSent=Een bevestigingsmail is vertuurd naar {0}. Volg de instructies in deze mail om het bijwerken van het e-mailadres te voltooien.
emailUpdatedTitle=E-mail bijgewerkt
emailUpdated=Het e-mailadres is met succes bijgewerkt naar {0}.
updatePasswordTitle=Wachtwoord bijwerken
codeSuccessTitle=Succescode
codeErrorTitle=Foutcode\: {0}
displayUnsupported=Opgevraagde weergave type is niet ondersteund
browserRequired=Om in te loggen is een browser vereist
browserContinue=Om het loginproces af te ronden is een browser vereist
browserContinuePrompt=Browser openen en doorgaan met inloggen? [y/n]:
browserContinueAnswer=y


termsTitle=Algemene Voorwaarden
termsText=<p>Algemene voorwaarden nog te definiëren</p>
termsPlainText=Algemene voorwaarden nog te definiëren
termsAcceptanceRequired=Accepteren van de algemene voorwaarden is verplicht.
acceptTerms=Ik ga akkoord met de algemene voorwaarden

deleteCredentialTitle={0} verwijderen
deleteCredentialMessage=Wilt u {0} verwijderen?

recaptchaFailed=Ongeldige Recaptcha
recaptchaNotConfigured=Recaptcha is verplicht, maar niet geconfigureerd
consentDenied=Toestemming geweigerd.

noAccount=Nieuwe gebruiker?
username=Gebruikersnaam
usernameOrEmail=Gebruikersnaam of e-mailadres
firstName=Voornaam
givenName=Roepnaam
fullName=Volledige naam
lastName=Achternaam
familyName=Familienaam
email=E-mailadres
password=Wachtwoord
passwordConfirm=Bevestig wachtwoord
passwordNew=Nieuw wachtwoord
passwordNewConfirm=Bevestiging nieuw wachtwoord
hidePassword=Wachtwoord verbergen
showPassword=Wachtwoord tonen
rememberMe=Ingelogd blijven
authenticatorCode=Authenticatiecode
address=Adres
street=Straat
locality=Woonplaats
region=Provincie of gewest
postal_code=Postcode
country=Land
emailVerified=E-mailadres geverifieerd
website=Website
phoneNumber=Telefoon
phoneNumberVerified=Telefoonnnummer geverifieerd
gender=Geslacht
birthday=Geboortedatum
zoneinfo=Tijdzone
gssDelegationCredential=GSS delegatie credential
logoutOtherSessions=Uitloggen op andere apparaten

profileScopeConsentText=Gebruikersprofiel
emailScopeConsentText=E-mailadres
addressScopeConsentText=Adres
phoneScopeConsentText=Telefoonnummer
offlineAccessScopeConsentText=Offline toegang
samlRoleListScopeConsentText=Mijn rollen
rolesScopeConsentText=Gebruikersrollen
organizationScopeConsentText=Organisatie

restartLoginTooltip=Inlogproces opnieuw starten

loginTotpIntro=U bent verplicht om tweefactor-authenticatie in te stellen om dit account te kunnen gebruiken
loginTotpStep1=Installeer een van de volgende applicaties op uw mobile telefoon
loginTotpStep2=Open de applicatie en scan de barcode
loginTotpStep3=Voer de eenmalige code in die door de applicatie is aangeleverd en klik op ''Verzenden'' om de setup te voltooien.
loginTotpManualStep2=Open de applicatie en voer de sleutel in:
loginTotpManualStep3=Gebruik de volgende configuratiewaarden als de applicatie dit ondersteunt:
loginTotpUnableToScan=Scannen mislukt?
loginTotpScanBarcode=Barcode scannen?
loginCredential=Inlogmiddel
loginOtpOneTime=Eenmalige code
loginTotpType=Type
loginTotpAlgorithm=Algoritme
loginTotpDigits=Cijfers
loginTotpInterval=Interval
loginTotpCounter=Teller
loginTotpDeviceName=Apparaatnaam

loginTotp.totp=Time-based
loginTotp.hotp=Counter-based


loginChooseAuthenticator=Inlogmethode kiezen

oauthGrantRequest=Wilt u deze toegangsrechten verlenen?
inResource=in


emailVerifyInstruction1=Een e-mail met instructies om uw e-mailadres te verifiëren is zojuist verzonden.
emailVerifyInstruction2=Heeft u geen verificatiecode ontvangen in uw e-mail?
emailVerifyInstruction3=om opnieuw een e-mail te versturen.
emailLinkIdpTitle=Link {0}
emailLinkIdp1=Er is een e-mail verzonden met instructies om {0} account {1} te koppelen met uw {2} account.
emailLinkIdp2=Heeft u geen verificatiecode in uw e-mail ontvangen?
emailLinkIdp3=om opnieuw een e-mail te versturen.
emailLinkIdp4=Als u het e-mailadres al geverifieerd hebt in een andere browser
emailLinkIdp5=om door te gaan.

backToLogin=&laquo; Terug naar Inloggen

emailInstruction=Voer uw gebruikersnaam of e-mailadres in en wij sturen u een e-mailbericht met instructies voor het aanmaken van een nieuw wachtwoord.
emailInstructionUsername=Voer uw gebruikersnaam in en wij sturen u een e-mailbericht met instructies voor het aanmaken van een nieuw wachtwoord.

copyCodeInstruction=Kopieer deze code en plak deze in uw applicatie:
pageExpiredTitle=Inlogpagina is verlopen
pageExpiredMsg1=Om het loginproces opnieuw te beginnen
pageExpiredMsg2=Om door te gaan met het loginproces
personalInfo=Persoonlijke informatie:
role_admin=Admin
role_realm-admin=Realm beheren
role_create-realm=Realm aanmaken
role_create-client=Client aanmaken
role_view-realm=Bekijk realm
role_view-users=Bekijk gebruikers
role_view-applications=Bekijk applicaties
role_view-clients=Bekijk clients
role_view-events=Bekijk gebeurtenissen
role_view-identity-providers=Bekijk identity providers
role_manage-realm=Beheer realm
role_manage-users=Gebruikers beheren
role_manage-applications=Beheer applicaties
role_manage-identity-providers=Beheer identity providers
role_manage-clients=Beheer clients
role_manage-events=Beheer gebeurtenissen
role_view-profile=Profiel bekijken
role_manage-account=Beheer account
role_manage-account-links=Beheer accountlinks
role_read-token=Token lezen
role_offline-access=Offline toegang
client_account=Account
client_account-console=Accountconsole
client_security-admin-console=Security Admin Console
client_admin-cli=Admin CLI
client_realm-management=Realm-beheer
client_broker=Broker

requiredFields=Verplichte velden

invalidUserMessage=Gebruikersnaam of wachtwoord ongeldig.
invalidUsernameMessage=Gebruikersnaam ongeldig.
invalidUsernameOrEmailMessage=Gebruikersnaam of e-mailadres ongeldig.
invalidPasswordMessage=Wachtwoord ongeldig.
invalidEmailMessage=Ongeldig e-mailadres.
accountDisabledMessage=Account is geblokkeerd, neem contact op met de beheerder.
# These properties are deliberately the same as "invalidUsernameMessage", so by default, it is not possible to recognize the reason of failed authentication is temporarily disabled account
# Deze eigenschappen zijn opzettelijk gelijk aan "invalidUsernameMessage", zodat het standaard niet mogelijk is om te herkennen dat het inloggen mislukt door een tijdelijk geblokkeerd account.
accountTemporarilyDisabledMessage=Gebruikersnaam of wachtwoord ongeldig.
accountPermanentlyDisabledMessage=Gebruikersnaam of wachtwoord ongeldig.
# These properties are deliberately the same as "invalidTotpMessage", so by default, it is not possible to recognize the reason of failed authentication is temporarily disabled account
# Deze eigenschappen zijn opzettelijk gelijk aan "invalidTotpMessage", zodat het standaard niet mogelijk is om te herkennen dat het inloggen mislukt is  door een tijdelijk geblokkeerd account.
accountTemporarilyDisabledMessageTotp=Ongeldige authenticatiecode.
accountPermanentlyDisabledMessageTotp=Ongeldige authenticatiecode.
expiredCodeMessage=Logintijd verlopen. Gelieve opnieuw in te loggen.
expiredActionMessage=Actietijd verlopen. Log daarom opnieuw in.
expiredActionTokenNoSessionMessage=Actietijd verlopen.
expiredActionTokenSessionExistsMessage=Actietijd verlopen. Gelieve de actie opnieuw te starten.
sessionLimitExceeded=Er zijn te veel sessies
identityProviderLogoutFailure=SAML IdP Logout gefaald

missingFirstNameMessage=Voer uw voornaam in.
missingLastNameMessage=Voer uw achternaam in.
missingEmailMessage=Voer uw e-mailadres in.
missingUsernameMessage=Voer uw gebruikersnaam in.
missingPasswordMessage=Voer uw wachtwoord in.
missingTotpMessage=Voer uw authenticatiecode in.
missingTotpDeviceNameMessage=Voer uw apparaatnaam in.
notMatchPasswordMessage=Wachtwoorden komen niet overeen.

error-invalid-value=Ongeldige waarde.
error-invalid-blank=Voer een waarde in.
error-empty=Vooer een waarde in.
error-invalid-length=Lengte moet tussen {1} en {2} zijn.
error-invalid-length-too-short=Minimale lengte is {1}.
error-invalid-length-too-long=Maximale lengte is {2}.
error-invalid-email=Ongeldig e-mailaddress.
error-invalid-number=Ongeldig getal.
error-number-out-of-range=Getal moet tussen {1} en {2} liggen.
error-number-out-of-range-too-small=Getal moet een minimaal {1} zijn.
error-number-out-of-range-too-big=Getal moet maximaal {2} zijn.
error-pattern-no-match=Ongeldige waarde.
error-invalid-uri=Ongeldige URL.
error-invalid-uri-scheme=Ongeldig URL scheme.
error-invalid-uri-fragment=Ongeldig URL fragment.
error-user-attribute-required=Dit veld is verplicht.
error-invalid-date=Ongeldige datum.
error-user-attribute-read-only=Dit veld is read-only.
error-username-invalid-character=Waarde bevat ongeldig teken.
error-person-name-invalid-character=Waarde bevat ongeldig teken.
error-reset-otp-missing-id=Kies een OTP-configuratie.

invalidPasswordExistingMessage=Ongeldig bestaand wachtwoord.
invalidPasswordBlacklistedMessage=Ongeldig wachtwoord: wachtwoord is geblacklist.
invalidPasswordConfirmMessage=Wachtwoord komt niet overeen met wachtwoordbevestiging.
invalidTotpMessage=Ongeldige authenticatiecode.

usernameExistsMessage=Gebruikersnaam bestaat al.
emailExistsMessage=E-mailadres bestaat al.

federatedIdentityExistsMessage=Gebruiker met {0} {1} bestaat al. Log in bij accountbeheer om het account te koppelen.
federatedIdentityUnavailableMessage=Gebruiker {0}, ingelogd met identity provider {1} bestaat niet. Neem contact op met de beheerder.
federatedIdentityUnmatchedEssentialClaimMessage=Het ID token dat is uitgegeven door de identity provider, matcht niet met de geconfigureerde essential claim. Neem contact op met de beheerder.

confirmLinkIdpTitle=Account bestaat al
confirmOverrideIdpTitle=Broker link bestaat al
federatedIdentityConfirmLinkMessage=Gebruiker met {0} {1} bestaat al. Hoe wilt u doorgaan?
federatedIdentityConfirmOverrideMessage=U probeert om uw account {0} te koppelen aan het {1} account {2}. Maar uw account is al gekoppeld met het andere {3} account {4}. Weet u zeker dat u de bestaande koppeling wilt vervangen door het nieuwe account?
federatedIdentityConfirmReauthenticateMessage=Authenticeer om uw account te koppelen met {0}
nestedFirstBrokerFlowMessage=De {0} gebruiker {1} is niet gekoppeld aan een bekende gebruiker.
confirmLinkIdpReviewProfile=Nalopen profiel
confirmLinkIdpContinue=Voeg toe aan bestaande account
confirmOverrideIdpContinue=Ja, koppeling met bestaand account vervangen

configureTotpMessage=U moet de Mobile Authenticator configuren om uw account te activeren.
configureBackupCodesMessage=U moet back-up codes instellen om uw account te activeren.
updateProfileMessage=U moet uw gebruikersprofiel bijwerken om uw account te activeren.
updatePasswordMessage=U moet uw wachtwoord wijzigen om uw account te activeren.
updateEmailMessage=U moet uw e-mailadres bijwerken om uw account te activeren.
resetPasswordMessage=U moet uw wachtwoord wijzigen.
verifyEmailMessage=U moet uw e-mailadres verifiëren om uw account te activeren.
linkIdpMessage=U moet uw e-mailadres verifiëren om uw account te koppelen aan {0}.

emailSentMessage=U ontvangt binnenkort een e-mail met verdere instructies.
emailSendErrorMessage=Het versturen van de e-mail is mislukt, probeer het later opnieuw.

accountUpdatedMessage=Uw account is bijgewerkt.
accountPasswordUpdatedMessage=Uw wachtwoord is gewijzigd.

delegationCompleteHeader=Login gelukt
delegationCompleteMessage=U mag uw browser sluiten en terug gaan naar uw consoletoepassing.
delegationFailedHeader=Login mislukt
delegationFailedMessage=U mag uw browser sluiten en teruggaan naar uw consoletoepassing om daar te proberen het inlogproces nog een keer te starten.

noAccessMessage=Geen toegang

invalidPasswordMinLengthMessage=Ongeldig wachtwoord, de minimumlengte is {0} tekens.
invalidPasswordMaxLengthMessage=Ongeldig wachtwoord, de maximumlengte is {0} tekens.
invalidPasswordMinDigitsMessage=Ongeldig wachtwoord, deze moet minstens {0} cijfers bevatten.
invalidPasswordMinLowerCaseCharsMessage=Ongeldig wachtwoord, deze moet minstens {0} kleine letters bevatten.
invalidPasswordMinUpperCaseCharsMessage=Ongeldig wachtwoord, deze moet minstens {0} hoofdletters bevatten.
invalidPasswordMinSpecialCharsMessage=Ongeldig wachtwoord, deze moet minstens {0} speciale tekens bevatten.
invalidPasswordNotUsernameMessage=Ongeldig wachtwoord, deze mag niet overeen komen met de gebruikersnaam.
invalidPasswordNotContainsUsernameMessage=Ongeldig wachtwoord, de gebruikersnaam mag er niet in voorkomen.
invalidPasswordNotEmailMessage=Ongeldig wachtwoord, deze mag niet overeen komen met het e-mailadres.
invalidPasswordRegexPatternMessage=Ongeldig wachtwoord, deze komt niet overeen met opgegeven reguliere expressie(s).
invalidPasswordHistoryMessage=Ongeldig wachtwoord, deze mag niet overeen komen met een van de laatste {0} wachtwoorden.
invalidPasswordGenericMessage=Ongeldig wachtwoord: het nieuwe wachtwoord voldoet niet aan de opgestelde wachtwoordeisen.

failedToProcessResponseMessage=Het verwerken van de respons is mislukt
httpsRequiredMessage=HTTPS vereist
realmNotEnabledMessage=Realm niet geactiveerd
invalidRequestMessage=Ongeldige request
successLogout=U bent nu uitgelogd
failedLogout=Afmelden is mislukt
unknownLoginRequesterMessage=De login requester is onbekend
loginRequesterNotEnabledMessage=De login requester is niet geactiveerd
bearerOnlyMessage=Bearer-only applicaties mogen geen browserlogin initiëren
standardFlowDisabledMessage=Client mag geen browserlogin starten met het opgegeven response_type. Standard flow is uitgeschakeld voor de client.
implicitFlowDisabledMessage=Client mag geen browserlogin starten met opgegeven response_type. Implicit flow is uitgeschakeld voor de client.
invalidRedirectUriMessage=Ongeldige redirect-URI
unsupportedNameIdFormatMessage=Niet-ondersteund NameIDFormat
invalidRequesterMessage=Ongeldige requester
registrationNotAllowedMessage=Registratie is niet toegestaan
resetCredentialNotAllowedMessage=Het opnieuw instellen van de aanmeldgegevens is niet toegestaan

permissionNotApprovedMessage=Recht verworpen.
noRelayStateInResponseMessage=Geen relay state in antwoord van de identity provider.
insufficientPermissionMessage=Onvoldoende rechten om identiteiten te koppelen.
couldNotProceedWithAuthenticationRequestMessage=Het authenticatieverzoek naar de identity provider wordt afgebroken.
couldNotObtainTokenMessage=Kon geen token bemachtigen van de identity provider.
unexpectedErrorRetrievingTokenMessage=Onverwachte fout bij het ophalen van de token van de identity provider.
unexpectedErrorHandlingResponseMessage=Onverwachte fout bij het verwerken van de respons van de identity provider.
identityProviderAuthenticationFailedMessage=Verificatie mislukt. Er kon niet worden geauthenticeerd met de identity provider.
couldNotSendAuthenticationRequestMessage=Kan het authenticatieverzoek niet verzenden naar de identity provider.
unexpectedErrorHandlingRequestMessage=Onverwachte fout bij het verwerken van het authenticatieverzoek naar de identity provider.
invalidAccessCodeMessage=Ongeldige toegangscode.
sessionNotActiveMessage=Sessie inactief.
invalidCodeMessage=Er is een fout opgetreden, probeer nogmaals in te loggen vanuit uw applicatie.
cookieNotFoundMessage=Restart login cookie niet gevonden. Miscchien is het verlopen; misschien is het verwijderd of cookies zijn uitgeschakeld in uw browser. Als cookies zijn uitgeschakeld, schakel deze dan in. Klik op Terug naar applicatie om opnieuw in te loggen.
insufficientLevelOfAuthentication=Er is niet voldaan aan het verzochte authenticatieniveau.
identityProviderUnexpectedErrorMessage=Onverwachte fout tijdens de authenticatie met de identity provider
identityProviderMissingStateMessage=State parameter ontbreekt in respons van identity provider.
identityProviderMissingCodeOrErrorMessage=Ontbrekende code of error parameter in response van identity provider.
identityProviderInvalidResponseMessage=Ongeldige respons van identity provider.
identityProviderInvalidSignatureMessage=Ongeldige handtekening/signature in respons van identity provider.
identityProviderNotFoundMessage=Geen identity provider gevonden met deze naam.
identityProviderLinkSuccess=Uw account is met succes gekoppeld aan {0} account {1}.
staleCodeMessage=Deze pagina is verlopen. Keer terug naar uw applicatie om opnieuw in te loggen.
realmSupportsNoCredentialsMessage=Realm ondersteunt geen enkel soort aanmeldgegeven.
credentialSetupRequired=Inloggen onmogelijk, aanmeldgegevens moeten worden ingesgeld.
identityProviderNotUniqueMessage=Realm ondersteunt meerdere identity providers. Er kon niet bepaald worden welke identity provider er gebruikt zou moeten worden tijdens de authenticatie.
emailVerifiedMessage=Uw e-mailadres is geverifieerd.
emailVerifiedAlreadyMessage=Uw e-mailadres werd eerder al geverifieerd.
staleEmailVerificationLink=De link die u gebruikt, is verlopen. Misschien heeft u uw e-mailadres al eerder geverifieerd.
identityProviderAlreadyLinkedMessage=De door {0} teruggegeven gefedereerde identiteit is al aan een andere gebruiker gekoppeld.
confirmAccountLinking=Bevestig dat het account {0} van identity provider {1} wordt gekoppeld met uw account.
confirmEmailAddressVerification=Bevestig dat e-mailadres {0} valide is.
confirmExecutionOfActions=Voer de volgende actie(s) uit

backToApplication=&laquo; Terug naar de applicatie
missingParameterMessage=Missende parameters: {0}
clientNotFoundMessage=Client niet gevonden.
clientDisabledMessage=Client is inactief.
invalidParameterMessage=Ongeldige parameter: {0}
alreadyLoggedIn=U bent al ingelogd.
differentUserAuthenticated=U bent in deze sessie al als de gebruiker "{0}" aangemeld. Log eerst uit.
brokerLinkingSessionExpired=Broker account linking aangevraagd, maar de huidige sessie in verlopen.
proceedWithAction=&raquo; Klik hier om verder te gaan
acrNotFulfilled=Authenticatievereisten niet voldaan (acr)

requiredAction.CONFIGURE_TOTP=OTP instellen
requiredAction.TERMS_AND_CONDITIONS=Algemene voorwaarden accepteren
requiredAction.UPDATE_PASSWORD=Wachtwoord bijwerken
requiredAction.UPDATE_PROFILE=Profiel bijwerken
requiredAction.VERIFY_EMAIL=E-mail bevestigen
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=AUTHN herstelcodes aanmaken
requiredAction.webauthn-register-passwordless=Webauthn Passwordless registreren
requiredAction.webauthn-register=Webauthn registreren

invalidTokenRequiredActions=Required actions included in the link are not valid

doX509Login=U wordt ingelogd als\:
clientCertificate=X509 client certificate\:
noCertificate=[No Certificate]


pageNotFound=Pagina niet gevonden
internalServerError=Er is een interne serverfout opgetreden

console-username=Gebruikersnaam:
console-password=Wachtwoord:
console-otp=Eenmalige code:
console-new-password=Nieuw wachtwoord:
console-confirm-password=Bevestig wachtwoord:
console-update-password=Een update van uw wachtwoord is verplicht.
console-verify-email=U bent verplicht om uw e-mailadres te verifiëren. Een e-mail met de verificatiecode is naar {0} gestuurd. Gelieve deze code hieronder in te voeren.
console-email-code=E-mail Code:
console-accept-terms=Accepteert u de algemene voorwaarden? [y/n]:
console-accept=y

# Identity provider
identity-provider-redirector=Gebruik een andere Identity Provider
identity-provider-login-label=Of login met
idp-email-verification-display-name=E-mail Verificatie
idp-email-verification-help-text=Bevestig uw account per e-mail.
idp-username-password-form-display-name=Gebruikersnaam en wachtwoord
idp-username-password-form-help-text=Bevestig uw account door in te loggen.
