<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>integration-arquillian-tests-other</artifactId>
        <groupId>org.keycloak.testsuite</groupId>
        <version>999.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>integration-arquillian-tests-sssd</artifactId>

    <name>SSSD tests</name>

    <properties>
        <exclude.sssd>**/sssd/**/*Test.java</exclude.sssd>
    </properties>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>${exclude.sssd}</exclude>
                    </excludes>
                </configuration>
            </plugin>

        </plugins>

    </build>

    <profiles>
        <profile>
            <id>sssd-testing</id>
            <properties>
                <exclude.sssd>-</exclude.sssd>
            </properties>
        </profile>
    </profiles>

</project>
