doSave=Zapisz
doCancel=Anuluj
doLogOutAllSessions=Wyloguj wszystkie sesje
doRemove=Usuń
doAdd=Dodaj
doSignOut=Wyloguj
doLogIn=Logowanie
doLink=Link
noAccessMessage=<PERSON><PERSON> dostępu

personalInfoSidebarTitle=Dane konta
accountSecuritySidebarTitle=Bezpieczeństwo konta
signingInSidebarTitle=Zaloguj się
deviceActivitySidebarTitle=Aktywność na urządzeniach
linkedAccountsSidebarTitle=Połączone konta

editAccountHtmlTitle=Edycja konta
personalInfoHtmlTitle=Dane osobiste
federatedIdentitiesHtmlTitle=Połączone tożsamości
accountLogHtmlTitle=Dziennik konta
changePasswordHtmlTitle=Zmień hasło
deviceActivityHtmlTitle=Aktywność urządzeń
sessionsHtmlTitle=Sesje
accountManagementTitle=Zarządzanie kontem
authenticatorTitle=Uwierzytelnienie dwuetapowe
applicationsHtmlTitle=Aplikacje
linkedAccountsHtmlTitle=Połączone konta

accountManagementWelcomeMessage=Witamy w zarządzaniu kontem
accountManagementBaseThemeCannotBeUsedDirectly=Podstawowy motyw konta zawiera jedynie tłumaczenia dla konsoli konta. \
    Aby wyświetlić konsolę konta, musisz ustawić nadrzędny motyw swojego motywu na inny motyw konta lub dostarczyć własny plik index.ftl. \
    Zobacz dokumentację, aby uzyskać więcej informacji.
personalInfoIntroMessage=Zarządzaj informacjami podstawowymi o sobie
accountSecurityTitle=Bezpieczeństwo Konta
accountSecurityIntroMessage=Kontroluj swoje hasło i dostęp
applicationsIntroMessage=Śledź i zarządzaj uprawnieniami aplikacji do twojego konta
resourceIntroMessage=Udostępnij swoje zasoby członkom zespołu
passwordLastUpdateMessage=Twoje hasło zostało zaktualizowane
updatePasswordTitle=Aktualizuj hasło
updatePasswordMessageTitle=Miej pewność, że wybrałeś silne hasło
updatePasswordMessage=Silne hasło zawiera mieszaninę cyfr, liter i symboli. Nie używaj zwykłych słów oraz haseł używanych na innych kontach.
personalSubTitle=Twoje dane osobiste
personalSubMessage=Zarządzaj informacjami podstawowymi: twoim imieniem, nazwiskiem oraz emailem

authenticatorCode=Kod jednorazowy
email=Email
firstName=Imię
givenName=Imię
fullName=Pełna nazwa
lastName=Nazwisko
familyName=Nazwisko rodowe
password=Hasło
currentPassword=Aktualne hasło
passwordConfirm=Potwierdzenie
passwordNew=Nowe hasło
username=Nazwa użytkownika
address=Adres
street=Ulica
locality=Miejscowość
region=Stan, województwo, region
postal_code=Kod pocztowy
country=Kraj
emailVerified=Email zweryfikowany
website=Strona internetowa
phoneNumber=Nr telefonu
phoneNumberVerified=Nr telefonu zweryfikowany
gender=Płeć
birthday=Data urodzenia
zoneinfo=Strefa czasowa
gssDelegationCredential=Poświadczenia delegowane GSS

profileScopeConsentText=Profil użytkownika
emailScopeConsentText=Adres email
addressScopeConsentText=Adres
phoneScopeConsentText=Telefon
offlineAccessScopeConsentText=Dostęp offline
samlRoleListScopeConsentText=Moje role
rolesScopeConsentText=Role użytkownika
organizationScopeConsentText=Organizacja

role_admin=Admin
role_realm-admin=Admin strefy
role_create-realm=Utwórz strefę
role_view-realm=Przeglądaj strefy
role_view-users=Przeglądaj użytkowników
role_view-applications=Przeglądaj aplikacje
role_view-groups=Zobacz grupy
role_view-clients=Przeglądaj klientów
role_view-events=Przeglądaj zdarzenia
role_view-identity-providers=Przeglądaj dostawców tożsamości
role_view-consent=Przeglądaj zgody
role_manage-realm=Zarządzaj strefami
role_manage-users=Zarządzaj użytkownikami
role_manage-applications=Zarządzaj aplikacjami
role_manage-identity-providers=Zarządzaj dostawcami tożsamości
role_manage-clients=Zarządzaj klientami
role_manage-events=Zarządzaj zdarzeniami
role_view-profile=Przeglądaj profil
role_manage-account=Zarządzaj kontem
role_manage-account-links=Zarządzaj linkami konta
role_manage-consent=Zarządzaj zgodami
role_read-token=Odczytaj token
role_offline-access=Dostęp offline
role_uma_authorization=Uzyskaj uprawnienia
client_account=Konto
client_account-console=Konsola konta
client_security-admin-console=Konsola administratora bezpieczeństwa
client_admin-cli=Admin CLI
client_realm-management=Zarządzanie strefą
client_broker=Broker


requiredFields=Wymagane pola
allFieldsRequired=Wszystkie pola są wymagane

backToApplication=&laquo; Powrót do aplikacji
backTo=Wróć do: {0}

date=Data
event=Zdarzenie
ip=IP
client=Klient
clients=Aplikacje klienckie
details=Szczegóły
started=Rozpoczęta
lastAccess=Ostatni dostęp
expires=Data ważności
applications=Aplikacje

account=Konto
federatedIdentity=Połączone tożsamości
authenticator=Uwierzytelnienie dwuetapowe
device-activity=Aktywność urządzenia
sessions=Sesje
log=Dziennik

application=Aplikacja
availableRoles=Dostępne role
grantedPermissions=Przydzielone uprawnienia
grantedPersonalInfo=Przydzielone dane osobiste
additionalGrants=Dodatkowe przydziały
action=Akcje
inResource=w
fullAccess=Pełny dostęp
offlineToken=Token offline
revoke=Odbierz uprawnienia

configureAuthenticators=Skonfigurowane autentykatory
mobile=Mobilne
totpStep1=Zainstaluj jedną z następujących aplikacji na telefonie komórkowym:
totpStep2=Otwórz aplikację i zeskanuj kod kreskowy:
totpStep3=Wprowadź jednorazowy kod podany przez aplikację i kliknij Zapisz aby zakończyć konfigurację.
totpStep3DeviceName=Podaj nazwę urządzenia aby lepiej zarządzać swoimi urządzeniami haseł jednorazowych.

totpManualStep2=Otwórz aplikację i wprowadź klucz:
totpManualStep3=Użyj poniższych wartości konfiguracji, jeśli aplikacja pozwala na ich ustawienie:
totpUnableToScan=Nie można skanować?
totpScanBarcode=Zeskanować kod paskowy?

totp.totp=Oparte o czas
totp.hotp=Oparte o licznik

totpType=Typ
totpAlgorithm=Algorytm
totpDigits=Cyfry
totpInterval=Interwał
totpCounter=Licznik
totpDeviceName=Nazwa urządzenia

totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator

irreversibleAction=Ta akcja jest nieodwracalna
deletingImplies=Usunięcie konta spowoduje:
errasingData=Usunięcie wszystkich danych
loggingOutImmediately=Natychmiastowe wylogowanie
accountUnusable=Nie będzie można więcej korzystać z aplikacji na tym koncie

missingUsernameMessage=Proszę podać nazwę użytkownika.
missingFirstNameMessage=Proszę podać imię.
invalidEmailMessage=Nieprawidłowy adres email.
missingLastNameMessage=Proszę podać nazwisko.
missingEmailMessage=Proszę podać e-mail.
missingPasswordMessage=Proszę podać hasło.
notMatchPasswordMessage=Hasła nie są zgodne.
invalidUserMessage=Nieprawidłowy użytkownik
updateReadOnlyAttributesRejectedMessage=Nie można zaktualizować atrybutu oznaczonego "tylko do odczytu"

missingTotpMessage=Proszę podać kod uwierzytelniający.
missingTotpDeviceNameMessage=Proszę podać nazwę urządzenia.
invalidPasswordExistingMessage=Nieprawidłowe aktualne hasło.
invalidPasswordConfirmMessage=Potwierdzenie hasła nie jest zgodne.
invalidTotpMessage=Nieprawidłowy kod uwierzytelniający.

usernameExistsMessage=Nazwa użytkownika już jest wykorzystana.
emailExistsMessage=Email już istnieje.

readOnlyUserMessage=Zmiana nie jest możliwa, ponieważ edycja konta jest zablokowana.
readOnlyUsernameMessage=Zmiana nazwy użytkownika nie jest możliwa, ponieważ edycja konta jest zablokowana.
readOnlyPasswordMessage=Zmiana hasła nie jest możliwa, ponieważ edycja konta jest zablokowana.

successTotpMessage=Mobilny autentykator skonfigurowany.
successTotpRemovedMessage=Mobilny autentykator usunięty.

successGrantRevokedMessage=Cofnięto uprawnienia.

accountUpdatedMessage=Twoje konto zostało zaktualizowane.
accountPasswordUpdatedMessage=Twoje hasło zostało zmienione.

missingIdentityProviderMessage=Dostawca tożsamości nie został wybrany.
invalidFederatedIdentityActionMessage=Nieprawidłowa akcja.
identityProviderNotFoundMessage=Podany dostawca tożsamości nie istnieje.
federatedIdentityLinkNotActiveMessage=Podana tożsamość nie jest już aktywna.
federatedIdentityRemovingLastProviderMessage=Nie można usunąć ostatniej połączonej tożsamości, jeżeli nie ustawiłeś hasła.
federatedIdentityBoundOrganization=Nie możesz usunąć połączenia z dostawcą tożsamości powiązanego z organizacją.
identityProviderRedirectErrorMessage=Nieudane przekierowanie do zewnętrznego dostawcy tożsamości.
identityProviderRemovedMessage=Dostawca tożsamości został usunięty.
identityProviderAlreadyLinkedMessage=Połączona tożsamość {0} jest już przypisana do innego użytkownika.
staleCodeAccountMessage=Strona wygasła. Prosimy spróbować ponownie.
consentDenied=Zgoda wycofana.
access-denied-when-idp-auth=Brak dostępu przy użyciu {0}

accountDisabledMessage=Konto jest zablokowane, skontaktuj się z administratorem.

accountTemporarilyDisabledMessage=Konto jest tymczasowo zablokowane, skontaktuj się z administratorem lub spróbuj później.
invalidPasswordMinLengthMessage=Nieprawidłowe hasło: minimalna długość {0}.
invalidPasswordMaxLengthMessage=Nieprawidłowe hasło: maksymalna długość {0}.
invalidPasswordMinLowerCaseCharsMessage=Nieprawidłowe hasło: brak małych liter (co najmniej {0}).
invalidPasswordMinDigitsMessage=Nieprawidłowe hasło: brak cyfr (co najmniej {0}).
invalidPasswordMinUpperCaseCharsMessage=Nieprawidłowe hasło: brak dużych liter (co najmniej {0}).
invalidPasswordMinSpecialCharsMessage=Nieprawidłowe hasło: brak znaków specjalnych (co najmniej {0}).
invalidPasswordNotUsernameMessage=Nieprawidłowe hasło: nie może być takie samo jak nazwa użytkownika.
invalidPasswordNotContainsUsernameMessage=Nieprawidłowe hasło: nie może zawierać nazwy użytkownika.
invalidPasswordNotEmailMessage=Nieprawidłowe hasło: nie może być zgodne z adresem email.
invalidPasswordRegexPatternMessage=Nieprawidłowe hasło: nie spełnia przyjętych reguł.
invalidPasswordHistoryMessage=Nieprawidłowe hasło: jest identyczne jak jedno z ostatnich ({0}) haseł.
invalidPasswordBlacklistedMessage=Nieprawidłowe hasło: jest na liście haseł zabronionych.
invalidPasswordGenericMessage=Nieprawidłowe hasło: nowe hasło nie spełnia polityki haseł.

# Authorization
myResources=Moje zasoby
myResourcesSub=Moje zasoby
doDeny=Zabroń
doRevoke=Cofnij
doApprove=Akceptuj
doRemoveSharing=Usuń udostępnianie
doRemoveRequest=Usuń żądanie
peopleAccessResource=Osoby z dostępem do tego zasobu
resourceManagedPolicies=Uprawnienia dające dostęp do tego zasobu
resourceNoPermissionsGrantingAccess=Brak uprawnień dających dostęp do tego zasobu
anyAction=Dowolna akcja
description=Opis
name=Nazwa
scopes=Zakres
resource=Zasób
user=Użytkownik
peopleSharingThisResource=Osoby współdzielące ten zasób
shareWithOthers=Udostępnij innym
needMyApproval=Wymagana moja akceptacja
requestsWaitingApproval=Twoje żądanie czeka na akceptację
icon=Ikona
requestor=Żądający
owner=Właściciel
resourcesSharedWithMe=Zasoby współdzielone ze mną
permissionRequestion=Żądania uprawnień
permission=Uprawnienia
shares=udostępnienia
notBeingShared=Ten zasób nie jest współdzielony.
notHaveAnyResource=Nie masz żadnych zasobów
noResourcesSharedWithYou=Brak zasobów udostępnionych dla Ciebie
havePermissionRequestsWaitingForApproval=Masz {0} żądań uprawnień oczekujących na akceptację.
clickHereForDetails=Więcej szczegółów...
resourceIsNotBeingShared=Zasób nie jest współdzielony

# Applications
applicationName=Nazwa
applicationType=Typ aplikacji
applicationInUse=Tylko w użyciu
clearAllFilter=Wyczyść filtry
activeFilters=Aktywne filtry
filterByName=Filtruj wg nazwy ...
allApps=Wszystkie aplikacje
internalApps=Wewnętrzne aplikacje
thirdpartyApps=Zewnętrzne aplikacje
appResults=Wyniki
clientNotFoundMessage=Nie znaleziono klienta

# Linked account
authorizedProvider=Dostawca logowania
authorizedProviderMessage=Dostawca logowania powiązany z kontem
identityProvider=Dostawca tożsamości
identityProviderMessage=Powiązanie konta ze skonfigurowanym dostawcą tożsamości
socialLogin=Serwisy społecznościowe
userDefined=Użytkownika
removeAccess=Usuń dostęp
removeAccessMessage=Konieczne będzie ponowne nadanie dostępu, aby użyć konta z tej aplikacji

#Authenticator
authenticatorStatusMessage=Dwuskładnikowe uwierzytelnianie jest obecnie
authenticatorFinishSetUpTitle=Dwuskładnikowe uwierzytelnianie
authenticatorFinishSetUpMessage=Przy każdym logowaniu, będziesz proszony o wpisanie kodu drugiego składnika uwierzytelniania.
authenticatorSubTitle=Ustaw dwuskładnikowe uwierzytelnianie
authenticatorSubMessage=Aby zwiększyć poziom bezpieczeństwa swojego konta, włącz co najmniej jedną z dostępnych metod dwuskładnikowego uwierzytelniania.
authenticatorMobileTitle=Autentykator mobilny
authenticatorMobileMessage=Użyj aplikacji mobilnej do otrzymywania kodów dwuskładnikowego uwierzytelniania.
authenticatorMobileFinishSetUpMessage=Moduł uwierzytelniający został powiązany z Twoim telefonem.
authenticatorActionSetup=Ustaw
authenticatorSMSTitle=Kod SMS
authenticatorSMSMessage=Keycloak wyśle kod weryfikacyjny na Twój telefon jako dwuskładnikowe uwierzytelnianie.
authenticatorSMSFinishSetUpMessage=Wiadomości są wysyłane do
authenticatorDefaultStatus=domyślne
authenticatorChangePhone=Zmień numer telefonu

#Authenticator - Mobile Authenticator setup
authenticatorMobileSetupTitle=Konfiguracja uwierzytelniania mobilnego
smscodeIntroMessage=Podaj numer telefonu, kod weryfikacyjny zostanie wysłany na podany numer.
mobileSetupStep1=Zainstaluj aplikację na telefonie. Podane aplikację są wspierane.
mobileSetupStep2=Otwórz aplikację i zeskanuj kod:
mobileSetupStep3=Wpisz kod wygenerowany przez aplikację i zapisz aby zakończyć konfigurację.
scanBarCode=Chcesz zeskanować kod 2D?
enterBarCode=Wpisz kod jednorazowy
doCopy=Kopiuj
doFinish=Zakończ

#Authenticator - SMS Code setup
authenticatorSMSCodeSetupTitle=Konfiguracja SMS
chooseYourCountry=Wybierz kraj
enterYourPhoneNumber=Wpisz numer telefonu
sendVerficationCode=Wyślij kod weyfikacyjny
enterYourVerficationCode=Wpisz kod weryfikacyjny

#Authenticator - backup Code setup
authenticatorBackupCodesSetupTitle=Zapasowy mechanizm uwierzytelniania
realmName=Domena
doDownload=Pobierz
doPrint=Drukuj
generateNewBackupCodes=Generuj nowe kody zapasowe
backtoAuthenticatorPage=Powrót do ustawień uwierzytelniania


#Resources
resources=Zasoby
sharedwithMe=Udostępnione dla mnie
share=Udostępnij
sharedwith=Udostępnione dla
accessPermissions=Prawa dostępu
permissionRequests=Żądania dostępu
approve=Akceptuj
approveAll=Akceptuj wszystkie
people=osoby
perPage=na stronie
currentPage=Aktualna strona
sharetheResource=Udostępnij zasób
group=Grupa
selectPermission=Wybierz uprawnienie
addPeople=Dodaj osoby do udostępniania
addTeam=Dodaj zespół do udostępniania
myPermissions=Moje uprawnienia
waitingforApproval=Oczekuje na zatwierdzenie
anyPermission=Dowolne uprawnienia

# Openshift messages
openshift.scope.user_info=Informacje użytkownika
openshift.scope.user_check-access=Informacje o uprawnieniach
openshift.scope.user_full=Pełny dostęp
openshift.scope.list-projects=Lista projektów

error-invalid-value=Nieprawidłowa wartość.
error-invalid-blank=Podaj wartość.
error-empty=Podaj wartość.
error-invalid-length=Atrybut {0} musi mieć długość pomiędzy {1} a {2}
error-invalid-length-too-short=Atrybut {0} musi mieć minimalną długość {1}
error-invalid-length-too-long=Atrybut {0} musi mieć maksymalną długość {2}
error-invalid-email=Nieprawidłowy adres email.
error-invalid-number=Nieprawidłowy numer.
error-number-out-of-range=Atrybut {0} musi zawierać się pomiędzy {1} a {2}
error-number-out-of-range-too-small=Atrybut {0} musi mieć minimalną wartość {1}
error-number-out-of-range-too-big=Atrybut {0} musi mieć maksymalną wartość {2}
error-pattern-no-match=Nieprawidłowa wartość.
error-invalid-uri=Nieprawidłowy URL.
error-invalid-uri-scheme=Nieprawidłowy wyróżnik URL.
error-invalid-uri-fragment=Nieprawidłowy adres URL.
error-user-attribute-required=Określ atrybut {0}.
error-invalid-date=Nieprawidłowa data.
error-user-attribute-read-only=Pole {0} jest tylko do odczytu.
error-username-invalid-character=Login zawiera nieprawidłowy znak.
error-person-name-invalid-character=Nazwa zawiera nieprawidłowy znak.
