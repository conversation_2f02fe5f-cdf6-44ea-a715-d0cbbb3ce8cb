{"id": "test-duplicate-emails", "realm": "test-duplicate-emails", "enabled": true, "sslRequired": "external", "registrationAllowed": true, "resetPasswordAllowed": true, "editUsernameAllowed": true, "loginWithEmailAllowed": false, "duplicateEmailsAllowed": true, "requiredCredentials": ["password"], "defaultRoles": ["user"], "smtpServer": {"from": "<EMAIL>", "host": "localhost", "port": "3025"}, "users": [{"username": "non-duplicate-email-user", "enabled": true, "email": "non-duplicate-email-user@localhost", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "password"}], "realmRoles": ["user", "offline_access"], "clientRoles": {"test-app": ["customer-user"], "account": ["view-profile", "manage-account"]}}, {"username": "duplicate-email-user1", "enabled": true, "email": "duplicate-email-user@localhost", "firstName": "Agent", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "password"}], "realmRoles": ["user", "offline_access"], "clientRoles": {"test-app": ["customer-user"], "account": ["view-profile", "manage-account"]}}, {"username": "duplicate-email-user2", "enabled": true, "email": "duplicate-email-user@localhost", "firstName": "Agent", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "password"}], "realmRoles": ["user", "offline_access"], "clientRoles": {"test-app": ["customer-user"], "account": ["view-profile", "manage-account"]}}], "scopeMappings": [{"client": "test-app", "roles": ["user"]}], "clients": [{"clientId": "test-app", "enabled": true, "baseUrl": "http://localhost:8180/auth/realms/master/app/auth", "redirectUris": ["http://localhost:8180/auth/realms/master/app/auth/*", "https://localhost:8543/auth/realms/master/app/auth/*"], "adminUrl": "http://localhost:8180/auth/realms/master/app/admin", "secret": "password"}], "roles": {"realm": [{"name": "user", "description": "Have User privileges"}, {"name": "admin", "description": "Have Administrator privileges"}, {"name": "customer-user-premium", "description": "Have User Premium privileges"}, {"name": "sample-realm-role", "description": "Sample realm role"}], "client": {"test-app": [{"name": "customer-user", "description": "Have Customer User privileges"}, {"name": "customer-admin", "description": "Have Customer Admin privileges"}, {"name": "sample-client-role", "description": "Sample client role"}, {"name": "customer-admin-composite-role", "description": "Have Customer Admin privileges via composite role", "composite": true, "composites": {"realm": ["customer-user-premium"], "client": {"test-app": ["customer-admin"]}}}]}}, "groups": [], "clientScopeMappings": {}, "internationalizationEnabled": true, "supportedLocales": ["en", "de"], "defaultLocale": "en", "eventsListeners": ["jboss-logging", "event-queue"]}