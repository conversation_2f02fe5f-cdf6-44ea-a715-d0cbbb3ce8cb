doSave=Saugoti
doCancel=Atšaukti

doLogOutAllSessions=Atjungti visas sesijas
doRemove=Šalinti
doAdd=Pridėti
doSignOut=Atsijungti

editAccountHtmlTitle=Redaguoti paskyrą
federatedIdentitiesHtmlTitle=Susietos paskyros
accountLogHtmlTitle=Paskyros žurnalas
changePasswordHtmlTitle=Keisti slaptažodį
sessionsHtmlTitle=Prisijungimo sesijos
accountManagementTitle=Keycloak Naudotojų Administravimas
authenticatorTitle=Autentifikatorius
applicationsHtmlTitle=Programos

authenticatorCode=Vienkartinis kodas
email=El. paštas
firstName=Vardas
givenName=Pavardė
fullName=Pilnas vardas
lastName=Pavardė
familyName=Pavardė
password=Slaptažodis
passwordConfirm=Pakartotas slaptažodis
passwordNew=Naujas slaptažodis
username=Naudotojo vardas
address=Adresas
street=Gatvė
locality=Miestas arba vietovė
region=Rajonas
postal_code=Pašto kodas
country=Šalis
emailVerified=El. pašto adresas patvirtintas
gssDelegationCredential=GSS prisijungimo duomenų delegavimas

role_admin=Administratorius
role_realm-admin=Srities administravimas
role_create-realm=Kurti sritį
role_view-realm=Peržiūrėti sritį
role_view-users=Peržiūrėti naudotojus
role_view-applications=Peržiūrėti programas
role_view-clients=Peržiūrėti klientines programas
role_view-events=Peržiūrėti įvykių žurnalą
role_view-identity-providers=Peržiūrėti tapatybės teikėjus
role_manage-realm=Valdyti sritis
role_manage-users=Valdyti naudotojus
role_manage-applications=Valdyti programas
role_manage-identity-providers=Valdyti tapatybės teikėjus
role_manage-clients=Valdyti programas
role_manage-events=Valdyti įvykius
role_view-profile=Peržiūrėti paskyrą
role_manage-account=Valdyti paskyrą
role_read-token=Skaityti prieigos rakšą
role_offline-access=Darbas neprisijungus
role_uma_authorization=Įgauti UMA autorizavimo teises
client_account=Paskyra
client_security-admin-console=Saugumo administravimo konsolė
client_admin-cli=Administravimo CLI
client_realm-management=Srities valdymas
client_broker=Tarpininkas


requiredFields=Privalomi laukai
allFieldsRequired=Visi laukai yra privalomi

backToApplication=&laquo; Grįžti į programą
backTo=Atgal į {0}

date=Data
event=Įvykis
ip=IP
client=Klientas
clients=Klientai
details=Detaliau
started=Sukūrimo laikas
lastAccess=Vėliausia prieiga
expires=Galioja iki
applications=Programos

account=Paskyra
federatedIdentity=Susieta tapatybė
authenticator=Autentifikatorius
sessions=Sesijos
log=Įvykiai

application=Programa
availablePermissions=Galimos teisės
grantedPermissions=Įgalintos teisės
grantedPersonalInfo=Įgalinta asmeninė informacija
additionalGrants=Papildomi įgaliojimai
action=Veiksmas
inResource=yra
fullAccess=Pilna prieiga
offlineToken=Režimo neprisijungus raktas (token)
revoke=Atšaukti įgaliojimą

configureAuthenticators=Sukonfigūruotas autentifikatorius
mobile=Mobilus
totpStep1=Installa una delle seguenti applicazioni sul tuo cellulare:
totpStep2=Atidarykite programėlę ir nuskenuokite barkodą arba įveskite kodą.
totpStep3=Įveskite programėlėje sugeneruotą vieną kartą galiojantį kodą ir paspauskite Saugoti norėdami prisijungti.

missingUsernameMessage=Prašome įvesti naudotojo vardą.
missingFirstNameMessage=Prašome įvesti vardą.
invalidEmailMessage=Neteisingas el. pašto adresas.
missingLastNameMessage=Prašome įvesti pavardę.
missingEmailMessage=Prašome įvesti el. pašto adresą.
missingPasswordMessage=Prašome įvesti slaptažodį.
notMatchPasswordMessage=Slaptažodžiai nesutampa.

missingTotpMessage=Prašome įvesti autentifikacijos kodą.
invalidPasswordExistingMessage=Neteisingas dabartinis slaptažodis.
invalidPasswordConfirmMessage=Pakartotas slaptažodis nesutampa.
invalidTotpMessage=Neteisingas autentifikacijos kodas.

usernameExistsMessage=Toks naudotojas jau egzistuoja.
emailExistsMessage=El. pašto adresas jau egzistuoja.

readOnlyUserMessage=Tik skaitymui sukonfigūruotos paskyros duomenų atnaujinti neleidžiama.
readOnlyPasswordMessage=Tik skaitymui sukonfigūruotos paskyros slaptažodžio atnaujinti neleidžiama.

successTotpMessage=Mobilus autentifikatorius sukonfigūruotas.
successTotpRemovedMessage=Mobilus autentifikatorius pašalintas.

successGrantRevokedMessage=Įgalinimas pašalintas sėkmingai.

accountUpdatedMessage=Jūsų paskyros duomenys sėkmingai atnaujinti.
accountPasswordUpdatedMessage=Jūsų paskyros slaptažodis pakeistas.

missingIdentityProviderMessage=Nenurodytas tapatybės teikėjas.
invalidFederatedIdentityActionMessage=Neteisingas arba nežinomas veiksmas.
identityProviderNotFoundMessage=Nurodytas tapatybės teikėjas nerastas.
federatedIdentityLinkNotActiveMessage=Nurodyta susieta tapatybė neaktyvi.
federatedIdentityRemovingLastProviderMessage=Jūs negalite pašalinti paskutinio tapatybės teikėjo sąsajos, nes Jūs neturite nusistatę paskyros slaptažodžio.
identityProviderRedirectErrorMessage=Klaida nukreipiant į tapatybės teikėjo puslapį.
identityProviderRemovedMessage=Tapatybės teikėjas sėkmingai pašalintas.
identityProviderAlreadyLinkedMessage=Susieta tapatybė iš {0} jau susieta su kita paskyra.
staleCodeAccountMessage=Puslapio galiojimas baigėsi. Bandykite dar kartą.
consentDenied=Prieiga draudžiama.

accountDisabledMessage=Paskyros galiojimas sustabdytas, kreipkitės į administratorių.

accountTemporarilyDisabledMessage=Paskyros galiojimas laikinai sustabdytas. Kreipkitės į administratorių arba pabandykite vėliau.
invalidPasswordMinLengthMessage=Per trumpas slaptažodis: mažiausias ilgis {0}.
invalidPasswordMinLowerCaseCharsMessage=Neteisingas slaptažodis: privaloma įvesti {0} mažąją raidę.
invalidPasswordMinDigitsMessage=Neteisingas slaptažodis: privaloma įvesti {0} skaitmenį.
invalidPasswordMinUpperCaseCharsMessage=Neteisingas slaptažodis: privaloma įvesti {0} didžiąją raidę.
invalidPasswordMinSpecialCharsMessage=Neteisingas slaptažodis: privaloma įvesti {0} specialų simbolį.
invalidPasswordNotUsernameMessage=Neteisingas slaptažodis: slaptažodis negali sutapti su naudotojo vardu.
invalidPasswordRegexPatternMessage=Neteisingas slaptažodis: slaptažodis netenkina regex taisyklės(ių).
invalidPasswordHistoryMessage=Neteisingas slaptažodis: slaptažodis negali sutapti su prieš tai buvusiais {0} slaptažodžiais.
