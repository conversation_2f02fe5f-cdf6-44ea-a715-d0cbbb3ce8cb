<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2020 Red Hat, Inc. and/or its affiliates
  ~ and other contributors as indicated by the <AUTHOR>
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  ~
  ~
  ~ Wildfly Elytron 1.13.0.CR3+ RESTEasy client SSL truststore configuration.
  ~ Used by instances of javax.ws.rs.client.ClientBuilder within various tests
  ~ from Keycloak testsuite to create new javax.ws.rs.client.Client instances
  ~ using the default client builder implementation class provided by the
  ~ JAX-RS implementation provider.
  ~
  ~ See KEYCLOAK-15692, ELY-1891 issues & PRs of EAP7-1219 issue for details.
  ~
  ~ Note: This file (constituting the configuration of the Elytron client that
  ~       should be used) is to be discovered automagically per:
  ~
  ~ https://docs.wildfly.org/21/Client_Guide.html#wildfly-config-xml-discovery
  ~ since:
  ~       1) It's named 'wildfly-config.xml' and
  ~       2) Is present in the class path.
  ~
  ~ Alternatively, set the 'wildfly.config.url' system property to point the
  ~ client to the location where its configuration resides.
  ~
  -->
<configuration>
    <authentication-client xmlns="urn:elytron:client:1.4">
        <key-stores>
            <key-store name="client-side-truststore" type="JKS">
                <file name="${client.truststore:}"/>
                <key-store-clear-password password="${client.truststore.passphrase:secret}"/>
            </key-store>
        </key-stores>
        <ssl-contexts>
            <ssl-context name="wildfly-elytron-resteasy-client-context">
                <trust-store key-store-name="client-side-truststore"/>
                <provider-name name="${elytron.client.outbound.ssl.jsse.provider:}"/>
            </ssl-context>
        </ssl-contexts>
        <ssl-context-rules>
            <rule use-ssl-context="wildfly-elytron-resteasy-client-context"/>
        </ssl-context-rules>
    </authentication-client>
</configuration>
