emailVerificationSubject=Verificar email
emailVerificationBody=Alguém criou uma conta {2} com este endereço de e-mail. Se foi você, clique no link abaixo para verificar o seu endereço de email.\n\n{0}\n\nEste link irá expirar dentro de {3}.\n\nSe não foi você quem criou esta conta, basta ignorar esta mensagem.
emailVerificationBodyHtml=<p>Alguém criou uma conta {2} com este endereço de e-mail. Se foi você, clique no link abaixo para verificar o seu endereço de email.</p><p><a href="{0}">Link para verificação de endereço de email</a></p><p>Este link irá expirar dentro de {3}.</p><p>Se não foi você quem criou esta conta, basta ignorar esta mensagem.</p>
emailTestSubject=[KEYCLOAK] - Mensagem de teste SMTP
emailTestBody=Esta é uma mensagem de teste
emailTestBodyHtml=<p>Esta é uma mensagem de teste</p>
identityProviderLinkSubject=Vincular {0}
identityProviderLinkBody=Alguém quer vincular a sua conta "{1}" com a conta "{0}" do usuário {2} . Se foi você, clique no link abaixo para vincular as contas.\n\n{3}\n\nEste link irá expirar em {5}.\n\nSe você não quer vincular a conta, apenas ignore esta mensagem. Se você vincular as contas, você será capaz de logar em {1} fazendo login em {0}.
identityProviderLinkBodyHtml=<p>Alguém quer vincular a sua conta <b>{1}</b> com a conta <b>{0}</b> do usuário {2} . Se foi você, clique no link abaixo para vincular as contas.</p><p><a href="{3}">Link para confirmar vinculação de contas</a></p><p>Este link irá expirar em {5}.</p><p>Se você não quer vincular a conta, apenas ignore esta mensagem. Se você vincular as contas, você será capaz de logar em {1} fazendo login em {0}.</p>
passwordResetSubject=Redefinir senha
passwordResetBody=Alguém solicitou uma alteração de senha da sua conta {2}. Se foi você, clique no link abaixo para redefini-la.\n\n{0}\n\nEste link e código expiram em {3}.\n\nSe você não deseja redefinir sua senha, apenas ignore esta mensagem e nada será alterado.
passwordResetBodyHtml=<p>Alguém solicitou uma alteração de senha da sua conta {2}. Se foi você, clique no link abaixo para redefini-la.</p><p><a href="{0}">Link para redefinir a senha</a></p><p>Este link irá expirar em {3}.</p><p>Se você não deseja redefinir sua senha, apenas ignore esta mensagem e nada será alterado.</p>
executeActionsSubject=Atualize sua conta
executeActionsBody=Um administrador solicitou que você atualize sua conta {2} com a(s) seguinte(s) etapa(s): {3}. Clique no link abaixo para iniciar o processo.\n\n{0}\n\nEste link irá expirar em {4}.\n\nSe você não tem conhecimento de que o administrador solicitou isso, basta ignorar esta mensagem e nada será alterado.
executeActionsBodyHtml=<p>Um administrador solicitou que você atualize sua conta {2} com a(s) seguinte(s) etapa(s): {3}. Clique no link abaixo para iniciar o processo.</p><p><a href="{0}">Link para atualizar a conta</a></p><p>Este link irá expirar em {4}.</p><p>Se você não tem conhecimento de que o administrador solicitou isso, basta ignorar esta mensagem e nada será alterado.</p>
eventLoginErrorSubject=Erro de login
eventLoginErrorBody=Uma tentativa de login malsucedida da sua conta foi detectada em {0} de {1}. Se não foi você, por favor, entre em contato com um administrador.
eventLoginErrorBodyHtml=<p>Uma tentativa de login malsucedida da sua conta foi detectada em {0} de {1}. Se não foi você, por favor, entre em contato com um administrador.</p>
eventRemoveTotpSubject=Remover OTP
eventRemoveTotpBody=OTP foi removido da sua conta em {0} de {1}. Se não foi você, por favor, entre em contato com um administrador.
eventRemoveTotpBodyHtml=<p>OTP foi removido da sua conta em {0} de {1}. Se não foi você, por favor, entre em contato com um administrador.</p>
eventUpdatePasswordSubject=Atualizar senha
eventUpdatePasswordBody=Sua senha foi alterada em {0} de {1}. Se não foi você, por favor, entre em contato com um administrador.
eventUpdatePasswordBodyHtml=<p>Sua senha foi alterada em {0} de {1}. Se não foi você, por favor, entre em contato com um administrador.</p>
eventUpdateTotpSubject=Atualizar OTP
eventUpdateTotpBody=A autenticação de dois fatores foi atualizada para a sua conta em {0} de {1}. Se não foi você, por favor, entre em contato com um administrador.
eventUpdateTotpBodyHtml=<p>A autenticação de dois fatores foi atualizada para a sua conta em {0} de {1}. Se não foi você, por favor, entre em contato com um administrador.</p>
requiredAction.CONFIGURE_TOTP=Configurar Autenticação de Dois Fatores
requiredAction.TERMS_AND_CONDITIONS=Termos e Condições
requiredAction.UPDATE_PASSWORD=Atualizar Senha
requiredAction.UPDATE_PROFILE=Atualizar Perfil
requiredAction.VERIFY_EMAIL=Verificar Email

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#segundos|1#segundo|1<segundos}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#minutos|1#minuto|1<minutos}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#horas|1#hora|1<horas}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#dias|1#dia|1<dias}
emailVerificationBodyCode=Verifique o seu endereço de e-mail inserindo o seguinte código.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Verifique o seu endereço de e-mail inserindo o seguinte código.</p><p><b>{0}</b></p>
orgInviteSubject=Convite para participar da organização {0}
emailUpdateConfirmationSubject=Verifique nova mensagem
orgInviteBodyPersonalized=Olá, "{5}" "{6}".\n\nVocê foi convidado para participar da organização "{3}". Clique no link abaixo para entrar.\n\n{0}\n\nEsse link expira em {4}.\n\nSe você não quiser participar da organização ignore essa mensagem.
orgInviteBody=Você está sendo convidado para participar da organização {3}. Clique no link abaixo para entrar.\n\n{0}\n\nEsse link irá expirar em {4}.\n\nSe você não quiser participar da organização ignore essa mensagem.
orgInviteBodyHtml=<p>Você está sendo convidado para participar da organização {3}. Clique no link abaixo para entrar. </p><p><a href="{0}">Link para entrar na organização</a></p><p>Este link expira em {4}.</p><p>Se você não quiser participar da organização ignore essa mensagem.</p>
orgInviteBodyPersonalizedHtml=<p>Olá, {5} {6}.</p><p>Você foi convidado para entrar na organização {3}. Clique no link abaixo para entrar. </p><p><a href="{0}">Link para entrar na organização</a></p><p>Esse link expirará dentro de {4}.</p><p>Se você não quiser entrar na organização, ignore essa mensagem.</p>
eventUserDisabledByPermanentLockoutBody=Seu usuário foi desativado permanentemente devido a múltiplas tentativas fracassadas em {0}. Por favor, contate um administrador.
eventUserDisabledByTemporaryLockoutBody=Seu usuário foi desativado temporariamente devido a múltiplas tentativas fracassadas em {0}. Por favor, contate um administrador se necessário.
emailUpdateConfirmationBody=Para atualizar sua conta {2} com o endereço de email {1}, clique no link abaixo\n\n{0}\n\nEste link expirará em {3}.\n\nSe você não deseja prosseguir com esta modificação, apenas ignore esta mensagem.
emailUpdateConfirmationBodyHtml=<p>Para atualizar sua conta {2} com o endereço de email {1}, clique no link abaixo</p><p><a href="{0}">{0}</a></p><p>Este link expirará em {3}.</p><p>Se você não deseja prosseguir com esta modificação, apenas ignore esta mensagem.</p>
eventUpdateCredentialSubject=Atualizar credencial
eventUpdateCredentialBody=Sua credencial {0} foi alterada em {1} a partir de {2}. Se não foi você, por favor, contate um administrador.
eventUpdateCredentialBodyHtml=<p>Sua credencial {0} foi alterada em {1} a partir de {2}. Se não foi você, por favor, contate um administrador.</p>
eventRemoveCredentialSubject=Remover credencial
eventRemoveCredentialBody=A credencial {0} foi removida da sua conta em {1} a partir de {2}. Se não foi você, por favor, contate um administrador.
eventRemoveCredentialBodyHtml=<p>A credencial {0} foi removida da sua conta em {1} a partir de {2}. Se não foi você, por favor, contate um administrador.</p>
eventUserDisabledByTemporaryLockoutSubject=Usuário desativado por bloqueio temporário
eventUserDisabledByTemporaryLockoutHtml=<p>Seu usuário foi desativado temporariamente devido a múltiplas tentativas fracassadas em {0}. Por favor, contate um administrador se necessário.</p>
eventUserDisabledByPermanentLockoutSubject=Usuário desativado por bloqueio permanente
eventUserDisabledByPermanentLockoutHtml=<p>Seu usuário foi desativado permanentemente devido a múltiplas tentativas fracassadas em {0}. Por favor, contate um administrador.</p>
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=Gerar Códigos de Recuperação
