<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2016 Red Hat, Inc. and/or its affiliates
  ~ and other contributors as indicated by the <AUTHOR>
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<EntitiesDescriptor Name="urn:mace:shibboleth:testshib:two"
                    xmlns="urn:oasis:names:tc:SAML:2.0:metadata"
        >
    <EntityDescriptor entityID="http://localhost:8280/sales-metadata/">
        <SPSSODescriptor AuthnRequestsSigned="true"
                protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol urn:oasis:names:tc:SAML:1.1:protocol http://schemas.xmlsoap.org/ws/2003/07/secext">
            <NameIDFormat>urn:oasis:names:tc:SAML:2.0:nameid-format:transient
            </NameIDFormat>
            <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="http://localhost:8080/sales-metadata/"/>
            <AssertionConsumerService
                    Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="http://localhost:8080/sales-metadata/"
                    index="1" isDefault="true" />
            <KeyDescriptor use="signing">
                <dsig:KeyInfo xmlns:dsig="http://www.w3.org/2000/09/xmldsig#">
                    <dsig:X509Data>
                        <dsig:X509Certificate>
                            MIIDBTCCAe2gAwIBAgIITqRFxEpkfYUwDQYJKoZIhvcNAQELBQAwMDEuMCwGA1UEAxMlaHR0cDovL2xvY2FsaG9zdDo4MDgwL3NhbGVzLXBvc3Qtc2lnLzAgFw0yNDA2MjIwOTI0MjlaGA8yMTI0MDUyOTA5MjQyOVowMDEuMCwGA1UEAxMlaHR0cDovL2xvY2FsaG9zdDo4MDgwL3NhbGVzLXBvc3Qtc2lnLzCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAJrL6/hAdv+UY5gI+uW0ffpb+d7BzcO++woey3okbMzXIyJK4YzpQTt+7NZ7o8dDcgO57PPjuIQZMCFG7J9whD3K3uKqw5KsR1cXHnHKg7qO3YC3LcUMsZu4sBHYe1gN7+yPJWpZnT4rJHC46rHlZxdXOkDolG5MBl1csOMEuqWI5IXOJVrp7S+aXoRmtnKMcK+GMsBJLEuZnZj9xpcCCCdAB5I6dwJBYZPlGVp7ryAxZSkaqrztswvReMtS0cZVLohSsPmYxpd5Lx296Spuz31kD1hKUIfd7Aw6JZqau/8cH54Q1G3bXu/H9A5/OpcsXWZ3m+5qE51YDZtFKzSD4IUCAwEAAaMhMB8wHQYDVR0OBBYEFC0egYF+Q//sZh4Coa/UJxyRnPpMMA0GCSqGSIb3DQEBCwUAA4IBAQByc3yhN7bHiInhcsFmPYQVOcXHyh/IK6CvPPRSMr+CvCcZ+8BrfUkDCQDILnFMiLrAYMIVsYlkyT3gvzt8Wk3fnhOHGIwzZNeAg6FKSo7fgyEsUSbOLOuMTCWfF9H/s8GpnlJplFD1AlPgFYV8fOom8Efa/Q5vvYt+kCVuHAgcJYgHqV68tWyAe0qV+r68ED6FACsv29GO5nDumQPKoFCwzQPWasfRWf99diByOCM8Q4GXKEkE6w0vSjCW1UFWiO1EkEWoYDoMvrXGbpkTWq3QMVepPN2VNz+EuHxcyZefqQXlve8b49h3rWfdf35Z4mAX3UmObHuqAxz991iAU1+U
                        </dsig:X509Certificate>
                    </dsig:X509Data>
                </dsig:KeyInfo>
            </KeyDescriptor>
        </SPSSODescriptor>
        <Organization>
            <OrganizationName xmlns="urn:oasis:names:tc:SAML:2.0:metadata"
                              xml:lang="en">JBoss</OrganizationName>
            <OrganizationDisplayName xmlns="urn:oasis:names:tc:SAML:2.0:metadata"
                                     xml:lang="en">JBoss by Red Hat</OrganizationDisplayName>
            <OrganizationURL xmlns="urn:oasis:names:tc:SAML:2.0:metadata"
                             xml:lang="en">http://localhost:8080/sales-metadata/</OrganizationURL>
        </Organization>
        <ContactPerson contactType="technical">
            <GivenName>The</GivenName>
            <SurName>Admin</SurName>
            <EmailAddress><EMAIL></EmailAddress>
        </ContactPerson>
    </EntityDescriptor>
</EntitiesDescriptor>
