{"id": "test", "realm": "test", "enabled": true, "sslRequired": "external", "registrationAllowed": true, "resetPasswordAllowed": true, "editUsernameAllowed": true, "privateKey": "MIICXAIBAAKBgQCrVrCuTtArbgaZzL1hvh0xtL5mc7o0NqPVnYXkLvgcwiC3BjLGw1tGEGoJaXDuSaRllobm53JBhjx33UNv+5z/UMG4kytBWxheNVKnL6GgqlNabMaFfPLPCF8kAgKnsi79NMo+n6KnSY8YeUmec/p2vjO2NjsSAVcWEQMVhJ31LwIDAQABAoGAfmO8gVhyBxdqlxmIuglbz8bcjQbhXJLR2EoS8ngTXmN1bo2L90M0mUKSdc7qF10LgETBzqL8jYlQIbt+e6TH8fcEpKCjUlyq0Mf/vVbfZSNaVycY13nTzo27iPyWQHK5NLuJzn1xvxxrUeXI6A2WFpGEBLbHjwpx5WQG9A+2scECQQDvdn9NE75HPTVPxBqsEd2z10TKkl9CZxu10Qby3iQQmWLEJ9LNmy3acvKrE3gMiYNWb6xHPKiIqOR1as7L24aTAkEAtyvQOlCvr5kAjVqrEKXalj0Tzewjweuxc0pskvArTI2Oo070h65GpoIKLc9jf+UA69cRtquwP93aZKtW06U8dQJAF2Y44ks/mK5+eyDqik3koCI08qaC8HYq2wVl7G2QkJ6sbAaILtcvD92ToOvyGyeE0flvmDZxMYlvaZnaQ0lcSQJBAKZU6umJi3/xeEbkJqMfeLclD27XGEFoPeNrmdx0q10Azp4NfJAY+Z8KRyQCR2BEG+oNitBOZ+YXF9KCpH3cdmECQHEigJhYg+ykOvr1aiZUMFT72HU0jnmQe2FVekuG+LJUt2Tm7GtMjTFoGpf0JwrVuZN39fOYAlo+nTixgeW7X8Y=", "publicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrVrCuTtArbgaZzL1hvh0xtL5mc7o0NqPVnYXkLvgcwiC3BjLGw1tGEGoJaXDuSaRllobm53JBhjx33UNv+5z/UMG4kytBWxheNVKnL6GgqlNabMaFfPLPCF8kAgKnsi79NMo+n6KnSY8YeUmec/p2vjO2NjsSAVcWEQMVhJ31LwIDAQAB", "requiredCredentials": ["password"], "defaultRoles": ["user"], "webAuthnPolicySignatureAlgorithms": ["ES256", "RS256", "RS1"], "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyRpEntityName": "keycloak-webauthn-2FA", "webAuthnPolicyCreateTimeout": 60, "webAuthnPolicyAvoidSameAuthenticatorRegister": true, "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256", "RS256", "RS1"], "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessRpEntityName": "keycloak-webauthn-passwordless-2FA", "webAuthnPolicyPasswordlessCreateTimeout": 60, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": true, "smtpServer": {"from": "<EMAIL>", "host": "localhost", "port": "3025", "fromDisplayName": "Keycloak SSO", "replyTo": "<EMAIL>", "replyToDisplayName": "Keycloak no-reply", "envelopeFrom": "<EMAIL>"}, "users": [{"username": "test-user@localhost", "enabled": true, "email": "test-user@localhost", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "password"}], "realmRoles": ["user", "offline_access"], "clientRoles": {"test-app": ["customer-user"], "account": ["view-profile", "manage-account"]}}, {"username": "john-doh@localhost", "enabled": true, "email": "john-doh@localhost", "firstName": "<PERSON>", "lastName": "Doh", "credentials": [{"type": "password", "value": "password"}], "realmRoles": ["user"], "clientRoles": {"test-app": ["customer-user"], "account": ["view-profile", "manage-account"]}}, {"username": "keycloak-user@localhost", "enabled": true, "email": "keycloak-user@localhost", "credentials": [{"type": "password", "value": "password"}], "realmRoles": ["user"], "clientRoles": {"test-app": ["customer-user"], "account": ["view-profile", "manage-account"]}}, {"username": "topGroupUser", "enabled": true, "email": "<EMAIL>", "credentials": [{"type": "password", "value": "password"}], "groups": ["/topGroup"]}, {"username": "level2GroupUser", "enabled": true, "email": "<EMAIL>", "credentials": [{"type": "password", "value": "password"}], "groups": ["/topGroup/level2group"]}, {"username": "roleRichUser", "enabled": true, "email": "<EMAIL>", "credentials": [{"type": "password", "value": "password"}], "groups": ["/roleRichGroup/level2group"], "clientRoles": {"test-app-scope": ["test-app-allowed-by-scope", "test-app-disallowed-by-scope"]}}, {"username": "non-duplicate-email-user", "enabled": true, "email": "non-duplicate-email-user@localhost", "firstName": "<PERSON>", "lastName": "<PERSON>", "credentials": [{"type": "password", "value": "password"}], "realmRoles": ["user", "offline_access"], "clientRoles": {"test-app": ["customer-user"], "account": ["view-profile", "manage-account"]}}], "scopeMappings": [{"client": "third-party", "roles": ["user"]}, {"client": "test-app", "roles": ["user"]}, {"client": "test-app-scope", "roles": ["user", "admin"]}], "clients": [{"clientId": "test-app", "enabled": true, "baseUrl": "http://localhost:8180/auth/realms/master/app/auth", "redirectUris": ["http://localhost:8180/auth/realms/master/app/auth/*", "https://localhost:8543/auth/realms/master/app/auth/*"], "adminUrl": "http://localhost:8180/auth/realms/master/app/admin", "secret": "password"}, {"clientId": "root-url-client", "enabled": true, "rootUrl": "http://localhost:8180/foo/bar", "adminUrl": "http://localhost:8180/foo/bar", "baseUrl": "/baz", "redirectUris": ["http://localhost:8180/foo/bar/*", "https://localhost:8543/foo/bar/*"], "secret": "password"}, {"clientId": "test-app-scope", "enabled": true, "redirectUris": ["http://localhost:8180/auth/realms/master/app/*", "https://localhost:8543/auth/realms/master/app/*"], "secret": "password", "fullScopeAllowed": "false"}, {"clientId": "third-party", "enabled": true, "consentRequired": true, "baseUrl": "http://localhost:8180/auth/realms/master/app/auth", "redirectUris": ["http://localhost:8180/auth/realms/master/app/*", "https://localhost:8543/auth/realms/master/app/*"], "secret": "password"}, {"clientId": "test-app-authz", "enabled": true, "baseUrl": "/test-app-authz", "adminUrl": "/test-app-authz", "bearerOnly": false, "authorizationSettings": {"allowRemoteResourceManagement": true, "policyEnforcementMode": "ENFORCING", "resources": [{"name": "Admin Resource", "uri": "/protected/admin/*", "type": "http://test-app-authz/protected/admin", "scopes": [{"name": "admin-access"}]}, {"name": "Protected Resource", "uri": "/*", "type": "http://test-app-authz/protected/resource", "scopes": [{"name": "resource-access"}]}, {"name": "Premium Resource", "uri": "/protected/premium/*", "type": "urn:test-app-authz:protected:resource", "scopes": [{"name": "premium-access"}]}, {"name": "Main Page", "type": "urn:test-app-authz:protected:resource", "scopes": [{"name": "urn:test-app-authz:page:main:actionForAdmin"}, {"name": "urn:test-app-authz:page:main:actionForUser"}, {"name": "urn:test-app-authz:page:main:actionForPremiumUser"}]}], "policies": [{"name": "Any Admin Policy", "description": "Defines that adminsitrators can do something", "type": "role", "config": {"roles": "[{\"id\":\"admin\"}]"}}, {"name": "Any User Policy", "description": "Defines that any user can do something", "type": "role", "config": {"roles": "[{\"id\":\"user\"}]"}}, {"name": "Only Premium User Policy", "description": "Defines that only premium users can do something", "type": "role", "logic": "POSITIVE", "config": {"roles": "[{\"id\":\"customer-user-premium\"}]"}}, {"name": "All Users Policy", "description": "Defines that all users can do something", "type": "aggregate", "decisionStrategy": "AFFIRMATIVE", "config": {"applyPolicies": "[\"Any User Policy\",\"Any Admin Policy\",\"Only Premium User Policy\"]"}}, {"name": "Premium Resource Permission", "description": "A policy that defines access to premium resources", "type": "resource", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"Premium Resource\"]", "applyPolicies": "[\"Only Premium User Policy\"]"}}, {"name": "Administrative Resource Permission", "description": "A policy that defines access to administrative resources", "type": "resource", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"Admin Resource\"]", "applyPolicies": "[\"Any Admin Policy\"]"}}, {"name": "Protected Resource Permission", "description": "A policy that defines access to any protected resource", "type": "resource", "decisionStrategy": "AFFIRMATIVE", "config": {"resources": "[\"Protected Resource\"]", "applyPolicies": "[\"All Users Policy\"]"}}, {"name": "Action 1 on Main Page Resource Permission", "description": "A policy that defines access to action 1 on the main page", "type": "scope", "decisionStrategy": "AFFIRMATIVE", "config": {"scopes": "[\"urn:test-app-authz:page:main:actionForAdmin\"]", "applyPolicies": "[\"Any Admin Policy\"]"}}, {"name": "Action 2 on Main Page Resource Permission", "description": "A policy that defines access to action 2 on the main page", "type": "scope", "decisionStrategy": "AFFIRMATIVE", "config": {"scopes": "[\"urn:test-app-authz:page:main:actionForUser\"]", "applyPolicies": "[\"Any User Policy\"]"}}, {"name": "Action 3 on Main Page Resource Permission", "description": "A policy that defines access to action 3 on the main page", "type": "scope", "decisionStrategy": "AFFIRMATIVE", "config": {"scopes": "[\"urn:test-app-authz:page:main:actionForPremiumUser\"]", "applyPolicies": "[\"Only Premium User Policy\"]"}}]}, "redirectUris": ["/test-app-authz/*"], "secret": "secret"}, {"clientId": "named-test-app", "name": "My Named Test App", "enabled": true, "baseUrl": "http://localhost:8180/namedapp/base", "redirectUris": ["http://localhost:8180/namedapp/base/*", "https://localhost:8543/namedapp/base/*"], "adminUrl": "http://localhost:8180/namedapp/base/admin", "secret": "password"}, {"clientId": "var-named-test-app", "name": "Test App Named - ${client_account}", "enabled": true, "baseUrl": "http://localhost:8180/varnamedapp/base", "redirectUris": ["http://localhost:8180/varnamedapp/base/*", "https://localhost:8543/varnamedapp/base/*"], "adminUrl": "http://localhost:8180/varnamedapp/base/admin", "secret": "password"}, {"clientId": "direct-grant", "enabled": true, "directAccessGrantsEnabled": true, "secret": "password", "webOrigins": ["http://localtest.me:8180"]}], "roles": {"realm": [{"name": "user", "description": "Have User privileges"}, {"name": "admin", "description": "Have Administrator privileges"}, {"name": "customer-user-premium", "description": "Have User Premium privileges"}, {"name": "sample-realm-role", "description": "Sample realm role"}, {"name": "attribute-role", "description": "has attributes assigned", "attributes": {"hello": ["world", "keycloak"]}}, {"name": "realm-composite-role", "description": "Realm composite role containing client role", "composite": true, "composites": {"realm": ["sample-realm-role"], "client": {"test-app": ["sample-client-role"], "account": ["view-profile"]}}}], "client": {"test-app": [{"name": "customer-user", "description": "Have Customer User privileges"}, {"name": "customer-admin", "description": "Have Customer Admin privileges"}, {"name": "sample-client-role", "description": "Sample client role"}, {"name": "customer-admin-composite-role", "description": "Have Customer Admin privileges via composite role", "composite": true, "composites": {"realm": ["customer-user-premium"], "client": {"test-app": ["customer-admin"]}}}], "test-app-scope": [{"name": "test-app-allowed-by-scope", "description": "Role allowed by scope in test-app-scope"}, {"name": "test-app-disallowed-by-scope", "description": "Role disallowed by scope in test-app-scope"}]}}, "groups": [{"name": "topGroup", "attributes": {"topAttribute": ["true"]}, "realmRoles": ["user"], "subGroups": [{"name": "level2group", "realmRoles": ["admin"], "clientRoles": {"test-app": ["customer-user"]}, "attributes": {"level2Attribute": ["true"]}}, {"name": "level2group2", "realmRoles": ["admin"], "clientRoles": {"test-app": ["customer-user"]}, "attributes": {"level2Attribute": ["true"]}}]}, {"name": "roleRichGroup", "attributes": {"topAttribute": ["true"]}, "realmRoles": ["user", "realm-composite-role"], "clientRoles": {"account": ["manage-account"]}, "subGroups": [{"name": "level2group", "realmRoles": ["admin"], "clientRoles": {"test-app": ["customer-user", "customer-admin-composite-role"]}, "attributes": {"level2Attribute": ["true"]}}, {"name": "level2group2", "realmRoles": ["admin"], "clientRoles": {"test-app": ["customer-user"]}, "attributes": {"level2Attribute": ["true"]}}]}], "clientScopeMappings": {"test-app": [{"client": "third-party", "roles": ["customer-user"]}, {"client": "test-app-scope", "roles": ["customer-admin-composite-role"]}], "test-app-scope": [{"client": "test-app-scope", "roles": ["test-app-allowed-by-scope"]}]}, "authenticationFlows": [{"alias": "browser-webauthn", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": false, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "DISABLED", "priority": 25, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 26, "autheticatorFlow": true, "flowAlias": "browser-webauth-organization", "userSetupAllowed": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "browser-webauthn-forms", "userSetupAllowed": false, "authenticatorFlow": true}]}, {"alias": "browser-webauth-organization", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 10, "autheticatorFlow": true, "flowAlias": "browser-webauthn-conditional-organization", "userSetupAllowed": false}]}, {"alias": "browser-webauthn-conditional-organization", "description": "Flow to determine if the organization identity-first login is to be used", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "organization", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"alias": "browser-webauthn-forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": false, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "webauthn-authenticator", "requirement": "REQUIRED", "priority": 21, "userSetupAllowed": false, "authenticatorFlow": false}]}, {"alias": "browser-webauthn-passwordless", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": false, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "browser-webauthn-passwordless-forms", "userSetupAllowed": false, "authenticatorFlow": true}]}, {"alias": "browser-webauthn-passwordless-forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": false, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "webauthn-authenticator", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "webauthn-authenticator-passwordless", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "authenticatorFlow": false}]}, {"alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "authenticatorFlow": true}]}, {"alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}]}, {"alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "authenticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "authenticatorFlow": true}]}, {"alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "client-secret-jwt", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "client-x509", "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false, "authenticatorFlow": false}]}, {"alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "OPTIONAL", "priority": 30, "userSetupAllowed": false, "authenticatorFlow": false}]}, {"alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}]}, {"alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "authenticatorFlow": true}]}, {"alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}]}, {"alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "basic-auth", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "basic-auth-otp", "requirement": "DISABLED", "priority": 30, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 40, "userSetupAllowed": false, "authenticatorFlow": false}]}, {"alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "authenticatorFlow": true}]}, {"alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "authenticatorFlow": false}]}, {"alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "authenticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "OPTIONAL", "priority": 40, "userSetupAllowed": false, "authenticatorFlow": false}]}, {"alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "authenticatorFlow": false}]}], "authenticatorConfig": [{"alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": true, "priority": 51, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": true, "defaultAction": false, "priority": 52, "config": {}}], "browserFlow": "browser-webauthn", "internationalizationEnabled": true, "supportedLocales": ["en", "de"], "defaultLocale": "en", "eventsListeners": ["jboss-logging", "event-queue"]}