doLogIn=サインイン
doRegister=登録
doRegisterSecurityKey=登録
doCancel=キャンセル
doSubmit=送信
doBack=戻る
doYes=はい
doNo=いいえ
doContinue=続ける
doIgnore=無視
doAccept=承認
doDecline=却下
doForgotPassword=パスワードをお忘れですか？
doClickHere=クリックしてください
doImpersonate=代理ログイン
doTryAgain=再試行してください
doTryAnotherWay=別の方法を試してください
kerberosNotConfigured=Kerberosは設定されていません
kerberosNotConfiguredTitle=Kerberosは設定されていません
bypassKerberosDetail=Kerberosでログインしていないか、ブラウザーでKerberosログインの設定がされていません。他の手段でログインするには「続ける」をクリックしてください。
kerberosNotSetUp=Kerberosが設定されていません。ログインできません。
registerTitle=登録
loginAccountTitle=アカウントにサインイン
loginTitle={0}にサインイン
loginTitleHtml={0}
impersonateTitle={0}ユーザーの代理
impersonateTitleHtml=<strong>{0}</strong>ユーザーの代理
realmChoice=レルム
unknownUser=不明なユーザー
loginTotpTitle=モバイルオーセンティケーターのセットアップ
loginProfileTitle=アカウント情報の更新
loginTimeout=ログイン試行がタイムアウトしました。ログインは最初から開始されます。
oauthGrantTitle={0}へのアクセスを許可
oauthGrantTitleHtml={0}
errorTitle=申し訳ございません。
errorTitleHtml=<strong>申し訳ございません。</strong>
emailVerifyTitle=メールアドレス検証
emailForgotTitle=パスワードをお忘れですか？
updatePasswordTitle=パスワードの更新
codeSuccessTitle=成功コード
codeErrorTitle=エラーコード: {0}
displayUnsupported=要求された表示タイプがサポートされていません
browserRequired=ログインに必要なブラウザー
browserContinue=ログインを完了するために必要なブラウザー
browserContinuePrompt=ブラウザーを開いてログインを続行しますか？ [y/n]:
browserContinueAnswer=y


termsTitle=利用規約
termsText=<p>利用規約はここに書きます</p>
termsPlainText=定義される利用規約。
recaptchaFailed=無効なreCAPTCHA
recaptchaNotConfigured=reCAPTCHAが必須ですが設定されていません
consentDenied=同意が拒否されました。
noAccount=新規ユーザーですか？
username=ユーザー名
usernameOrEmail=ユーザー名またはメールアドレス
firstName=名
givenName=名
fullName=氏名
lastName=姓
familyName=姓
email=メールアドレス
password=パスワード
passwordConfirm=パスワード（確認）
passwordNew=新しいパスワード
passwordNewConfirm=新しいパスワード（確認）
rememberMe=ログイン状態の保存
authenticatorCode=ワンタイムコード
address=住所
street=番地
locality=市区町村
region=都道府県
postal_code=郵便番号
country=国
emailVerified=検証済みメールアドレス
gssDelegationCredential=GSS委譲クレデンシャル
profileScopeConsentText=ユーザープロファイル
emailScopeConsentText=メールアドレス
addressScopeConsentText=アドレス
phoneScopeConsentText=電話番号
offlineAccessScopeConsentText=オフラインアクセス
samlRoleListScopeConsentText=ロール
rolesScopeConsentText=ユーザーロール
restartLoginTooltip=ログインの再開
loginTotpIntro=このアカウントにアクセスするには、ワンタイムパスワードジェネレーターを設定する必要があります
loginTotpStep1=次のいずれかのアプリケーションをモバイルにインストールします。
loginTotpStep2=アプリケーションを開き、バーコードをスキャンします。
loginTotpStep3=アプリケーションから提供されたワンタイムコードを入力し、「送信」をクリックしてセットアップを終了します。
loginTotpStep3DeviceName=OTPデバイスの管理に役立つデバイス名を指定します。
loginTotpManualStep2=アプリケーションを開き、キーを入力します:
loginTotpManualStep3=アプリケーションで設定できる場合は、次の設定値を使用します。
loginTotpUnableToScan=スキャンできませんか？
loginTotpScanBarcode=バーコードをスキャンしますか？
loginCredential=クレデンシャル
loginOtpOneTime=ワンタイムコード
loginTotpType=タイプ
loginTotpAlgorithm=アルゴリズム
loginTotpDigits=桁
loginTotpInterval=間隔
loginTotpCounter=カウンター
loginTotpDeviceName=デバイス名
loginTotp.totp=時間ベース
loginTotp.hotp=カウンターベース
loginChooseAuthenticator=ログイン方法を選択してください
oauthGrantRequest=これらのアクセス権限を付与しますか？
inResource=in
emailVerifyInstruction1=メールアドレスを確認する手順を記載したメールを次のアドレス {0} に送信しました。
emailVerifyInstruction2=メールで確認コードを受け取っていませんか？
emailVerifyInstruction3=メールを再送信します。
emailLinkIdpTitle=リンク {0}
emailLinkIdp1={0}の{1}アカウントをあなたの{2}アカウントとリンクするための手順を記載したメールを送信しました。
emailLinkIdp2=メールで確認コードを受け取っていませんか？
emailLinkIdp3=メールを再送信します。
emailLinkIdp4=別のブラウザーでメールを確認済みの場合
emailLinkIdp5=続けるには
backToLogin=&laquo; ログインに戻る
emailInstruction=ユーザー名またメールアドレスを入力してください。新しいパスワードの設定方法をお知らせします。
copyCodeInstruction=このコードをコピーし、あなたのアプリケーションにペーストしてください:
pageExpiredTitle=ページの有効期限が切れています
pageExpiredMsg1=ログインプロセスを再開
pageExpiredMsg2=ログイン処理を続行
personalInfo=個人情報:
role_admin=管理者
role_realm-admin=レルム管理者
role_create-realm=レルムの作成
role_create-client=クライアントの作成
role_view-realm=レルムの参照
role_view-users=ユーザーの参照
role_view-applications=アプリケーションの参照
role_view-clients=クライアントの参照
role_view-events=イベントの参照
role_view-identity-providers=アイデンティティープロバイダーの参照
role_manage-realm=レルムの管理
role_manage-users=ユーザーの管理
role_manage-applications=アプリケーションの管理
role_manage-identity-providers=アイデンティティープロバイダーの管理
role_manage-clients=クライアントの管理
role_manage-events=イベントの管理
role_view-profile=プロファイルの参照
role_manage-account=アカウントの管理
role_manage-account-links=アカウントリンクの管理
role_read-token=トークンの参照
role_offline-access=オフラインアクセス
client_account=アカウント
client_account-console=アカウントコンソール
client_security-admin-console=セキュリティー管理コンソール
client_admin-cli=管理CLI
client_realm-management=レルム管理
client_broker=ブローカー
requiredFields=必須フィールド
invalidUserMessage=無効なユーザー名またはパスワードです。
invalidUsernameMessage=ユーザー名が無効です。
invalidUsernameOrEmailMessage=ユーザー名またはメールアドレスが無効です。
invalidPasswordMessage=パスワードが無効です。
invalidEmailMessage=無効なメールアドレスです。
accountDisabledMessage=アカウントが無効です。管理者に連絡してください。
accountTemporarilyDisabledMessage=無効なユーザー名またはパスワードです。
accountPermanentlyDisabledMessage=無効なユーザー名またはパスワードです。
accountTemporarilyDisabledMessageTotp=無効なオーセンティケーターコードです。
accountPermanentlyDisabledMessageTotp=無効なオーセンティケーターコードです。
expiredCodeMessage=ログインタイムアウトが発生しました。再度サインインしてください。
expiredActionMessage=アクションは期限切れです。今すぐログインしてください。
expiredActionTokenNoSessionMessage=アクションは期限切れです。
expiredActionTokenSessionExistsMessage=アクションは期限切れです。もう一度やり直してください。
missingFirstNameMessage=名を指定してください。
missingLastNameMessage=姓を指定してください。
missingEmailMessage=メールアドレスを指定してください。
missingUsernameMessage=ユーザー名を指定してください。
missingPasswordMessage=パスワードを指定してください。
missingTotpMessage=オーセンティケーターコードを指定してください。
missingTotpDeviceNameMessage=デバイス名を指定してください。
notMatchPasswordMessage=パスワードが一致していません。
invalidPasswordExistingMessage=既存のパスワードが不正です。
invalidPasswordBlacklistedMessage=無効なパスワード: パスワードがブラックリストに含まれています。
invalidPasswordConfirmMessage=パスワード確認が一致していません。
invalidTotpMessage=無効なオーセンティケーターコードです。
usernameExistsMessage=既に存在するユーザー名です。
emailExistsMessage=既に存在するメールアドレスです。
federatedIdentityExistsMessage={0}{1}のユーザーは既に存在します。そのアカウントをリンクするにはアカウント管理にログインしてください。
confirmLinkIdpTitle=既に存在するアカウントです
federatedIdentityConfirmLinkMessage={0}{1}のユーザーは既に存在します。継続しますか？
federatedIdentityConfirmReauthenticateMessage={1}でアカウントをリンクするために{0}として認証します
nestedFirstBrokerFlowMessage={0}ユーザー{1}は既知のユーザーにリンクされていません。
confirmLinkIdpReviewProfile=プロファイルの確認
confirmLinkIdpContinue=既存のアカウントに追加する
configureTotpMessage=アカウントを有効にするにはモバイル認証器のセットアップが必要です。
updateProfileMessage=アカウントを有効にするにはユーザープロファイルの更新が必要です。
updatePasswordMessage=アカウントを有効にするにはパスワードの更新が必要です。
resetPasswordMessage=パスワードを変更する必要があります。
verifyEmailMessage=アカウントを有効にするにはメールアドレスの確認が必要です。
linkIdpMessage=アカウントを{0}とリンクするにはメールアドレスの確認が必要です。
emailSentMessage=詳細な手順を記載したメールをすぐに受信してください。
emailSendErrorMessage=メールの送信に失敗しました。しばらく時間をおいてから再度お試しください。
accountUpdatedMessage=アカウントが更新されました。
accountPasswordUpdatedMessage=パスワードが更新されました。
delegationCompleteHeader=ログインに成功しました
delegationCompleteMessage=このブラウザーのウィンドウを閉じて、コンソールアプリケーションに戻ることができます。
delegationFailedHeader=ログインに失敗しました
delegationFailedMessage=このブラウザーウィンドウを閉じてコンソールアプリケーションに戻り、再度ログインを試みることができます。
noAccessMessage=アクセスがありません
invalidPasswordMinLengthMessage=無効なパスワード: 最小{0}の長さが必要です。
invalidPasswordMinDigitsMessage=無効なパスワード: 少なくとも{0}文字の数字を含む必要があります。
invalidPasswordMinLowerCaseCharsMessage=無効なパスワード: 少なくとも{0}文字の小文字を含む必要があります。
invalidPasswordMinUpperCaseCharsMessage=無効なパスワード: 少なくとも{0}文字の大文字を含む必要があります。
invalidPasswordMinSpecialCharsMessage=無効なパスワード: 少なくとも{0}文字の特殊文字を含む必要があります。
invalidPasswordNotUsernameMessage=無効なパスワード: ユーザー名と同じパスワードは禁止されています。
invalidPasswordRegexPatternMessage=無効なパスワード: 正規表現パターンと一致しません。
invalidPasswordHistoryMessage=無効なパスワード: 直近{0}個のパスワードのいずれかと同じパスワードは禁止されています。
invalidPasswordGenericMessage=無効なパスワード: 新しいパスワードはパスワードポリシーと一致しません。
failedToProcessResponseMessage=レスポンスの処理に失敗しました
httpsRequiredMessage=HTTPSが必須です
realmNotEnabledMessage=レルムが有効化されていません
invalidRequestMessage=無効なリクエストです
failedLogout=ログアウトに失敗しました
unknownLoginRequesterMessage=不明なログイン要求元です
loginRequesterNotEnabledMessage=ログイン要求元は有効ではありません
bearerOnlyMessage=bearer-onlyのアプリケーションはブラウザーログインを開始することが許可されていません
standardFlowDisabledMessage=与えられたresponse_typeでクライアントはブラウザーログインを開始することが許可されていません。標準フローは無効です。
implicitFlowDisabledMessage=与えられたresponse_typeでクライアントはブラウザーログインを開始することが許可されていません。インプリシットフローは無効です。
invalidRedirectUriMessage=無効なリダイレクトURIです
unsupportedNameIdFormatMessage=サポートされていないNameID Formatです
invalidRequesterMessage=無効な要求元です
registrationNotAllowedMessage=登録は許可されていません
resetCredentialNotAllowedMessage=クレデンシャルのリセットは許可されていません
permissionNotApprovedMessage=パーミッションは承認されていません。
noRelayStateInResponseMessage=アイデンティティープロバイダーからのレスポンスにRelayStateがありません。
insufficientPermissionMessage=アイデンティティーにリンクするには不十分なパーミッションです。
couldNotProceedWithAuthenticationRequestMessage=アイデンティティープロバイダーへの認証リクエストを続行できませんでした。
couldNotObtainTokenMessage=アイデンティティープロバイダーからトークンを取得できませんでした。
unexpectedErrorRetrievingTokenMessage=アイデンティティープロバイダーからのトークン取得で予期せぬエラーが発生しました。
unexpectedErrorHandlingResponseMessage=アイデンティティープロバイダーからのレスポンスを処理する際に予期せぬエラーが発生しました。
identityProviderAuthenticationFailedMessage=認証に失敗しました。アイデンティティープロバイダーを使用して認証できませんでした。
couldNotSendAuthenticationRequestMessage=アイデンティティープロバイダーに認証リクエストを送信することができませんでした。
unexpectedErrorHandlingRequestMessage=アイデンティティープロバイダーへの認証リクエストを処理する際に予期せぬエラーが発生しました。
invalidAccessCodeMessage=無効なアクセスコードです。
sessionNotActiveMessage=セッションが有効ではありません。
invalidCodeMessage=エラーが発生しました。アプリケーションを介して再度ログインしてください。
identityProviderUnexpectedErrorMessage=アイデンティティープロバイダーによる認証の際に予期せぬエラーが発生しました
identityProviderNotFoundMessage=該当の識別子を持つアイデンティティープロバイダーが見つかりませんでした。
identityProviderLinkSuccess=メールを正常に検証しました。元のブラウザーに戻ってログインしてください。
staleCodeMessage=このページはもはや有効ではありませんので、アプリケーションに戻り再度サインインしてください
realmSupportsNoCredentialsMessage=レルムはクレデンシャルタイプをサポートしていません。
credentialSetupRequired=ログインできません。クレデンシャルのセットアップが必要です。
identityProviderNotUniqueMessage=レルムは複数のアイデンティティープロバイダーをサポートしています。どのアイデンティティープロバイダーが認証に使用されるべきか判断できませんでした。
emailVerifiedMessage=メールアドレスが確認できました。
staleEmailVerificationLink=クリックしたリンクは古いリンクであり、有効ではありません。おそらく、すでにメールを確認しています。
identityProviderAlreadyLinkedMessage={0}によって返された連携済みアイデンティティーは、すでに別のユーザーにリンクされています。
confirmAccountLinking=アイデンティティープロバイダー{1}のアカウント{0}とあなたのアカウントとのリンクを確認してください。
confirmEmailAddressVerification=メールアドレス{0}の有効性を確認してください。
confirmExecutionOfActions=次の操作を実行します
backToApplication=&laquo; アプリケーションに戻る
missingParameterMessage=不足パラメーター\: {0}
clientNotFoundMessage=クライアントが見つかりません。
clientDisabledMessage=クライアントが無効になっています。
invalidParameterMessage=無効なパラメーター: {0}
alreadyLoggedIn=既にログインしています。
differentUserAuthenticated=すでにこのセッションで異なるユーザー''{0}''として認証されています。まずログアウトしてください。
brokerLinkingSessionExpired=要求されたブローカーアカウントのリンクは、現在のセッションでは有効ではありません。
proceedWithAction=&raquo; 続行するにはここをクリックしてください
requiredAction.CONFIGURE_TOTP=OTPの設定
requiredAction.TERMS_AND_CONDITIONS=利用規約
requiredAction.UPDATE_PASSWORD=パスワードの更新
requiredAction.UPDATE_PROFILE=プロファイルの更新
requiredAction.VERIFY_EMAIL=メールアドレスの検証
doX509Login=次のユーザーとしてログインします:
clientCertificate=X509クライアント証明書:
noCertificate=[証明書なし]


pageNotFound=ページが見つかりません
internalServerError=内部サーバーエラーが発生しました
console-username=ユーザー名:
console-password=パスワード:
console-otp=ワンタイムパスワード:
console-new-password=新しいパスワード:
console-confirm-password=パスワードの確認:
console-update-password=パスワードの更新が必要です。
console-verify-email=メールアドレスを確認する必要があります。確認コードを含むメールを{0}に送信しました。このコードを以下に入力してください。
console-email-code=メールコード:
console-accept-terms=利用規約に同意しますか？ [y/n]:
console-accept=y

# Openshift messages
openshift.scope.user_info=ユーザー情報
openshift.scope.user_check-access=ユーザーアクセス情報
openshift.scope.user_full=フルアクセス
openshift.scope.list-projects=プロジェクトの一覧表示

# SAML authentication
saml.post-form.title=認証リダイレクト
saml.post-form.message=リダイレクトしています。お待ちください。
saml.post-form.js-disabled=JavaScriptが無効になっています。有効にすることを強くお勧めします。継続するには、下のボタンをクリックしてください。

#authenticators
otp-display-name=オーセンティケーターアプリケーション
otp-help-text=オーセンティケーターアプリケーションから取得した確認コードを入力してください。
password-display-name=パスワード
password-help-text=パスワードを入力してサインインします。
auth-username-form-display-name=ユーザー名
auth-username-form-help-text=ユーザー名を入力してサインインを開始します
auth-username-password-form-display-name=ユーザー名とパスワード
auth-username-password-form-help-text=ユーザー名とパスワードを入力してサインインしてください。

# WebAuthn
webauthn-display-name=パスキー
webauthn-help-text=パスキーを使用してサインインしてください。
webauthn-passwordless-display-name=パスキー
webauthn-passwordless-help-text=パスワードレスサインインにパスキーを使用します。
webauthn-login-title=パスキーログイン
webauthn-registration-title=パスキーの登録
webauthn-available-authenticators=利用可能なパスキー

# WebAuthn Error
webauthn-error-title=パスキーエラー
webauthn-error-registration=パスキーの登録に失敗しました。<br /> {0}
webauthn-error-api-get=パスキーによる認証に失敗しました。<br /> {0}
webauthn-error-different-user=最初に認証されたユーザーは、パスキーによって認証されたユーザーではありません。
webauthn-error-auth-verification=パスキーの認証結果が無効です。<br /> {0}
webauthn-error-register-verification=パスキーの登録結果が無効です。<br /> {0}
webauthn-error-user-not-found=パスキーで認証された不明なユーザー。
identity-provider-redirector=別のアイデンティティープロバイダーと接続する
frontchannel-logout.title=ログアウト
frontchannel-logout.message=以下のアプリケーションからログアウトしました。
logoutConfirmTitle=ログアウト
logoutConfirmHeader=ログアウトしますか？
doLogout=ログアウト
readOnlyUsernameMessage=読み取り専用のため、ユーザー名を更新することはできません。
deletingAccountForbidden=アカウントを削除するために必要な権限がありません。管理者に連絡してください。
loginIdpReviewProfileTitle=アカウント情報の更新
reauthenticate=続行するためには再ログインしてください
oauthGrantTos=利用規約。
oauthGrantPolicy=プライバシーポリシー。
emailUpdateConfirmationSentTitle=確認メールが送信されました
termsAcceptanceRequired=利用規約に同意する必要があります。
acceptTerms=利用規約に同意します
deleteCredentialTitle={0}の削除
deleteCredentialMessage={0}を削除しますか？
hidePassword=パスワードの非表示
showPassword=パスワードの表示
website=Webページ
phoneNumber=電話番号
gender=性別
birthday=誕生日
zoneinfo=タイムゾーン
logoutOtherSessions=他のデバイスからのサインアウト
totpAppFreeOTPName=FreeOTP
totpAppGoogleName=Google Authenticator
verifyOAuth2DeviceUserCode=デバイスによるコードを入力し、送信をクリックしてください
oauth2DeviceInvalidUserCodeMessage=無効なコードです、再実行してください。
errorDeletingAccount=アカウントの削除中にエラーが発生しました
emailUpdateConfirmationSent=確認メールが{0}に送信されました。指示に従い、メールアドレスの更新を完了してください。
usb=USB
internal=Internal
emailUpdatedTitle=メールアドレスが更新されました
unknown=Unknown
emailUpdated=アカウントのメールアドレスが{0}に更新されました。
nfc=NFC
oauth2DeviceExpiredUserCodeMessage=コードの有効期限が切れてします。デバイスに戻りもう一度お試し下さい。
bluetooth=Bluetooth
organizationScopeConsentText=組織
phoneNumberVerified=電話番号（確認済）
totpAppMicrosoftAuthenticatorName=Microsoft Authenticator
oauth2DeviceAuthorizationGrantDisabledMessage=このクライアントはOAuth 2.0 デバイス認可グラントフローを開始することが許可されておらず、このクライアントに対してこのフローは無効化されています。
userDeletedSuccessfully=ユーザーの削除に成功しました
authenticateStrong=続行するには強力な認証が必要です。
oauthGrantReview=レビューすることができます
updateEmailTitle=メールアドレスの更新
oauth2DeviceVerificationTitle=デバイスログイン
oauth2DeviceVerificationCompleteHeader=デバイスログインに成功しました
oauth2DeviceVerificationCompleteMessage=このブラウザーのウィンドウを閉じて、デバイスに戻ることができます。
oauth2DeviceVerificationFailedHeader=デバイスログインに失敗しました
oauthGrantInformation={0}がデータをどのように扱うかを確認し、{0}を信頼してください。
doConfirmDelete=削除の確認
notMemberOfAnyOrganization=ユーザーはどの組織にも所属していません。
oauth2DeviceVerificationFailedMessage=このブラウザーのウィンドウを閉じて、使用しているデバイスに戻り、再度接続を試してください。
oauth2DeviceConsentDeniedMessage=デバイスの接続が拒否されました。
configureBackupCodesMessage=アカウントを有効にするには、バックアップコードを設定する必要があります。
identityProviderInvalidResponseMessage=アイデンティティープロバイダーからのレスポンスが無効です。
identityProviderMissingCodeOrErrorMessage=アイデンティティープロバイダーからのレスポンスにcodeまたはerrorパラメーターがありません。
emailVerifiedAlreadyMessage=メールアドレスは確認済みです。
auth-recovery-code-prompt=リカバリーコード#{0}
auth-recovery-code-header=リカバリー認証コードでログインする
recovery-codes-error-invalid=無効なリカバリー認証コード
recovery-code-config-header=リカバリー認証コード
recovery-codes-print=印刷
passkey-login-title=パスキーログイン
passkey-available-authenticators=利用可能なパスキー
passkey-unsupported-browser-text=このブラウザーではパスキーがサポートされていません。別のブラウザーを試すか、管理者にお問い合わせください。
idp-email-verification-display-name=メールアドレス検証
idp-email-verification-help-text=メールアドレスを検証してアカウントをリンクします。
error-invalid-blank=値を指定してください。
error-empty=値を指定してください。
error-invalid-length={1}と{2}の間の長さでなければなりません.
error-invalid-length-too-short=最小の長さは{1}です。
error-invalid-uri=無効なURLです。
error-person-name-invalid-character=値に無効な文字が含まれています。
error-username-invalid-character=値に無効な文字が含まれています。
invalidPasswordNotEmailMessage=無効なパスワード: メールアドレスと同じパスワードは禁止されています。
invalidPasswordNotContainsUsernameMessage=無効なパスワード: ユーザー名を含めることはできません。
successLogout=ログアウトしました
acrNotFulfilled=認証要件が満たされていません。
webauthn-unsupported-browser-text=このブラウザーではWebAuthnはサポートされていません。別のブラウザーを試すか、管理者にお問い合わせください。
webauthn-doAuthenticate=パスキーでサインイン
webauthn-createdAt-label=作成されました
webauthn-registration-init-label-prompt=登録したパスキーのラベルを入力してください。
webauthn-registration-init-label=パスキー（デフォルトラベル）
passkey-createdAt-label=作成されました
passkey-autofill-select=パスキーの選択
emailVerifyInstruction4=メールアドレスを確認するため、これから{0}のアドレスに指示のメールを送ります。
emailVerifyResend=検証メールを再送する
emailVerifySend=検証メールを送信する
sessionLimitExceeded=セッション数が多すぎます。
identityProviderLogoutFailure=SAML IdPログアウトの失敗
error-invalid-email=メールアドレスが無効です。
error-invalid-value=無効な値です。
error-invalid-number=無効な数値です。
error-number-out-of-range={1}と{2}の間の数値でなければなりません。
error-number-out-of-range-too-small={1}の最小値でなければなりません。
error-number-out-of-range-too-big={2}の最大値でなければなりません。
error-invalid-uri-scheme=無効なURLスキームです。
error-invalid-uri-fragment=無効なURLフラグメントです。
error-user-attribute-required=このフィールドを指定してください。
error-user-attribute-read-only=このフィールドは読み取り専用です。
error-reset-otp-missing-id=OTP設定を選択してください.
federatedIdentityUnavailableMessage=アイデンティティープロバイダー{1}で認証されたユーザー{0}が存在しません。管理者に連絡してください。
confirmOverrideIdpTitle=ブローカーのリンクがすでに存在します
federatedIdentityConfirmOverrideMessage=アカウント{0}を{1}のアカウント{2}にリンクしようとしています。しかし、アカウントは既に別の{3}のアカウント{4}とリンクされています。既存のリンクを新しいアカウントに置き換えますか？
updateEmailMessage=アカウントを有効にするには、メールアドレスを更新する必要があります。
confirmOverrideIdpContinue=はい、現在のアカウントとのリンクを上書きします
invalidPasswordMaxLengthMessage=無効なパスワード: 最大長は{0}です。
identityProviderMissingStateMessage=アイデンティティープロバイダーからのレスポンスにstateパラメーターがありません。
identityProviderInvalidSignatureMessage=アイデンティティープロバイダーからのレスポンスの署名が無効です。
requiredAction.CONFIGURE_RECOVERY_AUTHN_CODES=リカバリーコードの生成
requiredAction.webauthn-register-passwordless=パスワードレスWebAuthn登録
requiredAction.webauthn-register=WebAuthn登録
invalidTokenRequiredActions=リンクに含まれる必須アクションは無効です。
saml.artifactResolutionServiceInvalidResponse=アーティファクトを解決できません。
otp-reset-description=どのOTPの設定を削除する必要がありますか？
auth-x509-client-username-form-display-name=X509証明書
auth-x509-client-username-form-help-text=X509クライアント証明書を使用してサインインします。
auth-recovery-authn-code-form-display-name=リカバリー認証コード
auth-recovery-authn-code-form-help-text=以前に生成されたリストからリカバリー認証コードを入力します。
auth-recovery-code-info-message=指定されたリカバリーコードを入力してください。
recovery-codes-download=ダウンロード
recovery-codes-copy=コピー
recovery-codes-copied=コピーしました
recovery-codes-confirmation-message=これらのコードは安全な場所に保存しました。
recovery-codes-action-complete=セットアップを完了する
recovery-codes-action-cancel=セットアップをキャンセルする
recovery-codes-download-file-header=これらのリカバリコードを安全な場所に保管してください。
recovery-codes-download-file-date=これらのコードは以下で生成されました。
recovery-codes-label-default=リカバリーコード
passkey-doAuthenticate=パスキーでサインイン
identity-provider-login-label=または次の方法でサインイン
idp-username-password-form-display-name=ユーザー名とパスワード
idp-username-password-form-help-text=ログインしてアカウントをリンクしてください。
finalDeletionConfirmation=アカウントを削除すると元に戻すことはできません。アカウントを維持するには「キャンセル」をクリックしてください。
irreversibleAction=このアクションは不可逆的です。
deleteAccountConfirm=アカウントの削除の確認
deletingImplies=アカウントを削除することを意味します:
errasingData=すべてのデータを消去
loggingOutImmediately=直ちにログアウト
accountUnusable=このアカウントでアプリケーションを使用することはできません
access-denied=アクセスが拒否されました。
access-denied-when-idp-auth={0}での認証時にアクセスが拒否されました。
error-invalid-multivalued-size=属性{0}は少なくとも{1}、多くても{2} {2,choice,0#values|1#value|1<values}でなければならない。
organization.confirm-membership.title=組織${kc.org.name}に参加しようとしています。
organization.member.register.title=アカウントを作成して${kc.org.name}の組織に参加する
organization.select=続行するには、以下の組織を選択します。
notMemberOfOrganization=ユーザーは{0}の組織のメンバーではありません。
error-invalid-length-too-long=最大の長さは{2}です。
insufficientLevelOfAuthentication=要求された認証レベルが満たされていません。
error-pattern-no-match=無効な値です。
recovery-code-config-warning-title=これらのリカバリーコードは、このページを離れるともう表示されません
emailInstructionUsername=ユーザー名を入力してください。新しいパスワードの作成方法をお知らせします。
error-invalid-date=無効な日付です。
organization.confirm-membership=以下のリンクをクリックすると、{0}の組織のメンバーとなります。
recovery-code-config-warning-message=必ず印刷、ダウンロード、またはパスワードマネージャーにコピーして保管してください。この設定をキャンセルすると、これらのリカバリーコードはアカウントから削除されます。
recovery-codes-download-file-description=リカバリーコードは、オーセンティケーターにアクセスできない場合にアカウントにサインインするための 1 回限りのパスコードです。
federatedIdentityUnmatchedEssentialClaimMessage=アイデンティティープロバイダーから発行されたIDトークンが、設定された必須クレームと一致しません。管理者に連絡してください。
cookieNotFoundMessage=再起動ログインCookieが見つかりません。有効期限が切れているか、削除されているか、ブラウザーでCookieが無効になっている可能性があります。Cookieが無効になっている場合は、有効にしてください。「アプリケーションに戻る」をクリックして再度ログインしてください。
linkIdpActionTitle={0}をリンク中
linkIdpActionMessage=アカウントを{0}にリンクしますか？
