emailVerificationSubject=Verificer email
emailVerificationBody=Nogen har oprettet en {2} konto med denne email adresse. Hvis dette var dig, bedes du trykke på forbindet herunder for at verificere din email adresse \n\n{0}\n\nDette link vil udløbe inden for {3}.\n\nHvis det var dig der har oprettet denne konto, bedes du se bort fra denne mail.
emailVerificationBodyHtml=<p>Nogen har oprettet en {2} konto med denne email adresse. Hvis dette var dig, bedes du trykke på forbindet herunder for at verificere din email adresse</p><p><a href="{0}">Link til email verificering</a></p><p>Dette link vil udløbe inden for {3}.</p><p>Hvis det var dig der har oprettet denne konto, bedes du se bort fra denne mail.</p>
emailTestSubject=[KEYCLOAK] - SMTP test besked
emailTestBody=Dette er en test besked
emailTestBodyHtml=<p>Dette er en test besked</p>
identityProviderLinkSubject=Link {0}
identityProviderLinkBody=Nogen vil forbinde din "{1}" konto med "{0}" kontoen som er tilknyttet brugeren {2}. Hvis dette var dig, bedes du klikke på forbindet herunder for at forbinde de to konti\n\n{3}\n\nDette link vil udløbe efter {5}.\n\nHvis du ikke vil forbinde disse konti, kan du bare ignore denne besked. Hvis du vælger at forbinde de to konti, kan du logge ind som {1} via {0}.
identityProviderLinkBodyHtml=<p>Nogen vil forbinde din <b>{1}</b> konto med <b>{0}</b> kontoen som er tilknyttet brugeren {2}. Hvis dette var dig, bedes du klikke på forbindet herunder for at forbinde de to konti</p><p><a href="{3}">Bekræft</a></p><p>Dette link vil udløbe efter {5}.</p><p>nHvis du ikke vil forbinde disse konti, kan du bare ignore denne besked. Hvis du vælger at forbinde de to konti, kan du logge ind som {1} via {0}.</p>
passwordResetSubject=Gendan adgangskode
passwordResetBody=Nogen har forsøgt at nulstille adgangskoden til {2}. Hvis dette var dig, bedes du klikke på linket herunder for at nulstille adgangskoden.\n\n{0}\n\nDette link og kode vil udløbe efter {3}.\n\nHvis du ikke ønsker at nulstille din adgangskode, kan du se bort fra denne besked.
passwordResetBodyHtml=<p>Nogen har forsøgt at nulstille adgangskoden til {2}. Hvis dette var dig, bedes du klikke på linket herunder for at nulstille adgangskoden.</p><p><a href="{0}">Nulstil adgangskode</a></p><p>Dette link og kode vil udløbe efter {3}.</p><p>Hvis du ikke ønsker at nulstille din adgangskode, kan du se bort fra denne besked.</p>
executeActionsSubject=Opdater din konto
executeActionsBody=Din administrator beder dig opdatere din {2} konto ved at udføre følgende handling(er): {3}. Klik på linket herunder for at starte processen.\n\n{0}\n\nDette link udløber efter {4}.\n\nHvis du ikke mener at din administrator har efterspurgt dette, kan du blot se bort fra denne besked.
executeActionsBodyHtml=<p>Din administrator beder dig opdatere din {2} konto ved at udføre følgende handling(er): {3}. Klik på linket herunder for at starte processen.</p><p><a href="{0}">Opdater konto</a></p><p>Dette link udløber efter {4}.</p><p>Hvis du ikke mener at din administrator har efterspurgt dette, kan du blot se bort fra denne besked.</p>
eventLoginErrorSubject=Logind fejl
eventLoginErrorBody=Et fejlet logind forsøg er blevet registreret på din konto d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgående.
eventLoginErrorBodyHtml=<p>Et fejlet logind forsøg er blevet registreret på din konto d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgående.</p>
eventRemoveTotpSubject=Fjern OTP
eventRemoveTotpBody=OTP er blevet fjernet fra din konto d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgående.
eventRemoveTotpBodyHtml=<p>OTP er blevet fjernet fra din konto d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgående.</p>
eventUpdatePasswordSubject=Opdater adgangskode
eventUpdatePasswordBody=Din adgangskode er blevet opdateret d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgående.
eventUpdatePasswordBodyHtml=<p>Din adgangskode er blevet opdateret d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgående.</p>
eventUpdateTotpSubject=Opdater OTP
eventUpdateTotpBody=OTP blev opdateret på din konto d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgående.
eventUpdateTotpBodyHtml=<p>OTP blev opdateret på din konto d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgående.</p>

requiredAction.CONFIGURE_TOTP=Konfigurer OTP
requiredAction.TERMS_AND_CONDITIONS=Vilkår og Betingelser
requiredAction.UPDATE_PASSWORD=Opdater Adgangskode
requiredAction.UPDATE_PROFILE=Opdater Profil
requiredAction.VERIFY_EMAIL=Verificer Email

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds={0,choice,0#sekunder|1#sekund|1<sekunder}
linkExpirationFormatter.timePeriodUnit.minutes={0,choice,0#minutter|1#minut|1<minutter}
linkExpirationFormatter.timePeriodUnit.hours={0,choice,0#timer|1#time|1<timer}
linkExpirationFormatter.timePeriodUnit.days={0,choice,0#dage|1#dag|1<dage}

emailVerificationBodyCode=Verificer din email adresse ved at indtaste følgende kode.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Verificer din email adresse ved at indtaste følgende kode.</p><p><b>{0}</b></p>
