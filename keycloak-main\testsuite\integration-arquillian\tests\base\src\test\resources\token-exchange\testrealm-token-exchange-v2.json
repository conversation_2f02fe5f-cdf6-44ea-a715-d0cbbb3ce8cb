{"id": "e788774a-7c61-42ab-8927-b9efd38d1f73", "realm": "test", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxTemporaryLockouts": 0, "bruteForceStrategy": "MULTIPLE", "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "ccbbde4e-c98d-4355-9719-04338e4266fc", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "e788774a-7c61-42ab-8927-b9efd38d1f73", "attributes": {}}, {"id": "b1d62124-3a2c-4744-9b28-d390db6691c6", "name": "all-target", "description": "all-target", "composite": false, "clientRole": false, "containerId": "e788774a-7c61-42ab-8927-b9efd38d1f73", "attributes": {}}, {"id": "5f6173f5-7696-49e0-806e-7acf38e7d9e9", "name": "default-roles-test", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["view-profile", "manage-account"]}}, "clientRole": false, "containerId": "e788774a-7c61-42ab-8927-b9efd38d1f73", "attributes": {}}, {"id": "92e8da3e-d4a9-4327-8284-417996a5167f", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "e788774a-7c61-42ab-8927-b9efd38d1f73", "attributes": {}}], "client": {"realm-management": [{"id": "d9431fa2-9892-44a4-8b81-0a3f03481758", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "8a2850d1-7cae-474a-a035-dc5f713044de", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "0c9e2178-915a-4d81-99d5-ffe469068e1e", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "5328d2d3-ca78-496c-806d-208391367ac7", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "f1a215bd-0b5e-43d6-9ff0-eb39b3bccb77", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "18b7684a-58ef-4555-a2f6-a5962f5df3c7", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "663e3cac-13a9-4e85-95a0-821477584e27", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "0cd9ab56-1730-4506-ae38-bbd55918d73e", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "758162b7-8519-4d77-b3af-67a2d4c7ecac", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "53c8d0df-12d3-4ebc-9bcc-29f68552377b", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "3b0e11dd-a3ab-4cf7-8503-656485528c58", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "6ab563d5-b71c-44e7-a751-6dbfed348463", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "63e12297-bd94-40a8-ba6d-90a4a0f1ee4e", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "59b9ee81-6e04-40e9-9db7-11a52469f9a5", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["manage-clients", "query-groups", "view-authorization", "view-events", "view-realm", "query-realms", "view-clients", "query-users", "manage-realm", "view-users", "view-identity-providers", "manage-authorization", "impersonation", "manage-events", "manage-users", "create-client", "manage-identity-providers", "query-clients"]}}, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "f320e82f-b3f9-4e1e-a690-52047b2fd5d6", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "e03422f8-a256-46ff-8abc-f1c1bf045558", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "fe50cef5-3940-49b5-b727-d4d79ab7eb42", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "1a73efa6-e71b-48af-97ab-fc7ea43c78f8", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "6f3bef10-484d-446e-9bb4-370ecedcc79d", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}, {"id": "3d2124af-e46f-4e9a-a15a-a591864f9e93", "name": "uma_protection", "composite": false, "clientRole": true, "containerId": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "attributes": {}}], "requester-client": [], "security-admin-console": [], "target-client1": [{"id": "b7eb747d-7aac-4e36-8ade-5b9875c9ed07", "name": "target-client1-role", "description": "", "composite": false, "clientRole": true, "containerId": "192692cd-c5e4-42ff-a7ec-d5cb6228c0c7", "attributes": {}}], "account-console": [], "target-client2": [{"id": "548ae349-d3c5-4b7b-a1df-95e966ec0c1a", "name": "target-client2-role", "description": "", "composite": false, "clientRole": true, "containerId": "8d76ccf3-d0c2-4110-9120-06533cf9949c", "attributes": {}}], "broker": [{"id": "eec8fd31-5a9a-44e6-b1c5-7b8fc610cbf3", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "52fbbee7-44ca-4a36-814f-95f18f632994", "attributes": {}}], "requester-client-public": [], "target-client3": [], "invalid-requester-client": [], "subject-client": [], "admin-cli": [], "account": [{"id": "4d92df58-f573-4dcc-9428-83c67595b0d3", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "fb6a501b-6bbc-4fca-82da-16ac27d7854c", "attributes": {}}, {"id": "9c24c177-8221-4ced-aae7-5784d54cd506", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "fb6a501b-6bbc-4fca-82da-16ac27d7854c", "attributes": {}}, {"id": "8cfc7177-52e3-46f8-82a3-d8bf126242a7", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "fb6a501b-6bbc-4fca-82da-16ac27d7854c", "attributes": {}}, {"id": "7b4eec7a-ee1c-4eaf-a7bf-d52b555d46fd", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "fb6a501b-6bbc-4fca-82da-16ac27d7854c", "attributes": {}}, {"id": "fb9dd19d-2f96-4b37-8a5c-425117f5cae0", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "fb6a501b-6bbc-4fca-82da-16ac27d7854c", "attributes": {}}, {"id": "ad43f7f2-a444-4c87-b6ac-c141deff3b4d", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "fb6a501b-6bbc-4fca-82da-16ac27d7854c", "attributes": {}}, {"id": "baf95379-bdb9-4af2-adbb-5e91772dd1f6", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "fb6a501b-6bbc-4fca-82da-16ac27d7854c", "attributes": {}}, {"id": "636e5071-74e9-4fb2-b9c2-6eff04336e57", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "fb6a501b-6bbc-4fca-82da-16ac27d7854c", "attributes": {}}]}}, "groups": [], "defaultRole": {"id": "5f6173f5-7696-49e0-806e-7acf38e7d9e9", "name": "default-roles-test", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "e788774a-7c61-42ab-8927-b9efd38d1f73"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256", "RS256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256", "RS256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "users": [{"id": "38d2d491-6639-4de9-8de8-94c54373a672", "username": "john", "firstName": "<PERSON>", "lastName": "Bar", "email": "<EMAIL>", "emailVerified": false, "createdTimestamp": 1732884105204, "enabled": true, "totp": false, "credentials": [{"id": "2eea9617-dd53-4902-92d0-d3c119c46e17", "type": "password", "userLabel": "My password", "createdDate": 1732884115119, "secretData": "{\"value\":\"GNjvIqBcbM+mqCUt7nqYgl696zKTjNO1wLnvPkDfMFA=\",\"salt\":\"txHDSAn9HgpQ7GE8Be9zTQ==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":5,\"algorithm\":\"argon2\",\"additionalParameters\":{\"hashLength\":[\"32\"],\"memory\":[\"7168\"],\"type\":[\"id\"],\"version\":[\"1.3\"],\"parallelism\":[\"1\"]}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-test"], "clientRoles": {"target-client1": ["target-client1-role"], "target-client2": ["target-client2-role"]}, "notBefore": 0, "groups": []}, {"id": "c5eda8d5-be98-4719-96ad-755cbf36c8ec", "username": "mike", "firstName": "<PERSON>", "lastName": "Bar", "email": "<EMAIL>", "emailVerified": false, "createdTimestamp": 1732884105204, "enabled": true, "totp": false, "credentials": [{"id": "8d581ba9-c7ab-4877-909f-d1c1db66c702", "type": "password", "userLabel": "My password", "createdDate": 1732884115119, "secretData": "{\"value\":\"GNjvIqBcbM+mqCUt7nqYgl696zKTjNO1wLnvPkDfMFA=\",\"salt\":\"txHDSAn9HgpQ7GE8Be9zTQ==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":5,\"algorithm\":\"argon2\",\"additionalParameters\":{\"hashLength\":[\"32\"],\"memory\":[\"7168\"],\"type\":[\"id\"],\"version\":[\"1.3\"],\"parallelism\":[\"1\"]}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-test", "all-target"], "clientRoles": {"target-client1": ["target-client1-role"]}, "notBefore": 0, "groups": []}, {"id": "d1a6f32c-1d47-4c45-b5ab-35b8070d2a70", "username": "service-account-invalid-requester-client", "emailVerified": false, "createdTimestamp": **********808, "enabled": true, "totp": false, "serviceAccountClientId": "invalid-requester-client", "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-test"], "notBefore": 0, "groups": []}, {"id": "ce0f45f7-2929-46e8-b8e9-a1d59e645495", "username": "service-account-realm-management", "emailVerified": false, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "realm-management", "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-test"], "clientRoles": {"realm-management": ["uma_protection"]}, "notBefore": 0, "groups": []}, {"id": "cccb9363-685a-4575-8e20-5854797468d2", "username": "service-account-requester-client", "emailVerified": false, "createdTimestamp": **********976, "enabled": true, "totp": false, "serviceAccountClientId": "requester-client", "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": []}, {"id": "f0c55466-85c8-4f1d-abd8-ccce9d8ec549", "username": "service-account-subject-client", "emailVerified": false, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "subject-client", "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-test"], "clientRoles": {"target-client1": ["target-client1-role"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "optional-scope2", "roles": ["all-target"]}, {"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"target-client1": [{"clientScope": "default-scope1", "roles": ["target-client1-role"]}], "target-client2": [{"clientScope": "optional-scope2", "roles": ["target-client2-role"]}], "account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "fb6a501b-6bbc-4fca-82da-16ac27d7854c", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/test/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/test/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "7b18cc0e-c42f-4ca6-b4e6-2ca7a891d600", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/test/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/test/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "2edeb7ac-af3e-494e-80c7-2420bde5e9d9", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "539a834b-db63-4066-b1ee-a9656189857e", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "client.use.lightweight.access.token.enabled": "true", "post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "52fbbee7-44ca-4a36-814f-95f18f632994", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "true", "post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "cdaf7934-1eea-4b2d-ae64-327a67ffab7b", "clientId": "invalid-requester-client", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "secret", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"realm_client": "false", "oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "standard.token.exchange.enabled": "true", "frontchannel.logout.session.required": "true", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "display.on.consent.screen": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["service_account", "acr", "roles", "basic"], "optionalClientScopes": []}, {"id": "54b8e1b4-e912-4821-9335-3f0c0d2f4a2d", "clientId": "disabled-requester-client", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "secret", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"realm_client": "false", "oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "standard.token.exchange.enabled": "false", "post.logout.redirect.uris": "+", "frontchannel.logout.session.required": "true", "oauth2.device.authorization.grant.enabled": "false", "display.on.consent.screen": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["service_account", "acr", "default-scope1", "roles", "basic"], "optionalClientScopes": ["optional-scope2"]}, {"id": "a11ebbaf-c0fd-46df-9fe7-64e94ac8d945", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "B5Xigicu7waE006slTqw33uTI223m6Wh", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "authorizationServicesEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "true", "client.secret.creation.time": "**********", "post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "service_account", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "952643a3-2943-4734-9b51-8fa5956ebf54", "clientId": "requester-client", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "secret", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"realm_client": "false", "oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "standard.token.exchange.enabled": "true", "post.logout.redirect.uris": "+", "frontchannel.logout.session.required": "true", "oauth2.device.authorization.grant.enabled": "false", "display.on.consent.screen": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["service_account", "acr", "default-scope1", "roles", "basic"], "optionalClientScopes": ["optional-scope2", "offline_access"]}, {"id": "952643a3-2943-4734-9b51-8fa5956ebf55", "clientId": "requester-client-2", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "secret", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"realm_client": "false", "oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "standard.token.exchange.enabled": "true", "post.logout.redirect.uris": "+", "frontchannel.logout.session.required": "true", "oauth2.device.authorization.grant.enabled": "false", "display.on.consent.screen": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["service_account", "acr", "default-scope1", "roles", "basic"], "optionalClientScopes": ["optional-scope2", "offline_access"]}, {"id": "2daeae03-ff78-4f79-8e72-1c4d443e1655", "clientId": "requester-client-public", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"realm_client": "false", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "standard.token.exchange.enabled": "true", "frontchannel.logout.session.required": "true", "oauth2.device.authorization.grant.enabled": "false", "display.on.consent.screen": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["acr", "roles", "basic"], "optionalClientScopes": []}, {"id": "9d94d530-3335-4bb9-bea8-e9476a812473", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/test/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/test/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "client.use.lightweight.access.token.enabled": "true", "post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "da9492ab-d8c5-4a38-a832-39923838e289", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "202be39e-6622-4a07-aa7b-a4f3784f9a3d", "clientId": "subject-client", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "secret", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"realm_client": "false", "oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "standard.token.exchange.enabled": "true", "post.logout.redirect.uris": "+", "frontchannel.logout.session.required": "true", "oauth2.device.authorization.grant.enabled": "false", "display.on.consent.screen": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "290368fd-9ce8-491a-bb62-0b02386f3eee", "name": "subject-client", "protocol": "openid-connect", "protocolMapper": "oidc-audience-mapper", "consentRequired": false, "config": {"included.client.audience": "subject-client", "id.token.claim": "false", "lightweight.claim": "false", "access.token.claim": "true", "introspection.token.claim": "true"}}, {"id": "a59b1ac2-3de2-4913-89f5-c29ebd8a3f06", "name": "requester-client", "protocol": "openid-connect", "protocolMapper": "oidc-audience-mapper", "consentRequired": false, "config": {"included.client.audience": "requester-client", "id.token.claim": "false", "lightweight.claim": "false", "introspection.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "false"}}], "defaultClientScopes": ["service_account", "acr", "roles", "profile", "basic"], "optionalClientScopes": []}, {"id": "192692cd-c5e4-42ff-a7ec-d5cb6228c0c7", "clientId": "target-client1", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"realm_client": "false", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["acr", "roles", "basic"], "optionalClientScopes": []}, {"id": "8d76ccf3-d0c2-4110-9120-06533cf9949c", "clientId": "target-client2", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"realm_client": "false", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["acr", "roles", "basic"], "optionalClientScopes": []}, {"id": "656a1dee-8020-41a2-80fa-594a8093d58f", "clientId": "target-client3", "name": "", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "nI2vCSLpYQ0GHoTfnDMeu9BODpjfj6rT", "redirectUris": ["/*"], "webOrigins": ["/*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"realm_client": "false", "oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["acr", "roles", "basic"], "optionalClientScopes": []}], "clientScopes": [{"id": "ea98ca03-30e0-4c31-9973-421ca0e9c48c", "name": "basic", "description": "OpenID Connect scope for add all basic claims to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "8ec2a414-15a7-4bb7-89a1-7188a69c19db", "name": "auth_time", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "AUTH_TIME", "introspection.token.claim": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "auth_time", "jsonType.label": "long"}}, {"id": "34dc2703-df85-45ef-a8c1-a164fff6bdad", "name": "sub", "protocol": "openid-connect", "protocolMapper": "oidc-sub-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "f1ae15e7-9f30-4161-b4b4-2e497669d311", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${addressScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "6a87f6f2-b64d-46b6-a1b5-05a23280036f", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "introspection.token.claim": "true", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "ce5a8e01-ab35-40bc-958d-23a19a0bea51", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${emailScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "378219e7-2d85-4182-a104-0b760ac510dc", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "93024495-e985-4ad5-bd71-46d92c0e47fc", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "623db9d8-be8f-4c63-874a-2d68511f56cf", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "4f3567af-62bb-49e3-81c3-f631ece34d0f", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "d05841da-b6be-42a0-9a06-73d1aee3571d", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${profileScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "ecb3a4ce-40d9-4fd1-aee4-e0a03f6ed2db", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "304cccb7-8dff-4642-bcbb-a02f9c2c3760", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "long"}}, {"id": "e693b91d-ace5-4fc8-b773-e5f3d1b977c5", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "402bf576-5e6b-4066-bc7e-3cdc38f5a1ca", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "1622ae6c-f0b1-4e7c-919f-dea5bead1404", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "e7664219-d7ec-48dc-b240-93dff7fc3443", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "69a974f4-3869-4442-80a6-187e6e6ce1a8", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "8bf84e05-6697-4f9e-bf52-d478197b2726", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "ba5057c1-a653-486f-afd1-1e5c8a92c47c", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "4ae2526f-77e1-4fc8-9c78-8f0a070a9816", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "16a14431-2f83-465c-b689-70cbd133efd5", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "db45c182-5719-4607-a5cf-8aa112d31b08", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "927feb11-43ca-42b9-92de-d78eec193bc1", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "ec40fb51-d729-4881-921b-b587590f4268", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}]}, {"id": "7664e49c-ce13-4c98-87b7-dffaca048d6d", "name": "optional-scope2", "description": "", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}}, {"id": "2ae9c589-e0f8-43dc-923a-b481cd7eb7bb", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "b9749b0e-1f0f-4a67-ad43-733289b47dd0", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "ea7ff94a-63da-4139-b14c-b1947677c2c8", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "3b6371b8-ae71-4cdd-b807-bf247449505c", "name": "saml_organization", "description": "Organization Membership", "protocol": "saml", "attributes": {"display.on.consent.screen": "false"}, "protocolMappers": [{"id": "c5be2d4b-7a17-456f-9d23-ffcd59581b6d", "name": "organization", "protocol": "saml", "protocolMapper": "saml-organization-membership-mapper", "consentRequired": false, "config": {}}]}, {"id": "df23893a-1cfa-4ce1-b2e1-271f8acd5174", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "0487b356-ddcb-47f2-9c1b-46815422ac0f", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}]}, {"id": "cf5db3b0-be4f-4937-8cea-288683c2ea3a", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "a130f51d-94c4-4721-b7eb-922eaab9ad66", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "f6dbc11a-f838-4a5a-8a37-7225340a3aed", "name": "optional-scope3", "description": "", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}}, {"id": "9468f958-ad5b-49ef-87f6-3ceaf2c95a45", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${phoneScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "c2ed1036-4d62-4013-9b5d-29f2301ec742", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "0a397fe6-c965-4dbd-ab2b-1a972ec75085", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "39659cde-871e-4a56-917d-d55acb1857bd", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "7a2458da-d7af-48f2-b47b-8c38305397e1", "name": "default-scope1", "description": "", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}}, {"id": "119e9743-1757-41ab-ad62-c6ecac6dfde4", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "${rolesScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "ea6da298-4ca4-461e-9c5f-c1403579151a", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "d0caf67c-60db-4180-b60c-0ce33ce2193a", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "18a1df58-7462-4205-8ff4-01bca000793c", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "da0f8245-3480-45af-b64d-55997fbe1b65", "name": "service_account", "description": "Specific scope for a client enabled for service accounts", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "5eb7b1d2-ca61-449d-92cf-ee424fd47bf1", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "introspection.token.claim": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "d7baae08-a934-4307-9fc7-32de1e5f6099", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "introspection.token.claim": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"id": "41052c9d-c540-4d68-a1bf-51b55e8b468a", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "introspection.token.claim": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String"}}]}, {"id": "e4bc61cb-22f5-473b-988e-5a6717544030", "name": "organization", "description": "Additional claims about the organization a subject belongs to", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${organizationScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "30dd5081-e494-4651-a247-4ac4f48346c0", "name": "organization", "protocol": "openid-connect", "protocolMapper": "oidc-organization-membership-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "organization", "jsonType.label": "String"}}]}], "defaultDefaultClientScopes": ["role_list", "roles", "acr", "basic"], "defaultOptionalClientScopes": [], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "referrerPolicy": "no-referrer", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging", "event-queue"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "a29d9fa1-1a58-4199-8fef-85033050069f", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "e9767dba-fb59-4ecc-a873-dcb1ff6b73e4", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "840a750b-ca05-4fb4-9d96-5b820ef45fe3", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "b09d78bf-96dc-4726-ba0b-91ebe882633c", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "76b2ee49-e40f-407b-9a34-e375c466d244", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-role-list-mapper", "oidc-address-mapper", "oidc-usermodel-attribute-mapper", "saml-user-attribute-mapper", "oidc-full-name-mapper", "oidc-usermodel-property-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-property-mapper"]}}, {"id": "b31a7412-9ea6-4066-ad5b-84ba897706bb", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "b0135f8f-e87a-4949-8403-94fd89925a1f", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-property-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper", "oidc-full-name-mapper", "oidc-usermodel-attribute-mapper", "oidc-address-mapper", "saml-role-list-mapper"]}}, {"id": "d85b2fc3-24d6-4e5a-90e9-878ddef983ee", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}], "org.keycloak.keys.KeyProvider": [{"id": "bb34c674-9079-49a8-a507-03d2e7fa4d0e", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEogIBAAKCAQEApuC7ooZH2JSzp3u087qdsT/G4cWe8NUlbYQxHhTOfSAUp2UzpJqbwfKh7FMvaQA5gb9/JM3ic7z4j/zu277KV4WC91XRaGotnwsdDSEWd2oi7ijQCjJbGeHpMzlr+K9bRn3beG7s53xPgfQ+LD6dEW/jVfj0Ez2aulq5MtYcPHXGV9tDE+WOXGKuUxGkzNMXysxG/B0+LQkRRb63KhlZ4P6tON/rQalKJCGVCaKpv5TkKgs6vZcIc+zvaQF7WRnDIo4donasd+83d7U+s7UEQpnyjN29oNPjZ/G6WvfaSnpeeyojJrb3ZiBdJ9/SkmYa6jgoA0uXP3iCrnO1UH66fQIDAQABAoIBAEc3dEwFU2b9+veYZx52vzJ0HcufO1fbFsh4mD+WsJKcnMPdgB8Yri0kf27sBzkf0+rnzvpldOVguZE1Z7hW4WJdg4lLUf1mBscvhnviaYSXwoVx3Grll5RhoVRvhoEQUPsx3fY1HJwjpIi4s8nNBNn6roGzp3IIod4ytoOX9V/lT010U+Voz+CWCT4hfibe+NN8ZRKQirRvjgP3T95QARGaENiwKz1k5I6igqVqlAU9VI4c/iiimquL+Xj3E34Asox+EriqkoOu9zsxjW4hTpw3VZlut9yK+3WkntDylpQ+9rK68t+Ku5KH0RrL9hAXBksdLiw0IBkR4ftlZx/3bp8CgYEA649FHy3EhFzD6Qu4OOpoCsN/HbjO/uINKE6TFeIcHI5VO4sWXt0Pk4vrKXBQhmHH2d/f8yK7Ov2DC+uKc8lo6pySJtzTlcTjTZx9AX8wJzYcj3v1Fao4ELQ/Xsl8iBSS1z0W2tVqvwU1YurGAM7pcyTVWr+nNSeDC9sdI8/u4JMCgYEAtVvDcMiERmH/xKZyy8PWGrjWqyIAN/0oLgDDtzvheXslIrwy5ImKlo7r6A9B1276azjegVB7dCUKb04Z/PTmazJEs73EaeUIN2aAKdcJIQGUadZBz0mWiX5t38j0je6M9CnKY/EeuAasYeWpAONfA5EW8m6CGOc2wfmXGjzfsq8CgYAlth4ey7j2Z9OFb7bihcR6VKn5HCknbREmLs4lZrmRaMgEbFP8g++Gc5QGYlDB8s2H9+tmhVzsKMHeGqtjtg7x4HcVeaz2ATtSpYbtwmr3HBaecf3epg2rvu+WQTOKpaCMjN8n0ZrxUegmGefxIS8GnBL8IEqom9bDAyU7IoLfuQKBgE6Lz7mpHqDdEMk6zb0ytCgepdq4fx/ApQZHjnxtktnqW8sPd3tDlqJehA2djgagKhUGLFgVY2KKKFrMmLTV3HXQ77BpIAg9CbH00s1MNMf2xzCPezErzeb+uteRUVmnV6Tn9KAUbhmFymMb9HZOA3in/ihBdFcUzMJ2NttGQhxBAoGAZ+qRT1e8R92nz2cmrirZaiR9JxiHW1xGr0V9YRSbrCHF6uOG0jk/sJaA3zklJtbxG9WAPZZIyHEPbdn3zK03787RI+yuUyboYEjnKhGOJ8R/Htro94pSuF5b4NnCmkVZyOd2ONnX2ju0ujfvjtVm/QWvIbQyKnmk/yowT6RNw7U="], "keyUse": ["SIG"], "certificate": ["MIIClzCCAX8CBgGTd/BVmjANBgkqhkiG9w0BAQsFADAPMQ0wCwYDVQQDDAR0ZXN0MB4XDTI0MTEyOTEyMzkxOVoXDTM0MTEyOTEyNDA1OVowDzENMAsGA1UEAwwEdGVzdDCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKbgu6KGR9iUs6d7tPO6nbE/xuHFnvDVJW2EMR4Uzn0gFKdlM6Sam8HyoexTL2kAOYG/fyTN4nO8+I/87tu+yleFgvdV0WhqLZ8LHQ0hFndqIu4o0AoyWxnh6TM5a/ivW0Z923hu7Od8T4H0Piw+nRFv41X49BM9mrpauTLWHDx1xlfbQxPljlxirlMRpMzTF8rMRvwdPi0JEUW+tyoZWeD+rTjf60GpSiQhlQmiqb+U5CoLOr2XCHPs72kBe1kZwyKOHaJ2rHfvN3e1PrO1BEKZ8ozdvaDT42fxulr32kp6XnsqIya292YgXSff0pJmGuo4KANLlz94gq5ztVB+un0CAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAQbVccd4xZLPC3MV05nyOAfSyO61IZOdiqosQ5QuW35ms6wlmSiQycyTzb4dc0zDLtvYILFilzYTz5bJukFrmMYhlQomce2EpF1BTcdHYJzMxJH4cqWR+Sn7PLW7iIFGwxNw53jbTaxOjCWez/Dngmm+XOnphOVyQkR9ZummHsJC16dpEhiqmTD7ABmO/sUDh6SMHcb2PPa5/1BEVqgA9icVFRq8QjkRWpcicnQDgnBmEo3F0hcN9HUjwL8oY5MMvQkmDWpeIhdRM4mxiWmqKuUk0PYgL6e2JIcL6QUHkHD/NwAuzDZa8HL5snOFs+wjXmdBYkN7x9e5M0Ntdsp8Vbw=="], "priority": ["100"]}}, {"id": "4370c7ed-13f2-4a1d-ad7f-32bc7e5fa58e", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["205e7c58-77cc-4410-b2a6-86c38b3c6f90"], "secret": ["Pg8UpOQquorgqScQt8CXAg"], "priority": ["100"]}}, {"id": "33b2b8f5-98e2-4673-a976-141c0cec29dc", "name": "hmac-generated-hs512", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["7773be1a-8ab5-4b1b-ac95-080155a57725"], "secret": ["IDV48O476iFJT9GviZ6lAzwi0Sv2KrveWYB2Hsp-8uGcO2KgdxHBqPFYywOclj-nu08M4k-yzwV6YkER296ipxXwG3Yad_26kqZEgZXPM0dqG79QxAekOkXJyt7ttE1bMWs2MEnkbkK7ufw9Ji2zlOv9CCPNGbFcfTpJyX8moFQ"], "priority": ["100"], "algorithm": ["HS512"]}}, {"id": "61040414-276b-4bea-b7a5-e5f3630c5784", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAqwFJqy7TLm+DYt+AYMXPNwoLQFqckTK8yNGiM5WbWwlqko3iQuxz+pbTCOuFdBpEg2AM6uw8hGkJNR1JnbOTw7NutoVt+e5vZxPwk61C4Qfbh7OaaXEVMtSd0BtVr/4hKICIDvsF4JIPpZLXT4sZHw3m3LwQON8OngfvtWWF8jx0DX/vwXvhH/myOb8kS/6yyRWs/P9RK3YZNpRCCVGBFAAPfDSzxpYWDdU4vqgxaddV0hcjeZ69cdezXGY1YietaKiSvYow4OBoCypQq9z+JbqQ0Y6OLm4Zsas7NgIRGpCWpwE7S3KBTNsErcpkrMfv5iacGH5f703D+pKxuoanDwIDAQABAoIBABib7NVyaE3BxS9Qvrwue1g5B87REMBh2YKE40oieOMBreaSqJcSSXaB3LOR6/iotxIORo2gRZfrOGSpEakHk3eRtkMUsfOfzpm6PYrjCkGGzJ7wx9iYv4Dl0XgwwYS8QQVVHz9t/Is0Odriu5Lk5PlFGjjLOle/h0OoHLpcITSaJlTG/ib08rhbQuNai6pz34orngpdz8BKOq4WtKHeI0v/yfiJhUIIbbtpZ1yopTlezQA3AHt4zOpX5RRKPAfqU2rB2zfCgsynp2EhtWgihcSfuXEEep2xecFk1AH5xCd8nvlnfo4PjRQ+AKo2IsO34Ju7SAdvgpfgXHuDfpQwBZECgYEA1hEgsPPAlTCxpp563A2Z671LAZeGAkS+ZnajxQ/tquN6vRz/sPU4ncja88TZD3jSVPa3kwsaeHjiACu+F2c9Tb2c9kos9gu7dsJ395LLhvmtJocplYLJRrSkapwe9atCVvnWx+Q+Db2EqmnydbIdFrbubPq0rQzwXD863Kv5TCUCgYEAzIC5bV2CJBtYUNeTtnlKJA6MCNfTHTCHzwr2efRpLfMzfmhzHvyj9X7/cSCtseR139KiDAfXzoqNz2DPVwOE9yLSjliuLMI41CotBbeWq7De3wrFpgpx3Q/ruUWjwCTkpdN2M1abtfVsCva76befmiGrNPW8itrm0HR/v7Zo5iMCgYB/U16zgbiJxJXWOitvmfN0gXoMr1i5HrR1c5nDi87ct0N5GreHRMRxLVf2aJ6HEmFgwgK8xnzvLU+XtGzztKeFBwRGhiYqDx5o7BC5RCok1XtFEF4OnOUlVir5cnUDwOLLov06lirGt3QifLQHh3K/4Pz9+zzeNXT+GWnkebHltQKBgHk80zFgH1hSkdATjfG/BFJOAXz4nqMOc2UuZaM1X+mq5dtKBv3W30kaikvCCNeOzsbrs8AYIjvDboEELseyQXDWRye2sUO9vT0i5+Ac0AlnaSmn1Dc0t/5Lgv8qpwKxnJAmxkt3TAbawbvaPNs5TjhtP3e9O5LCZdYtStMYTCYrAoGBAI78+2EINbWr1/9nzEHmYi156pML1QiUcLoTBvfV5qh2PUq03QlhpkfkGH5y6wIFjBpNNRyvwbzzND9bjdzR26G1KF9hf1Z7xH0+ZSaMR5bFytmJxv8igYIUVI0WtjQM3LeYdALUjaqI8mYdwTNUzKlGmcYNUmDkq9Fu1jlcefLM"], "keyUse": ["ENC"], "certificate": ["MIIClzCCAX8CBgGTd/BWWjANBgkqhkiG9w0BAQsFADAPMQ0wCwYDVQQDDAR0ZXN0MB4XDTI0MTEyOTEyMzkxOVoXDTM0MTEyOTEyNDA1OVowDzENMAsGA1UEAwwEdGVzdDCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKsBSasu0y5vg2LfgGDFzzcKC0BanJEyvMjRojOVm1sJapKN4kLsc/qW0wjrhXQaRINgDOrsPIRpCTUdSZ2zk8OzbraFbfnub2cT8JOtQuEH24ezmmlxFTLUndAbVa/+ISiAiA77BeCSD6WS10+LGR8N5ty8EDjfDp4H77VlhfI8dA1/78F74R/5sjm/JEv+sskVrPz/USt2GTaUQglRgRQAD3w0s8aWFg3VOL6oMWnXVdIXI3mevXHXs1xmNWInrWiokr2KMODgaAsqUKvc/iW6kNGOji5uGbGrOzYCERqQlqcBO0tygUzbBK3KZKzH7+YmnBh+X+9Nw/qSsbqGpw8CAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAP3FtkIPSAR8ZLVoiWkCEnOl0KUk1WA+/Fnb+3zdfx8bLD2ngGYT2ErgfIMurIQF1ptNRTjMJF7U3hcOQIbS3myP6ImacjQ9xqiWDwueCs41sSYO2pfIilbpHJXpI+hFm3JbK9YtYYsn6NTZzb7QNslWKowPzXKWV6kq7RLshocj2OZC/YlXeze8L0m+PlA/33510nr7dKRhqxxagpFCdtBBxICwTNgoFpOjbMmyA0G0Z53/wc+9SBy4ysnf/S67QCFg+SAtlvNlvjxZpnsDg9ahNBaCW5keOdhhASVurZc6aWL+6JBziAak6n/N7b74NQZ9KgWg05Zu32NUENKEEsA=="], "priority": ["100"], "algorithm": ["RSA-OAEP"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "a5cf9ae2-f56a-48b7-a02e-9991a285f132", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "8a0f6dd7-96cd-4e02-9a5d-b2e75db524b4", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "d1e28eb0-1ce1-44f9-8acf-01dd16493952", "alias": "Browser - Conditional Organization", "description": "Flow to determine if the organization identity-first login is to be used", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "organization", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "3d2f6c2a-e76d-4c5d-9804-89ea2579bb67", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "99f00bd9-0aa0-473d-ac8d-5d9a29fe8ca8", "alias": "First Broker Login - Conditional Organization", "description": "Flow to determine if the authenticator that adds organization members is to be used", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "idp-add-organization-member", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "e0355304-503f-42a1-af30-930422b19003", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "70ba0b65-d5bf-4c1f-bb07-22ce51c00267", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "2cdff65b-a950-4355-9239-097718b6b88c", "alias": "Organization", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 10, "autheticatorFlow": true, "flowAlias": "Browser - Conditional Organization", "userSetupAllowed": false}]}, {"id": "f3b066ba-c149-4748-ba3a-5274a410d11b", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "4c749ecd-1045-4a59-b326-85d4942d1ad1", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "6827c49b-2d6e-41c0-878d-6aa6ff1972de", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "724fff13-9893-448f-a913-9c40874676f8", "alias": "browser", "description": "Browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 26, "autheticatorFlow": true, "flowAlias": "Organization", "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "d4057b9a-013c-4f95-b358-8e22cd7ee848", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "e7a8813e-97f3-48d5-a1c4-bfd34e5a7324", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "fd019ff0-5f39-4733-b274-062804f56d19", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "59d9a184-6e67-407d-8416-581c64302815", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 50, "autheticatorFlow": true, "flowAlias": "First Broker Login - Conditional Organization", "userSetupAllowed": false}]}, {"id": "6c5d45c5-3941-474c-bb67-1511fb1abc33", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "f5fe0754-2643-40b4-ac26-9baf673a6250", "alias": "registration", "description": "Registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "4751235c-0eaf-4acb-910c-0c2f75d18b16", "alias": "registration form", "description": "Registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-terms-and-conditions", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 70, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "1dc6d03a-d9ad-4347-8ebb-635010dc3d03", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "55a5e3d0-ee7f-46ee-a93f-0a255fd9b49c", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "cecadfa7-f77a-4579-ab83-fce6c8cb9ce5", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "b3cff144-522e-498d-a2d4-cf1710933ba8", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": true, "defaultAction": false, "priority": 80, "config": {}}, {"alias": "VERIFY_PROFILE", "name": "Verify Profile", "providerId": "VERIFY_PROFILE", "enabled": true, "defaultAction": false, "priority": 90, "config": {}}, {"alias": "delete_credential", "name": "Delete Credential", "providerId": "delete_credential", "enabled": true, "defaultAction": false, "priority": 100, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "firstBrokerLoginFlow": "first broker login", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "clientOfflineSessionMaxLifespan": "0", "oauth2DevicePollingInterval": "5", "clientSessionIdleTimeout": "0", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "clientOfflineSessionIdleTimeout": "0", "cibaInterval": "5", "realmReusableOtpCode": "false"}, "keycloakVersion": "26.1.2", "userManagedAccessAllowed": false, "organizationsEnabled": false, "verifiableCredentialsEnabled": false, "adminPermissionsEnabled": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}