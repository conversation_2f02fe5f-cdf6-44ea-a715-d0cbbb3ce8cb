{"id": "import-without-clients", "realm": "import-without-clients", "notBefore": 0, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "offlineSessionIdleTimeout": 2592000, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "045e743f-43b1-4e60-9d51-be3cbe71d6aa", "name": "uma_authorization", "description": "${role_uma_authorization}", "scopeParamRequired": false, "composite": false, "clientRole": false, "containerId": "import-without-clients"}, {"id": "d2d8ee95-b0a2-4e7c-b379-9f840be85935", "name": "offline_access", "description": "${role_offline-access}", "scopeParamRequired": true, "composite": false, "clientRole": false, "containerId": "import-without-clients"}]}, "groups": [], "defaultRoles": ["uma_authorization", "offline_access"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "browserSecurityHeaders": {"xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "a2a9471e-0f18-4847-b64a-7ad3cd1f0597", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "efba5b6b-dd97-4e2c-907d-c1c957e20ec4", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "12d8a070-da0e-4825-9fb5-6cb637e998e5", "name": "Allowed Client Templates", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {}}, {"id": "956db004-8833-4b95-9266-fedbf564c902", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "c3c67c56-3c88-46bd-8b23-d8f92dcd8231", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-attribute-mapper", "oidc-address-mapper", "saml-user-property-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-full-name-mapper", "saml-user-attribute-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "fd4d4055-f3fa-491a-8dd4-ee8ee9354b6d", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "1982560c-6689-434f-9a3a-6c6c5513b014", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "saml-user-attribute-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-full-name-mapper"], "consent-required-for-all-mappers": ["true"]}}, {"id": "c5ccbffe-a3fe-4c77-a6a7-3679e7f281f5", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}], "org.keycloak.keys.KeyProvider": [{"id": "bb5ce31b-932b-402b-8cc5-14e84e81e639", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "a8ddc80d-b0e3-4365-bd1f-12a47b2eb868", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "e3a30dae-8267-4673-b0f4-454491200cb9", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "662c0102-45d2-4719-b685-61a999d12f2d", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "a16d6bf5-3c20-41c1-b546-8a18fd64009d", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "5fc097f3-747a-478b-b01c-e0ca7cafdbb0", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "f5368d26-e65c-459e-ae80-cb298f895872", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "e02ce186-73d8-4ee7-a442-e1fe6bc66649", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "OPTIONAL", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "417fa197-dc0e-4e32-925b-1***********", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "a5745cc1-2413-41b3-bb72-a80c33c2e346", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "********-e5a1-46c7-ac08-f8e8c765090f", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "OPTIONAL", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "3dd5e424-da91-4401-b56f-b54da15c73cb", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "f0889639-f982-4354-bbec-1d7d80a1d24f", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "07606ef3-c2ef-4b77-9660-2cc7fbe60be1", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "OPTIONAL", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "5bd8314b-75db-4677-9699-e3f5d814cc24", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "19402384-99b2-4088-96ca-e50c2ba56b30", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "388ce6bb-09c8-43a8-a429-5dbd5516510f", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"_browser_header.xFrameOptions": "SAMEORIGIN", "_browser_header.strictTransportSecurity": "max-age=31536000; includeSubDomains", "permanentLockout": "false", "quickLoginCheckMilliSeconds": "1000", "_browser_header.xRobotsTag": "none", "maxFailureWaitSeconds": "900", "minimumQuickLoginWaitSeconds": "60", "failureFactor": "30", "actionTokenGeneratedByUserLifespan": "300", "maxDeltaTimeSeconds": "43200", "_browser_header.xContentTypeOptions": "nosniff", "actionTokenGeneratedByAdminLifespan": "43200", "bruteForceProtected": "false", "_browser_header.contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "waitIncrementSeconds": "60"}, "userManagedAccessAllowed": false}